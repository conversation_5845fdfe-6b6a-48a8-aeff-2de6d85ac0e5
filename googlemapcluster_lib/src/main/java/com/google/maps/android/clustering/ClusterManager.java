package com.google.maps.android.clustering;

import android.content.Context;
import android.os.AsyncTask;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.Marker;
import com.google.maps.android.MarkerManager;
import com.google.maps.android.clustering.algo.Algorithm;
import com.google.maps.android.clustering.algo.GridBasedAlgorithm;
import com.google.maps.android.clustering.algo.PreCachingAlgorithmDecorator;
import com.google.maps.android.clustering.view.ClusterRenderer;
import com.google.maps.android.clustering.view.DefaultClusterRenderer;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Groups many items on a map based on zoom level.
 * <p/>
 * ClusterManager should be added to the map as an:
 * <ul>
 * <li>{@link com.google.android.gms.maps.GoogleMap.OnCameraChangeListener}</li>
 * <li>{@link com.google.android.gms.maps.GoogleMap.OnMarkerClickListener}</li>
 * </ul>
 */
public class ClusterManager<T extends ClusterItem> implements
		GoogleMap.OnCameraChangeListener, GoogleMap.OnMarkerClickListener,
		GoogleMap.OnInfoWindowClickListener, GoogleMap.OnMarkerDragListener {
	private final MarkerManager mMarkerManager;
	private final MarkerManager.Collection mMarkers;
	private final MarkerManager.Collection mClusterMarkers;

	private Algorithm<T> mAlgorithm;
	private final ReadWriteLock mAlgorithmLock = new ReentrantReadWriteLock();
	private ClusterRenderer<T> mRenderer;

	private GoogleMap mMap;
	private CameraPosition mPreviousCameraPosition;
	private ClusterTask mClusterTask;
	private final ReadWriteLock mClusterTaskLock = new ReentrantReadWriteLock();

	private OnClusterItemClickListener<T> mOnClusterItemClickListener;
	private OnClusterInfoWindowClickListener<T> mOnClusterInfoWindowClickListener;
	private OnClusterItemInfoWindowClickListener<T> mOnClusterItemInfoWindowClickListener;
	private OnClusterClickListener<T> mOnClusterClickListener;
	private OnClusterItemDragStartListener<T> mOnClusterItemDragStartListener;
	private OnClusterItemDragListener<T> mOnClusterItemDragListener;
	private OnClusterItemDragEndListener<T> mOnClusterItemDragEndListener;
	private OnClusterCameraChangeListener mOnClusterCameraChangeListener = null;
	private final Handler handler = new Handler(Looper.getMainLooper());
	private Runnable runnable;

	// Nivel de zoom actual
	private float mZoomLevel;

	public ClusterManager(Context context, GoogleMap map) {
		this(context, map, new MarkerManager(map));
	}

	public ClusterManager(Context context, GoogleMap map,
			MarkerManager markerManager) {
		mZoomLevel = map.getCameraPosition().zoom;
		mMap = map;
		mMarkerManager = markerManager;
		mClusterMarkers = markerManager.newCollection();
		mMarkers = markerManager.newCollection();
		mRenderer = new DefaultClusterRenderer<T>(context, map, this);
		mAlgorithm = new PreCachingAlgorithmDecorator<T>(
				new GridBasedAlgorithm<T>());
		mClusterTask = new ClusterTask();
		mRenderer.onAdd();
	}

	public MarkerManager.Collection getMarkerCollection() {
		return mMarkers;
	}

	public MarkerManager.Collection getClusterMarkerCollection() {
		return mClusterMarkers;
	}

	public MarkerManager getMarkerManager() {
		return mMarkerManager;
	}

	public void setRenderer(ClusterRenderer<T> view) {
		mRenderer.setOnClusterClickListener(null);
		mRenderer.setOnClusterItemClickListener(null);
		mRenderer.setOnClusterItemDragStartListener(null);
		mRenderer.setOnClusterItemDragListener(null);
		mRenderer.setOnClusterItemDragEndListener(null);

		mClusterMarkers.clear();
		mMarkers.clear();
		mRenderer.onRemove();
		mRenderer = view;
		mRenderer.onAdd();
		mRenderer.setOnClusterClickListener(mOnClusterClickListener);
		mRenderer
				.setOnClusterInfoWindowClickListener(mOnClusterInfoWindowClickListener);
		mRenderer.setOnClusterItemClickListener(mOnClusterItemClickListener);
		mRenderer
				.setOnClusterItemInfoWindowClickListener(mOnClusterItemInfoWindowClickListener);
		mRenderer
				.setOnClusterItemDragStartListener(mOnClusterItemDragStartListener);
		mRenderer.setOnClusterItemDragListener(mOnClusterItemDragListener);
		mRenderer
				.setOnClusterItemDragEndListener(mOnClusterItemDragEndListener);

//		cluster();
	}

	public void setAlgorithm(Algorithm<T> algorithm) {
		mAlgorithmLock.writeLock().lock();
		try {
			if (mAlgorithm != null) {
				algorithm.addItems(mAlgorithm.getItems());
			}
			mAlgorithm = new PreCachingAlgorithmDecorator<T>(algorithm);
		} finally {
			mAlgorithmLock.writeLock().unlock();
		}
//		cluster();
	}

	public void clearItems() {
		mAlgorithmLock.writeLock().lock();
		try {
			mAlgorithm.clearItems();
		} finally {
			mAlgorithmLock.writeLock().unlock();
		}
	}

	public void addItems(Collection<T> items) {
		mAlgorithmLock.writeLock().lock();
		try {
			mAlgorithm.addItems(items);
		} finally {
			mAlgorithmLock.writeLock().unlock();
		}

	}

	public void addItem(T myItem) {
		mAlgorithmLock.writeLock().lock();
		try {
			mAlgorithm.addItem(myItem);
		} finally {
			mAlgorithmLock.writeLock().unlock();
		}
	}

	public void removeItem(T item) {
		mAlgorithmLock.writeLock().lock();
		try {
			mAlgorithm.removeItem(item);
		} finally {
			mAlgorithmLock.writeLock().unlock();
		}
	}

	/**
	 * Force a re-cluster. You may want to call this after adding new item(s).
	 */
	public void cluster() {
		if (runnable != null) {
			handler.removeCallbacks(runnable);
		}

		runnable = () -> {
			mClusterTaskLock.writeLock().lock();
			try {
				// Attempt to cancel the in-flight request.
				mClusterTask.cancel(true);
				mClusterTask = new ClusterTask();
				mClusterTask.execute(mMap.getCameraPosition().zoom);
			} finally {
				mClusterTaskLock.writeLock().unlock();
			}
		};

		handler.postDelayed(runnable, 50);
	}

	/**
	 * Might re-cluster.
	 * 
	 * @param cameraPosition
	 */
	@Override
	public void onCameraChange(CameraPosition cameraPosition) {
		if (mRenderer instanceof GoogleMap.OnCameraChangeListener) {
			((GoogleMap.OnCameraChangeListener) mRenderer).onCameraChange(cameraPosition);
		}

		// Don't re-compute clusters if the map has just been
		// panned/tilted/rotated.
		CameraPosition position = mMap.getCameraPosition();

		mZoomLevel = position.zoom;

		if (mPreviousCameraPosition == null || mPreviousCameraPosition.zoom != position.zoom) {
			mPreviousCameraPosition = mMap.getCameraPosition();
		}

		if (mOnClusterCameraChangeListener != null)
			mOnClusterCameraChangeListener.onClusterCameraChange(position);
	}

	@Override
	public boolean onMarkerClick(Marker marker) {
        // Si el tag del marker es un Runnable, se ejecuta. Se hace así para ejecutar código
        // relacionado a un marcador que no se incluye en el cluster (por ejemplo, en
        // markersOperationsDones, en el archivo GesElemMapFragment.java). No es la manera indicada
        // de hacerlo, pero introducir estos elementos en el cluster implicaría bastante trabajo
        // (y puede que no interese que estos elementos se agrupen).
        if (marker.getTag() instanceof Runnable)
            ((Runnable) marker.getTag()).run();
		//if(marker.getTitle() != null && marker.getTitle().startsWith("NivelLLenado"))
			//return false;

		return getMarkerManager().onMarkerClick(marker);
	}

	@Override
	public void onInfoWindowClick(Marker marker) {
		getMarkerManager().onInfoWindowClick(marker);
	}

	@Override
	public void onMarkerDrag(Marker marker) {
		getMarkerManager().onMarkerDrag(marker);
	}

	@Override
	public void onMarkerDragStart(Marker marker) {
		getMarkerManager().onMarkerDragStart(marker);
	}

	@Override
	public void onMarkerDragEnd(Marker marker) {
		getMarkerManager().onMarkerDragEnd(marker);
	}

	/**
	 * Runs the clustering algorithm in a background thread, then re-paints when
	 * results come back.
	 */
	private class ClusterTask extends
			AsyncTask<Float, Void, Set<? extends Cluster<T>>> {
		@Override
		protected Set<? extends Cluster<T>> doInBackground(Float... zoom) {
			mAlgorithmLock.readLock().lock();
			try {
				return mAlgorithm.getClusters(zoom[0]);
			} finally {
				mAlgorithmLock.readLock().unlock();
			}
		}

		@Override
		protected void onPostExecute(Set<? extends Cluster<T>> clusters) {
			mRenderer.onClustersChanged(clusters);
					}
	}

	/**
	 * Sets a callback that's invoked when a Cluster is tapped. Note: For this
	 * listener to function, the ClusterManager must be added as a click
	 * listener to the map.
	 */
	public void setOnClusterClickListener(OnClusterClickListener<T> listener) {
		mOnClusterClickListener = listener;
		mRenderer.setOnClusterClickListener(listener);
	}

	/**
	 * Sets a callback that's invoked when a Cluster is tapped. Note: For this
	 * listener to function, the ClusterManager must be added as a info window
	 * click listener to the map.
	 */
	public void setOnClusterInfoWindowClickListener(
			OnClusterInfoWindowClickListener<T> listener) {
		mOnClusterInfoWindowClickListener = listener;
		mRenderer.setOnClusterInfoWindowClickListener(listener);
	}

	/**
	 * Sets a callback that's invoked when an individual ClusterItem is tapped.
	 * Note: For this listener to function, the ClusterManager must be added as
	 * a click listener to the map.
	 */
	public void setOnClusterItemClickListener(
			OnClusterItemClickListener<T> listener) {
		mOnClusterItemClickListener = listener;
		mRenderer.setOnClusterItemClickListener(listener);
	}

	public void setOnClusterItemDragStartListener(
			OnClusterItemDragStartListener<T> listener) {
		mOnClusterItemDragStartListener = listener;
		mRenderer.setOnClusterItemDragStartListener(listener);
	}

	public void setOnClusterItemDragListener(
			OnClusterItemDragListener<T> listener) {
		mOnClusterItemDragListener = listener;
		mRenderer.setOnClusterItemDragListener(listener);
	}

	public void setOnClusterItemDragEndListener(
			OnClusterItemDragEndListener<T> listener) {
		mOnClusterItemDragEndListener = listener;
		mRenderer.setOnClusterItemDragEndListener(listener);
	}

	public void setOnClusterCameraChangeListener(
			OnClusterCameraChangeListener listener) {
		mOnClusterCameraChangeListener = listener;
	}

	/**
	 * Sets a callback that's invoked when an individual ClusterItem's Info
	 * Window is tapped. Note: For this listener to function, the ClusterManager
	 * must be added as a info window click listener to the map.
	 */
	public void setOnClusterItemInfoWindowClickListener(
			OnClusterItemInfoWindowClickListener<T> listener) {
		mOnClusterItemInfoWindowClickListener = listener;
		mRenderer.setOnClusterItemInfoWindowClickListener(listener);
	}

	public float getZoom() {

		return mZoomLevel;
	}

	/**
	 * Called when a Cluster is clicked.
	 */
	public interface OnClusterClickListener<T extends ClusterItem> {
		boolean onClusterClick(Cluster<T> cluster);
	}

	/**
	 * Called when a Cluster's Info Window is clicked.
	 */
	public interface OnClusterInfoWindowClickListener<T extends ClusterItem> {
		void onClusterInfoWindowClick(Cluster<T> cluster);
	}

	/**
	 * Called when an individual ClusterItem is clicked.
	 */
	public interface OnClusterItemClickListener<T extends ClusterItem> {
		boolean onClusterItemClick(T item);
	}

	/**
	 * Called when an individual ClusterItem's Info Window is clicked.
	 */
	public interface OnClusterItemInfoWindowClickListener<T extends ClusterItem> {
		void onClusterItemInfoWindowClick(T item);
	}

	/**
	 * Llamada mientras se esti a moviendo un item
	 */
	public interface OnClusterItemDragListener<T extends ClusterItem> {
		void onClusterItemDrag(T item);
	}

	/**
	 * Llamada cuando se levanta un item para moverlo
	 */
	public interface OnClusterItemDragStartListener<T extends ClusterItem> {
		void onClusterItemDragStart(T item);
	}

	/**
	 * Llamada cuando se deja caer un item despuis de moverlo
	 */
	public interface OnClusterItemDragEndListener<T extends ClusterItem> {
		void onClusterItemDragEnd(T item);
	}

	/**
	 * Llamada cuando se hace zoom o se mueve la cartografia
	 */
	public interface OnClusterCameraChangeListener {
		void onClusterCameraChange(CameraPosition cameraPosition);
	}

}
