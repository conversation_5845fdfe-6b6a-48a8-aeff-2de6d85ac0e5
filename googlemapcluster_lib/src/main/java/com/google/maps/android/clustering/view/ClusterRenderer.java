package com.google.maps.android.clustering.view;

import com.google.maps.android.clustering.Cluster;
import com.google.maps.android.clustering.ClusterItem;
import com.google.maps.android.clustering.ClusterManager;

import java.util.Set;

/**
 * Renders clusters.
 */
public interface ClusterRenderer<T extends ClusterItem> {

	/**
	 * Called when the view needs to be updated because new clusters need to be
	 * displayed.
	 * 
	 * @param clusters
	 *            the clusters to be displayed.
	 */
	void onClustersChanged(Set<? extends Cluster<T>> clusters);

	void setOnClusterClickListener(
			ClusterManager.OnClusterClickListener<T> listener);

	void setOnClusterInfoWindowClickListener(
			ClusterManager.OnClusterInfoWindowClickListener<T> listener);

	void setOnClusterItemClickListener(
			ClusterManager.OnClusterItemClickListener<T> listener);

	void setOnClusterItemInfoWindowClickListener(
			ClusterManager.OnClusterItemInfoWindowClickListener<T> listener);

	void setOnClusterItemDragStartListener(
			ClusterManager.OnClusterItemDragStartListener<T> listener);

	void setOnClusterItemDragListener(
			ClusterManager.OnClusterItemDragListener<T> listener);

	void setOnClusterItemDragEndListener(
			ClusterManager.OnClusterItemDragEndListener<T> listener);

	/**
	 * Called when the view is added.
	 */
	void onAdd();

	/**
	 * Called when the view is removed.
	 */
	void onRemove();
}