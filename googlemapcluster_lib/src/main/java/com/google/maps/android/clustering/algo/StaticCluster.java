package com.google.maps.android.clustering.algo;

import com.google.android.gms.maps.model.LatLng;
import com.google.maps.android.clustering.Cluster;
import com.google.maps.android.clustering.ClusterItem;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * A cluster whose center is determined upon creation.
 */
public class StaticCluster<T extends ClusterItem> implements Cluster<T> {
	private LatLng mCenter = null;
	private final List<T> mItems = new ArrayList<T>();

	public StaticCluster(LatLng center) {
		setCenter(center);
	}

	public boolean add(T t, double zoom) {
		double lat, lon, difLat, difLon;
		difLat = (mCenter.latitude - t.getPosition().latitude) / 2.0;
		difLon = (mCenter.longitude - t.getPosition().longitude) / 2.0;

		if (difLat < 0)
			difLat = -difLat;
		if (difLon < 0)
			difLon = -difLon;

		if (mCenter.latitude > t.getPosition().latitude)
			lat = mCenter.latitude - difLat;
		else
			lat = mCenter.latitude + difLat;

		if (mCenter.longitude > t.getPosition().longitude)
			lon = mCenter.longitude - difLon;
		else
			lon = mCenter.longitude + difLon;

		LatLng center = new LatLng(lat, lon);
		setCenter(center);

		return mItems.add(t);
	}

	public void setCenter(LatLng center) {
		mCenter = center;
	}

	@Override
	public LatLng getPosition() {
		return mCenter;
	}

	public boolean remove(T t) {
		return mItems.remove(t);
	}

	@Override
	public Collection<T> getItems() {
		return mItems;
	}

	@Override
	public int getSize() {
		return mItems.size();
	}

	@Override
	public String toString() {
		return "StaticCluster{" + "mCenter=" + mCenter + ", mItems.size="
				+ mItems.size() + '}';
	}
}