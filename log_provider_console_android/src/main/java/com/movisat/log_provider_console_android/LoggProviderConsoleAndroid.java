package com.movisat.log_provider_console_android;

import android.app.Activity;
import android.util.Log;

import com.movisat.log.ILoggProvider;
import com.movisat.log.LoggLine;
import com.movisat.log.LoggType;

public class Logg<PERSON>roviderConsoleAndroid implements ILoggProvider {
//    private final String resetColor = "\\x1B[0m";

    private Activity activity;

    public LoggProviderConsoleAndroid(Activity activity) {
        this.activity = activity;
    }

    public String getEmoji(LoggType type) {
        switch (type) {
            case DEBUG:
                return "🐛";
            case INFO:
                return "💡";
            case WARNING:
                return "⚠";
            case ERROR:
                return "⛔";
            case DATABASE:
                return "💾";
            case SYNCHRONIZATION:
                return "📡";
            case CATASTROPHE:
                return "😱";
        }
        return "";
    }

/*
    public String getColor(LoggType type) {
        switch (type) {
            case DEBUG:
                return "\\x1B[32m";
            case INFO:
                return "\\x1B[36m";
            case WARNING:
                return "\\x1B[33m";
            case ERROR:
                return "\\x1B[31m";
            case DATABASE:
                return "\\x1B[34m";
            case SYNCHRONIZATION:
                return "\\x1B[35m";
            case CATASTROPHE:
                return "\\x1B[31m";
        }
        return "";
    }*/


    @Override
    public boolean isStarted() {
        return true;
    }

    @Override
    public void start() {
    }


    @Override
    public void write(LoggLine line) {

        if (!BuildConfig.DEBUG) return;

        activity.runOnUiThread(() -> {
            String type = line.logType.toString();
            String logTypeEmoji = getEmoji(line.logType);
            String message = "[" + line.datetime + "] " + logTypeEmoji + " [" + type + "] " + line.message;

            switch (line.logType) {
                case DATABASE:
                case SYNCHRONIZATION:
                case DEBUG:
                    Log.d(type, message);
                    break;
                case INFO:
                    Log.i(type, message);
                    break;
                case WARNING:
                    Log.w(type, message);
                    break;
                case CATASTROPHE:
                case ERROR:
                    Log.e(type, message);
                    break;
            }
        });

    }

}
