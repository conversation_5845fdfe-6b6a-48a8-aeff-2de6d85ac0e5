package com.movisat.api;

public class ApiResponse<T> {
    public T body;
    public String message;
    public int code;
    public boolean isError = false;

    public ApiResponse() {
    }

    public ApiResponse(T body, String message, int code, boolean hasError) {
        this.body = body;
        this.message = message;
        this.code = code;
        this.isError = hasError;
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "body=" + body +
                ", message='" + message + '\'' +
                ", code=" + code +
                ", hasError=" + isError +
                '}';
    }
}
