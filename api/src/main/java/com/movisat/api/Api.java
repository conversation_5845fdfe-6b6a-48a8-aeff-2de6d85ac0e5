package com.movisat.api;

import android.util.Log;

import com.androidnetworking.AndroidNetworking;
import com.androidnetworking.common.ANRequest;
import com.androidnetworking.common.Priority;
import com.androidnetworking.error.ANError;
import com.androidnetworking.interfaces.StringRequestListener;
import com.google.gson.Gson;
import com.movisat.utils.ICallBack;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

/**
 * En el Manifest deben ir los siguientes permisos:
 * <uses-permission android:name="android.permission.INTERNET" />
 */
public class Api {

    private boolean isPrintLog = true;

    /// Permite definir el destino de los mensajes de log. Si no se asigna usará [Log.i()].
    public static ICallBack<String> logging;

    public Api noLogs() {
        Api api = new Api();
        api.isPrintLog = false;
        return api;
    }

    private void print(String message) {
        if (!isPrintLog) return;
        if (logging != null) logging.execute(message);
        else
            Log.i("API", message);

    }

    public void postString(String url, final Map<String, String> header, final String body, final ICallBack<ApiResponse<String>> callback) {
        final ApiResponse apiResponse = new ApiResponse<String>();

        ANRequest request = AndroidNetworking.post(url)
                .addHeaders(header)
                .addStringBody(body)
                .setContentType("application/x-www-form-urlencoded; charset=UTF-8") // custom ContentType
                .setPriority(Priority.IMMEDIATE)
                .build();

        print("🔺 POST_STRING [" + url + "] " + " Body: " + request.getRequestBody());

        request.getAsString(new StringRequestListener() {

            @Override
            public void onResponse(String response) {
                print("🔻 POST_STRING [" + url + "] " + " Respuesta: " + response.toString());
                apiResponse.body = response;
                apiResponse.code = 0;
                apiResponse.message = "";
                apiResponse.isError = false;
                callback.execute(apiResponse);
            }

            @Override
            public void onError(ANError anError) {
                apiResponse.code = anError.getErrorCode();
                apiResponse.message = anError.getMessage();
                apiResponse.isError = true;
                print("🔻 POST_STRING [" + url + "] " + " Error: " + anError.getErrorDetail() + " - " + apiResponse.toString());
                callback.execute(apiResponse);

            }
        });
    }


    public void post(String url, final Map<String, String> header, final Map<String, Object> body, final ICallBack<ApiResponse<String>> callback) throws JSONException {
        final ApiResponse apiResponse = new ApiResponse<String>();
        final JSONObject json = new JSONObject();
        for (Map.Entry p : body.entrySet()) {
            json.put(p.getKey().toString(), p.getValue());
        }

        print("🔺 POST [" + url + "] " + " Body: " + json.toString());

        ANRequest request = AndroidNetworking.post(url)
                .addHeaders(header)
                .addJSONObjectBody(json)
                .setPriority(Priority.IMMEDIATE)
                .build();


        request.getAsString(new StringRequestListener() {

            @Override
            public void onResponse(String response) {
                print("🔻 POST [" + url + "] " + " Respuesta: " + response.toString());
                apiResponse.body = response;
                apiResponse.code = 0;
                apiResponse.message = "";
                apiResponse.isError = false;
                callback.execute(apiResponse);
            }

            @Override
            public void onError(ANError anError) {
                apiResponse.code = anError.getErrorCode();
                apiResponse.message = anError.getMessage();
                apiResponse.isError = true;
                print("🔻 POST [" + url + "] " + " Error: " + anError.getErrorDetail() + " - " + apiResponse.toString());
                callback.execute(apiResponse);

            }
        });
    }


    public void get(String url, final Map<String, String> header, final ICallBack<ApiResponse<String>> callback) {
        final ApiResponse apiResponse = new ApiResponse<String>();

        ANRequest request = AndroidNetworking.get(url)
                .addHeaders(header)
                .setPriority(Priority.IMMEDIATE)
                .build();

        print("🔺 GET [" + url + "] ");

        request.getAsString(new StringRequestListener() {

            @Override
            public void onResponse(String response) {
                print("🔻 GET [" + url + "] " + " Respuesta: " + response.toString());
                apiResponse.body = response;
                apiResponse.code = 0;
                apiResponse.message = "";
                apiResponse.isError = false;
                callback.execute(apiResponse);
            }

            @Override
            public void onError(ANError anError) {
                apiResponse.code = anError.getErrorCode();
                apiResponse.message = anError.getMessage();
                apiResponse.isError = true;
                print("🔻 GET [" + url + "] " + " Error: " + anError.getErrorDetail() + " - " + apiResponse.toString());
                callback.execute(apiResponse);
            }
        });
    }
}

