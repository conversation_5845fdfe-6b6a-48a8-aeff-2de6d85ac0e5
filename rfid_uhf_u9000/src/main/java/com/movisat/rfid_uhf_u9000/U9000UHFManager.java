package com.movisat.rfid_uhf_u9000;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import com.magicrf.uhfreaderlib.reader.Tools;
import com.magicrf.uhfreaderlib.reader.UhfReader;
import com.movisat.utils.IReturn;
import com.movisat.utils_android.AbisType;
import com.movisat.utils_android.UtilssAndroid;

import java.util.List;

/**
 * Es necesario añadir lo siguiente en el proyecto principal:
 * - app > build.gradle > defaultConfig > ndk { abiFilters "armeabi", "armeabi-v7a", "x86", "mips" }
 */
public class U9000UHFManager {
   private static U9000UHFManager mInstance;
   private static String TAG = "U9000UHFManager";
   private UhfReader reader = null;
   private String PATH_UHF = "/dev/ttyS2";
   private boolean isInit = false;
   private List<byte[]> epcList;
   private String epcStr;
   private int power = 26;
   private ITagReadedAdditional tagReadedAdditional;
   private IPlaySound playSound;
   private IReturn<Context> getContext;

   public static U9000UHFManager get() {
      if (mInstance == null)
         mInstance = new U9000UHFManager();
      return mInstance;
   }

   public boolean isCompatible() {
      return UtilssAndroid.hasAbis(new AbisType[]{AbisType.armeabi_v7a, AbisType.armeabi, AbisType.x86, AbisType.mips,});
   }

   public boolean initRFID(ITagReadedAdditional tagReadedAdditional, IPlaySound playSound, IReturn<Context> getContext) {
      this.tagReadedAdditional = tagReadedAdditional;
      this.playSound = playSound;
      this.getContext = getContext;

      if (Build.MODEL.equals("PDA")) {
         LFPowerUtils.power("1");
         UhfReader.setPortPath(PATH_UHF);
         reader = UhfReader.getInstance();
         isInit = reader.setOutputPower(power);
         if (isInit) {
            Log.d(TAG, "initRFID: UHF reader initialized");
         } else {
            Log.d(TAG, "initRFID: UHF reader not initialized");
         }
         byte[] versionBytes = reader.getFirmware();
         String version = "Version not found";
         if (versionBytes != null) {
            version = new String(versionBytes);
         }
         Log.e(TAG, "initRFID: Module version: " + version);
         return isInit;
      }
      return false;
   }

   public boolean isInit() {
      return isInit;
   }

   public String readTag() {
      reader.close();
      UhfReader.setPortPath(PATH_UHF);
      reader = UhfReader.getInstance();
      reader.setOutputPower(power);

      if (isInit) {
         try {
            epcList = reader.inventoryRealTime();

            // La primera lectura nunca funciona.
            if (epcList == null || epcList.isEmpty()) {
               Log.i(TAG, "Se vuelve a leer porque no el resultado de la primera lectura no es válido");
               epcList = reader.inventoryRealTime();
            }

            if (epcList != null && !epcList.isEmpty()) {
               epcStr = Tools.Bytes2HexString(epcList.get(0), epcList.get(0).length);
            }

            if (epcList != null && !epcList.isEmpty()) {
               reader.selectEpc(Tools.HexString2Bytes(epcStr));
               byte[] accessPassword = new byte[4];
               for (int i = 0; i < accessPassword.length; i++)
                  accessPassword[i] = 0;
               byte[] data = reader.readFrom6C(2, 0, 6, accessPassword);
               String dataStr = LFByteUtils.bytesToHexString(data);
               playSound.execute();
               if (dataStr.length() >= 12) {
                  if (checkTag(dataStr)) {
                     Log.i(TAG, "TAG leído: " + dataStr);
                     tagReadedAdditional.execute(dataStr);
                     return dataStr;
                  } else {
                     Log.i(TAG, "TAG no checkTag(dataStr)");
                  }
               } else {
                  Log.i(TAG, "TAG no dataStr.length() >= 12");
               }
            } else {
               Log.i(TAG, "TAG no epcList != null && !epcList.isEmpty()");
            }
         } catch (Exception e) {
            Log.e(TAG, "Error al leer TAG: " + e.getMessage());
         }
      } else {
         Log.i(TAG, "TAG no leído");
         //return "Error. Module not init";
      }
      TagNotFoundToast.get().showToast(getContext.execute());
      return "Error";
   }

   boolean checkTag(String tag) {
      boolean res = true;
      int count = 0;
      char[] id = tag.toCharArray();

      for (char id_aux : id) {
         if ((id_aux < '0' || id_aux > '9') && (id_aux < 'A' || id_aux > 'F')) {
            res = false;
         }
         if (id_aux == '0')
            count++;
      }
      return res;
   }

   public boolean setPower(int power) {
      try {
         if (reader.setOutputPower(power)) {
            this.power = power;
            return true;
         }
         return false;
      } catch (Exception e) {
         return false;
      }
   }

   public boolean isPressedButtonBlueFront(int keyCode) {
      return keyCode == 307 && isInit();
   }

   public boolean isPressedButtonBlueLeft(int keyCode) {
      return keyCode == 308 && isInit();
   }

   public boolean isPressedButtonBlueRight(int keyCode) {
      return keyCode == 309 && isInit();
   }
}
