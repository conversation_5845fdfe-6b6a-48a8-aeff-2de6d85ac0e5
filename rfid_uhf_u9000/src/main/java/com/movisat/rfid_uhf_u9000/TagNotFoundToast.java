package com.movisat.rfid_uhf_u9000;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Toast;

import androidx.constraintlayout.widget.ConstraintLayout;


/**
 * Clase para mostrar un Toast informativo de que el proceso de lectura UHF está iniciado.
 * Este Toast se mostrará con {@link #showToast(Context)} durante el tiempo especificado.
 */
public class TagNotFoundToast {

    private static final int BLINK_DURATION_MS = 300;

    private static TagNotFoundToast mInstance;

    private Toast mToastToShow;

    private ValueAnimator mBackgroundAnimation;


    /**
     * Devuelve la instancia de la clase con la que mostrar el mensaje de lectura UHF.
     *
     * @return Instancia de UHFReadingToast.
     */
    public static TagNotFoundToast get() {
        if (mInstance == null)
            mInstance = new TagNotFoundToast();
        return mInstance;
    }


    private TagNotFoundToast() {
    }


    /**
     * Muestra un Toast para informar del estado de lectura UHF.
     *
     * @param context Contexto de la aplicación
     */
    public void showToast(Context context) {
        try {
            cancel();

            View layout = LayoutInflater.from(context).inflate(R.layout.toast_uhf_reading2, null);

            final ConstraintLayout layoutToast = layout.findViewById(R.id.layoutToastTagNotFound);

            // Animación de fondo del Toast
            int colorFrom = context.getResources().getColor(R.color.toast_uhf_reading_background);
            int colorTo = context.getResources().getColor(R.color.toast_uhf_reading_background2);
            mBackgroundAnimation = ValueAnimator.ofObject(new ArgbEvaluator(), colorFrom, colorTo);
            mBackgroundAnimation.setStartDelay(0);
            mBackgroundAnimation.setDuration(BLINK_DURATION_MS);
            mBackgroundAnimation.setRepeatCount(3500 / BLINK_DURATION_MS + 1);
            mBackgroundAnimation.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator valueAnimator) {
                    layoutToast.setBackgroundColor((int) valueAnimator.getAnimatedValue());
                }
            });
            mBackgroundAnimation.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    // Para que el Toast desaparezca inmediatamente
                    layoutToast.setVisibility(View.GONE);
                }
            });

            // Configuración del Toast
            mToastToShow = new Toast(context.getApplicationContext());
            mToastToShow.setView(layout);
            mToastToShow.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
            mToastToShow.setDuration(Toast.LENGTH_SHORT);
            mToastToShow.getView().setVisibility(View.VISIBLE);

            // Muestra el Toast e inicia el countdown y la animación
            mToastToShow.show();
            mBackgroundAnimation.start();

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Cancela el Toast, ocultándolo de la pantalla.
     */
    public void cancel() {
        if (mBackgroundAnimation != null) {
            mBackgroundAnimation.end();
        }
    }
}
