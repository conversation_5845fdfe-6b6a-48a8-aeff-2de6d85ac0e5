plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 21
        targetSdk 33
        versionCode 1
        versionName "1.0"
        ndk { abiFilters "armeabi-v7a", "armeabi", "x86", "mips" }
    }

    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    buildToolsVersion '30.0.3'

    namespace 'com.movisat.rfid_uhf_u9000'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation files('libs/uhf_lib.jar')
    implementation 'androidx.multidex:multidex:2.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation project(path: ':utils')
    implementation project(path: ':utils_android')
}