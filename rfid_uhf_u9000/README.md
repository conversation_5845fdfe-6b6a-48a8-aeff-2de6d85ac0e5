### Flujo de publicación de aplicaciones que usen esta librería

Esta librería, por el momento, no tiene compatibilidad con procesadores de 64 bits.

Google Play obliga a que las aplicaciones sean compatibles con 64 bits.

Por lo que si se añade la compatibilidad, se puede publicar, pero no se podrá leer.

La solución es añadir una variable global de compilación para generar 2 versiones. Una irá sin abis filter
en el build.gradle de la aplicación a Google Play. Otra con abis filter de 32 bits a AppCenter.

IMPORTANTE: la versión de AppCenter debe tener un código de versión superior para evitar que se actualice
en Google Play.