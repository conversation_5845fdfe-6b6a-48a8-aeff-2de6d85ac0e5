<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <HorizontalScrollView
        android:layout_width="fill_parent"
        android:layout_height="50sp">

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="1.0">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="destination1"
                android:text="DESTINO 1" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="destination2"
                android:text="DESTINO 2" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="destination3"
                android:text="DESTINO 3" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="routeCalculated1"
                android:text="RUTA CALCULADA 1" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="routeCalculated2"
                android:text="RUTA CALCULADA 2" />


            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="routeOptimized1"
                android:text="RUTA OPTIMIZADA 1" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="routeOptimized2"
                android:text="RUTA OPTIMIZADA 2" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="routeOptimized3"
                android:text="RUTA OPTIMIZADA 3 LIMITADA" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="routeOptimized4"
                android:text="RUTA OPTIMIZADA 4" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:onClick="stop"
                android:text="DETENER" />
        </LinearLayout>
    </HorizontalScrollView>

    <fragment
        android:id="@+id/navigator_child_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1" />


</LinearLayout>
