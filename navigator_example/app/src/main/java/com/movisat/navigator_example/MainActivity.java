package com.movisat.navigator_example;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentTransaction;

import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;

import com.movisat.navigator.NavigatorController;
import com.movisat.navigator.NavigatorControllerState;
import com.movisat.navigator.NavigatorFragment;
import com.movisat.navigator.OnGetIconPOI;
import com.movisat.navigator.OnSaveFile;
import com.movisat.navigator.POI;
import com.movisat.navigator.POIManager;
import com.movisat.navigator.Route;
import com.movisat.navigator.RouteOptimized;
import com.movisat.navigator.RouteOptimizedPoint;
import com.movisat.navigator.RouteOptimized;
import com.movisat.navigator.RoutePoint;
import com.movisat.utils.ICallBack;
import com.movisat.utils.LatLong;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

public class MainActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        controller.onStateChange.subscribe(onMapStateChanged);
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.navigator_child_fragment, new NavigatorFragment(controller)).commit();
    }

    private NavigatorController controller = NavigatorController.page();
    // Limita el mostrado del diálog de asignar idioma.
    private boolean isStartedMessageShowed = false;

    final ICallBack<NavigatorControllerState> onMapStateChanged = (state) -> {
    };

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        controller.dispose();
    }

    public void stop(View view) {
        controller.routeManager.stop();
    }

    public void destination1(View view) {
        controller.routeManager.setDestination(new LatLong(38.09324389, -1.175838889));
    }

    public void destination2(View view) {
        controller.routeManager.setDestination(new LatLong(38.07329083, -1.155564167));
    }

    public void destination3(View view) {
        controller.routeManager.setDestination(new LatLong(38.04687444, -1.207781667));
    }

    public void routeCalculated1(View view) {
        ArrayList<RoutePoint> points = new ArrayList();
        points.add(new RoutePoint(new LatLong(38.003273, -1.144614)));
        points.add(new RoutePoint(new LatLong(38.003053, -1.146403)));
        points.add(new RoutePoint(new LatLong(38.008616, -1.148838)));
        points.add(new RoutePoint(new LatLong(38.007762, -1.140068)));
        points.add(new RoutePoint(new LatLong(38.002487, -1.129081)));
        controller.routeManager.setRoute(new Route(points, "RouteFlexible1"));
    }

    public void routeCalculated2(View view) {
        ArrayList<RoutePoint> points = new ArrayList();
        points.add(new RoutePoint(new LatLong(38.003273, -1.144614)));
        points.add(new RoutePoint(new LatLong(37.999883, -1.120884)));
        points.add(new RoutePoint(new LatLong(37.983006, -1.114104)));
        points.add(new RoutePoint(new LatLong(37.968527, -1.128738)));
        points.add(new RoutePoint(new LatLong(37.955737, -1.150711)));
        points.add(new RoutePoint(new LatLong(37.938478, -1.166074)));
        points.add(new RoutePoint(new LatLong(37.927511, -1.166160)));
        points.add(new RoutePoint(new LatLong(37.926868, -1.211500)));
        points.add(new RoutePoint(new LatLong(37.972485, -1.218195)));
        controller.routeManager.setRoute(new Route(points, "RouteFlexible2"));
    }

    public void routeOptimized1(View view) {
        controller.routeManager.setRouteOptimized(
                new RouteOptimized(
                        new ArrayList<RouteOptimizedPoint>(
                                Arrays.asList(
                                        new RouteOptimizedPoint(new LatLong(38.003273, -1.144614), "Punto 1", "Punto 1 mensaje"),
                                        new RouteOptimizedPoint(new LatLong(38.002550, -1.145842), "Punto 2", "Punto 2 mensaje"),
                                        new RouteOptimizedPoint(new LatLong(38.001502, -1.144255), "Punto 3", "Punto 3 mensaje"),
                                        new RouteOptimizedPoint(new LatLong(38.001257, -1.144051), "Punto 4", "Punto 4 mensaje"),
                                        new RouteOptimizedPoint(new LatLong(38.001908, -1.142903), "Punto 5", "Punto 5 mensaje")
                                )
                        ),
                        "RouteOptimized1",
                        new OnSaveFile() {
                            @Override
                            public String execute(String json) {
                                return create(view.getContext(), json);
                            }
                        },
                        false
                )
        );
    }

    public void routeOptimized2(View view) {
        ArrayList<RouteOptimizedPoint> points = new ArrayList<RouteOptimizedPoint>();
        Location location = new Location(LocationManager.GPS_PROVIDER);

        for (int i = 0; i < 10000; i++) {
            if (i == 0) {
                points.add(new RouteOptimizedPoint(new LatLong(38.003273, -1.144614)));
                location.setLatitude(38.003273);
                location.setLongitude(-1.144614);
            } else {
                location = getLocationInLatLngRad(50, location);
                points.add(new RouteOptimizedPoint(randomColor(), new LatLong(location.getLatitude(), location.getLongitude())));
            }
        }


        controller.routeManager.setRouteOptimized(
                new RouteOptimized(
                        points,
                        "RouteOptimized2",
                        new OnSaveFile() {
                            @Override
                            public String execute(String json) {
                                return create(view.getContext(), json);
                            }
                        },
                        false
                )
        );
    }

    public void routeOptimized3(View view) {
        ArrayList<RouteOptimizedPoint> points = new ArrayList<RouteOptimizedPoint>();
        Location location = new Location(LocationManager.GPS_PROVIDER);

        ArrayList<POI> pois = new ArrayList<>();

        for (int i = 0; i < 1000; i++) {
            if (i == 0) {
                points.add(new RouteOptimizedPoint(new LatLong(38.003273, -1.144614)));
                location.setLatitude(38.003273);
                location.setLongitude(-1.144614);
            } else {
                location = getLocationInLatLngRad(20, location);
                points.add(new RouteOptimizedPoint(new LatLong(location.getLatitude(), location.getLongitude())));
                if (i % 10 == 0) {
                    pois.add(new POI(new LatLong(location.getLatitude(), location.getLongitude()), String.valueOf(i), "test"));
                }
            }
        }


        RouteOptimized routeFixed = new RouteOptimized(
                points,
                "RouteOptimized2",
                new OnSaveFile() {
                    @Override
                    public String execute(String json) {
                        return create(view.getContext(), json);
                    }
                },
                true
        );

        routeFixed.setLimitedVisiblePoints(true);
        controller.routeManager.setRouteOptimized(routeFixed);



        controller.setPoiManagerBuilder(() -> new POIManager(new OnGetIconPOI() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public File execute(POI poi) {

                return test("1", "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAFRklEQVR42u2X328UVRTHv3Pnx3Z327KltRAIEsAopBRoty0QjCYmRgwhEqOJFLBgYtT4oA/GxAcTn41/gfGlFkpfTPTF+GQ0AVNprZRWpQRCsWBbuu3Slt3u7vy4fu8MbXe7LVswURO9yWRn5tx7zud+z7n3zmr4h5v2P8BKhrb3W+VKtvaPOwvGHXzvyIp959s3n3y5bKyCl4feeF5en7wBQw/BMEwIQyBkmrToSE5MwRMeatdWY23lGoRDYXgMm3Uc6LqOSLgMIcuC1DxMp+4icTuJsjVRuI6LdCaNTDYLU+owNQOpuVn0dV3QCgBeeueIvHzzGu8kbOnC88AAuv+s0annCZhlFiA0SCmDy7cquwZDGNi4YQNGx8eRvpvFzOgktjRsw8z0LLJzGeiagOFxbFaiMhYuBlCt7oVdMmPPIVJRBifnwgxb/uyk53KmhHA9DtDg8hIG7+iU4X0YwZnFYjGYpsDknTtAWsLROfupFIyQBYfqJa/fRqz6EaxbH10eQLWtB7fLNdURTIxNI1ZTgWnOoLyiHLZtwwqZTI+FuUwW0aiO9GwORiRESAOCaYpGo7AsHSO//4Gp0RmU0TbRN4LqHetRsaUGk1cnsGVbrR9nRYDGtiapJE9ngEjEgMMcatRauye9oZtIpagS1ZkcS6GiqhypdAqu1HxnSjGzPIQ5puHu+B3MDo5jbd0GVD2+DplEFte/7iuIWQQQPxWXHgPZLmCxEKX0fJmFUF2ZAqF6UXrm07Edvtfh0uSo1PDZcknLepgenebsh+HZEuHaSqyLb4LlWBj8qvv+AE2vKQBV3RIWpS1YX5rrg2iUW8pAEUgBR0lEYId1EdZ05FgvU8MJTCoA2isfraICNagqq0ZP13clAE41E4BF57IIheW/y4dgwfuzVkM9L1gBLpeMgnF1DSFabALkqFDq13GMXb2Jx57Zg2hYL8j9yik4GfdFt+nU0MwlwbV7v8K/PLVWFaAMetlMj8XRLBsmi2nK2LzRWJiCArm0GSUBina03a+2LAS41NGz1Iw9bfsCOz31t/9YZK9vbVzwbJYAkItOm3GxfTFY/bEmDJzpzYPai/7PF4PtPtGC/o4LKz7Xt8b9X9PTSwPUn2haeDHQ0bsswM7WJr8YBs/0LA9wvBlqJc2PUQBKpVIpkPkO+k/nK9BCZ0GAnUcbg2FcBYNdvVja9lAdjwXspywfmuMeqgZ2KXoW1EBnTyEAr8GzxQANJ/ZyVbj+jAc6f8oDiBNgFSkIZhznjBcH1x9txsDZnoJ8+inKC7CgwPF9/uqQXAcDnb0PDtB0dB93Ni4abi75EhaowppQS3I+TQ2U/ee8olTAav+eBwigNRahWJ0CDa102JnvkDVwdrHI1Cwvnu5eeG5uO4Ce9vPLKhYANPuB1HH8wDXQeGw/PATnfv+Z7qVmtJx8MrgxdFz47Psiez2lF+qslEGokjshcyUbYxW4lszhfNcP2rNvHpaJmYRvE0HxB/uxDH7V4STUzqgLtenB5seHf0So/ZRp4MEKjeeKYRTnvwjg5NtvyXJ9CLlkGp92dBd1fvHdV+TwrRvc8xlUXUL6W66qB50BeI7DyeR8taSnTk6BKDuPDY0i9kRNaYD9h5+Tm2pdfHtuCImhkVV9Mbd+0Cav3BpWhyGzQICsw/o554/de+hpeXNkHMlEAtuf2loaQLWqHZtluRvByJXfVv3JvnHXdvn6y3X46MMvtOVs6vfWpculv4ofps0HuF+Q+7W/DLD5wG7pzGYxx2+DqcGhvx9AtarGOpns++WhfP17/5r9ZwD+BJ2Jej8POdvBAAAAAElFTkSuQmCC");
               /* // Ejemplo extraído de https://mkyong.com/java/java-read-a-file-from-resources-folder/
                ClassLoader classLoader = getClass().getClassLoader();
                URL resource = classLoader.getResource("raw/icon_test.png");
                if (resource == null) {
                    return null;
                } else {

                    // failed if files have whitespaces or special characters
                    //return new File(resource.getFile());

                    try {
                        return new File(resource.toURI());
                    } catch (URISyntaxException e) {
                        e.printStackTrace();
                        return null;
                    }
                }
*/
            }
        }));

        POIManager poiManager = controller.getPoiManager();
        for (POI p : pois)
            poiManager.add(p);
    }


    public void routeOptimized4(View view) {
        ArrayList<RouteOptimizedPoint> points = new ArrayList<RouteOptimizedPoint>();
        ArrayList<POI> pois = new ArrayList<>();

        LatLong ll = new LatLong(40.591254878648755, -3.496106977261882);
        points.add(new RouteOptimizedPoint(ll));
        pois.add(new POI(ll, String.valueOf(0), "test"));

        ll = new LatLong(40.59193108974255, -3.4952915857588507);
        points.add(new RouteOptimizedPoint(ll));
        pois.add(new POI(ll, String.valueOf(1), "test2"));

        ll = new LatLong(40.59248508891594, -3.49470149980271);
        points.add(new RouteOptimizedPoint(ll));
        pois.add(new POI(ll, String.valueOf(2), "test3"));

        ll = new LatLong(40.59199626635412, -3.493735904601752);
        points.add(new RouteOptimizedPoint(ll));
        pois.add(new POI(ll, String.valueOf(3), "test4"));



        RouteOptimized routeFixed = new RouteOptimized(
                points,
                "RouteOptimized2",
                new OnSaveFile() {
                    @Override
                    public String execute(String json) {
                        return create(view.getContext(), json);
                    }
                },
                true
        );

        routeFixed.setLimitedVisiblePoints(true);
        controller.routeManager.setRouteOptimized(routeFixed);



        controller.setPoiManagerBuilder(() -> new POIManager(new OnGetIconPOI() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public File execute(POI poi) {

                return test("1", "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAFRklEQVR42u2X328UVRTHv3Pnx3Z327KltRAIEsAopBRoty0QjCYmRgwhEqOJFLBgYtT4oA/GxAcTn41/gfGlFkpfTPTF+GQ0AVNprZRWpQRCsWBbuu3Slt3u7vy4fu8MbXe7LVswURO9yWRn5tx7zud+z7n3zmr4h5v2P8BKhrb3W+VKtvaPOwvGHXzvyIp959s3n3y5bKyCl4feeF5en7wBQw/BMEwIQyBkmrToSE5MwRMeatdWY23lGoRDYXgMm3Uc6LqOSLgMIcuC1DxMp+4icTuJsjVRuI6LdCaNTDYLU+owNQOpuVn0dV3QCgBeeueIvHzzGu8kbOnC88AAuv+s0annCZhlFiA0SCmDy7cquwZDGNi4YQNGx8eRvpvFzOgktjRsw8z0LLJzGeiagOFxbFaiMhYuBlCt7oVdMmPPIVJRBifnwgxb/uyk53KmhHA9DtDg8hIG7+iU4X0YwZnFYjGYpsDknTtAWsLROfupFIyQBYfqJa/fRqz6EaxbH10eQLWtB7fLNdURTIxNI1ZTgWnOoLyiHLZtwwqZTI+FuUwW0aiO9GwORiRESAOCaYpGo7AsHSO//4Gp0RmU0TbRN4LqHetRsaUGk1cnsGVbrR9nRYDGtiapJE9ngEjEgMMcatRauye9oZtIpagS1ZkcS6GiqhypdAqu1HxnSjGzPIQ5puHu+B3MDo5jbd0GVD2+DplEFte/7iuIWQQQPxWXHgPZLmCxEKX0fJmFUF2ZAqF6UXrm07Edvtfh0uSo1PDZcknLepgenebsh+HZEuHaSqyLb4LlWBj8qvv+AE2vKQBV3RIWpS1YX5rrg2iUW8pAEUgBR0lEYId1EdZ05FgvU8MJTCoA2isfraICNagqq0ZP13clAE41E4BF57IIheW/y4dgwfuzVkM9L1gBLpeMgnF1DSFabALkqFDq13GMXb2Jx57Zg2hYL8j9yik4GfdFt+nU0MwlwbV7v8K/PLVWFaAMetlMj8XRLBsmi2nK2LzRWJiCArm0GSUBina03a+2LAS41NGz1Iw9bfsCOz31t/9YZK9vbVzwbJYAkItOm3GxfTFY/bEmDJzpzYPai/7PF4PtPtGC/o4LKz7Xt8b9X9PTSwPUn2haeDHQ0bsswM7WJr8YBs/0LA9wvBlqJc2PUQBKpVIpkPkO+k/nK9BCZ0GAnUcbg2FcBYNdvVja9lAdjwXspywfmuMeqgZ2KXoW1EBnTyEAr8GzxQANJ/ZyVbj+jAc6f8oDiBNgFSkIZhznjBcH1x9txsDZnoJ8+inKC7CgwPF9/uqQXAcDnb0PDtB0dB93Ni4abi75EhaowppQS3I+TQ2U/ee8olTAav+eBwigNRahWJ0CDa102JnvkDVwdrHI1Cwvnu5eeG5uO4Ce9vPLKhYANPuB1HH8wDXQeGw/PATnfv+Z7qVmtJx8MrgxdFz47Psiez2lF+qslEGokjshcyUbYxW4lszhfNcP2rNvHpaJmYRvE0HxB/uxDH7V4STUzqgLtenB5seHf0So/ZRp4MEKjeeKYRTnvwjg5NtvyXJ9CLlkGp92dBd1fvHdV+TwrRvc8xlUXUL6W66qB50BeI7DyeR8taSnTk6BKDuPDY0i9kRNaYD9h5+Tm2pdfHtuCImhkVV9Mbd+0Cav3BpWhyGzQICsw/o554/de+hpeXNkHMlEAtuf2loaQLWqHZtluRvByJXfVv3JvnHXdvn6y3X46MMvtOVs6vfWpculv4ofps0HuF+Q+7W/DLD5wG7pzGYxx2+DqcGhvx9AtarGOpns++WhfP17/5r9ZwD+BJ2Jej8POdvBAAAAAElFTkSuQmCC");
               /* // Ejemplo extraído de https://mkyong.com/java/java-read-a-file-from-resources-folder/
                ClassLoader classLoader = getClass().getClassLoader();
                URL resource = classLoader.getResource("raw/icon_test.png");
                if (resource == null) {
                    return null;
                } else {

                    // failed if files have whitespaces or special characters
                    //return new File(resource.getFile());

                    try {
                        return new File(resource.toURI());
                    } catch (URISyntaxException e) {
                        e.printStackTrace();
                        return null;
                    }
                }
*/
            }
        }));

        POIManager poiManager = controller.getPoiManager();
        for (POI p : pois)
            poiManager.add(p);
    }

    private File test(String name, String base64ImageData) {
        FileOutputStream fos = null;
        File f = null;
        try {
            if (base64ImageData != null) {

                File ff = getApplicationContext().getExternalFilesDir(Environment.DIRECTORY_PICTURES);
                f = new File(ff.getAbsolutePath(), name + ".png");

                if (f.exists()) return f;

                fos = new FileOutputStream(f);
                byte[] decodedString = android.util.Base64.decode(base64ImageData, android.util.Base64.DEFAULT);
                fos.write(decodedString);
                fos.flush();
                fos.close();
            }

        } catch (Exception e) {
            e.printStackTrace();

        } finally {
            if (fos != null) {
                fos = null;
            }
        }
        return f;
    }

    private String create(Context context, String jsonString) {
        String fileName = "route.ofg";
        try {
            FileOutputStream fos = context.openFileOutput(fileName, Context.MODE_PRIVATE);
            if (jsonString != null) {
                fos.write(jsonString.getBytes());
            }
            fos.close();

            return context.getFilesDir().getAbsolutePath() + "/" + fileName;
        } catch (FileNotFoundException fileNotFound) {
            return null;
        } catch (IOException ioException) {
            return null;
        }

    }


    protected static Location getLocationInLatLngRad(double radiusInMeters, Location currentLocation) {
        double x0 = currentLocation.getLongitude();
        double y0 = currentLocation.getLatitude();

        Random random = new Random();

        // Convert radius from meters to degrees.
        double radiusInDegrees = radiusInMeters / 5000f; // 111320f;

        // Get a random distance and a random angle.
        double u = random.nextDouble();
        double v = random.nextDouble();
        double w = radiusInDegrees * Math.sqrt(u);
        double t = 2 * Math.PI * v;
        // Get the x and y delta values.
        double x = w * Math.cos(t);
        double y = w * Math.sin(t);

        // Compensate the x value.
        double new_x = x / Math.cos(Math.toRadians(y0));

        double foundLatitude;
        double foundLongitude;

        foundLatitude = y0 + y;
        foundLongitude = x0 + new_x;

        Location copy = new Location(currentLocation);
        copy.setLatitude(foundLatitude);
        copy.setLongitude(foundLongitude);
        return copy;
    }

    public static String randomColor() {
        // create object of Random class
        Random obj = new Random();
        int rand_num = obj.nextInt(0xffffff + 1);
// format it as hexadecimal string and print
        return String.format("#%06x", rand_num);
    }
}