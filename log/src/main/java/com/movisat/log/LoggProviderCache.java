package com.movisat.log;

import java.util.ArrayList;
import java.util.List;

import static com.movisat.log.LoggType.SYNCHRONIZATION;

public class LoggProviderCache implements ILoggProvider {
    private List<String> lines = new ArrayList<>();
    private int maxLines = 50000;

    @Override
    public boolean isStarted() {
        return true;
    }

    @Override
    public void start() {
    }

    public List<String> getLines() {
        return lines;
    }


    @Override
    public void write(LoggLine line) {
        switch (line.logType) {
            case DATABASE:
            case SYNCHRONIZATION:
            case DEBUG:
                return;
            case ERROR:
            case WARNING:
            case INFO:
            case CATASTROPHE:
                break;
        }

        String type = line.logType.toString();

        try {
            if (lines.size() >= maxLines) lines = lines.subList(lines.size() / 2, lines.size() - 1);
            lines.add("[" + line.datetime + "] " + " [" + type + "] " + line.message);
        } catch (Exception e) {
            lines.clear();
        }
    }

}
