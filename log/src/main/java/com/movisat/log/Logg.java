package com.movisat.log;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class Logg {
    //================================================================================
    // Fields
    //================================================================================
    List<ILoggProvider> providers = new ArrayList<>();
    boolean isEnabled = true;
    String lastLog = "";


    //================================================================================
    // Methods - Static
    //================================================================================
    public static void disable() {
        get().isEnabled = false;
    }

    public static void enable() {
        get().isEnabled = true;
    }

    public static void addProvider(ILoggProvider provider) {
        if (get().providers.contains(provider)) return;
        get().providers.add(provider);
    }


    public static void debug(String tag, String message) {
        get().write("[" + tag + "] " + message, LoggType.DEBUG);
    }

    public static void database(String tag, String message) {
        get().write("[" + tag + "] " + message, LoggType.DATABASE);
    }

    public static void info(String tag, String message) {
        get().write("[" + tag + "] " + message, LoggType.INFO);
    }

    public static void warning(String tag, String message) {
        get().write("[" + tag + "] " + message, LoggType.WARNING);
    }

    public static void error(String tag, String message) {
        get().write("[" + tag + "] " + message, LoggType.ERROR);
    }

    public static void catastrophe(String tag, String message) {
        get().write("[" + tag + "] " + message, LoggType.CATASTROPHE);
    }


    public static void debug(String message) {
        get().write(message, LoggType.DEBUG);
    }

    public static void database(String message) {
        get().write(message, LoggType.DATABASE);
    }

    public static void info(String message) {
        get().write(message, LoggType.INFO);
    }

    public static void warning(String message) {
        get().write(message, LoggType.WARNING);
    }

    public static void error(String message) {
        get().write(message, LoggType.ERROR);
    }

    public static void synchro(String message) {
        get().write(message, LoggType.SYNCHRONIZATION);
    }

    public static void catastrophe(String message) {
        get().write(message, LoggType.CATASTROPHE);
    }

    //================================================================================
    // Methods - Private
    //================================================================================
    private void write(String message, LoggType logType) {
        if (!isEnabled) return;
        if (providers.isEmpty()) return;
        if(lastLog.equals(message)) return;
        lastLog = message;

        writeLine(message, logType);
    }

    private String dateToString(Date date) {
        if (date == null) return "";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(date);
    }

    private void writeLine(String message, LoggType logType) {
        try {
            final LoggLine line = new LoggLine(
                    logType,
                    dateToString(Calendar.getInstance().getTime()),
                    message
            );

            notifyProviders(line);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void notifyProviders(LoggLine logLine) {
        synchronized (this) {
            for (ILoggProvider provider : providers) {
                if (!provider.isStarted()) provider.start();
                provider.write(logLine);
            }
        }
    }


    //================================================================================
    // Singleton
    //================================================================================
    private static Logg instance;

    private Logg() {
    }

    private static Logg get() {
        if (instance == null) {
            synchronized (Logg.class) {
                if (instance == null) {
                    instance = new Logg();
                }

            }
        }
        return instance;
    }
}

