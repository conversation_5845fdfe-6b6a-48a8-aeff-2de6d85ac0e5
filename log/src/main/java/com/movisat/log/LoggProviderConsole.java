package com.movisat.log;

public class LoggProviderConsole implements ILoggProvider {
    private final String resetColor = "\u001b[0m";

    public String getEmoji(LoggType type) {
        switch (type) {
            case DEBUG:
                return "🕷";
            case INFO:
                return "💡";
            case WARNING:
                return "⚠";
            case ERROR:
                return "⛔";
            case DATABASE:
                return "💾";
            case SYNCHRONIZATION:
                return "📡";
            case CATASTROPHE:
                return "😱";
        }
        return "";
    }


    public String getColor(LoggType type) {
        switch (type) {
            case DEBUG:
                return "\u001b[32m";
            case INFO:
                return "\u001b[36m";
            case WARNING:
                return "\u001b[33m";
            case ERROR:
                return "\u001b[31m";
            case DATABASE:
                return "\u001b[34m";
            case SYNCHRONIZATION:
                return "\u001b[35m";
            case CATASTROPHE:
                return "\u001b[31m";
        }
        return "";
    }


    @Override
    public boolean isStarted() {
        return true;
    }

    @Override
    public void start() {
    }


    @Override
    public void write(LoggLine line) {
        String type = line.logType.toString();
        String logTypeColor = getColor(line.logType);
        String logTypeEmoji = getEmoji(line.logType);
        // No se muestran los colores ni emojis.
//        System.out.println(logTypeColor + "[" + line.datetime + "] " + logTypeEmoji + " [" + type + "] " + line.message + " " + resetColor);
        System.out.println("[" + line.datetime + "] [" + type + "] " + line.message);
    }

}
