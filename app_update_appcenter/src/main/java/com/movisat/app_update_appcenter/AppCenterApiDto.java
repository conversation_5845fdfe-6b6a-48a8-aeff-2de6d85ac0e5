package com.movisat.app_update_appcenter;

import java.util.HashMap;

public class AppCenterApiDto {

    public String appName;
    public String appDisplayName;

    /// Version con formato de número entero.
    public String version;

    /// Version con formato x.x.xx.
    public String shortVersion;
    public String downloadUrl;

    public static AppCenterApiDto fromJson(
            HashMap<String, Object> json) {
        AppCenterApiDto obj = new AppCenterApiDto();
        obj.appName = (String) json.get("app_name");
        obj.appDisplayName = (String) json.get("app_display_name");
        obj.version = (String) json.get("version");
        obj.shortVersion = (String) json.get("short_version");
        obj.downloadUrl = (String) json.get("download_url");
        return obj;
    }
}
