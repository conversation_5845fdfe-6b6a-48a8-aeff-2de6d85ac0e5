package com.movisat.app_update_appcenter;


import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.movisat.api.Api;
import com.movisat.api.ApiResponse;
import com.movisat.log.Logg;
import com.movisat.utils.ICallBack;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppCenterApi {

    final static String TAG = "AppCenterApi";


    public void execute(
            String appSecret,
            boolean isPublicVersion,
            ICallBack<AppCenterApiDto> callback
    ) {
        try {
            if (isPublicVersion) internalGetVersionPublic(appSecret, callback);
            else internalGetVersionPrivate(appSecret, callback);
        } catch (Exception e) {
            Logg.error("[" + TAG + "] Error al buscar actualizaciones (Pública: " + isPublicVersion + ") : " + e.getMessage());
        }
    }

    private void internalGetVersionPublic(String appSecret,
                                          ICallBack<AppCenterApiDto> callback) {

        HashMap<String, String> header = new HashMap<String, String>();
        header.put("accept", "application/json");

        new Api().get(
                "https://api.appcenter.ms/v0.1/public/sdk/apps/" + appSecret + "/releases/latest",
                header,
                response -> response(response, callback)
        );


    }

    private void response(ApiResponse<String> response,
                          ICallBack<AppCenterApiDto> callback) {
        if (!response.isError) {
            HashMap<String, Object> retMap = new Gson().fromJson(
                    response.body, new TypeToken<HashMap<String, Object>>() {
                    }.getType()
            );
            callback.execute(AppCenterApiDto.fromJson(retMap));
        }
    }

    private void internalGetVersionPrivate(String appSecret,
                                           ICallBack<AppCenterApiDto> callback) {
        HashMap<String, String> header = new HashMap<String, String>();
        header.put("accept", "application/json");
        header.put("X-API-Token", "d7de6c7b36417cf0e11d555ff6b3a3de9d1e20d3");

        new Api().get(
                "https://api.appcenter.ms/v0.1/sdk/apps/" + appSecret + "/releases/private/latest",
                header,
                response -> response(response, callback)
        );

    }

}
