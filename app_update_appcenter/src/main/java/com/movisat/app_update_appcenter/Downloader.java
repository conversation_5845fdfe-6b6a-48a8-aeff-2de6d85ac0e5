package com.movisat.app_update_appcenter;

import android.app.Activity;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Environment;

import com.movisat.log.Logg;

import java.io.File;

public class Downloader {
    private Activity activity;
    private DownloadManager manager;
    private long lastDownloadId;
    private String destination;


    /**
     * Descarga el APK y abre la carpeta de descargas.
     * <p>
     * Notas:
     * - No se ha podido iniciar la instalación directamente, todos las pruebas generaban un
     * ActivityNotFoundException aunque se comprobasen permisos o se ejecutase desde la aplicación principal.
     * - Aunque exista previamente el fichero, se vuelve a descargar, porque no se puede verificar
     * que el fichero que existe se haya descargado correctamente.
     *
     * @param url
     * @param fileNameWithExtension
     * @param activity
     */
    public void execute(String url, String fileNameWithExtension, Activity activity) {
        this.activity = activity;

        Environment
                .getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                .mkdirs();
        destination = Environment
                .getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "/" + fileNameWithExtension;

        activity.registerReceiver(onComplete, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
        manager = (DownloadManager) activity.getSystemService(Context.DOWNLOAD_SERVICE);
        Uri uri = Uri.parse(url);
        DownloadManager.Request request = new DownloadManager.Request(uri);
        request.setDestinationInExternalPublicDir(
                Environment.DIRECTORY_DOWNLOADS,
                fileNameWithExtension
        );
        lastDownloadId = manager.enqueue(request);
    }

    public void installApp(String path, Context context)
    {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        File file = new File(path);

        intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");

        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK
                | Intent.FLAG_GRANT_READ_URI_PERMISSION
                | Intent.FLAG_ACTIVITY_NO_HISTORY
                | Intent.FLAG_ACTIVITY_NEW_DOCUMENT
                | Intent.FLAG_ACTIVITY_MULTIPLE_TASK);

        context.startActivity(intent);
    }


    BroadcastReceiver onComplete = new BroadcastReceiver() {
        public void onReceive(Context ctxt, Intent ii) {
            activity.unregisterReceiver(onComplete);
            File installPath = new File(destination);
            if (installPath.exists()) {
                installApp(destination, activity);
                //TODO: Ahora mismo el servicio sólo descarga e instala, valorar si se quiere tener 
                // también la funcionalidad de descargar y abrir la carpeta de descargas.
                // activity.startActivity(new Intent(DownloadManager.ACTION_VIEW_DOWNLOADS));
            } else {
                Logg.error("file path:" + destination + " is not exists");
            }
        }
    };


/*
    public void queryStatus(View v) {
        Cursor c = manager.query(new DownloadManager.Query().setFilterById(lastDownloadId));

        if (c == null) {
            Toast.makeText(this, "Download not found!", Toast.LENGTH_LONG).show();
        } else {
            c.moveToFirst();

            Log.d(getClass().getName(), "COLUMN_ID: " +
                    c.getLong(c.getColumnIndex(DownloadManager.COLUMN_ID)));
            Log.d(getClass().getName(), "COLUMN_BYTES_DOWNLOADED_SO_FAR: " +
                    c.getLong(c.getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)));
            Log.d(getClass().getName(), "COLUMN_LAST_MODIFIED_TIMESTAMP: " +
                    c.getLong(c.getColumnIndex(DownloadManager.COLUMN_LAST_MODIFIED_TIMESTAMP)));
            Log.d(getClass().getName(), "COLUMN_LOCAL_URI: " +
                    c.getString(c.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)));
            Log.d(getClass().getName(), "COLUMN_STATUS: " +
                    c.getInt(c.getColumnIndex(DownloadManager.COLUMN_STATUS)));
            Log.d(getClass().getName(), "COLUMN_REASON: " +
                    c.getInt(c.getColumnIndex(DownloadManager.COLUMN_REASON)));

            Toast.makeText(this, statusMessage(c), Toast.LENGTH_LONG).show();
        }
    }

    public void viewLog(View v) {
        startActivity(new Intent(DownloadManager.ACTION_VIEW_DOWNLOADS));
    }

    private String statusMessage(Cursor c) {
        String msg = "???";

        switch (c.getInt(c.getColumnIndex(DownloadManager.COLUMN_STATUS))) {
            case DownloadManager.STATUS_FAILED:
                msg = "Download failed!";
                break;

            case DownloadManager.STATUS_PAUSED:
                msg = "Download paused!";
                break;

            case DownloadManager.STATUS_PENDING:
                msg = "Download pending!";
                break;

            case DownloadManager.STATUS_RUNNING:
                msg = "Download in progress!";
                break;

            case DownloadManager.STATUS_SUCCESSFUL:
                msg = "Download complete!";
                break;

            default:
                msg = "Download is nowhere in sight";
                break;
        }

        return (msg);
    }*/

}
