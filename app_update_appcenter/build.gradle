plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 32
    buildToolsVersion "30.0.3"
    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation project(':utils')
    implementation project(':log')
    implementation project(':api')
    implementation 'com.google.code.gson:gson:2.8.6'
}