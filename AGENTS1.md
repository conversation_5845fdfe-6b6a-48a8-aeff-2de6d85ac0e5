## Metada<PERSON> & Scope

- Repo: `ecosat-movil`
- Branch/Commit: Assumption — main working copy; Git metadata not accessible in sandbox. Please confirm the authoritative branch/commit. Missing input: Which branch/commit should be the baseline for this audit?
- Generated: 2025-09-04
- Analysis Scope: Entire repository under root; Gradle modules, manifests, Java sources under `ecoSAT/` and `android-core/*` libraries; resources under `ecoSAT/src/main/res`; assets under `ecoSAT/src/main/assets`.
- <PERSON><PERSON> observed: Android Gradle Plugin `com.android.tools.build:gradle:8.11.1` in `build.gradle:13`.

## Executive Summary

- Monolithic Android Java app (`:ecoSAT`) with multiple legacy library modules (logging, utils, network wrapper, RFID) wired via multi-module Gradle (`settings.gradle:1–14`).
- App uses a local SQLite database created/updated from SQL assets (`ecoSAT/src/main/assets/ecosat_create.txt`, `ecosat_update.txt`) accessed via raw `SQLiteDatabase` wrappers (`ecoSAT/src/main/java/com/movisat/utilities/Database.java:1–220`).
- Networking implemented via Apache HttpClient and Fast Android Networking; base URL taken from DB config key `webSvc` (not code constant) (`ecoSAT/src/main/java/com/movisat/synchronize/ClientWebSvc.java:87–107`, `android-core/api/src/main/java/com/movisat/api/Api.java:1–120`).
- Background work is custom Threads/Services (`OutBox`, `DBSynchro`, `SyncService`), (`ecoSAT/src/main/java/com/movisat/outbox/OutBox.java:1–120`, `ecoSAT/src/main/java/com/movisat/synchronize/DBSynchro.java:1–200`, `ecoSAT/src/main/java/com/movisat/services/SyncService.java:1–200`).
- Logging uses custom `Logg` providers and DB-backed error capture (`android-core/log/src/main/java/com/movisat/log/Logg.java:1–120`, `ecoSAT/src/main/java/com/movisat/ecosat/MyLoggerHandler.java:1–220`).

## System Overview

```
+-----------------------------+
|         :ecoSAT (app)       |
|  Activities/Fragments       |
|  Services (Location/Sync)   |
|  SQLite (raw) via Database  |
|  EventBus, Maps, NFC/UHF    |
|  Config (DB-backed)         |
+--------------+--------------+
               |
         Uses modules
               v
+----------------+   +------------------+   +---------------------------+
| :api           |   | :utils/:utils_.. |   | :log/:log_provider_..    |
| Fast Android   |   | Helpers, timers, |   | Log facade + Android log |
| Networking API |   | platform utils   |   | provider                  |
+----------------+   +------------------+   +---------------------------+
               |           \
               |            \  +-------------------------+
               |             ->| :rfid_uhf_u9000         |
               |               | Reader integration JAR   |
               v               +-------------------------+
         Backend (HTTP)
         Base URL from Config DB
```

## Module & Dependency Map

| Module | Type (app/lib) | Key responsibilities | Public APIs | Depends on | Consumed by | Notes |
|---|---|---|---|---|---|---|
| `:ecoSAT` | app | Main Android app, UI/screens, services, DB access | Activities, Services, DB wrappers | `:api`, `:googlemapcluster_lib`, `:log`, `:log_provider_console_android`, `:materialDesign`, `:rfid_uhf_u9000`, `:utils_android`, `:utils` | — | `build.gradle` shows dependencies and SDK 35 (`ecoSAT/build.gradle:1–120`). |
| `:api` | lib | Simple HTTP wrapper via Fast Android Networking | `Api`, `ApiResponse` | `:utils` | `:ecoSAT`, `:app_update_appcircle` | `android-core/api/build.gradle:1–200`; `Api.java:1–120`. |
| `:utils` | lib | Cross-cutting utils (collections, time, math) | `Utilss`, `Timer` | — | `:ecoSAT`, others | `android-core/utils/build.gradle:1–120`, sources under `android-core/utils/src/main/java/com/movisat/utils/`. |
| `:utils_android` | lib | Android-specific utilities | `PhotoService`, `UtilssAndroid` | `:utils` | `:ecoSAT`, `:rfid_uhf_u9000` | `android-core/utils_android/build.gradle:1–120`. |
| `:log` | lib | Logging facade and model | `Logg` | — | `:ecoSAT`, `:log_provider_console_android` | `android-core/log/build.gradle:1–120`, `Logg.java:1–120`. |
| `:log_provider_console_android` | lib | Android Logcat provider for `Logg` | `LoggProviderConsoleAndroid` | `:log` | `:ecoSAT` | `android-core/log_provider_console_android/build.gradle:1–200`. |
| `:googlemapcluster_lib` | lib | Maps clustering helpers | N/A | Play Services Maps | `:ecoSAT` | `googlemapcluster_lib/build.gradle:1–200`. |
| `:rfid_uhf_u9000` | lib | UHF RFID reader integration | `U9000UHFManager` | `:utils`, `:utils_android` | `:ecoSAT` | `android-core/rfid_uhf_u9000/build.gradle:1–200`. |
| `:materialDesign` | lib | Third-party UI widgets | N/A | `nineoldandroids` JAR | `:ecoSAT` | `materialDesign/build.gradle:1–120`. |

## Build, Variants & Dependencies

- Settings: modules included (`settings.gradle:1–14`). Commented `:app_update_appcircle` optional updater.
- App plugin: `com.android.application` (`ecoSAT/build.gradle:1–20`).
- SDKs: `compileSdkVersion 35`, `targetSdkVersion 35`, `minSdkVersion 21` (`ecoSAT/build.gradle:6–28`).
- Build types: `release { minifyEnabled false }` with default ProGuard file (`ecoSAT/build.gradle:29–38`).
- Global repos: Google, JitPack, MavenCentral (`build.gradle:4–22`).
- Key runtime deps in app: AppCompat, Material, GMS Maps/Location, EventBus, Gson (`ecoSAT/build.gradle:56–84`).
- Build commands:
  - Debug: `./gradlew :ecoSAT:assembleDebug`
  - Release: `./gradlew :ecoSAT:assembleRelease`
  - All libs: `./gradlew assemble`

## Application Components

Permissions and app-level flags:
- Cleartext, legacy storage, large heap, disallow backup; Google Maps API key present (`ecoSAT/src/main/AndroidManifest.xml:1–200`).
- Permissions include INTERNET, ACCESS_FINE_LOCATION, CAMERA, NFC, POST_NOTIFICATIONS, FOREGROUND_SERVICE, etc. Also `READ_PRIVILEGED_PHONE_STATE` declared (signature-only; unused by code on API ≥29) (`ecoSAT/src/main/AndroidManifest.xml:1–120`; `ecoSAT/src/main/java/com/movisat/utilities/Phone.java:260–560`).

Activities (exported unless noted; see manifest for full list):
- Launcher: `MainActivity` (singleTop) (`ecoSAT/src/main/AndroidManifest.xml:1–200`).
- NFC: `NfcActivity` with `TECH_DISCOVERED` intent filter (`ecoSAT/src/main/AndroidManifest.xml:1–200`; `ecoSAT/src/main/java/com/movisat/ecosat/NfcActivity.java:1–120`).
- Auth/entry: `MyLoadingActivity`, `LoginActivity`, `Activacion` (`ecoSAT/src/main/AndroidManifest.xml:1–200`, `200–400`; `ecoSAT/src/main/java/com/movisat/ecosat/LoginActivity.java:1–220`).
- Domain screens: `OperationsActivity`, `AddElemActivity`, `ProcesarElementoActivity`, `NivelLlenadoActivity`, `FilterElemActivity`, `FlotaActivity` (SEARCH intent + searchable meta-data), `FlotaHistoricoActivity`, `AreasActivity`, `IncidenciasActivity`, `AddInciActivity`, `AsignarIncidenciasActivity`, `SeleccionUsuarioIncidenciaActivity`, `FilterInciActivity`, `FilterInciTiposActivity`, `UpdateEstadoIncidencia`, `InfoActivity`, `AddPesajeActivity`, `TagNotFoundListActivity`, `SustituirElemActivity`, `DepositarElemActivity`, `RetirarElemActivity`, `AddElemActivity2`, `SeleccionarVehiculoActivity`, `GruposActivity`, `SettingsActivity`, `CallApiActivity`, `SustituirElementoActivity` (`ecoSAT/src/main/AndroidManifest.xml:1–200`, `200–400`).

Services:
- `com.movisat.services.MyLocationService` (foreground type location) (`ecoSAT/src/main/AndroidManifest.xml:200–400`; `ecoSAT/src/main/java/com/movisat/services/MyLocationService.java:1–120`).
- `com.movisat.services.SyncService` (foreground) for data sync notifications (`ecoSAT/src/main/AndroidManifest.xml:200–400`; `ecoSAT/src/main/java/com/movisat/services/SyncService.java:1–200`).

BroadcastReceivers:
- Boot/start: `RestartReceiver` (BOOT_COMPLETED) (`ecoSAT/src/main/AndroidManifest.xml:1–200`; `ecoSAT/src/main/java/com/movisat/ecosat/RestartReceiver.java:1–120`).
- Shutdown: `.ShutdownReceiver` (`ecoSAT/src/main/AndroidManifest.xml:200–400`; `ecoSAT/src/main/java/com/movisat/application/ShutdownReceiver.java:1–120`).
- Notif channel trigger: `.NotificacionesActivity` (receiver) with action `com.movisat.INCIDENCIAS` (`ecoSAT/src/main/AndroidManifest.xml:200–400`; `ecoSAT/src/main/java/com/movisat/ecosat/NotificacionesActivity.java:1–200`).
- Others: `com.movisat.fragment.GestionElementos.receiverGestionElementos`, `.UpdateEstadoIncidencia.receiverUpdateEstado` (both exported) (`ecoSAT/src/main/AndroidManifest.xml:200–400`).

ContentProviders:
- `androidx.core.content.FileProvider` (authorities `com.movisat.ecosat`, not exported) with `res/xml/file_paths.xml` (`ecoSAT/src/main/AndroidManifest.xml:1–200`; `ecoSAT/src/main/res/xml/file_paths.xml:1–80`).
- `com.movisat.utilities.MovisatShared` (exported=true) exposing `content://com.movisat.ecosat.shareDB/conf` (`ecoSAT/src/main/AndroidManifest.xml:200–400`; `ecoSAT/src/main/java/com/movisat/utilities/MovisatShared.java:1–240`).

## Navigation & UI Flows

- Entry: Launcher to `MainActivity` (singleTop). Login flow via `LoginActivity` which reads/writes config keys (`loginUsuario`, `loginEmpresa`, `passwUsuario`) in DB (`ecoSAT/src/main/java/com/movisat/ecosat/LoginActivity.java:1–220`).
- NFC: `BaseActivity` enables reader mode; fallback via `NfcActivity` intent when reader mode fails (`ecoSAT/src/main/java/com/movisat/ecosat/BaseActivity.java:80–140`; `ecoSAT/src/main/java/com/movisat/ecosat/NfcActivity.java:1–120`).
- Search: `FlotaActivity` declares searchable meta-data and `SEARCH` intent (`ecoSAT/src/main/AndroidManifest.xml:1–200`; `ecoSAT/src/main/res/xml/searchable.xml:1–80`).
- Theming: `CustomActionBarTheme`/`MainTheme` with edge-to-edge opt-out flags (`ecoSAT/src/main/res/values/themes.xml:1–120`).

## Data & Persistence

- DB bootstrap: SQL assets create/update schema (`ecoSAT/src/main/assets/ecosat_create.txt:1–160`; `ecoSAT/src/main/assets/ecosat_update.txt:1–160`).
- DB Access: `com.movisat.utilities.Database` handles opening and running create/update scripts; connections retrieved via `Database.getConnection` using `MainActivity` context (`ecoSAT/src/main/java/com/movisat/utilities/Database.java:1–220`).
- Config store: DB table `conf` accessed via `Config` (cached map + async writes) with keys e.g., `webSvc`, `versionScript`, `ultSincro`, `loginEmpresa`, `loginUsuario`, `passwUsuario`, `disableOutbox`, `fakeDevice` (`ecoSAT/src/main/java/com/movisat/utilities/Config.java:1–260`; usages in `ClientWebSvc.java:87–107`, `SyncService.java:1–120`, `OutBox.java:1–120`, `Phone.java:260–560`, `LoginActivity.java:1–220`).
- Entities and DAOs: Dozens of `DB*` classes (manual CRUD) and data models in `ecoSAT/src/main/java/com/movisat/database/` e.g., `DBIncidencia`, `DBElemento`, `DBUsuario`, etc. (`ecoSAT/src/main/java/com/movisat/database/DBIncidencia.java:1–120`).
- Caching: In-memory caches for config; other caches via DB tables (`conf`, etc.).
- Serialization: JSON via `org.json` and `Gson` (in updater libs); requests bodies built as JSON strings (`android-core/api/src/main/java/com/movisat/api/Api.java:80–160`).

## Networking Contracts

- Base URL: Read from config key `webSvc` (`ecoSAT/src/main/java/com/movisat/synchronize/ClientWebSvc.java:87–107`). Why it matters: environment switching is controlled at runtime via DB config, not flavors.
- HTTP client(s):
  - Apache HttpClient in `ClientWebSvc` (custom retry/token refresh) (`ecoSAT/src/main/java/com/movisat/synchronize/ClientWebSvc.java:1–120`).
  - Fast Android Networking wrapper in `:api` module (`android-core/api/src/main/java/com/movisat/api/Api.java:1–220`).
- Auth: Custom token fetching/refresh (stored in Config keys `ultToken` and `ultRefreshToken`) (`ecoSAT/src/main/java/com/movisat/synchronize/ClientWebSvc.java:38–86`).
- Example endpoints (paths appended to `webSvc`):
  - Login (Indra): `/api/usuarios/login/minsait` (POST) (`ecoSAT/src/main/java/com/movisat/apis/LoginIndraApi.java:1–120`).
  - Sensor levels: `/api/sensores/niveles/fraccion/{empresa}` (POST) (`ecoSAT/src/main/java/com/movisat/apis/RecogidasApi.java:1–120`).
  - Washes: `/api/sensores/lavados/{empresa}` (POST) (`ecoSAT/src/main/java/com/movisat/apis/LavadosApi.java:1–120`).
- Timeouts/retries: Per `ClientWebSvc` constants (e.g., `TIMEOUT`), up to three retries including token refresh; `Api` wrapper immediate priority requests.
- SSL/TLS: No certificate pinning observed; app allows cleartext traffic (`ecoSAT/src/main/AndroidManifest.xml:1–200`).
- Error handling: `Api` reports `ApiResponse.isError`; `ClientWebSvc` throws `HttpException` to drive retry/token refresh.

## Background Work & Scheduling

- Sync Thread: `DBSynchro` (long class) orchestrates multi-table sync with handlers for UI feedback (`ecoSAT/src/main/java/com/movisat/synchronize/DBSynchro.java:1–200`).
- Outbox Thread: `OutBox` runs continuously, batching and posting queued operations, defers during sync (`ecoSAT/src/main/java/com/movisat/outbox/OutBox.java:1–120`, `200–560`).
- Foreground services: `SyncService` posts start/end notifications for initial sync (`ecoSAT/src/main/java/com/movisat/services/SyncService.java:1–200`).
- Location service: `MyLocationService` provides GPS with Kalman filtering (`ecoSAT/src/main/java/com/movisat/services/MyLocationService.java:1–120`).
- Custom threads/services used.

## Configuration & Feature Flags

- DB-backed flags in `Config` and `Environment`: e.g., `tipoSoft` (`isSoftIndra`/`isSoftCamacho`), UI bits like `incidenciaImprocedente`, `frecProcesado`, tag behavior toggles (`TAG_LARGO_Y_CORTO`, `TAG_UHF_EXTENDIDO`) (`ecoSAT/src/main/java/com/movisat/utilities/Config.java:1–120`; `ecoSAT/src/main/java/com/environment/Environment.java:1–200`; `ecoSAT/src/main/java/com/movisat/ecosat/MainActivity.java:200–320`).
- Critical runtime keys: `webSvc`, `ultToken`, `ultRefreshToken`, `ultSincro`, `disableOutbox`, `fakeDevice` (controls IMEI spoofing), `versionScript` (DB migrations) (`ClientWebSvc.java:1–120`; `Phone.java:260–560`; `Database.java:120–220`).

## Error Handling, Logging & Telemetry

- App logging: `Logg` facade with console provider for debug builds (`android-core/log/src/main/java/com/movisat/log/Logg.java:1–120`, `android-core/log_provider_console_android/.../LoggProviderConsoleAndroid.java:1–200`).
- Error DB: `MyLoggerHandler` persists errors in `DBError` table (`ecoSAT/src/main/java/com/movisat/ecosat/MyLoggerHandler.java:1–220`).
- Notification channels: created in `SyncService` and `NotificacionesActivity` (`ecoSAT/src/main/java/com/movisat/services/SyncService.java:1–200`; `ecoSAT/src/main/java/com/movisat/ecosat/NotificacionesActivity.java:1–200`).

## Testing & Quality

- Unit tests: Only one JUnit test present (`ecoSAT/src/test/java/FrecuenciaProcesadoStateTest.java:1–220`).
- Instrumentation tests: None found.
- Lint: Heavily disabled via `ecoSAT/lint.xml:1–200` (many issues ignored including exported components and AllowBackup). Should re-enable with curated baseline.
- Static analysis: No Detekt/Checkstyle/SpotBugs configured.

## External Services & SDKs

- Google Play Services: Maps (`com.google.android.gms:play-services-maps`) and Location (`play-services-location`) (`ecoSAT/build.gradle:63–84`).
- Fast Android Networking (`com.github.amitshekhariitbhu.Fast-Android-Networking:android-networking`) (`android-core/api/build.gradle:1–200`).
- EventBus (`org.greenrobot:eventbus:3.0.0`) (`ecoSAT/build.gradle:63–84`).
- RFID libraries (U9000 JAR) (`android-core/rfid_uhf_u9000/build.gradle:1–200`).

## Release & CI/CD

- No CI configuration files found (no GitHub Actions/Jenkinsfile visible).
- Optional updater libs exist (`android-core/app_update_appcircle`, `android-core/app_update_appcenter`) but `app_update_appcircle` is commented out in settings (`settings.gradle:9–14`).
- Build locally via Gradle; signing configs not present in repo; expect stored in local or CI secrets.

## Traceability Index

- C-1: Modules list → `settings.gradle:1–14`
- C-2: App SDK/Deps → `ecoSAT/build.gradle:1–84`
- C-3: Global Gradle plugin → `build.gradle:4–22`
- C-4: Manifest flags (cleartext, storage, backup) → `ecoSAT/src/main/AndroidManifest.xml:1–200`
- C-5: Permissions declared → `ecoSAT/src/main/AndroidManifest.xml:1–120`
- C-6: Provider `MovisatShared` exported → `ecoSAT/src/main/AndroidManifest.xml:200–400`; `ecoSAT/src/main/java/com/movisat/utilities/MovisatShared.java:1–240`
- C-7: Launcher/MainActivity → `ecoSAT/src/main/AndroidManifest.xml:1–200`
- C-8: NFC Activity and tech-filter → `ecoSAT/src/main/AndroidManifest.xml:1–200`; `ecoSAT/src/main/res/xml/nfc_tech_list.xml:1–80`; `ecoSAT/src/main/java/com/movisat/ecosat/NfcActivity.java:1–120`
- C-9: Services (Location/Sync) → `ecoSAT/src/main/AndroidManifest.xml:200–400`; service classes under `ecoSAT/src/main/java/com/movisat/services/`
- C-10: Receivers list → `ecoSAT/src/main/AndroidManifest.xml:200–400`; `ecoSAT/src/main/java/com/movisat/ecosat/RestartReceiver.java:1–120`; `ecoSAT/src/main/java/com/movisat/application/ShutdownReceiver.java:1–120`
- C-11: Searchable meta-data → `ecoSAT/src/main/AndroidManifest.xml:1–200`; `ecoSAT/src/main/res/xml/searchable.xml:1–80`
- C-12: Themes/edge-to-edge opt-out → `ecoSAT/src/main/res/values/themes.xml:1–120`
- C-13: Database bootstrap scripts → `ecoSAT/src/main/assets/ecosat_create.txt:1–160`; `ecosat_update.txt:1–160`
- C-14: Database helper → `ecoSAT/src/main/java/com/movisat/utilities/Database.java:1–220`
- C-15: Config class and keys → `ecoSAT/src/main/java/com/movisat/utilities/Config.java:1–260`
- C-16: Base URL from Config → `ecoSAT/src/main/java/com/movisat/synchronize/ClientWebSvc.java:87–107`
- C-17: Apache HttpClient usage → `ecoSAT/src/main/java/com/movisat/synchronize/ClientWebSvc.java:1–120`
- C-18: Fast Android Networking → `android-core/api/src/main/java/com/movisat/api/Api.java:1–220`; `android-core/api/build.gradle:1–200`
- C-19: Example endpoints → `ecoSAT/src/main/java/com/movisat/apis/RecogidasApi.java:1–120`; `LavadosApi.java:1–120`; `LoginIndraApi.java:1–120`
- C-20: OutBox thread → `ecoSAT/src/main/java/com/movisat/outbox/OutBox.java:1–120`, `200–560`
- C-21: DBSynchro thread → `ecoSAT/src/main/java/com/movisat/synchronize/DBSynchro.java:1–200`
- C-22: SyncService notifications → `ecoSAT/src/main/java/com/movisat/services/SyncService.java:1–200`
- C-23: Logging facade → `android-core/log/src/main/java/com/movisat/log/Logg.java:1–120`; provider → `android-core/log_provider_console_android/.../LoggProviderConsoleAndroid.java:1–200`
- C-24: Error DB logger → `ecoSAT/src/main/java/com/movisat/ecosat/MyLoggerHandler.java:1–220`
- C-25: Lint disabled → `ecoSAT/lint.xml:1–200`
- C-26: Test presence → `ecoSAT/src/test/java/FrecuenciaProcesadoStateTest.java:1–220`
- C-27: IMEI/Android ID handling → `ecoSAT/src/main/java/com/movisat/utilities/Phone.java:260–560`
- C-28: Maps API key in manifest meta-data → `ecoSAT/src/main/AndroidManifest.xml:1–200`
- C-29: RFID U9000 SDK/ABI filters → `android-core/rfid_uhf_u9000/build.gradle:1–200`; `U9000UHFManager.java:1–200`