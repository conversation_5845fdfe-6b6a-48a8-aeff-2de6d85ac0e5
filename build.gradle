// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        maven { url 'https://jitpack.io' }
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.11.1'
    }
}

allprojects {
    repositories {
        google()
        maven { url 'https://jitpack.io' }
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
