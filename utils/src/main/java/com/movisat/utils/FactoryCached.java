package com.movisat.utils;

import java.util.HashMap;


public class FactoryCached<K, V>
{
    private HashMap<K, V> values = new HashMap<K, V>();
    private IFunc<K, V> builder;

    public FactoryCached(IFunc<K, V> builder)
    {
        this.builder = builder;
    }

    public V get(K key)
    {
        if (!values.containsKey(key) || values.get(key) == null)
            values.put(key, builder.execute(key));
        return values.get(key);
    }

    public void clear()
    {
        values.clear();
    }

}