package com.movisat.utils;

import java.util.ArrayList;

public class Stream<T> {
    private ArrayList<ICallBack<T>> subscribers = new ArrayList<ICallBack<T>>();
    private T value;

    public Stream(T startValue) {
        value = startValue;
    }

    public T getValue() {
        return value;
    }

    public void subscribe(ICallBack<T> subscriber) {
        if (subscribers.contains(subscriber)) return;
        subscribers.add(subscriber);
        if (value != null)
            subscriber.execute(value);
    }

    public void unsubscribe(ICallBack<T> subscriber) {
        if (!subscribers.contains(subscriber)) return;
        subscribers.remove(subscriber);
    }

    public void notify(T value) {

        this.value = value;
        if(subscribers != null && subscribers.size() > 0) {
            for (ICallBack subscriber : subscribers) {
                try {
                    subscriber.execute(this.value);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public void dispose() {
        subscribers.clear();
        value = null;
    }
}
