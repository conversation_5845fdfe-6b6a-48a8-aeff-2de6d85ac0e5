package com.movisat.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class Timer {
    DateFormat format = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
    Date date;
    int seconds;

    public Timer(int seconds) {
        this.seconds = seconds;
        calculateNextTime();
    }

    public boolean isOver() {
        boolean _isOver = date == null || Calendar.getInstance().getTime().after(date);
        if (_isOver) calculateNextTime();
        return _isOver;
    }

    private void calculateNextTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, seconds);
        date = calendar.getTime();
    }

    /// Se utiliza para borrar el temporizador actual y que pueda volver a ejecutarse directamente.
    public void restart() {
        date = null;
    }
}
