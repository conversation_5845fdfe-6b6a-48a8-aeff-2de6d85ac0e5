package com.movisat.utils;

import java.util.HashMap;


public class FactoryCachedParam<P, V>
{
    private HashMap<String, V> values = new HashMap<String, V>();
    private IFunc<P, V> builder;
    private IFunc<P, String> paramToKey;

    public FactoryCachedParam(IFunc<P, V> builder, IFunc<P, String> paramToKey)
    {
        this.builder = builder;
        this.paramToKey = paramToKey;
    }

    public V get(P param)
    {
        String key = paramToKey.execute(param);
        if(key == null || key.isEmpty()) return null;
        if (!values.containsKey(key) || values.get(key) == null)
            values.put(key, builder.execute(param));
        return values.get(key);
    }

    public void clear()
    {
        values.clear();
    }

}