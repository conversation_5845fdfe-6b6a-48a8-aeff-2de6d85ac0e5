package com.movisat.utils;

import java.util.Timer;
import java.util.TimerTask;

public class DelayedOperation {

    private Timer timer;

    public void execute(int milliseconds, final IFuncVoid function) {
        if (timer != null) cancel();
        if (timer == null) timer = new Timer();

        final DelayedOperation ins = this;
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                function.execute();
                ins.cancel();
            }
        }, milliseconds);
    }

    public void cancel() {
        if (timer != null) timer.cancel();
        timer = null;
    }
}
