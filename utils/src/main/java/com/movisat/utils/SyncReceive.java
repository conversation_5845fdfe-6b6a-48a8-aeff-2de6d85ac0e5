package com.movisat.utils;

import java.util.Date;
import java.util.List;


public class SyncReceive<T> {
    private Preference<Date> lastSync;
    private IFunc<T, Date> getSyncDate;
    private ICallBack<Date> getAll;
    private IFunc<T, Boolean> save;
    private ICallBack<T> onErrorSaving;
    private ICallBack<String> printError;

    public SyncReceive(Preference<Date> lastSync, IFunc<T, Date> getSyncDate, ICallBack<Date> getAll, IFunc<T, Boolean> save, ICallBack<T> onErrorSaving, ICallBack<String> printError) {
        this.lastSync = lastSync;
        this.getSyncDate = getSyncDate;
        this.getAll = getAll;
        this.save = save;
        this.onErrorSaving = onErrorSaving;
        this.printError = printError;
    }

    void execute() {
        try {
            Date syncDate = lastSync.get();
            getAll.execute(syncDate);
        } catch (Exception e) {
            e.printStackTrace();
            printError.execute("[" + this.getClass() + "] Error al sincronizar: " + e.getMessage());
        }
    }

    void onReceive(List<T> list) {
        if (list == null || list.isEmpty()) return;

        Date syncDate = lastSync.get();
        T lastModel = null;
        try {
            for (T m : list) {
                lastModel = m;
                if (save.execute(m)) {
                    Date md = getSyncDate.execute(m);
                    if (md == null)
                        printError.execute("[" + this.getClass() + "] Se ha obtenido una fecha nula del modelo. Modelo: " + lastModel.toString());
                    else {
                        syncDate = Utilss.dateGetLast(syncDate, md);
                        lastSync.set(syncDate);
                    }
                } else {
                    printError.execute("[" + this.getClass() + "] No se ha guardado el modelo por un motivo desconocido. Modelo: " + lastModel.toString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            printError.execute("[" + this.getClass() + "] Error al guardar el modelo: " + e.getMessage() + ". Modelo: " + lastModel.toString());
            onErrorSaving.execute(lastModel);
        }
    }


}
