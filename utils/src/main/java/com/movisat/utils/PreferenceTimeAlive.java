package com.movisat.utils;

import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class PreferenceTimeAlive<T> {
    private Preference<T> value;
    private Preference<Long> timer;
    private int minutesAlive;

    public PreferenceTimeAlive(Preference<T> value, Preference<Long> timer, int minutesAlive) {
        this.value = value;
        this.timer = timer;
        this.minutesAlive = minutesAlive;
    }

    public T get() {
        if (getIsAlive()) return value.get();
        return null;
    }

    private boolean getIsAlive() {
        boolean _isAlive = true;
        try {
            Long _timer = timer.get();
            if (_timer != null && _timer > 0) {
                long current = TimeUnit.MILLISECONDS.toMinutes(Calendar.getInstance().getTime().getTime());
                long saved = TimeUnit.MILLISECONDS.toMinutes(_timer);
                if ((current - saved) > minutesAlive) _isAlive = false;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return _isAlive;
    }

    public void set(T v) {
        value.set(v);
        timer.set(Calendar.getInstance().getTime().getTime());
    }
}
