package com.movisat.utils;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class Parser {
    public static Long toLong(String value) {
        if (value == null || value.isEmpty()) return new Long(0);
        return Long.valueOf(value);
    }

    public static String toString(Long lng) {
        if (lng == null) return "";
        return Long.toString(lng);
    }

    public static String toString(int i) {
        return String.valueOf(i);
    }

    public static String toString(boolean b) {
        return String.valueOf(b);
    }

    public static Date toDate(String value) {
        if (value == null || value.isEmpty()) return null;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            if (value.contains("T")) value = value.replace('T', ' ');
            date = format.parse(value);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static String toString(Date date) {
        if (date == null) return "";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(date);
    }

    public static Integer toInteger(String value) {
        if (value == null || value.isEmpty()) return null;
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException ex) {
        }
        return null;
    }

    public static Integer toInteger(double value) {
        return (int) Math.round(value);
    }

    public static Integer toInteger(boolean value) {
        if (value) return 1;
        return 0;
    }

    public static boolean toBoolean(int value) {
        if (value == 1) return true;
        return false;
    }

    public static boolean toBoolean(String value) {
        if (value == null) return false;
        if (value.isEmpty()) return false;
        if (Utilss.equals(value, "1")) return true;
        if (Utilss.equals(value.toLowerCase(), "true")) return true;
        if (Utilss.equals(value.toLowerCase(), "yes")) return true;
        return false;
    }


    public static String toPK(Date value) {
        if (value == null) return "";
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        return format.format(value);
    }

    public static <T extends Object> List<T> toList(T[] arr) {
        // Hay que convertir el array en una lista y añadirlo a otra lista para que el resultado
        // sea una lista con tamaño dinámico y no genere error al modificarla.
        // El método asList devuelve una lista de tamaño fijo y genera UnsupportedException al modificarse.
        return new ArrayList<T>(Arrays.asList(arr));
    }

    public static double cutDecimals(double value, int i) {
        if (i == 0) return value;
        String format = "#.";
        for (int j = 0; j < i; j++) format += "#";

        DecimalFormat df = new DecimalFormat(format);
        return toDouble(df.format(value).replace(',', '.'));
    }

    private static double toDouble(String value) {
        if (Utilss.isNullOrEmpty(value)) return 0.0;
        return Double.parseDouble(value);
    }
}
