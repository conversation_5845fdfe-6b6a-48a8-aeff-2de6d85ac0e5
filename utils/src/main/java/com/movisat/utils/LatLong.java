package com.movisat.utils;

public class LatLong {
    private double lat = 0;
    private double lng = 0;
    private int x = 0;
    private int y = 0;

    public LatLong(double lat, double lng) {
        this.lat = lat;
        this.lng = lng;
        try {
            y = (int) (lat * 100000);
            x = (int) (lng * 100000);
        } catch (Exception e) {
            x = 0;
            y = 0;
        }
    }

    public double getLat() {
        return lat;
    }

    public double getLng() {
        return lng;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public boolean isValid() {
        if (x == 0 && y == 0) return false;
        if (lat == 0 && lng == 0) return false;
        return true;
    }

    public boolean isEqual(LatLong latLong) {
        if (latLong == null) return false;
        if (latLong.lat != lat) return false;
        if (latLong.lng != lng) return false;
        return true;
    }

    public LatLong clone() {
        return new LatLong(lat, lng);
    }

    @Override
    public String toString() {
        return "" + lat + "," + lng;
    }
}

