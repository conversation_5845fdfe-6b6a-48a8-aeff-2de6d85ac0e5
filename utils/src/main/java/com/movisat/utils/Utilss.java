package com.movisat.utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Random;

public class Utilss {
    public static boolean equals(String s1, String s2) {
        if (s1 == null && s2 == null) return true;
        if (s1 == null) return false;
        if (s2 == null) return false;
        return s1.equals(s2);
    }


    // Radio de la Tierra en metros
    private static final double EARTH_RADIUS = 6371000;

    public static double calculateDistanceInMeters(LatLong one, LatLong two) {
        return calculateDistanceInMeters(one.getLat(), one.getLng(), two.getLat(), two.getLng());
    }

    public static double calculateDistanceInMeters(double lat1, double lon1, double lat2, double lon2) {
        // Convertir latitudes y longitudes de grados a radianes
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        // Calcular diferencias de coordenadas
        double deltaLat = lat2Rad - lat1Rad;
        double deltaLon = lon2Rad - lon1Rad;

        // Aplicar la fórmula del semiverseno
        double a = Math.sin(deltaLat / 2) * Math.sin(deltaLat / 2) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(deltaLon / 2) * Math.sin(deltaLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // Calcular y devolver la distancia
        return Math.abs(EARTH_RADIUS * c);
    }

    public static boolean isNullOrEmpty(List o) {
        return o == null || o.isEmpty();
    }

    public static boolean isNullOrEmpty(Collection o) {
        return o == null || o.isEmpty();
    }


    public static boolean isNullOrEmpty(Object[] o) {
        return o == null || o.length == 0;
    }

    public static boolean isNullOrEmpty(int[] o) {
        return o == null || o.length == 0;
    }

    public static boolean isFilled(List o) {
        return !isNullOrEmpty(o);
    }

    public static boolean isFilled(Object[] o) {
        return !isNullOrEmpty(o);
    }

    public static boolean isFilled(int[] o) {
        return !isNullOrEmpty(o);
    }

    public static boolean isFilled(Collection o) {
        return !isNullOrEmpty(o);
    }

    public static <T extends Object> ArrayList<T> sublist(ArrayList<T> items, int start, int end) {
        if (!isFilled(items)) return new ArrayList<>();
        if (items.size() <= (end + 1)) return items;
        return new ArrayList<T>(items.subList(start, end));
    }

    public static <T extends Object> List<T> where(List<T> items, IFunc<T, Boolean> match) {
        if (!isFilled(items)) return new ArrayList<>();
        List<T> result = new ArrayList<>();
        for (T i : items)
            if (match.execute(i)) result.add(i);
        return result;
    }

    public static <T extends Object> T firstWhere(List<T> items, IFunc<T, Boolean> match) {
        if (!isFilled(items)) return null;
        for (T i : items)
            if (match.execute(i)) return i;
        return null;
    }


    public static <T extends Object> void forEach(List<T> items, ICallBack<T> action) {
        if (!isFilled(items)) return;
        for (T i : items)
            action.execute(i);
    }

    public static <T extends Object> void forEach(Collection<T> items, ICallBack<T> action) {
        if (!isFilled(items)) return;
        for (T i : items)
            action.execute(i);
    }

    public static <T> T firstWhere(T[] items, IFunc<T, Boolean> match) {
        if (isNullOrEmpty(items)) return null;
        for (int i = 0; i < items.length; i++)
            if (match.execute(items[i])) return items[i];
        return null;
    }

    public static <T, E> List<E> map(List<T> items, IFunc<T, E> convert) {
        if (!isFilled(items)) return new ArrayList<>();
        List<E> result = new ArrayList<>();
        for (T i : items)
            result.add(convert.execute(i));
        return result;
    }

    public static <T> int indexWhere(T[] items, IFunc<T, Boolean> match) {
        if (isNullOrEmpty(items)) return -1;
        for (int i = 0; i < items.length; i++) {
            if (match.execute(items[i])) return i;
        }
        return -1;
    }

    public static <T> int indexWhere(List<T> items, IFunc<T, Boolean> match) {
        if (isNullOrEmpty(items)) return -1;
        for (int i = 0; i < items.size(); i++) {
            if (match.execute(items.get(i))) return i;
        }
        return -1;
    }

    public static boolean isNullOrEmpty(String o) {
        return o == null || o.isEmpty();
    }

    public static boolean isNullOrEmptyLiteral(String o) {
        return o == null || o.isEmpty() || o.equals("null");
    }

    public static boolean isNullOrEmptyTrim(String o) {
        return o == null || o.trim().isEmpty();
    }

    public static String normalize(String o) {
        if (o == null) return null;
        if (o.isEmpty()) return "";
        if (o.equals("null")) return null;
        return o;
    }

    public static boolean isFilled(String o) {
        return !isNullOrEmpty(o);
    }

    public static Date dateGetLast(Date d1, Date d2) {
        if (d1 == null && d2 == null) return null;
        if (d1 == null) return d2;
        if (d2 == null) return d1;
        if (d1.before(d2)) return d2;
        return d1;
    }

    public static Date addMinutes(int minutes, Date beforeTime) {
        final long ONE_MINUTE_IN_MILLIS = 60000;//millisecs

        long curTimeInMs = beforeTime.getTime();
        Date afterAddingMins = new Date(curTimeInMs + (minutes * ONE_MINUTE_IN_MILLIS));
        return afterAddingMins;
    }

    public static Date addSeconds(int seconds, Date beforeTime) {
        final long ON_SECOND_IN_MILLIS = 1000;//millisecs

        long curTimeInMs = beforeTime.getTime();
        Date afterAddingMins = new Date(curTimeInMs + (seconds * ON_SECOND_IN_MILLIS));
        return afterAddingMins;
    }

    public static String join(List list, String separator) {
        if (isNullOrEmpty(list)) return "";
        String value = "";
        if (list.size() > 0) {
            // No uso el String.join porque tiene requisitos del versión del SDK.
            for (int i = 0; i < list.size(); i++) {
                value += list.get(i).toString();
                if ((i + 1) < list.size()) value += separator;
            }
        }
        return value;
    }

    public static String getFormatted(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    public static String getFormattedNow(String format) {
        return getFormatted(new Date(), format);
    }

    public static Date now() {
        return new Date();
    }

    public static int getLength(List l) {
        if (isNullOrEmpty(l)) return 0;
        return l.size();
    }

    public static <T> T getLast(List<T> l) {
        if (isNullOrEmpty(l)) return null;
        return l.get(l.size() - 1);
    }

    public static <T> T getLast(T[] l) {
        if (isNullOrEmpty(l)) return null;
        return l[l.length - 1];
    }

    public static int getRandom(int min, int max) {
        return new Random().nextInt((max - min) + 1) + min;
    }

    public static boolean getRandom() {
        return new Random().nextBoolean();
    }

    public static boolean isPathImage(String path) {
        if (isNullOrEmpty(path)) return false;
        String ext = path.substring(path.lastIndexOf(".") + 1);
        return ext.equalsIgnoreCase("jpg") || ext.equalsIgnoreCase("jpeg") || ext.equalsIgnoreCase("png");
    }

    public static double getDiffInDays(String start, String end) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date startDate = sdf.parse(start);
            Date endDate = sdf.parse(end);
            assert startDate != null;
            assert endDate != null;
            long diff = startDate.getTime() - endDate.getTime();
            long diffDays = diff / (24 * 60 * 60 * 1000);
            return (double) Math.abs(diffDays);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    public static LatLong calculateIntersectionPoint(LatLong point, List<LatLong> route) {
        if (route.size() < 2) {
            return point;
        }

        LatLong closestIntersection = null;
        double minDistance = Double.MAX_VALUE;

        for (int i = 0; i < route.size() - 1; i++) {
            LatLong start = route.get(i);
            LatLong end = route.get(i + 1);

            LatLong intersection = findPerpendicularIntersection(point, start, end);
            double distance = calculateDistance(point, intersection);

            if (distance < minDistance) {
                minDistance = distance;
                closestIntersection = intersection;
            }
        }

        return closestIntersection;
    }

    private static LatLong findPerpendicularIntersection(LatLong point, LatLong start, LatLong end) {
        double x1 = start.getLat();
        double y1 = start.getLng();
        double x2 = end.getLat();
        double y2 = end.getLng();

        double x3 = point.getLat();
        double y3 = point.getLng();

        double px = x2 - x1;
        double py = y2 - y1;

        double norm = px * px + py * py;

        double u = ((x3 - x1) * px + (y3 - y1) * py) / norm;

        double ix = x1 + u * px;
        double iy = y1 + u * py;

        return new LatLong(ix, iy);
    }

    private static double calculateDistance(LatLong p1, LatLong p2) {
        double latDiff = p1.getLat() - p2.getLat();
        double lonDiff = p1.getLng() - p2.getLng();
        return Math.sqrt(latDiff * latDiff + lonDiff * lonDiff);
    }

    private static final double TOLERANCE = 1e-6;

    public static boolean isPointOnPolyline(LatLong point, List<LatLong> polyline) {
        for (int i = 0; i < polyline.size() - 1; i++) {
            LatLong start = polyline.get(i);
            LatLong end = polyline.get(i + 1);
            if (isPointOnSegment(point, start, end)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isPointOnSegment(LatLong point, LatLong start, LatLong end) {
        double crossProduct = (point.getLng() - start.getLng()) * (end.getLat() - start.getLat()) -
                (point.getLat() - start.getLat()) * (end.getLng() - start.getLng());
        if (Math.abs(crossProduct) > TOLERANCE) {
            return false;
        }

        double dotProduct = (point.getLat() - start.getLat()) * (end.getLat() - start.getLat()) +
                (point.getLng() - start.getLng()) * (end.getLng() - start.getLng());
        if (dotProduct < 0) {
            return false;
        }

        double squaredLength = (end.getLat() - start.getLat()) * (end.getLat() - start.getLat()) +
                (end.getLng() - start.getLng()) * (end.getLng() - start.getLng());
        if (dotProduct > squaredLength) {
            return false;
        }

        return true;
    }
}
