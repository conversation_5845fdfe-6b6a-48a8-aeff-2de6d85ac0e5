package com.movisat.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PreferenceListString {

    private Preference<String> preference;

    public PreferenceListString(Preference<String> preference) {
        this.preference = preference;
    }

    public List<String> get() {
        String value = preference.get();
        if (value == null || value.isEmpty()) return new ArrayList<String>();
        String[] arr = value.split(";");
        if (arr == null || arr.length == 0) return new ArrayList<String>();

        // Hay que convertir el array en una lista y añadirlo a otra lista para que el resultado
        // sea una lista con tamaño dinámico y no genere error al modificarla.
        // El método asList devuelve una lista de tamaño fijo y genera UnsupportedException al modificarse.
        return new ArrayList<String>(Arrays.asList(arr));
    }

    public void set(List<String> values) {
        if (values == null) values = new ArrayList<String>();
        String value = "";
        if (values.size() > 0) {
            // No uso el String.join porque tiene requisitos del versión del SDK.
            for (int i = 0; i < values.size(); i++) {
                value += values.get(i);
                if ((i + 1) < values.size()) value += ";";
            }
        }
        preference.set(value);
    }

    public void add(String value) {
        List<String> values = get();
        if (values.contains(value)) return;
        values.add(value);
        set(values);
    }

    public void remove(String value) {
        List<String> values = get();
        if(values == null || values.isEmpty()) return;
        if (!values.contains(value)) return;
        values.remove(value);
        set(values);
    }

    public boolean contains(String value) {
        List<String> values = get();
        if(values == null || values.isEmpty()) return false;
        return values.contains(value);
    }

    public void clear() {
        preference.set("");
    }
}
