package com.movisat.ecosat;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

import com.movisat.utils.LatLong;
import com.movisat.utils.Utilss;

import java.util.ArrayList;
import java.util.List;

public class CalculateIntersectionPointTest {

    @Test
    public void test() {
        List<LatLong> route = new ArrayList<>();
        route.add(new LatLong(0, 0));
        route.add(new LatLong(1, 1));

        LatLong point = new LatLong(1, 0);

        LatLong intersection = Utilss.calculateIntersectionPoint(point, route);
        System.out.println("Punto de intersección: (" + intersection.getLat() + ", " + intersection.getLng() + ")");
        assertEquals(0.5, intersection.getLat(), 0.0001);
        assertEquals(0.5, intersection.getLng(), 0.0001);

        route.add(new LatLong(2, 2));
        point = new LatLong(2, 0);
        intersection = Utilss.calculateIntersectionPoint(point, route);
        System.out.println("Punto de intersección: (" + intersection.getLat() + ", " + intersection.getLng() + ")");
        assertEquals(1, intersection.getLat(), 0.0001);
        assertEquals(1, intersection.getLng(), 0.0001);

        route.clear();
        route.add(new LatLong(0, 0));
        route.add(new LatLong(7, 0));
        point = new LatLong(3, 3);
        intersection = Utilss.calculateIntersectionPoint(point, route);
        System.out.println("Punto de intersección: (" + intersection.getLat() + ", " + intersection.getLng() + ")");
        assertEquals(3, intersection.getLat(), 0.0001);
        assertEquals(0, intersection.getLng(), 0.0001);
    }
}
