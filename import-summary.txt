ECLIPSE ANDROID PROJECT IMPORT SUMMARY
======================================

Manifest Merging:
-----------------
Your project uses libraries that provide manifests, and your Eclipse
project did not explicitly turn on manifest merging. In Android Gradle
projects, manifests are always merged (meaning that contents from your
libraries' manifests will be merged into the app manifest. If you had
manually copied contents from library manifests into your app manifest
you may need to remove these for the app to build correctly.

Ignored Files:
--------------
The following files were *not* copied into the new Gradle project; you
should evaluate whether these are still needed in your project and if
so manually move them:

From EcoSAT:
* key
* notas.txt
* proguard-project.txt
From MaterialDesign:
* build.gradle
* gradle.properties
* proguard-project.txt
From google-map-cluster_lib:
* build.gradle
* tests\
* tests\src\
* tests\src\com\
* tests\src\com\google\
* tests\src\com\google\maps\
* tests\src\com\google\maps\android\
* tests\src\com\google\maps\android\PolyUtilTest.java
* tests\src\com\google\maps\android\SphericalUtilTest.java
* tests\src\com\google\maps\android\heatmaps\
* tests\src\com\google\maps\android\heatmaps\GradientTest.java
* tests\src\com\google\maps\android\heatmaps\UtilTest.java
* tests\src\com\google\maps\android\quadtree\
* tests\src\com\google\maps\android\quadtree\PointQuadTreeTest.java

Replaced Jars with Dependencies:
--------------------------------
The importer recognized the following .jar files as third party
libraries and replaced them with Gradle dependencies instead. This has
the advantage that more explicit version information is known, and the
libraries can be updated automatically. However, it is possible that
the .jar file in your project was of an older version than the
dependency we picked, which could render the project not compileable.
You can disable the jar replacement in the import wizard and try again:

android-support-v4.jar => com.android.support:support-v4:19.1.0

Replaced Libraries with Dependencies:
-------------------------------------
The importer recognized the following library projects as third party
libraries and replaced them with Gradle dependencies instead. This has
the advantage that more explicit version information is known, and the
libraries can be updated automatically. However, it is possible that
the source files in your project were of an older version than the
dependency we picked, which could render the project not compileable.
You can disable the library replacement in the import wizard and try
again:

google-play-services_lib => [com.google.android.gms:play-services:+]

Moved Files:
------------
Android Gradle projects use a different directory structure than ADT
Eclipse projects. Here's how the projects were restructured:

In MaterialDesign:
* AndroidManifest.xml => materialDesign\src\main\AndroidManifest.xml
* assets\ => materialDesign\src\main\assets\
* libs\nineoldandroids-2.4.0.jar => materialDesign\libs\nineoldandroids-2.4.0.jar
* lint.xml => materialDesign\lint.xml
* res\ => materialDesign\src\main\res\
* src\ => materialDesign\src\main\java\
In google-map-cluster_lib:
* AndroidManifest.xml => googlemapcluster_lib\src\main\AndroidManifest.xml
* assets\ => googlemapcluster_lib\src\main\assets
* res\ => googlemapcluster_lib\src\main\res\
* src\ => googlemapcluster_lib\src\main\java\
In EcoSAT:
* AndroidManifest.xml => ecoSAT\src\main\AndroidManifest.xml
* assets\ => ecoSAT\src\main\assets\
* libs\commons-net-2.0-ftp.jar => ecoSAT\libs\commons-net-2.0-ftp.jar
* lint.xml => ecoSAT\lint.xml
* res\ => ecoSAT\src\main\res\
* src\ => ecoSAT\src\main\java\

Next Steps:
-----------
You can now build the project. The Gradle project needs network
connectivity to download dependencies.

Bugs:
-----
If for some reason your project does not build, and you determine that
it is due to a bug or limitation of the Eclipse to Gradle importer,
please file a bug at http://b.android.com with category
Component-Tools.

(This import summary is for your information only, and can be deleted
after import once you are satisfied with the results.)
