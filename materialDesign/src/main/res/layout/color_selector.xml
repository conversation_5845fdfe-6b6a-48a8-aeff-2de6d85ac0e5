<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:materialdesign="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootSelector"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#55000000"
    android:padding="40dp" >

    <LinearLayout
        android:id="@+id/contentSelector"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/dialog_background"
        android:orientation="vertical" >

        <View
            android:id="@+id/viewColor"
            android:layout_width="fill_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#FFF"
            android:padding="16dp" >

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal" >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:textSize="17sp"
                    android:text="R"
                    android:textColor="#4D4D4D" />

                <com.gc.materialdesign.views.Slider
                    android:id="@+id/red"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:background="#4D4D4D"
                    materialdesign:max="255"
                    materialdesign:min="0"
                    materialdesign:showNumberIndicator="true" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal" >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:textSize="17sp"
                    android:text="G"
                    android:textColor="#4D4D4D" />

                <com.gc.materialdesign.views.Slider
                    android:id="@+id/green"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:background="#4D4D4D"
                    materialdesign:max="255"
                    materialdesign:min="0"
                    materialdesign:showNumberIndicator="true" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal" >

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:textSize="17sp"
                    android:text="B"
                    android:textColor="#4D4D4D" />

                <com.gc.materialdesign.views.Slider
                    android:id="@+id/blue"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:background="#4D4D4D"
                    materialdesign:max="255"
                    materialdesign:min="0"
                    materialdesign:showNumberIndicator="true" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

</RelativeLayout>