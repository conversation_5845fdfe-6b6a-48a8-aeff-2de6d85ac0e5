<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent" >

    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:minHeight="56dp"
        android:layout_alignParentBottom="true" >

        <RelativeLayout
            android:id="@+id/snackbar"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:background="#333333"
            android:paddingBottom="18dp"
            android:paddingLeft="24dp"
            android:paddingRight="24dp"
            android:paddingTop="18dp"
            android:visibility="invisible" >

            <com.gc.materialdesign.views.ButtonFlat
                android:id="@+id/buttonflat"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="24dp"
                android:text="ACTION" />

             <TextView
                android:id="@+id/text"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toLeftOf="@id/buttonflat"
                android:textColor="#FFF" />

        </RelativeLayout>
    </RelativeLayout>

</RelativeLayout>