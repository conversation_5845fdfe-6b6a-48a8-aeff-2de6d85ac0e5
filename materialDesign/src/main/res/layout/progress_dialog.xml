<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#55000000"
    android:padding="32dp" >

    <RelativeLayout
        android:id="@+id/contentDialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/dialog_background"
        android:padding="24dp" >

        <com.gc.materialdesign.views.ProgressBarCircularIndeterminate
            android:id="@+id/progressBarCircularIndetermininate"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_centerVertical="true"
            android:background="#1E88E5" />

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toRightOf="@+id/progressBarCircularIndetermininate"
            android:layout_marginLeft="8dp"
            android:text="Title"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="#000" />
    </RelativeLayout>

</RelativeLayout>