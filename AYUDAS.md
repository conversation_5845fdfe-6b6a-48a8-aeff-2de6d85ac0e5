# Dudas y errores comunes en Android

Fichero para apuntar todo lo que necesite recordarse o que agilice la resolución de problemas.

##  Arranque de proyectos

- Ponerse en rama adecuada o commit adecuados con VSCode, hacer git module init;git module update. 

- En general usar VSCode para usar Git con proyectos Android, no Android Studio.

- Buscar info en DEBUG sobre lo necesario para debugear. Ahora mismo usamos las credenciales del proyecto de Cieza el usuario SOPORTE con contraseña OMEGA3.

- A veces es necesario comentar unas líneas indicadas en el build.gradle de EcoSat porque nos aparece un mensaje de error en la app.

## Errores con dependencias

- File > sync project with gradle files

- Si no, Build > Clean project, File > Restart and invalidate caches

## Clase Logg

- Requiere de  Logg.addProvider(new LoggProviderConsole()) para mostrar errores en el logcat.

- Utilizar variables finales para las etiquetas del Logg, estaría bien tenerlas todas en alguna clase estática.

- Aprovecharemos los MANTIS para ir dejando pistas sobre los errores capturándolos con esta clase en lugares críticos.

## Errores en el login

- EcoSat. Error 200, este dispositivo está asociado a otro proyecto: le facilitamos el IMEI a Dani y lo reasigna.
- EcoSat. Error 301, no hay licencia para este tipo de producto: hay que borrar alguna licencia para poder entrar.