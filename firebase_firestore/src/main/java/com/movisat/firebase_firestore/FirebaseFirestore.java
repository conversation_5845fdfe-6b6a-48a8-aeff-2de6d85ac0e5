package com.movisat.firebase_firestore;

import android.net.Uri;

import androidx.annotation.NonNull;

import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.firebase.storage.FirebaseStorage;
import com.google.firebase.storage.StorageReference;
import com.google.firebase.storage.UploadTask;
import com.movisat.utils.Utilss;

import java.io.File;

public class FirebaseFirestore {

    FirebaseStorage storage;
    StorageReference storageReference;

    public void upload(String filePath, String fileName, String aplicationName, String aplicationVersion, String folderName) {
        // Create a storage reference from our app
        if (storage == null) {
            storage = FirebaseStorage.getInstance();
            storageReference = storage.getReference();
        }

        if (Utilss.isNullOrEmpty(aplicationName))
            aplicationName = "DESCONOCIDO";
        if (Utilss.isNullOrEmpty(aplicationVersion))
            aplicationVersion = "DESCONOCIDO";
        if (Utilss.isNullOrEmpty(folderName))
            folderName = Utilss.getFormattedNow("yyyyMMdd_HH_mm_ss_SSS");

        StorageReference ref
                = storageReference
                .child(
                        aplicationName + "/" + aplicationVersion + "/" + folderName + "/" + fileName);

        UploadTask uploadTask = ref.putFile(Uri.fromFile(new File(filePath)));
        uploadTask.addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(@NonNull Exception exception) {
                // Handle unsuccessful uploads
            }
        }).addOnSuccessListener(new OnSuccessListener<UploadTask.TaskSnapshot>() {
            @Override
            public void onSuccess(UploadTask.TaskSnapshot taskSnapshot) {
                // taskSnapshot.getMetadata() contains file metadata such as size, content-type, etc.
                // ...
            }
        });
    }
}
