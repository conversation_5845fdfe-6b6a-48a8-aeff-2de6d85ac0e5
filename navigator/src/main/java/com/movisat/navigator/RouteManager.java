package com.movisat.navigator;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.sygic.sdk.api.ApiItinerary;
import com.sygic.sdk.api.ApiNavigation;
import com.sygic.sdk.api.exception.GeneralException;
import com.sygic.sdk.api.exception.LoadRouteException;
import com.sygic.sdk.api.exception.NavigationException;
import com.sygic.sdk.api.model.WayPoint;
import com.movisat.utils.*;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Timer;
import java.util.TimerTask;

public class RouteManager {
    //================================================================================
    // Fields
    //================================================================================
    private final String TAG = "RouteManager";
    public final Stream<RouteManagerState> onStateChange = new Stream<RouteManagerState>(RouteManagerState.NOT_STARTED);
    private NavigatorController controller;
    private Destination destination = null;
    private Route route = null;
    private RouteOptimized routeOptimized = null;
    private Timer timer = null;
    //================================================================================
    // Subscriptions
    //================================================================================
    private final ICallBack<NavigatorControllerState> onNavigatorStateChange = state -> update(state);

    private ICallBack<NavigatorEvent> onEventReceived = value -> {
        Log.d(TAG, "Evento recibido: " + String.valueOf(value));

        // Si cancela la ruta, borramos la posición actual.
        if (value == NavigatorEvent.ROUTE_USERCANCEL) {
            if (destination != null)
                destination.isPlaying = false;
        } else if (value == NavigatorEvent.ROUTE_FINISH) {
            // Cuando se alcance un destino, comprobamos si el destino se asignó para llegar
            // a una ruta precalculada y así mostrarla.
            if (routeOptimized != null && routeOptimized.isFirstPointNavigable() && !routeOptimized.isFirstPointReached) {
                routeOptimized.isFirstPointReached = true;
                setRoute();
            }
            // Si el destino actual está en progreso, lo marco como finalizado.
            if (destination != null && destination.isPlaying) {
                Log.d(TAG, "Se ha recibido un ROUTE_FINISH habiendo un destino. Se marca como finalizado sin deterner la navegación.");

                // Se marca como finalizado sin detener la navegación para que Sygic siga enviando eventos.
                // Cuando envíe el evento OFF_ROUTE (si se sale del itinerario) se volverá a calcular el itinerario con el mismo destino.
                destination.isPlaying = false;
            }
        } else if (value == NavigatorEvent.OFF_ROUTE) {
            // Hay que evitar volver a enrutar a un destino una vez que la ruta a iniciado o está finalizada.
            // Esto generaba otra navegación adicional quue solapaba la actual.ﬁ
            if (routeOptimized != null && (routeOptimized.isPlaying || routeOptimized.isFirstPointReached)) {
                return;
            }

            // Cuando pasa relativamente cerca (unos 80 metros) del destino, la ruta se da por finalizada.
            // Se necesita volver a mostrar la ruta de navegación si se aleja o toma otro camino.
            if (destination != null && !destination.isPlaying) {
                Log.d(TAG, "Se ha recibido un OFF_ROUTE habiendo un destino. Se vuelve a ejectuar la navegación.");
                setDestination();
            }
        }

    };

    //================================================================================
    // Constructors
    //================================================================================
    protected RouteManager(NavigatorController controller) {
        this.controller = controller;
        this.controller.onStateChange.subscribe(onNavigatorStateChange);
        this.controller.onEvent.subscribe(onEventReceived);
    }

    //================================================================================
    // Methods - Public
    //================================================================================
    public boolean hasRouteActive() {
        if (route != null)
            return route.isPlaying;

        if (routeOptimized != null)
            return routeOptimized.isPlaying && routeOptimized.isFirstPointReached;

        return false;
    }

    public boolean hasAnyDestination() {
        return (route != null && route.isPlaying) ||
                (routeOptimized != null && routeOptimized.isPlaying) ||
                (destination != null && destination.isPlaying);
    }

    public void setDestination(LatLong newDestination) {
        if (newDestination == null) return;
        if (!newDestination.isValid()) return;
        // Evitamos que se vuelva a asignar la ruta si es igual a la actual.
        if (destination != null && destination.isPlaying && destination.latlong.isEqual(newDestination))
            return;
        // La almacenamos para ejecutarla cuando el navegador esté iniciado.
        destination = new Destination(newDestination);
        setDestination();
    }

    public void setRoute(Route route) {
        if (route == null || route.getPoints() == null || route.getPoints().isEmpty()) return;
        // La almacenamos para ejecutarla cuando el navegador esté iniciado.
        this.route = route;
        setRoute();
    }

    public void setRouteOptimized(RouteOptimized routeOptimized) {
        if (routeOptimized == null || routeOptimized.getPoints() == null || routeOptimized.getPoints().isEmpty())
            return;
        // La almacenamos para ejecutarla cuando el navegador esté iniciado.
        this.routeOptimized = routeOptimized;
        // Establecemos el primero punto como destino navegable.
        if (routeOptimized.isFirstPointNavigable())
            setDestination(routeOptimized.getPoints().get(0).latlong);
        setRoute();
    }


    public void stop() {
        try {
            // No detenemos la navegación si el navegador no está iniciado.
            // Hacerlo, genera a veces un error interno de Sygic que se propaga a la aplicación.
            if (onStateChange.getValue() == RouteManagerState.STARTED) {
                ApiNavigation.stopNavigation(0);
                onStateChange.notify(RouteManagerState.STOPPED);
            }
            if (route != null) {
                ApiItinerary.deleteItinerary(route.getName(), 0);
                route.isPlaying = false;
            }
            if (routeOptimized != null) {
                routeOptimized.isPlaying = false;
            }

            if (destination != null) {
                destination.isPlaying = false;
            }
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "stop", "", e);
        }
    }


    /***
     * Obtiene la posición actual de la ruta en la que se encuentra el usuario.
     * @return el índice de la ruta. -1 si ha ocurrido un error.
     */
    public int getCurrentRouteIndex() {
        try {
            String info = ApiNavigation.getRouteStatus(0);
            return (new JSONObject(info)).getInt("currentIndex");
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "getCurrentRouteIndex", "", e);
        }
        return -1;
    }

    public void dispose() {
        if (timer != null) timer.cancel();
        onStateChange.dispose();

        this.controller.onStateChange.unsubscribe(onNavigatorStateChange);
        this.controller.onEvent.unsubscribe(onEventReceived);
    }

    //================================================================================
    // Methods - Private
    //================================================================================
    private void update(NavigatorControllerState state) {
        setDestination();
        setRoute();
    }

    private boolean canDoAnything() {
        if (controller.onStateChange.getValue() != NavigatorControllerState.READY_FOR_USE)
            return false;
        return true;
    }

    private void setDestination() {
        if (!canDoAnything()) return;
        if (!canStartDestination()) return;
        if (onStateChange.getValue() != RouteManagerState.STARTED)
            onStateChange.notify(RouteManagerState.STARTED);
        startDestination();
    }

    private void setRoute() {
        if (!canDoAnything()) return;
        if (!canStartRoute() && !canStartRouteFixed()) return;
        if (onStateChange.getValue() != RouteManagerState.STARTED)
            onStateChange.notify(RouteManagerState.STARTED);
        startRoute();
    }

    private boolean canStartDestination() {
        if (destination == null) return false;
        if (destination.isPlaying) return false;
        if (route != null && route.isPlaying) return false;
        if (routeOptimized != null && routeOptimized.isPlaying) return false;
        return true;
    }

    private boolean canStartRoute() {
        return !(route == null || route.getPoints() == null || route.getPoints().isEmpty() || route.isPlaying);
    }

    private boolean canStartRouteFixed() {
        return !(routeOptimized == null || routeOptimized.getPoints() == null || routeOptimized.getPoints().isEmpty() || routeOptimized.isPlaying || (routeOptimized.isFirstPointNavigable() && !routeOptimized.isFirstPointReached));
    }


    /// Inicia rutas normales y rutas optimizadas.
    private void startRoute() {
        try {
            if (canStartRoute()) {
                String routeJson = RouteToJsonMapper.CreateJsonItinerary(route);
                ApiItinerary.addItinerary(routeJson, route.getName(), 0);
                ApiItinerary.setRoute(route.getName(), 0, 0);
                route.isPlaying = true;

            } else if (canStartRouteFixed()) {
                generateRouteFixed();
            }
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "startRoute", "", e);
        }
    }

    private void generateRouteFixed() throws LoadRouteException {
        controller.notifyLoading(true);
        String json = RouteFixedToJsonMapper.execute(routeOptimized);
        String path = null;

        try {
            path = routeOptimized.getOnSaveFile().execute(json);
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "generateRouteFixed", "", e);
        }

        try {
            if (path != null && !path.isEmpty()) {
                // showOnly a false se utiliza para que la ruta vaya desvaneciéndose conforme la complete.
                // A true, la ruta no desaparece y siempre tiene el mismo color.
                String jparams = "{\"startFromIndex\":" + routeOptimized.getPointIndex() + ", \"showOnly\": false}";
                ApiNavigation.loadComputedRoute(path, jparams, 0);
                routeOptimized.isPlaying = true;
                listenerRouteFixedPosition();
            }
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "generateRouteFixed", "", e);
        }
        cancelLoading();
    }

    private void startDestination() {
        controller.notifyLoading(true);
        try {
            WayPoint wp = new WayPoint("W", destination.latlong.getX(), destination.latlong.getY());
            ApiNavigation.startNavigation(wp, 0, false, 0);
            destination.isPlaying = true;
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "startDestination", "", e);
        }
        cancelLoading();
    }

    private void cancelLoading() {
        if (!controller.onLoading.getValue()) return;
        final Handler handler = new Handler(Looper.getMainLooper());
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                controller.notifyLoading(false);
            }
        }, 3000);
    }


    /***
     * Cada pocos segundos lee la posición actual de una ruta fija para asignarla al objeto.
     *
     * Se utilizará para poder modificar la longitud (clonando el objeto de la ruta) y tener
     * el detalle de la última posición por la que se quedó.
     */
    private void listenerRouteFixedPosition() {
        if (timer != null) return;
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    if (routeOptimized != null && routeOptimized.isPlaying) {
                        int index = getCurrentRouteIndex();
                        int currentIndex = routeOptimized.getPointIndex();
                        routeOptimized.setPointIndex(index);
                        // Si está llegando al final de la ruta cuando la ruta está limitada, restablecemos
                        // la ruta para que vuelva a pintar el resto de puntos.
                        if (routeOptimized.isLimitedVisiblePoints() && index > currentIndex)
                            generateRouteFixed();
                    }
                } catch (Throwable e) {
                    NavigatorController.catchError(TAG, "listenerRouteFixedPosition", "", e);
                }
            }
        }, 0, 2000);// Cada 2 segundos
    }

    public Destination getDestination() {
        return this.destination;
    }
}
