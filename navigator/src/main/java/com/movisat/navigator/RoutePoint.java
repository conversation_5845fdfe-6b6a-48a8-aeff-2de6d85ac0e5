package com.movisat.navigator;

import com.movisat.utils.LatLong;

public class RoutePoint {
    private LatLong latlong;
    private String startDate;
    private String endDate;

    public RoutePoint(LatLong latlong) {
        this.latlong = latlong;
    }

    private RoutePoint(LatLong latlong, String startDate, String endDate) {
        this.latlong = latlong;
        this.startDate = startDate;
        this.endDate = endDate;
    }


    RoutePoint basic(LatLong latlong) {
        return new RoutePoint(latlong, null, null);
    }

    RoutePoint withTime(LatLong latlong, String startDate, String endDate) {
        return new RoutePoint(latlong, startDate, endDate);
    }


    public LatLong getLatlong() {
        return latlong;
    }

    public String getStartDate() {
        return startDate;
    }

    public String getEndDate() {
        return endDate;
    }
}
