package com.movisat.navigator;

import android.util.Log;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;

class MapIconToJsonMapper {

    private static final String TAG = "MapIconToJsonMapper";

    public static String execute(ArrayList<MapIcon> icons) {
        try {
            JSONArray iconList = new JSONArray();
            JSONObject icon;
            for (int i = 1; i < icons.size(); i++) {
                icon = new JSONObject();
                JSONObject props = new JSONObject();
                props.put("name", icons.get(i).getName());
                props.put("radius", 5);
                props.put("transparency", 90);
                props.put("color", "#1010BF");
                props.put("borderColor", "#1010BF");
                props.put("notificationData", icons.get(i).getName());

                JSONObject geometry = new JSONObject();
                JSONArray coors = new JSONArray();
                coors.put(icons.get(i).getLatLong().getLng());
                coors.put(icons.get(i).getLatLong().getLat());
                geometry.put("type", "Point");
                geometry.put("coordinates", coors);

                icon.put("type", "Feature");
                icon.put("properties", props);
                icon.put("geometry", geometry);

                if (icon == null) return null;
                iconList.put(icon);
            }
            JSONObject json = new JSONObject();
            json.put("type", "FeatureCollection");
            json.put("features", iconList);
            return json.toString();
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "execute", "", e);
        }
        return null;
    }

}
