package com.movisat.navigator;

import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.movisat.utils.ICallBack;
import com.sygic.aura.ResourceManager;
import com.sygic.aura.utils.PermissionsUtils;

import org.jetbrains.annotations.NotNull;

public class NavigatorFragment extends Fragment {

    private static final String TAG = "NavigatorFragment";

    private NavigatorController controller;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private Runnable runnable = new Runnable() {
        @Override
        public void run() {
            // Tratamos de cambiar el estado tras el tiempo de espera, para que intente iniciar de nuevo si sigue en estado STARTING.
            controller.changeStatePage(NavigatorControllerState.NOT_STARTED);
            startSygic();
        }
    };

    private final ICallBack<Void> onForceRebuildFragment = value -> start();

    public NavigatorFragment(NavigatorController controller) {
        super(R.layout.navigator_fragment);
        this.controller = controller;
        this.controller.onForceRebuildFragment.subscribe(onForceRebuildFragment);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        start();
    }

    @Override
    public void onResume() {
        super.onResume();
        start();
    }

    private void start() {
        try {
            if (!controller.canRebuildFragment()) return;
            controller.onRebuilding();

            checkSygicResources();
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "start", "", e);
        }
    }

    private void checkSygicResources() {

        ResourceManager resourceManager = new ResourceManager(getActivity(), null);
        if (resourceManager.shouldUpdateResources()) {
            resourceManager.updateResources(new ResourceManager.OnResultListener() {
                @Override
                public void onError(int errorCode, @NotNull String message) {
                    controller.changeStatePage(NavigatorControllerState.NOT_STARTED);
                }

                @Override
                public void onSuccess() {
                    startSygic();
                }
            });

        } else {
            startSygic();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        for (int res : grantResults) {
            if (res != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(getActivity(), "Tienes que aceptar todos los permisos", Toast.LENGTH_LONG).show();
                return;
            }
        }

        // all permissions are granted
        checkSygicResources();
    }


    private void startSygic() {
        try {
            SygicNaviFragment fragment = controller.getSygicFragment();
            getChildFragmentManager().beginTransaction().replace(R.id.sygicmap, fragment).commitAllowingStateLoss();
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "startSygic", "", e);
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        handler.removeCallbacks(runnable);
        controller.changeStatePage(NavigatorControllerState.HIDDEN);
    }

}
