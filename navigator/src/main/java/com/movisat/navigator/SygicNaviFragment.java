package com.movisat.navigator;

import android.app.Activity;
import android.util.Log;

import com.sygic.aura.embedded.IApiCallback;
import com.sygic.aura.embedded.SygicFragmentSupportV4;
import com.sygic.sdk.api.events.ApiEvents;

public class SygicNaviFragment extends SygicFragmentSupportV4 {

    private NavigatorController controller;

    public SygicNaviFragment(NavigatorController controller) {
        this.controller = controller;
    }

    private final IApiCallback apiCallback = new IApiCallback() {
        @Override
        public void onEvent(int event, String s) {
            String eventName = "DESCONOCIDO";
            if (NavigatorController.EVENTS.containsKey(event)) {
                NavigatorEvent e = NavigatorController.EVENTS.get(event);
                controller.onEvent.notify(e);
                eventName = e.toString();
            } else {
                Log.w("SYGIC EVENT", "No se ha encontrado evento para el código " + String.valueOf(event));
            }

            Log.d("SYGIC EVENT", "Evento recibido: " + String.valueOf(event) + " - " + eventName + ". Datos: " + s);
            if (event == ApiEvents.EVENT_APP_EXIT) {
                controller.changeStatePage(NavigatorControllerState.CLOSED);
                controller.changeStateTab(NavigatorControllerState.CLOSED);
            } else if (event == ApiEvents.EVENT_APP_STARTED) {
                POIManager.cleanAllPois();
                // make sure API is only used after this event occurs
                controller.changeStatePage(NavigatorControllerState.READY_FOR_USE);
                controller.changeStateTab(NavigatorControllerState.READY_FOR_USE);
            } else if (event == ApiEvents.EVENT_ROUTE_COMPUTED) {
                // make sure API is only used after this event occurs
                controller.changeStatePage(NavigatorControllerState.READY_FOR_USE);
                controller.changeStateTab(NavigatorControllerState.READY_FOR_USE);
            }
        }

        @Override
        public void onServiceConnected() {
            controller.changeStatePage(NavigatorControllerState.STARTED);
            controller.changeStateTab(NavigatorControllerState.STARTED);
        }

        @Override
        public void onServiceDisconnected() {
            // Solo se cambia el estado de la página, el de tabs lo gestionará la aplicación principal.
            controller.changeStatePage(NavigatorControllerState.HIDDEN);
        }
    };


    @Override
    public void onResume() {
        this.startNavi();
        this.setCallback(apiCallback);
        super.onResume();
    }

}