package com.movisat.navigator;

import android.util.Log;

import com.movisat.utils.ICallBack;
import com.movisat.utils.LatLong;
import com.movisat.utils.Stream;
import com.sygic.sdk.api.Api;
import com.sygic.sdk.api.ApiItinerary;
import com.sygic.sdk.api.ApiNavigation;
import com.sygic.sdk.api.ApiPoi;
import com.sygic.sdk.api.exception.GeneralException;
import com.sygic.sdk.api.exception.LoadRouteException;
import com.sygic.sdk.api.model.Poi;
import com.sygic.sdk.api.model.PoiCategory;
import com.sygic.sdk.api.model.Position;
import com.sygic.sdk.api.model.WayPoint;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class POIManager {

    private static final String TAG = "POIManager";
    //================================================================================
    // Fields
    //================================================================================
    private NavigatorController controller;
    private OnGetIconPOI onGetIconPOI;
    private ArrayList<POI> tempPois = new ArrayList<>();
    private ArrayList<POI> pois = new ArrayList<>();
    private ArrayList<String> categories = new ArrayList<>();

    //================================================================================
    // Subscriptions
    //================================================================================
    private final ICallBack<NavigatorControllerState> onNavigatorStateChange = state -> update(state);

    //================================================================================
    // Constructors
    //================================================================================
    public POIManager(OnGetIconPOI onGetIconPOI) {
        this.onGetIconPOI = onGetIconPOI;
    }

    //================================================================================
    // Methods - Public
    //================================================================================
    public void add(POI poi) {
        tempPois.add(poi);
        showPois();
    }


    public void remove(String name) {
        for (POI p : pois) {
            if (p.getName().equals(name)) {
                remove(p);
            }
        }
    }

    public void remove(POI p) {
        try {
            ApiPoi.deletePoi(p.getPoi(), 0);
            pois.remove(p);
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "remove", "Error al eliminar el POI" + p.getName(), e);
        }
    }


    //================================================================================
    // Methods - Private
    //================================================================================
    boolean createCategory(POI poi) {
        try {
            if (categories.contains(poi.getCategory())) return true;

            File file = onGetIconPOI.execute(poi);
            if (file == null) throw new Exception("El fichero del POI " + poi.getCategory() + "es nulo");
            if (!file.exists())
                throw new Exception("El fichero del POI " + poi.getCategory() + " no existe " + file.getAbsolutePath());
            ApiPoi.addPoiCategory(poi.getCategory(), file.getAbsolutePath(), "ES", 0);

            // Actualizamos la lista de categorías.
            categories.add(poi.getCategory());
            return true;
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "createCategory", "Error al crear la categoría " + poi.getCategory(), e);
            return false;
        }
    }

    private void getCurrentCategories() {
        if (categories.isEmpty()) {
            try {
                ArrayList<PoiCategory> nativeCategories = ApiPoi.getPoiCategoryList(1000);
                for (PoiCategory c : nativeCategories) {
                    categories.add(c.getName());
                }
            } catch (Throwable e) {
                NavigatorController.catchError(TAG, "getCurrentCategories", "", e);
            }
        }
    }

    public static void cleanAllPois() {
        try {
            ArrayList<PoiCategory> nativeCategories = ApiPoi.getPoiCategoryList(1000);
            for (PoiCategory c : nativeCategories) {
                cleanCategoryPoi(c.getName());
            }
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "cleanAllPois", "", e);
        }
    }

    static void cleanCategoryPoi(String category) {

        // Ignoramos las categorías que no sean propias.
        if (!category.contains(POI.categoryPrefix)) return;

        // Eliminamos los POI existentes de cada categoría.
        try {
            ArrayList<Poi> nativePois = ApiPoi.getPoiList(category, false, 0);
            if (nativePois != null && !nativePois.isEmpty()) {
                for (Poi p : nativePois) {
                    ApiPoi.deletePoi(p, 0);
                }
            }
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "cleanCategoryPoi", "", e);
        }
    }

    void setController(NavigatorController controller) {
        this.controller = controller;
        this.controller.onStateChange.subscribe(onNavigatorStateChange);
    }

    private void update(NavigatorControllerState state) {
        showPois();
    }

    private void showPois() {
        if (!canDoAnything()) return;
        if (tempPois.isEmpty()) return;

        getCurrentCategories();

        for (POI poi : tempPois) {
            if (createCategory(poi)) {
                try {
                    // Cuando se va a mostrar un POI que ya estaba añadido, el objeto POI nativo se habrá asignado,
                    // por lo tanto no lo volvemos a mostrar.
                    if (poi.getPoi() != null) continue;

                    Position pos = new Position(poi.getLatlong().getX(), poi.getLatlong().getY());
                    Poi nativePoi = new Poi(pos, poi.getCategory(), poi.getName(), "", false);
                    ApiPoi.addPoi(nativePoi, 0);
                    poi.setPoi(nativePoi);
                    pois.add(poi);
                } catch (Throwable e) {
                    NavigatorController.catchError(TAG, "showPois", "Error al crear el POI " + poi.getName(), e);
                }
            }
        }
        tempPois.clear();
    }

    private boolean canDoAnything() {
        if (controller.onStateChange.getValue() != NavigatorControllerState.READY_FOR_USE)
            return false;
        return true;
    }

    void dispose() {
        pois.clear();
        categories.clear();
    }
}
