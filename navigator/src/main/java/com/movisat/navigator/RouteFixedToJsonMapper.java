package com.movisat.navigator;

import android.util.Log;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;

class RouteFixedToJsonMapper {

    private static final String TAG = "RouteFixedToJsonMapper";

    public static String execute(RouteOptimized route) {
        try {
            ArrayList<RouteOptimizedPoint> lst = route.getPoints();
            JSONArray routeparts = new JSONArray();
            for (int i = 0; i < lst.size(); i++) {
                // Limita el número de puntos visibles segun se ha indicado en la ruta.
                if (route.isLimitedVisiblePoints() && i > (route.getPointIndex() + NavigatorEnvironment.limitedVisiblePoints)) {
                    break;
                }
                JSONObject wp = createWaypoint(lst.get(i));
                if (wp == null) return null;
                routeparts.put(wp);
            }
            JSONObject json = new JSONObject();
            json.put("name", "Bratislava-pickup");
            json.put("version", route.getVersion());
            json.put("guidePoints", routeparts);
            return json.toString();
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "execute", "", e);
        }
        return null;
    }

    private static JSONObject createWaypoint(RouteOptimizedPoint point) {
        int ilat = (int) (point.latlong.getLat() * 100000);
        int ilon = (int) (point.latlong.getLng() * 100000);

        JSONObject obj = new JSONObject();
        try {
            obj.put("lon", Integer.valueOf(ilon));
            obj.put("lat", Integer.valueOf(ilat));
            if (point.colorHex != null && !point.colorHex.isEmpty())
                obj.put("color", point.colorHex);
            if (point.name != null && !point.name.isEmpty())
                obj.put("name", point.name);
            obj.put("instruction", Integer.valueOf(point.instruction));
            obj.put("roundaboutIndex", Integer.valueOf(point.roundaboutIndex));
            if (point.messageTts != null && !point.messageTts.isEmpty())
                obj.put("messageTts", point.messageTts);
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "createWaypoint", "", e);
            obj = null;
        }
        return obj;
    }

}
