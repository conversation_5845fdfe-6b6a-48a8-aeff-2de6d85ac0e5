package com.movisat.navigator;

public enum NavigatorEvent {
    APP_STARTED,
    ROUTE_USERCANCEL,
    WAYPOINT_VISITED,
    ROUTE_FINISH,
    ROUTE_COMPUTED,
    OFF_ROUTE,
    OFF_ROUTE_EXT,
    <PERSON>P<PERSON><PERSON><PERSON><PERSON><PERSON>_FINISHED,
    <PERSON><PERSON><PERSON>ARY_CHANGED,
    <PERSON><PERSON><PERSON>ARY_WARNING,
    APP_EXIT,
    MAIN_MENU,
    CONTEXT_MENU,
    EXIT_MENU,
    CUSTOM_MENU,
    CH<PERSON><PERSON>_LANGUAGE,
    CH<PERSON>GE_ORIENTATION,
    RADAR_WARNING,
    POI_WARNING,
    GEOFENCE,
    RESTRICTED_ROAD,
    BORDER_CROSSING,
    SPEED_EXCEEDING,
    SPEED_LIMIT_CHANGED,
    <PERSON>ITMAP_CLICK,
    POI_CLIC<PERSON>,
    WAYP<PERSON>INT_CLICKED,
    SH<PERSON>E_POSITION,
    BROWSE_MAP_EXIT,
    WAYPOINT_MARKED_AS_VISITED,
    <PERSON><PERSON><PERSON>C<PERSON>,
}
