package com.movisat.navigator;

import android.util.Log;

import com.movisat.utils.ICallBack;
import com.movisat.utils.LatLong;
import com.movisat.utils.Stream;
import com.sygic.sdk.api.ApiItinerary;
import com.sygic.sdk.api.ApiMaps;
import com.sygic.sdk.api.ApiNavigation;
import com.sygic.sdk.api.exception.GeneralException;
import com.sygic.sdk.api.exception.LoadRouteException;
import com.sygic.sdk.api.model.WayPoint;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class MapManager {
    private static final String TAG = "MapManager";
    //================================================================================
    // Fields
    //================================================================================
    private NavigatorController controller;
    private ArrayList<MapIcon> icons;

    //================================================================================
    // Subscriptions
    //================================================================================
    private final ICallBack<NavigatorControllerState> onNavigatorStateChange = state -> update(state);

    //================================================================================
    // Constructors
    //================================================================================
    public MapManager(ArrayList<MapIcon> icons) {
        this.icons = icons;
    }

    //================================================================================
    // Methods - Public
    //================================================================================
    public static void clean() {
        try {
            ApiMaps.unloadGeoFile("geo1", 0);
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "clean", "", e);
        }
    }

    //================================================================================
    // Methods - Private
    //================================================================================
    void setController(NavigatorController controller) {
        this.controller = controller;
        this.controller.onStateChange.subscribe(onNavigatorStateChange);
    }

    private void update(NavigatorControllerState state) {
        if (!canDoAnything()) return;
        if (icons == null || icons.isEmpty()) return;
        generatePoints();
    }

    private boolean canDoAnything() {
        if (controller.onStateChange.getValue() != NavigatorControllerState.READY_FOR_USE)
            return false;
        return true;
    }

    private void generatePoints() {
        String json = MapIconToJsonMapper.execute(icons);
        if (json != null && !json.isEmpty()) {
            try {
                ApiMaps.loadGeoFile("geo1", json, 0);
            } catch (Throwable e) {
                NavigatorController.catchError(TAG, "generatePoints", "unload geo file error", e);
            }
        }
    }


    void dispose() {
        clean();
    }


}
