package com.movisat.navigator;

import androidx.annotation.Nullable;

import com.movisat.utils.LatLong;
import com.sygic.sdk.api.model.Poi;

public class POI {

    static String categoryPrefix = "movisat";
    private LatLong latlong;
    private String name;
    private String category;
    private Poi poi;

    public POI(LatLong latlong, String name, String category) {
        this.latlong = latlong;
        this.name = name;
        // Le añadimos un texto fijo a la categoría, ya que si se usan categorías ya existentes
        // que vienen por defecto, generará un error.
        this.category = categoryPrefix + category;
    }

    public boolean isCategory(String category) {
        return category.equals(category) || category.replace(categoryPrefix, "").equals(category);
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (obj == null) return false;
        if (obj == this) return true;
        if (!(obj instanceof POI)) return false;
        POI o = (POI) obj;
        return o.name == this.name;
    }

    public LatLong getLatlong() {
        return latlong;
    }

    public String getName() {
        return name;
    }

    public String getCategory() {
        return category;
    }

    Poi getPoi() {
        return poi;
    }

    void setPoi(Poi poi) {
        this.poi = poi;
    }

}
