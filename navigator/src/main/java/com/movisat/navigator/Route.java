package com.movisat.navigator;

import java.util.ArrayList;

public class Route {
    private ArrayList<RoutePoint> points;
    private String name;
    public boolean isPlaying = false;

    public Route(ArrayList<RoutePoint> points, String name) {
        this.points = points;
        this.name = name;
    }

    public ArrayList<RoutePoint> getPoints() {
        return points;
    }

    public String getName() {
        return name;
    }
}
