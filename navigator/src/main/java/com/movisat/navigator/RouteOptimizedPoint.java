package com.movisat.navigator;

import com.movisat.utils.LatLong;

public class RouteOptimizedPoint {
    public LatLong latlong;
    public String colorHex = "#FF0000FF"; // Azul por defecto
    public String name;
    /**
     * defines instruction to be given at the point, -1 denotes the instruction to be inferred automatically,
     * -2 denotes no instruction is given, see the Instruction table bellow for details.
     */
    public int instruction = -1;
    /**
     * The exit index defined for roundabout instructions.
     */
    public int roundaboutIndex = 1;
    /**
     * The text for the spoken instruction at the point.
     */
    public String messageTts;

    public RouteOptimizedPoint(LatLong latlong) {
        this.latlong = latlong;
    }

    public RouteOptimizedPoint(LatLong latlong, String name) {
        this.latlong = latlong;
        this.name = name;
    }

    public RouteOptimizedPoint(String colorHex, LatLong latlong) {
        this.latlong = latlong;
        this.colorHex = colorHex;
    }

    public RouteOptimizedPoint(LatLong latlong, String name, String messageTts) {
        this.latlong = latlong;
        this.name = name;
        this.messageTts = messageTts;
    }

    public RouteOptimizedPoint(Lat<PERSON><PERSON> latlong, String colorHex, String name, int instruction, int roundaboutIndex, String messageTts) {
        this.latlong = latlong;
        this.colorHex = colorHex;
        this.name = name;
        this.instruction = instruction;
        this.roundaboutIndex = roundaboutIndex;
        this.messageTts = messageTts;
    }
}
