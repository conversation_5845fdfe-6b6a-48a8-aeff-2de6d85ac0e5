package com.movisat.navigator;

import java.util.ArrayList;

public class RouteOptimized {

    boolean isFirstPointReached = false;

    /**
     * Indica si al cargar la ruta, primero se cargará la navegación hasta el primer punto antes de mostrarla.
     */
    private boolean isFirstPointNavigable = false;

    private ArrayList<RouteOptimizedPoint> points;
    private String name;

    /**
     * Función que guarda el json pasado por parámetro y retorna la ruta del fichero.
     */
    private OnSaveFile onSaveFile;
    private String version = "1.0.0.2";
    public boolean isPlaying = false;
    private boolean limitedVisiblePoints = false;
    private int pointIndex = 0;


    public RouteOptimized(ArrayList<RouteOptimizedPoint> points, String name, OnSaveFile onSaveFile, boolean isFirstPointNavigable) {
        this.points = points;
        this.name = name;
        this.onSaveFile = onSaveFile;
        this.isFirstPointNavigable = isFirstPointNavigable;
    }

    public ArrayList<RouteOptimizedPoint> getPoints() {
        return points;
    }

    public String getName() {
        return name;
    }

    public String getVersion() {
        return version;
    }

    public OnSaveFile getOnSaveFile() {
        return onSaveFile;
    }

    public int getPointIndex() {
        return pointIndex;
    }

    public void setPointIndex(int pointIndex) {
        if (pointIndex < this.pointIndex) return;
        this.pointIndex = pointIndex;
    }



    public boolean isLimitedVisiblePoints() {
        return limitedVisiblePoints && NavigatorEnvironment.limitedVisiblePoints > 2;
    }

    public void setLimitedVisiblePoints(boolean limitedVisiblePoints) {
        this.limitedVisiblePoints = limitedVisiblePoints;
    }

    public boolean isFirstPointNavigable() {
        return isFirstPointNavigable;
    }

}
