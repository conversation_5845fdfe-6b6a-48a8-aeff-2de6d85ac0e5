package com.movisat.navigator;

import android.app.Activity;
import android.content.pm.PackageManager;
import android.util.Log;

import com.movisat.utils.*;
import com.sygic.aura.ResourceManager;
import com.sygic.aura.utils.PermissionsUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NavigatorController {

    private static final String TAG = "NavigatorController";

    //================================================================================
    // Fields
    //================================================================================
    public static ICallBack<String> catchError;
    public final Stream<NavigatorControllerState> onStateChange = new Stream<>(NavigatorControllerState.NOT_STARTED);
    public final Stream<NavigatorEvent> onEvent = new Stream<>(null);
    protected final Stream<Void> onForceRebuildFragment = new Stream<>(null);
    public final Stream<Boolean> onLoading = new Stream<Boolean>(false);

    public final RouteManager routeManager = new RouteManager(this);
    private SygicNaviFragment fragment = null;
    private NavigatorControllerMode mode;
    private POIManager poiManager;
    private IReturn<POIManager> poiManagerBuilder;
    private MapManager mapManager;
    private IReturn<MapManager> mapManagerBuilder;

    // Máquina de estados.
    private static final HashMap<NavigatorControllerState, List<NavigatorControllerState>> stateMachine = new HashMap<NavigatorControllerState, List<NavigatorControllerState>>() {{
        put(NavigatorControllerState.NOT_STARTED, new ArrayList<>(
                Arrays.asList(
                        NavigatorControllerState.STARING,
                        NavigatorControllerState.STARTED,
                        NavigatorControllerState.CLOSED)));

        put(NavigatorControllerState.STARING, new ArrayList<>(
                Arrays.asList(
                        NavigatorControllerState.NOT_STARTED,
                        NavigatorControllerState.STARTED,
                        NavigatorControllerState.HIDDEN,
                        NavigatorControllerState.CLOSED)));

        put(NavigatorControllerState.STARTED, new ArrayList<>(
                Arrays.asList(
                        NavigatorControllerState.HIDDEN,
                        NavigatorControllerState.CLOSED,
                        NavigatorControllerState.READY_FOR_USE)));

        put(NavigatorControllerState.READY_FOR_USE, new ArrayList<>(
                Arrays.asList(
                        NavigatorControllerState.READY_FOR_USE,
                        NavigatorControllerState.HIDDEN,
                        NavigatorControllerState.CLOSED)));

        put(NavigatorControllerState.REBUILDING, new ArrayList<>(
                Arrays.asList(
                        NavigatorControllerState.READY_FOR_USE,
                        NavigatorControllerState.HIDDEN,
                        NavigatorControllerState.CLOSED)));

        put(NavigatorControllerState.HIDDEN, new ArrayList<>(
                Arrays.asList(
                        NavigatorControllerState.REBUILDING,
                        NavigatorControllerState.READY_FOR_USE,
                        NavigatorControllerState.CLOSED)));

        put(NavigatorControllerState.CLOSED, null);
    }};


    //================================================================================
    // Constructors
    //================================================================================
    private NavigatorController(NavigatorControllerMode mode) {
        this.mode = mode;
        onStateChange.subscribe(v -> {
            boolean isLoading = false;
            switch (v) {
                case STARING:
                case STARTED:
                case REBUILDING:
                    isLoading = true;
                    break;
            }
            notifyLoading(isLoading);
        });
    }

    public static NavigatorController tab() {
        return new NavigatorController(NavigatorControllerMode.TAB);
    }

    public static NavigatorController page() {
        return new NavigatorController(NavigatorControllerMode.PAGE);
    }

    //================================================================================
    // Methods - Public
    //================================================================================
    public void notifyLoading(boolean isLoading) {
        if (onLoading.getValue() != isLoading) {
            onLoading.notify(isLoading);
            Log.d(TAG, "is loading? " + isLoading);
        }
    }

    public void dispose() {
        // Notificamos que no está cargando para desactivar cualquier indicador que esté mostrando la UI.
        notifyLoading(false);

        if (fragment != null) {
            fragment = null;
        }

        // Notificamos evento de cierre antes de cancelar el stream.
        changeStatePage(NavigatorControllerState.CLOSED);

        // Managers
        routeManager.dispose();
        if (poiManager != null) poiManager.dispose();
        if (mapManager != null) mapManager.dispose();

        // Streams
        onStateChange.dispose();
        onEvent.dispose();
        onForceRebuildFragment.dispose();
    }

    public void forceRebuildFragment() {
        onRebuilding();
        onForceRebuildFragment.notify(null);
    }

    public void changeStateTab(NavigatorControllerState newState) {
        if (mode != NavigatorControllerMode.TAB) return;
        changeState(newState);
    }

    public void setPoiManagerBuilder(IReturn<POIManager> builder) {
        if (this.poiManagerBuilder == null) this.poiManagerBuilder = builder;
    }

    public boolean hasPoiManager() {
        return poiManagerBuilder != null;
    }

    public POIManager getPoiManager() {
        if (poiManagerBuilder == null) return null;
        if (poiManager == null) {
            poiManager = poiManagerBuilder.execute();
            poiManager.setController(this);
        }
        return poiManager;
    }

    public void setMapManagerBuilder(IReturn<MapManager> builder) {
        if (this.mapManagerBuilder == null) this.mapManagerBuilder = builder;
    }

    public boolean hasMapManager() {
        return mapManagerBuilder != null;
    }

    public MapManager getMapManager() {
        if (mapManagerBuilder == null) return null;
        if (mapManager == null) {
            mapManager = mapManagerBuilder.execute();
            mapManager.setController(this);
        }
        return mapManager;
    }

    public void setMapManager(MapManager mapManager) {
        this.mapManager = mapManager;
        this.mapManager.setController(this);
    }

    public static void catchError(String tag, String method, String message, Throwable throwable) {
        throwable.printStackTrace();
        String m = "[" + tag + "] [" + method + "] " + message + " => " + throwable.getMessage();
        Log.e(TAG, m);
        if (catchError != null)
            catchError.execute(m);
    }

    //================================================================================
    // Methods - Private
    //================================================================================
    protected void changeStatePage(NavigatorControllerState newState) {
        if (mode != NavigatorControllerMode.PAGE) return;
        changeState(newState);
    }

    private void changeState(NavigatorControllerState newState) {
        boolean canChange = canChangeToState(newState);
        Log.d(TAG, "Cambiando estado de " + getState() + " a " + newState + ". ¿Válido? " + canChange);
        if (!canChange) return;
        onStateChange.notify(newState);
    }

    private boolean canChangeToState(NavigatorControllerState newState) {
        if (newState == null) return false;
        if (onStateChange.getValue() == newState) return false;
        if (!stateMachine.containsKey(getState())) return false;
        if (Utilss.isNullOrEmpty(stateMachine.get(getState()))) return false;
        if (!stateMachine.get(getState()).contains(newState)) return false;
        return true;
    }

    private boolean isNecessaryRefreshFragmentInstance() {
        if (onStateChange.getValue() == NavigatorControllerState.NOT_STARTED) return true;
        return false;
    }


    /**
     * Indica si el fragment puede reconstruirse según el estado actual.
     * <p>
     * En modo TAB solo se podrá reconstruir cuando la activity que gestiona los tab lo indique.
     * Esto podrá hacerlo llamando a onRebuilding.
     * <p>
     * En modo PAGE se podrá reconstruir cuando esté en un estado de inactividad.
     *
     * @return si puede recontruir el framework.
     */
    protected boolean canRebuildFragment() {
        if (mode == NavigatorControllerMode.TAB) {
            if (getState() == NavigatorControllerState.STARING) return true;
            if (getState() == NavigatorControllerState.REBUILDING) return true;
        } else {
            if (getState() == NavigatorControllerState.NOT_STARTED) return true;
            if (getState() == NavigatorControllerState.HIDDEN) return true;
        }
        return false;
    }


    protected boolean hasFragment() {
        return fragment != null;
    }

    protected SygicNaviFragment getSygicFragment() {
        if (fragment == null || isNecessaryRefreshFragmentInstance())
            fragment = new SygicNaviFragment(this);
        return fragment;
    }

    /**
     * Cambia el estado actual a uno de los estados que indica que el framework está en reconstruicción.
     */
    protected void onRebuilding() {
        if (onStateChange.getValue() == NavigatorControllerState.NOT_STARTED) {
            changeStatePage(NavigatorControllerState.STARING);
            changeStateTab(NavigatorControllerState.STARING);
        } else if (onStateChange.getValue() == NavigatorControllerState.HIDDEN) {
            changeStatePage(NavigatorControllerState.REBUILDING);
            changeStateTab(NavigatorControllerState.REBUILDING);
        }
    }

    //================================================================================
    // Methods - Static
    //================================================================================
    public static boolean checkResources(Activity activity) {
        try {
            ResourceManager resourceManager = new ResourceManager(activity, null);
            return !resourceManager.shouldUpdateResources();
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "checkResources", "Error al comprobar los recursos", e);
        }
        return false;
    }

    //================================================================================
    // Getters & Setters
    //================================================================================
    public NavigatorControllerState getState() {
        return onStateChange.getValue();
    }


    static final Map<Integer, NavigatorEvent> EVENTS = new HashMap<Integer, NavigatorEvent>() {{
        put(1010, NavigatorEvent.APP_STARTED);
        put(1101, NavigatorEvent.ROUTE_USERCANCEL);
        put(1102, NavigatorEvent.WAYPOINT_VISITED);
        put(1103, NavigatorEvent.ROUTE_FINISH);
        put(1105, NavigatorEvent.ROUTE_COMPUTED);
        put(1106, NavigatorEvent.OFF_ROUTE);
        put(1110, NavigatorEvent.OFF_ROUTE_EXT);
        put(1107, NavigatorEvent.OPTIMIZATION_FINISHED);
        put(1108, NavigatorEvent.ITINERARY_CHANGED);
        put(1109, NavigatorEvent.ITINERARY_WARNING);
        put(1100, NavigatorEvent.APP_EXIT);
        put(1150, NavigatorEvent.MAIN_MENU);
        put(1151, NavigatorEvent.CONTEXT_MENU);
        put(1152, NavigatorEvent.EXIT_MENU);
        put(1153, NavigatorEvent.CUSTOM_MENU);
        put(1160, NavigatorEvent.CHANGE_LANGUAGE);
        put(1161, NavigatorEvent.CHANGE_ORIENTATION);
        put(1170, NavigatorEvent.RADAR_WARNING);
        put(1171, NavigatorEvent.POI_WARNING);
        put(1172, NavigatorEvent.GEOFENCE);
        put(1173, NavigatorEvent.RESTRICTED_ROAD);
        put(1180, NavigatorEvent.BORDER_CROSSING);
        put(1181, NavigatorEvent.SPEED_EXCEEDING);
        put(1182, NavigatorEvent.SPEED_LIMIT_CHANGED);
        put(1190, NavigatorEvent.BITMAP_CLICK);
        put(1191, NavigatorEvent.POI_CLICK);
        put(1192, NavigatorEvent.WAYPOINT_CLICKED);
        put(1193, NavigatorEvent.SHARE_POSITION);
        put(1194, NavigatorEvent.BROWSE_MAP_EXIT);
        put(1195, NavigatorEvent.WAYPOINT_MARKED_AS_VISITED);
        put(2000, NavigatorEvent.CALLBACK);
    }};
}
