package com.movisat.navigator;

import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

class RouteToJsonMapper {

    private static final String TAG = "RouteToJsonMapper";

    public static String CreateJsonItinerary(Route route) {
        try {
            ArrayList<RoutePoint> lst = route.getPoints();
            JSONArray routeparts = new JSONArray();
            JSONObject routepart;
            JSONObject wpFrom;
            JSONObject wpTo;
            for (int i = 1; i < lst.size(); i++) {
                wpFrom = null;
                String pointType = "via";
                // Guardamos la referencia del primer punto para que Sygic pueda definir el inicio de cada uno de los puntos.
                if (i == 1) {
                    pointType = "start";
                    wpFrom = createWaypoint(lst.get(0).getLatlong().getLat(), lst.get(0).getLatlong().getLng(), lst.get(0).getStartDate(), lst.get(0).getEndDate(), 0, pointType);
                }
                // Si es el último punto, indicamos que es el punto de finalización.
                else if (i == (lst.size() - 1)) {
                    pointType = "finish";
                }
                wpTo = createWaypoint(lst.get(i).getLatlong().getLat(), lst.get(i).getLatlong().getLng(), lst.get(i).getStartDate(), lst.get(i).getEndDate(), i, pointType);
                routepart = createRoutepart(wpFrom, wpTo);
                if (routepart == null) return null;
                routeparts.put(routepart);
            }
            JSONObject json = new JSONObject();
            json.put("name", "Bratislava-pickup");
            json.put("version", "2.2");
            json.put("routeParts", routeparts);
            return json.toString();
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "CreateJsonItinerary", "", e);
        }
        return null;
    }

    private static JSONObject createWaypoint(double lat, double lon, String time1, String time2, int waypointId, String wtype) {
        int ilat = (int) (lat * 100000);
        int ilon = (int) (lon * 100000);

        JSONObject obj = new JSONObject();
        try {
            obj.put("lat", Integer.valueOf(ilat));
            obj.put("lon", Integer.valueOf(ilon));
            obj.put("waypointId", Integer.valueOf(waypointId));
            obj.put("type", wtype);
            if (time1 != null && time2 != null) {
                JSONObject tw = new JSONObject();
                tw.put("startTime", time1);
                tw.put("endTime", time2);
                tw.put("stopDelay", Integer.valueOf(180));
                obj.put("timeWindow", tw);
            }
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "createWaypoint", "", e);
            obj = null;
        }
        return obj;
    }

    private static JSONObject createRoutepart(JSONObject wpFrom, JSONObject wpTo) {
        JSONObject obj = new JSONObject();
        try {
            if (wpFrom != null) obj.put("waypointFrom", wpFrom);
            obj.put("waypointTo", wpTo);
        } catch (Throwable e) {
            NavigatorController.catchError(TAG, "createRoutepart", "", e);
            obj = null;
        }
        return obj;
    }
}
