plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 32

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 32
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    namespace 'com.movisat.utils_android'
}

dependencies {
    implementation project(path: ':utils')
    implementation 'androidx.core:core:1.8.0'
}