package com.movisat.utils_android;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;

import androidx.core.content.FileProvider;

import com.movisat.utils.ICallBack;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Plugin de obtención de fotos desde la cámara, enfocado en Android 12 o superiores.
 *
 * Es necesario añadir al Manifest lo siguiente:
 *
 * <application>
 *    ...
 *    <provider
 *         android:name="androidx.core.content.FileProvider"
 *         android:authorities="com.movisat.ecosat"
 *         android:exported="false"
 *         android:grantUriPermissions="true">
 *         <meta-data
 *             android:name="android.support.FILE_PROVIDER_PATHS"
 *             android:resource="@xml/file_paths"></meta-data>
 *     </provider>
 *     ...
 * </application>
 *
 * Y crear un fichero xml en la carpeta res/xml/file_paths.xml con el siguiente contenido:
 *
 * <?xml version="1.0" encoding="utf-8"?>
 * <paths xmlns:android="http://schemas.android.com/apk/res/android">
 *     <external-files-path name="my_images" path="Pictures" />
 * </paths>
 */
public class PhotoService {

    public ICallBack<String> registerLog;

    private static final String TAG = "PhotoService";
    public static final int REQUEST_IMAGE_CAPTURE = 1;
    private static String sync = "";
    private static PhotoService instance;
    /// La última imagen se queda guardada hasta que se realize otra.
    private File photo;

    public static PhotoService get() {
        synchronized (sync) {
            if (instance == null)
                instance = new PhotoService();
        }
        return instance;
    }

    public void takePhoto(Activity activity) {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        // Ensure that there's a camera activity to handle the intent
        if (takePictureIntent.resolveActivity(activity.getPackageManager()) != null) {
            // Create the File where the photo should go
            photo = null;
            try {
                photo = createImageFile(activity);
            } catch (IOException ex) {
                ex.printStackTrace();
            }
            // Continue only if the File was successfully created
            if (photo != null) {
                Uri photoURI = FileProvider.getUriForFile(
                        activity,
                        "com.movisat.ecosat",
                        photo
                );
                takePictureIntent.addFlags(
                        Intent.FLAG_GRANT_WRITE_URI_PERMISSION |
                                Intent.FLAG_GRANT_READ_URI_PERMISSION
                );
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                activity.startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
            }
        }
    }

    private File createImageFile(Activity activity) throws IOException {

        File photoFile = null;

        try {
            String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String imageFileName = "JPEG_" + timeStamp + "_";
            File storageDir = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
            photoFile = File.createTempFile(
                    imageFileName,  /* prefix */
                    ".jpg",         /* suffix */
                    storageDir      /* directory */
            );

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return photoFile;
    }

    public Bitmap setPic() {
        if (photo == null) return null;

        int targetW = 1024;
        int targetH = 768;

        // Get the dimensions of the bitmap BitmapFactory.Options bmOptions =
        BitmapFactory.Options bmOptions = new BitmapFactory.Options();

        bmOptions.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(photo.getAbsolutePath(), bmOptions);
        int photoW = bmOptions.outWidth;
        int photoH = bmOptions.outHeight;

        // Determine how much to scale down the image int scaleFactor =
        Math.min(photoW / targetW, photoH / targetH);

        int scaleFactor = Math.min(photoW / targetW, photoH / targetH);

        // Decode the image file into a Bitmap sized to fill the View
        bmOptions.inJustDecodeBounds = false;
        bmOptions.inSampleSize = scaleFactor;
        bmOptions.inPurgeable = true;

        Bitmap bitmap = BitmapFactory.decodeFile(photo.getAbsolutePath(), bmOptions);
        //Bitmap imageResize = Utils.ResizeImage(bitmap, 800, 600);
        // jcaballero cambio la llamada al metodo ResizeImage por este, que decodifica la imagen
        // dandole las opciones que calcula segun el tamanio, tal cual indica en la documentacion de android
        Bitmap imageResize = decodeBitmapFromFile(photo.getAbsolutePath(), targetW, targetH);
        //

        // mImageView.setImageBitmap(bitmap);
        return imageResize;
    }

    private Bitmap decodeBitmapFromFile(String path, int reqWidth, int reqHeight) {

        // First decode with inJustDecodeBounds=true to check dimensions
        final BitmapFactory.Options options = new BitmapFactory.Options();
        //options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(path, options);

        // Calculate inSampleSize, Raw height and width of image
        final int height = options.outHeight;
        final int width = options.outWidth;
        options.inPreferredConfig = Bitmap.Config.RGB_565;
        int inSampleSize = 1;

        if (height > reqHeight) {
            inSampleSize = Math.round((float) height / (float) reqHeight);
        }
        int expectedWidth = width / inSampleSize;

        if (expectedWidth > reqWidth) {
            // if(Math.round((float)width / (float)reqWidth) > inSampleSize) //
            // If bigger SampSize..
            inSampleSize = Math.round((float) width / (float) reqWidth);
        }

        options.inSampleSize = inSampleSize;

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false;

        return BitmapFactory.decodeFile(path, options);
    }

}
