plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 34
    buildToolsVersion "33.0.0"
    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    namespace 'com.movisat.app_update_appcircle'
}

dependencies {
    implementation project(':utils')
    implementation project(':log')
    implementation project(':api')
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'androidx.core:core:1.12.0'

    // Optional: Add OkHttp for enhanced HTTP capabilities if needed
    // implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}