package com.movisat.app_update_appcircle;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Environment;
import com.movisat.log.Logg;
import java.io.File;

public class Downloader {
    private Activity activity;
    private String destination;

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    public void execute(String url, String fileNameWithExtension, Activity activity) {
        this.activity = activity;

        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).mkdirs();
        destination = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "/" + fileNameWithExtension;

        activity.registerReceiver(onComplete, new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));

        DownloadManager manager = (DownloadManager) activity.getSystemService(Context.DOWNLOAD_SERVICE);
        DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileNameWithExtension);
        manager.enqueue(request);
    }

    private void installApp(String path, Context context) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setDataAndType(Uri.fromFile(new File(path)), "application/vnd.android.package-archive");
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_GRANT_READ_URI_PERMISSION |
                Intent.FLAG_ACTIVITY_NO_HISTORY | Intent.FLAG_ACTIVITY_NEW_DOCUMENT |
                Intent.FLAG_ACTIVITY_MULTIPLE_TASK);

        try {
            context.startActivity(intent);
            Logg.info("Started APK installation: " + path);
        } catch (Exception e) {
            Logg.error("Failed to start APK installation: " + e.getMessage());
        }
    }

    BroadcastReceiver onComplete = new BroadcastReceiver() {
        public void onReceive(Context ctxt, Intent ii) {
            try {
                activity.unregisterReceiver(onComplete);
                File installPath = new File(destination);
                if (installPath.exists()) {
                    Logg.info("Download completed, starting installation");
                    installApp(destination, activity);
                } else {
                    Logg.error("Downloaded file not found: " + destination);
                }
            } catch (Exception e) {
                Logg.error("Download completion error: " + e.getMessage());
            }
        }
    };
}