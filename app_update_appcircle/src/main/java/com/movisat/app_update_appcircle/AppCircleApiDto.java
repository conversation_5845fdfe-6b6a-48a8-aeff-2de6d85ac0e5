package com.movisat.app_update_appcircle;

import java.util.HashMap;

public class AppCircleApiDto {
    public String appName;
    public String appDisplayName;
    public String version;
    public String shortVersion;
    public String downloadUrl;
    public String profileId;
    public String appVersionId;
    public String releaseNotes;
    public String uploadDate;
    public long fileSize;

    public static AppCircleApiDto fromJson(HashMap<String, Object> json) {
        AppCircleApiDto dto = new AppCircleApiDto();

        dto.appName = getString(json, "name");
        dto.appDisplayName = dto.appName;
        dto.version = getVersion(json);
        dto.shortVersion = EcoSATVersionParser.formatVersionForDisplay(dto.version);
        dto.downloadUrl = getString(json, "downloadUrl");
        dto.profileId = getString(json, "profileId");
        dto.appVersionId = getString(json, "id");
        dto.releaseNotes = getString(json, "releaseNotes");
        dto.uploadDate = getString(json, "uploadDate");

        Object fileSizeObj = json.get("fileSize");
        if (fileSizeObj instanceof Number) {
            dto.fileSize = ((Number) fileSizeObj).longValue();
        }

        return dto;
    }

    private static String getVersion(HashMap<String, Object> json) {
        for (String key : new String[]{"versionCode", "version", "buildNumber", "versionNumber", "versionName"}) {
            String value = getString(json, key);
            if (value != null) return value;
        }
        return null;
    }

    private static String getString(HashMap<String, Object> json, String key) {
        Object value = json.get(key);
        return value != null ? value.toString() : null;
    }

    public boolean isNewerThan(String currentVersion) {
        return version != null && currentVersion != null &&
                EcoSATVersionParser.isNewerVersion(version, currentVersion);
    }

    public String getDisplayVersion() {
        if (shortVersion != null && !shortVersion.isEmpty()) return shortVersion;
        return version != null ? EcoSATVersionParser.formatVersionForDisplay(version) : "Unknown";
    }
}