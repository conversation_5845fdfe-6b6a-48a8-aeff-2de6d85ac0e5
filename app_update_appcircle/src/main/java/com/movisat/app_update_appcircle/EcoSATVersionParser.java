package com.movisat.app_update_appcircle;

import com.movisat.log.Logg;

public class EcoSATVersionParser {
    private static final String TAG = "EcoSATVersionParser";

    public static class ParsedVersion {
        public final int year, month, day, release;
        public final String originalVersion;

        public ParsedVersion(int year, int month, int day, int release, String originalVersion) {
            this.year = year;
            this.month = month;
            this.day = day;
            this.release = release;
            this.originalVersion = originalVersion;
        }

        public String toVersionCode() {
            return String.format("%02d%02d%02d%02d", year, month, day, release);
        }

        public String toVersionName() {
            return String.format("%02d.%02d.%02d.%02d", year, month, day, release);
        }
    }

    public static ParsedVersion parseVersion(String version) {
        if (version == null || version.trim().isEmpty()) return null;

        version = version.trim();

        try {
            if (version.contains(".")) {
                return parseDottedVersion(version);
            }

            if (version.length() == 8 && version.matches("\\d{8}")) {
                return parseNumericVersion(version);
            }

            String numericPart = version.replaceAll("[^\\d]", "");
            if (numericPart.length() >= 8) {
                return parseNumericVersion(numericPart.substring(0, 8));
            }
        } catch (Exception e) {
            Logg.error("[" + TAG + "] Parse error: " + e.getMessage());
        }

        return null;
    }

    private static ParsedVersion parseDottedVersion(String version) {
        String[] parts = version.split("\\.");
        if (parts.length != 4) return null;

        int year = Integer.parseInt(parts[0]);
        int month = Integer.parseInt(parts[1]);
        int day = Integer.parseInt(parts[2]);
        int release = Integer.parseInt(parts[3]);

        return validateAndCreate(year, month, day, release, version);
    }

    private static ParsedVersion parseNumericVersion(String version) {
        if (version.length() != 8) return null;

        int year = Integer.parseInt(version.substring(0, 2));
        int month = Integer.parseInt(version.substring(2, 4));
        int day = Integer.parseInt(version.substring(4, 6));
        int release = Integer.parseInt(version.substring(6, 8));

        return validateAndCreate(year, month, day, release, version);
    }

    private static ParsedVersion validateAndCreate(int year, int month, int day, int release, String original) {
        if (year < 20 || year > 99 || month < 1 || month > 12 ||
                day < 1 || day > 31 || release < 1 || release > 99) {
            return null;
        }
        return new ParsedVersion(year, month, day, release, original);
    }

    public static int compareVersions(String version1, String version2) {
        ParsedVersion v1 = parseVersion(version1);
        ParsedVersion v2 = parseVersion(version2);

        if (v1 == null && v2 == null) return 0;
        if (v1 == null) return -1;
        if (v2 == null) return 1;

        return compareVersions(v1, v2);
    }

    public static int compareVersions(ParsedVersion v1, ParsedVersion v2) {
        int cmp = Integer.compare(v1.year, v2.year);
        if (cmp != 0) return cmp;

        cmp = Integer.compare(v1.month, v2.month);
        if (cmp != 0) return cmp;

        cmp = Integer.compare(v1.day, v2.day);
        if (cmp != 0) return cmp;

        return Integer.compare(v1.release, v2.release);
    }

    public static boolean isNewerVersion(String version1, String version2) {
        return compareVersions(version1, version2) > 0;
    }

    public static String formatVersionForDisplay(String versionCode) {
        ParsedVersion parsed = parseVersion(versionCode);
        return parsed != null ? parsed.toVersionName() : versionCode;
    }
}