package com.movisat.app_update_appcircle;

import android.net.Uri;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.movisat.api.Api;
import com.movisat.log.Logg;
import com.movisat.utils.ICallBack;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppCircleApi {
    private static final String TAG = "AppCircleApi";
    private static final String AUTH_URL = "https://auth.appcircle.io/auth/v1/token";
    private static final String API_BASE = "https://api.appcircle.io/distribution/v2/profiles/";

    public void execute(String profileId, String personalApiToken, ICallBack<AppCircleApiDto> callback) {
        getAccessToken(personalApiToken, accessToken -> {
            if (accessToken != null) {
                getLatestVersion(profileId, accessToken, callback);
            } else {
                callback.execute(null);
            }
        });
    }

    private void getAccessToken(String personalApiToken, ICallBack<String> callback) {
        HashMap<String,String> headers = new HashMap<>();
        headers.put("accept", "application/json");
        headers.put("content-type", "application/x-www-form-urlencoded");

        new Api().postString(
                AUTH_URL,
                headers,
                "pat=" + Uri.encode(personalApiToken),
                response -> {
                    if (!response.isError && response.body != null) {
                        try {
                            Map<String, Object> authResponse = new Gson().fromJson(
                                    response.body, new TypeToken<HashMap<String, Object>>() {}.getType()
                            );
                            callback.execute((String) authResponse.get("access_token"));
                        } catch (Exception e) {
                            Logg.error("[" + TAG + "] Auth error: " + e.getMessage());
                            callback.execute(null);
                        }
                    } else {
                        callback.execute(null);
                    }
                }
        );
    }

    private void getLatestVersion(String profileId, String accessToken, ICallBack<AppCircleApiDto> callback) {
        HashMap<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json");
        headers.put("authorization", "Bearer " + accessToken);

        new Api().get(
                API_BASE + profileId,
                headers,
                response -> {
                    if (!response.isError && response.body != null) {
                        try {
                            Map<String, Object> profileData = new Gson().fromJson(
                                    response.body, new TypeToken<Map<String, Object>>() {}.getType()
                            );

                            List<Map<String, Object>> appVersions = (List<Map<String, Object>>) profileData.get("appVersions");
                            if (appVersions != null && !appVersions.isEmpty()) {
                                Map<String, Object> latest = findLatestVersion(appVersions);
                                if (latest != null) {
                                    getDownloadLink(profileId, (String) latest.get("id"), accessToken, latest, callback);
                                    return;
                                }
                            }
                        } catch (Exception e) {
                            Logg.error("[" + TAG + "] Parse error: " + e.getMessage());
                        }
                    }
                    callback.execute(null);
                }
        );
    }

    private void getDownloadLink(String profileId, String appVersionId, String accessToken,
                                 Map<String, Object> versionData, ICallBack<AppCircleApiDto> callback) {

        HashMap<String, String> headers = new HashMap<>();
        headers.put("accept", "application/json");
        headers.put("authorization", "Bearer " + accessToken);

        new Api().get(
                API_BASE + profileId + "/app-versions/" + appVersionId + "/download",
                headers,
                response -> {
                    if (!response.isError && response.body != null) {
                        try {
                            AppCircleApiDto dto = AppCircleApiDto.fromJson(new HashMap<>(versionData));
                            dto.downloadUrl = response.body.trim();
                            callback.execute(dto);
                        } catch (Exception e) {
                            Logg.error("[" + TAG + "] Download link error: " + e.getMessage());
                            callback.execute(null);
                        }
                    } else {
                        callback.execute(null);
                    }
                }
        );
    }

    private Map<String, Object> findLatestVersion(List<Map<String, Object>> versions) {
        Map<String, Object> latest = null;
        String latestCode = null;

        for (Map<String, Object> version : versions) {
            String code = getVersionCode(version);
            if (code != null && (latestCode == null || EcoSATVersionParser.isNewerVersion(code, latestCode))) {
                latest = version;
                latestCode = code;
            }
        }
        return latest;
    }

    private String getVersionCode(Map<String, Object> version) {
        for (String key : new String[]{"versionCode", "version", "buildNumber", "versionNumber"}) {
            Object value = version.get(key);
            if (value != null) return value.toString();
        }
        return null;
    }
}