package com.movisat.log_provider_sentry;

import android.app.Activity;

import com.movisat.log.ILoggProvider;
import com.movisat.log.LoggLine;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.sentry.Sentry;

public class LoggProviderSentry implements ILoggProvider {

    private Activity activity;
    private List<String> messages = new ArrayList<>();
    private Map<String, Object> deviceContext;

    public LoggProviderSentry(Activity activity, Map<String, Object> deviceContext) {
        this.activity = activity;
        this.deviceContext = deviceContext;
        start();
    }

    @Override
    public boolean isStarted() {
        return true;
    }

    @Override
    public void start() {
        Sentry.configureScope(scope -> {
            for (Map.Entry<String, Object> entry : deviceContext.entrySet()) {
                scope.setTag(entry.getKey(), entry.getValue().toString());
            }
        });
    }

    @Override
    public void write(LoggLine line) {
        switch (line.logType) {
            case DATABASE:
            case SYNCHRONIZATION:
            case DEBUG:
            case INFO:
            case WARNING:
                return;
            case CATASTROPHE:
            case ERROR:
                break;
        }

        // Añadimos un limite al envñio de mensajes por inicio de app para evitar alcanzar el limite de Sentry.
        String type = line.logType.toString();
        String message = "[" + type + "] " + line.message;
        if(messages.contains(message)) return;
        messages.add(message);

        activity.runOnUiThread(() -> {
            Sentry.captureMessage(message);
        });
    }
}
