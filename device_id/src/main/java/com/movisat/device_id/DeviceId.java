package com.movisat.device_id;

import android.annotation.SuppressLint;
import android.content.Context;
import android.provider.Settings;
import android.telephony.TelephonyManager;

public class DeviceId {

    //================================================================================
    // Fields
    //================================================================================
    private String deviceId;

    //================================================================================
    // Methos - Public
    //================================================================================

    public String get() {
        return deviceId;
    }

    /**
     * La aplicación debe solicitar el permiso antes de realizar esta llamada.
     * https://stackoverflow.com/questions/42688135/read-phone-state-runtime-error-in-android-m
     *
     * En el Manifest deben ir los siguientes permisos:
     *      <uses-permission android:name="android.permission.READ_PHONE_STATE" />
     *      <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" tools:ignore="ProtectedPermissions" />
     *
     * @param context
     * @return IMEI o AndroidID
     */
    public String start(Context context) {
        if (deviceId != null && !deviceId.isEmpty()) return deviceId;

        try {
            if (android.os.Build.VERSION.SDK_INT >= 29) {
                String androidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
                while (androidId.length() < 17)
                    androidId += "0";


                deviceId = androidId;
            } else {
                TelephonyManager manager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
                @SuppressLint("MissingPermission")
                String imei = manager.getDeviceId();
                if (imei != null) {
                    while (imei.length() < 17)
                        imei += "0";
                } else {
                    imei = "";
                }
                deviceId = imei;
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return deviceId;
    }

    //================================================================================
    // Singleton
    //================================================================================
    private static volatile DeviceId instance = null;

    public static DeviceId getInstance() {
        if (instance == null) {
            synchronized (DeviceId.class) {
                if (instance == null) {
                    instance = new DeviceId();
                }
            }
        }
        return instance;
    }


}
