# XXXXXXXX
- AP-630: Añado visualización de fotos con un click y ampliación por gestos. Para seleccionar la foto hay que mantenerla pulsada.
- AP-630: A<PERSON><PERSON> histórico de imágenes que permite visualizar las imágenes que tiene el servidor de incidencias y elementos.

# 25072901
- 7349/AP-607: Arreglo las operaciones de recogida y nivel de llenado que provocaba que no apareciese el check verde en el elemento del mapa.
- 7349/AP-607: Añado la sincronización de operaciones a la sincronización general, antes solo se hacía cuando cambiabas las horas de recogida y lavado de la configuración manualmente.

# 25072501
- 7352/AP-609: Arreglada la superposición de barras de navegación en Android 15.
- 7352/AP-609: Actualización CompileSdk 35, Android Gradle Plugin 8.11.1, Gradle 8.13, necesario para la superposición de las barras en Android 15 

# 25070401
- 7262/AP-478: Arreglado un crash en Asignar Incidencias cuando una incidencia se queda sin estado por un fallo en la sincronización.

# 25070301
- 7262/AP-478: Las notificaciones se muestran en todas las versiones de Android.

# 25070201
- 7228/AP-557: Añado más posibilidades de que se abra la cámara para tomar fotos de incidencias y elementos.
- 7262/AP-478: El filtro por tipo de incidencias siempre está activo por defecto.
- 7262/AP-478: Bajo el tamaño del texto del filtro por tipo a la mitad.

# 25062501
- Añado Muncipio debajo de Dirección en la información de la incidencia.
- Cambio el id por el nombre del modelo en la info de incidencia.
- Desde Asignar incidencias solamente se visualiza la info de incidencia y las fotos almacenadas.
- El texto observaciones lo muevo debajo del motivo en la info de incidencia.

# 25062001
- Añado toda la información de la incidencia en la pantalla de cambio de estado / detalle de incidencia.
- Desde asignar incidencia se accede directamente al detalle de la incidencia con toda la información.
- Mejoro la carga de incidencias.
- El propietario en el detalle y el filtro solamente aparece si Asignar incidencia está activo.
- Localizar usuario en el mapa con una pulsación y seguimiento GPS con pulsación larga.
- Pantalla filtrar por tipo de incidencia, los cambios se reflejan en el mapa y en los listados de incidencias.
- Filtro avanzado en la listas de incidencias.
- Susituido appcenter con api de appcircle para la actualización de la app sin Google Play.

# 25031001
- 7036, 6998. no se enviaban las imágenes del elemento.

Se estaban borrando los elementos sin id externo durante la sincronización. Estos se volvían a crear con un id interno diferente. Provocando que los paquetes de creación de imagen no encontrasen su elemento y se quedasen atascados.

- 7054. Revertidos cambios de la versión 25021901.

# 25021901
- 6998: Añado logs para un mejor seguimiento al añadir y modificar contenedores.
- Soluciono un problema al abrir el menu lateral.

# 24100101
- 6717: Desaparecen los contenedores de la visualización en mapa cuando se usa el campo búsqueda. Se estaban borrando los marcadores al centrar el mapa.
- 6759: Añado iconos de operaciones directamente al marcador del elemento
- 6759: No deja seleccionar elementos de la parte superior. Se estaba generando el info window de google de manera transparente recibiendo el onclick de la mitad superior de la pantalla.

# 24080601
- Compatibilidad con Android 14.
- 6574: Se impide que un operario haga pesajes de datos que no sean suyos.
- 6664: Fix en buscador de vehículos.
- 6717: Fix en iems mapa.
- 6722: Se fuerza sincro si no se puede obtener estado de incidencia.

# 24070801
- 6668: Se mejora la gestión de paquetes de incidencias.

# 24061201
- 6619: Se añade al listado de flota los turnos sin finalizar que tiene cada vehículo.

# 24060301
- Primera sincronización en segundo plano.
- Mejoras al abrir y cerrar la app.
- Se actualiza lista de elementos en memoria en cada acción.

# 24051301
- Se vuelven a añadir transacciones en elementos y tags, sin ellas la sincronización es demasiada lenta.
- Se usa la lista de elementos en memoria para renderizarlos en el mapa.
- Se modifican PRAGMAS de la base de datos para mejorar el rendimiento.
- Eliminación de código repetido para evitar repintados inncesarios.
- Se permite la ceación de elementos en cualquier ubicación al dispositivo con nombre MOVISAT
- Pequeñas correcciones en el filtrado de modelos y login.

# 24050201
- Se quitan transacciones en elementos y tags.

# 24042601
- Optimizaciones en mapa.

# 24042401
- Ajuste al validar licencia.

# 24040402
- 6401: Corrección al comprobar FechaBaja y se obtiene el nivel anterior si se borra el último.

# 24040401
- 6518: Problemas con el mapa al meter niveles de llenado. Se elimina método hashCode de elemento. 
- 6401: Borramos sensor de llenado por fracción si viene FechaBaja.

# 24022701
- 6402: Se guarda la fecha del servidor para flota/vehiculos sin gls y otros modelos.
- 6463: Me aseguro cerrar transacciones en la sincro de tags y se comprueban colas al obtener un valor del config.

# 24022201
- 6444: Se evitan duplicados en la búsqueda de elementos y se borra la lista solo antes de insertar la nueva para que siempre hay una.
- Campo matrícula como texto en la versión de Camacho.
- Se guarda la fecha más antigua entre la fecha de modificación más reciente de los elementos y la del servidor/dispositivo para solicitar las páginas.

# 24021601
- Se optimiza la sincro de elementos

# 24021401
- 6431: Se añade lógica retrocompatible para la sincro de elementos en nuevo endpoint.

# 24020901
- Corregido error al guardar la página actual.

# 24020101
- 6402: Elimino o inserto vehículo cuando sincroniza.
- 6418: Añado lógica en la sincro de elementos para los C71.
- Corrijo error al guardar configuraciones.
- Añado logs en para todas la llamadas

# 24012501
- 6400: Muestro mensaje si al meter lavado/llenado el elemento está en taller. Actualizo coordenadas de la operación o muestro mensaje si están a 0 al pulsar en Centrar.
- Bajo a 5 seg las notificaciones de incidencias. Sincronizo el resto de datos de incidencias en la sincro de cada min.
- Reinicializo variable completed a REQUEST_PENDING en todas las peticiones, por alguna razón, a veces estaba como REQUEST_ERROR lanzando una excepción innecesaria y confundiendo los logs.
- Añado propiedad bajo demanda cuando se modifica un elemento en Camacho.

# 24012301
- 6373: Mejoro sincro de operaciones al cambiar las horas y en la general.
- Mejoro cola de config db.
- Quito refresh de la barra superior q hacía al abrir/cerrar algún menú y al mover el mapa.

# 24012201
- Detecto bloqueo en db al sincronizar tags.

# 24011901
- Cargo elementos de búesqueda en otro hilo.

# 24011701
- Mejoras en la sincronización.
- Se añade cola en el registro de errores.
- Restricciones para trabajar con tags si no hay una primera sincro.
- Optimizaciones en el uso de la base de datos.

# 24011501
- Evito que se pare la barra de sync si no está activa.
- Borro matricula de elemento antiguo después de sincronizar tags para evitar error.
- Limpio lista de elementos de búsqueda para evitar duplicados.
- 6382: Reinicializo readers para el caso del UHF U9000.

# 24011101
- Optimización en sincronización de tags.
- Optimización en la barra de sincronización.

# 24010302
- Hotfix. Se corrige UPDATE en tags.

# 24010301
- 6364: Tags 134 repetidos
    - Registro lecturas 134 en base de datos.
    - Cuando elemVerMatricula es 2 registro tag al crear elemento.
    - Deshabilito botón de Aceptar al crear elemento.
    - Aumento lineas de errores
- 6325:
    - Borro incidencias cerradas hace más de un mes.
    - Corrección filtro elementos.

# 23121801
- 6347: 
    - Añado evento para actualizar listado de incidencias cuando hay una asignación.
- 6325:
    - Misma fecha recogida en elemento que el nivel de llenado.
    - Cambio query para evitar duplicados en la lista de asignar incidencias.
    - Coloco icono de incidencia encima del de elemento.
    - Refresco elementos en mapa cuando se filtran con ninguno.
    - Le quito matricula al sincronizar tags al elemento antiguo.
    - Inicializo readers solo una vez para evitar lags en los 134.
    - Busco último estado incidencia por interno y actualizo por interno o externo en función del interno.

# 23121302
- 6071: No sincroniza correctamente información de "PROCESADO"
    - Se añaden propiedades UltimoLavado y UltimoProcesado incorporadas en la recepción de los elmeentos.

# 23121301
- 6299. Creación de un elemento nuevo, y reubicó y modificó otro.
    - Se añaden comprobaciones.
- 6303. Creación de elementos fuera de la zona madrid.
    - Faltaba en una casuística en la que no había verificación de las coordenadas dentro de Madrid.
    - Se comprueban las coordenadas 0,0 en todos los proyectos.
- Se corrige bug en la creación de elementos sin cobertura.
- Nueva funcionalidad para asignar incidencias.

# 23113001
- 6301. Tras actualizar, y reparar APP, ya no sincroniza.
    - Se soluciona bug en el caso de que lleguen elementos duplicados.

# 23111303
- Se actualiza gmaps.

# 23111301
- 6185. Fallos con elementos en taller.
    - Bug al modificar elemento sin tag asociado y se lee un tag activo. 
    - No se puede crear incidencias sobre elementos en taller.
- 6265. Elementos duplicados.
    - Se modifica la tabla de elementos para hacer idExterno (Código de elemento) único.
    
# 23100401
- 6141. Fallo a la hora de crear un elemento mientras sincroniza
    - Se refrescaba token en el proceso de añadir elemento, esto no es necesario, ya se hace en el sincronizador.
    - Error de context al obtener IMEI.
    - Aumento timeout para cx 2G.
    - Error con la barra de progreso de sincro.
- 6185. Fallos con elementos en taller.
    - No pinto elementos con coordenadas 0.0.
    - Cuando se retira elemento a taller, a parte de cambiar el estado pongo coordenadas a 0.
    - Al buscar elemento y seleccionar un elemento, si está en taller muestro toast.
- Añado empresa y dispositivo al nombre de bbdd.

# 23091303
- Se actualizan permisos para poder hacer upgrade a Android 13.

# 23091301
- 6153. Se mejora toda la lógica relacionada con incidencias.
- Se corrige error al obtener el tag al añadir elemento.
- Cancelo toast al pulsar sobre otro item, en las listas de incidencias, operaciones y flota.
- Aumento timeout al enviar paquete para reducir el número de errores.
- Añado información descriptiva en logs.

# 23083001
- 6115. No sincroniza correctamente fechas de recogida.
    - Llevo las comprobaciones al establecer fechas de recogida y lavado al Elemento.
    - Actualizo fechas cuando se sincronizan operaciones al cambiar sus horas en configuración.
- 6129. Fallos con incidencias.
    - No dejo acceder al cambio de estado si no hay estado previo.
    - Muestro error si no se crea incidencia correctamente y revierto operación.
    - Uso fecha último estado en incidencia en lugar de fecha de modificación.
    - Mejoro actualización de estado para que solo se actualice la incidencia si hay un nuevo estado.
    - Evito segunda escritura cuando se envía el paquete de cambio de estado.
    
# 23070501
- Se añade mensaje de confirmación cuando al modificar un elemento se lee un tag de otro elemento que está en taller o activo.
- Se cargan los elementos en la barra de búsqueda tras sincro.
- El marcador del círculo rojo que se muestra al centrar mapa en un elemento ahora se quita al pulsar sobre él o a los 5 segundos.
- En Configuración se comprueba que sea un número lo que se introduce al cambiar las horas de las operaciones.
- Se controlan pulsaciones largas en los botones de lectura.
- Solucionado bug que ocultaba los iconos de búsqueda y menú derecho al sincronizar las operaciones realizadas.

# 23063001
- 5997. Lentitud con marcadores de frecuencia de procesado:
    - La frecuencia de procesado ahora se pinta en el mismo marcador del elemento.
    - Si la frecuencia de procesado es 0 no se muestra nada en el mapa.
- 6047. Fallos en la sincronización de incidencias:
    - Ahora se obtiene el último estado y la fecha de modificación de la propia incidencia.
    - Quito restricción para obtener todos los estados y no falte ninguno.
    - Se añade buscador, total de incidencias y se ordena por fecha más reciente.
- 6053. Fallos en lectores 134:
    - Al modificar un elemento aunque ya tenga un tag asociado se debe poder modificar.
    - En el caso de que el tag leído este taller se le quita el tag al elemento y se le asigna al que se está modificando.
- 6062. Al crear elemento se generan imágenes repetidas, aumento timeout de 9 a 60 segundos para que no se repitan paquetes en caso de mala cobertura.
- 6071. No sincroniza correctamente información de "PROCESADO":
    - Se obtienen todas las operaciones cuando se cambia su configuración, ya sean más o menos horas de las que estaban configuradas.
    - Al obtener lavados se actualizaba la ultima recogida en lugar de lavado.
    - Siempre se pedían todos los procesados, no se obtenía correctamente la fecha de ultSincroProcesados.
    - Le resto 10 segundos a las fechas de sensores de llenado y lavados, antes se le sumaba un segundo (ya no es necesario esto para evitar duplicados, en versión anterior se hace desde bbdd) y algunas no las sincronizaba.

# 23061401
- 6044. Solucionados problemas con las operaciones de lavado y recogida:
    - Se utilizaba la misma fecha de sincronización (ultSincroSensoresLlenado) tanto para niveles (y recogidas) como para lavados. Se le genera su clave a lavados.
    - En todas las sincronizaciones se rechazaban las operaciones del propio dispositivo, Se quita esta restricción y se añade a la propia bbdd.
    - Al cambiar el número de horas a partir de 597 no podía calcularlas. Ahora se pueden introducir hasta 468538.
    - Al cambiar las horas de nivel de llenado (en Configuración) y sincronizar las operaciones fuera del rango, todas las operaciones se guardaban como niveles aun viniendo también las de recogida.
    - En el listado de Operaciones realizadas al hacer el filtrado solo tenía en cuenta la configuración de las horas de lavado.
    - En el mapa al pintar los marcadores de operaciones no se obtenían los de niveles.
    - Se ordena el listado de operaciones realizadas por fecha y se hacen algunos retoques estéticos.
- 6053. fallos en la lectura de tag 134.
    - Se aumenta el número de registros de 750 a 2000 en la tabla de errores.
    - Se controlan los toast si ya se está leyendo.

# 23060601
- 6046. La sincronización en segundo plano no funcionaba debido a que se hace uso de un recurso de la interfaz de usuario al abrir la base de datos, lo cuál se hacía en cada página de elementos, ahora lo hacemos sólo una vez para toda la sincronización de elementos. La respuesta del servidor al pedirle su fecha se guardaba aunque fuese incorrecta, se válida.

# 23052401
- 6034. Marcadores de lavado y recogida no se ocultaban al aplicar un filtro a los elementos.
- Cambios estéticos en login y pesaje.

# 23051901
- 5960. Problemas de lentitud con bases de datos pesadas. Se elimina operación VACUUM al iniciar la app. Esto sirve para compactar y optimizar la base de datos, pero es un proceso intensivo en recursos y puede llevar tiempo, especialmente en bases de datos grandes. 
- 5960. Optimización: Además se mejora la lógica al obtener elementos reduciendo 200 ms por cada 100 elementos.
- 5987. Modificado proceso de lectura 134. Anteriormente se hacía una lectura sin tiempo definido, ahora se hacen las lecturas necesarias hasta que haya una válida o durante 3 segundos, el tiempo que se muestra el mensaje. Tras la lectura se pueden dar tres casos:
    - No detecta ningún tag, se informa de ello.
    - Detecta un tag, pero la lectura no es correcta, se informa de ello.
    - Lee un tag correctamente, continua con la acción que se este realizando.
- Se impide lectura mediante botones físicos en pantallas donde no corresponde como en la de inicio de sesión.
- 5987. Cuando va a mostrarse la lista de tags que no están en base de datos se muestra cartel informativo.
- 5987. Se modifica el título de la pantalla de nivel de llenado, en el caso de ser lectura ahora muestra: Elegir lectura de llenado.
- 5983. Cuando el tag está en taller se espera el mismo comportamiento al leerlo desde la pantalla de Crear elemento que desde el mapa, ahora se cierra la pantalla de Crear elemento y se muestra el mismo cartel informativo.
- 6023. Al añadir incidencia si el primer tipo no tenía modelo o motivo no abría la pantalla, ahora se abre, pero en el caso de que falten alguno de los tres no deja crearla.
- 6028. Mientras hace la primera sincronización si tiene frecuencia de procesado, niveles de llenado, etc., y se mueve el mapa con un zoom lo suficiente cercano como para mostrar los marcadores la app deja de responder o se congela. Solo se muestran los elementos durante la primera sincronización y tras esta ya se muestran todos los submarcadores.
- 6028. Optimización, al mover el mapa los marcadores de lavado y recogida parpadeaban y a veces se quedaban sin mostrar. Se evita repintado.
- Optimización, se evita el cálculo innecesario de los marcadores de niveles de llenado cuando los elementos visibles del mapa no tienen ningún nivel de llenado.
- 6028. En ocasiones había elementos que no sincronizaban a no ser que se restaurara la app. Se guarda la fecha sincronización antes de pedir los datos, la de la base de datos del servidor o, en el caso de no tener esta actualización el servidor, la del dispositivo.
- 6028. Se impide que se pueda depositar un elemento ya depositado.
- Solucionado error al obtener la ubicación cuando se modifica un elemento.
- Mejoras visuales sin importancia en Loading, Inicio de sesión, Información, Retirar/Depositar elemento y Menú derecho.

# 23051101
- Corrección. 0006016: Al leer un tag CORTO, el mensaje que muestra es largo.
> Si el tag UHF es corto en base de datos, se muestra como corto independientemente del resto de variables de la intranet.

- Eliminada lógica incorrecta que se aplicaba sobre los tag 134, las variables de la intranet `tagUHFExtendido` y `tagLargoYCorto` estaban afectando al tag 134. Aunque actualmente no producía ningún efecto, en algún momento podría haber generado un error. El tag 134 siempre será de 6 dígitos y no se verá afectado por ninguna variable de la intranet.

# 23051001.
- Corrección. 0005964: Añadir limitación geográfica. 
> Una mejora que se hizo sobre la corrección, introdujo un error al comprobar la empresa.

# 23042001
- Corrección. 0005951: No enviar lectura de tag con ninguna operación. Revisión del mantis, solo se enviará el sensor TAG (36) cuando:
-- El tag no exista y elemVerMatricula no sea 2.
-- Cuando esté seleccionada la opción del menú derecho Sensores > Lectura de TAGs.

# 23041401
### Correcciones Mantis
- Página de crear/modificar elemento. 0005947: Dar de alta en taller al modificar elemento no funciona. Ocultado campo "Dar de alta en taller" en la modificación, ahora solo es visible este campo en la creación.
- 0005951: No enviar lectura de tag con ninguna operación, crear una opción en el menú para ello. Se envía el sensor 36 siempre que el tag no exista en base de datos. También se abrirá la página de listado de tags cuando no exista, indepenientemente de la página actual en la que se encuentre la aplicación. Se ha añadido un nuevo botón al menú derecho para enviar siempre el sensor 36 al leer un tag (exista o no en la base de datos), esta nueva funcionalidad no abrirá la página de listado de tags en ningún caso.
- Página principal. 0005964: Añadir limitación geográfica. Añadido límite geográfico al crear o reposicionar elementos para la empresa de Madrid Contenur (796).
- 0005957: Smartphone con variable "tagUHFExtendido" no funciona correctamente al leer tag fuera de ruta. Relacionado con 0005951.
- 0005942: La app no inserta el sensor 36 con los lectores de 134. Relacionado con 0005951.
- 0005940: Crear elemento con estado TALLER (V. 23030901 /33030901). Con la opción de crear un elemento, al leer un tag asociado a un elemento en taller, ahora te pregunta si lo quieres depositar, en vez de cambiar ubicación. Al depositarlo, cambia a estado operativo y le asigna un nuevo punto de ubicación. Para que esto funcione, se debe depositar cerca de una acera, algunas localizaciones no permiten depositar el elemento y al sincronizar, desaparecerá (se revierte el cambio al no haberse aplicado en el servidor).

# 23032802
- Corrección. Mantis 0005938: Información Lectura LLenado VOLUMETRICO. Mostrar información de lectura de llenado solo en elementos con imei de volumétrico.
# 23032801
- Corrección. Mantis 0005937: No se muestra misma información al pinchar sobre un elemento o al leer tag VERSION INDRA.
# 23030901
### Correcciones Mantis
- 0005920: Incidencias incongruentes (vacías y sin TIPO/SUBTIPO) | V 33030101 (TEST). Página de listado de incidencias. Control de datos no informados para impedir que muestre información incongruente.
- 0005928: EcoSAT No Responde al trabajar con visualización de lecturas de llenado. Página principal, mapa. Los marcadores se estaban redibujando con demasiada frecuencia.
- 0005932: Ordenar TIPOS/SUBTIPOS incidencia al crear una alfabéticamente. Página creación incidencia. Ordenados desplegables de Motivo y Modelo de incidencia.

# 23030101
- Enviado al servidor usuario que realiza el cambio de estado de la incidencia.

### Correcciones
- Página de información. Ocultado check de LF activo cuando el lector sea un C71, el sensor da un falso positivo y se mostraba activo.

### Mantis 0005912: Fallos Varios última versión en TEST (*********** / ***********).
1.- No está la variable del último desarrollo (ASCAN) correctamente definida en la INTRANET (se llama frecRecogida cuando debe ser frecProcesado).
3.- En el dispositivo C71, conforme abres EcoSAT se cierra con un mensaje de error (tengo la versión INDRA 247 puesta).
5.- El código fisico (campo que se introduce en la versión INDRA 247) no llega a EcoSAT --> Lo meto en EcoSAT Móvil pero en EcoSAT no se visualiza.
6.- Con la ventan de CREACION ELEMENTOS activada, y el móvil sin usarlo durante unos 2-3 minutos, al acceder a EcoSAT Móvil me ha saltado el siguiente mensaje de error y al aceptar se ha reiniciado la APP.
7.- En la versión INDRA 247, no debe estar lo nuevo que se ha hecho de las incidencias (debe estar como estaba antes, pues la plataforma escritorio que utilizan no es EcoSAT, y no tiene integrado todo eso), es decir, que se vea su apartado observación.


# 23022701
- Corección. Eliminado texto literal "Subtipo" en el mensaje que aparece al pulsar sobre una incidencia, cuando la versión no es de Indra.
- Corrección. No se podía pulsar el marcador del elemento cuando tenía el icono de frecuencia de procesado.

# 23022202
- Corrección. Mantis 0005907 y 0005895: Problemas con la variable elemVerMatricula -> 1. Corrección previa alteró la visibilidad de los campos.

# 23022201
- Mantis 0005793: Mejoras ASCAN ECOVIDRIO (EcoSAT Móvil). Añadida funcionalidad de incidencias falsas e icono a los elementos del mapa con el indicador de frecuencia de recogida.
- Añadido actualizador automático desde AppCenter cuando haya una nueva versión. Solo diponible en versiones 3XXXXXXX (UHF U9000).

### Correcciones
- Mantis 0005881: No se eliminan del mapa los elementos nuevos sin conexión.
- Mantis 0005875: No funciona el buscador de elementos. Añadido feedback al usuario cuando no se encuentran elementos para que pueda saber si existen o no.
- Mantis 0005886: No se observan las observaciones de las incidencias en EcOSAT Móvil (V. *********** / ***********). No se signaban bien las observaciones indicadas en el campo de estado. Se ha separado correctamente la observación de la incidencia y la de su cambio de estado. Ahora se muestra también la observación original de la incidencia (no editable) en el cambio de estado.
- Mantis 0005876: 01/02 Fallo en filtros y en desplegable con opciones (crear elemento-modelo). *(El punto 4 se realizará en otro mantis)*.
- Mantis 0005878: Añadir en la versión de Indra tipo y subtipo a la ventana de detalles de incidencia. Se ha añadido el tipo de incidencia en el mensaje que aparece al pulsar sobre una incidencia del mapa.
- Mantis 0005884: No se muestra la página de tag leído.
- 0005881: No se eliminan del mapa los elementos nuevos sin conexión.
- Lector UHF U9000. Forzada inicialización de lector UHF para evitar que arranque la aplicación sin el lector activo.

# 23021009
- Hotfix. Mantis 0005901: No lee el tag del contenedor. Página creación / modificación de elemento. Cambio previo alteró comportamiento de la variable de la intranet elementoVerMatricula y no se mostraba la matrícula leída cuando no existía.

# 23021005
- Hotfix. Página de creación / modificación de elementos. Campo matrícula editable.

# 23021003
- Hotfix. Página de creación / modificación de elementos de Camacho. Al pulsar sobre confirmar, la aplicación se cerraba.

# 23021001
- Corrección. Mantis 0005861: No puede actualizar EcoSAT Móvil.

# 23020901
- Corrección. Página de creación o modificación de elemento. El tag no se estaba mostrando con la longitud correcta. Aún con la variable de tagLargoYcorto, al leer un tag largo se mostraba como tag corto.

# 23020701
- Corrección. Las variables "tagLargoYcorto" y "tagUHFExtendido" no se estaban guardando al recuperarlas de la intranet.

# 23020601
- Mantis 0005854: Desarrollo para lectura de tag LARGOS y CORTOS en un mismo proyecto. Se habilita poniendo la variable de la intranet "tagLargoYcorto" a 1.
- Corrección. Mantis 0005857: Problema con los cambios de estado de incidencia.
- Corrección. El lector UHF U9000 no se activa en el primer inicio de la aplicación despues de encender o reiniciar el equipo.

### Correcciones 3. 0005847: Fallos vistos en la versión de Indra
- Página de modificar elemento. Mostrado campo "Cod. Físico" en la versión de Indra y ocultado campo "Matrícula".
- Al marcar un elemento como recogido (opción "Recogida de elemento"), no se pinta sobre el elemento tick verde.
- A la hora de Crear un elemento, la aplicación se queda unos 5-10 segundos en negro.

# 23012501
- Corrección. Mantis 0005848: NFC no funciona al bloquear y desbloquear en OPPO.
- Corrección. Solucionado error en la validación de UHF. Al no validarse correctamente, impedía que se enviasen al servidor.

### Correcciones. Mantis 0005847.
- Página de creación de incidencia. Revertidos cambios visuales realizados en versiones anteriores.
- Página modificar elemento. Ocultado campo "Alta en taller" en la versión Indra.
- Página de modificar elemento. Mostrado campo "Cod. Físico" en la versión de Indra y ocultado campo "Matrícula".


# 23010401
- Corrección. Mantis 0005777: Al depositar un elemento se registra pero al momento de sincronizar desaparece. Modificada la llamada al servidor, antes usaba la llamada de modificar elemento, se ha cambiado a la que existe específicamente para depositar un elemento.
- Corrección. Mantis 0005840: Fallo al reparar aplicación. No se podía reparar la aplicación en algunos dispositivos, saltaba un mensaje de error.

# 22121901
- Corrección. Mantis 0005749. Solucionado error de lectura de NFCs en nuestros equipos y añadida la posibilidad de leer NFC en cualquier smartphone.

# 22120901
- Corrección. Detenida la sincronización de elementos cuando uno de los elementos recibidos no se puede gestionar. Antes seguía con la sincronización, provocando que se puediesen llegar a ver cosas diferentes en cada equipo.
- Mantis 0005811 y 0005806. Añadidos más mensajes de log para tratar de encontrar el problema si vuelve a pasar. No se ha podido reproducir.

# 22113001
- Unificados cambios de indra de la versión 22080301. La mayoría de estos cambios estaban ya integrados y eran visibles con la versión de software 247.
- Mejora en los mensajes de alerta que aparecen al leer TAGs para que no se superpongan.
- Corrección. Mantis 0005792. Con la unificación de cambios, se ha solucionado el problema de las imágenes que no cargan.
- Corrección. Mantis 0005798. Las imágenes de las incidencias llegaban a EcoSat con un tamaño muy pequeño.
- Corrección. Mantis 0005805. Las imágenes ahora se guardan en base64 en el modelo, para evitar que pueda enviarse una incorrecta.

# 23112201
- Corrección. Ahora se detecta si es u9000.

# 23112201
- Corrección. Programación de refuerzo al leer tag.

# 22112201
- Corrección. Mantis 0005735. Página principal. Visualización de iconos de nivel de llenado y limpieza sobre elementos, según la configuración seleccionada. Antes no se aplicaba correctamente la configuración.
- Página de creación y cambio de estado de incidencias. Mejora en los iconos de operaciones sobre las imágenes para que sean más fáciles de entender.

# 22112101
- Corrección. Mantis 0005789. Página principal. Al leer un TAG y tener activado el menú de depositado, ahora se envía correctamente un evento de depositado al servidor. Antes solo se realizaba esta acción al realizar el depositado desde la página de depositar elemento.

# 22111701
- Compatibilidad a Android 12.
- Mantis 0005742. Página de edición de estado de incidencia. Añadidas validaciones adicionales para evitar que la aplicación se cierre.
- Página de información. Modificados nombres de los lectores de TAGs por unos más genéricos que no indican las marcas de los dispositivos que tenemos.

# 22111501
- Corrección. Mantis 0005765. El lector UHF U9000 estaba restringido a versiones de Android 9 o superiores. Lo que provocaba que no funcionase en Android 8. Se elimina la restricción.

# 22111401
- Corrección. Mantis 0005765. Reinicio del lector UHF U9000 en cada lectura, para evitar que se bloquee.

# 22110901
- Corrección. Mantis 0005735. Icono del check verde de los niveles de llenado no se venían con las configuraciones de zoom de cluster. 
- Mantis 0005763, 0005749. Añadidos más mensajes informativos para el usuario cuando realiza una lectura de TAG. A veces no se muestran los mensajes y da la sensación de que no realiza la lectura.
- Página de información. Añadidos checks con los tipos de lecturas de TAGs disponibles en el dispositivo. Se ha añadido a modo informativo para poder ver si el equipo lee cierto TAG o simplemente ha arrancado el módulo de lectura.

# 22101601
- Añadido Sentry para el registro de errores.
- Mantis 0005742. Página de creación de incidencias. Añadidas comprobaciones al añadir imágen de la galería y confirmar la creación de la incidencia. Se ha hecho para evitar que la aplicación se reinicie y detectar a futuro la causa real del problema. 

# 22093001
- Correción. Mantis 0005737. No se obtenían imágenes de la cámara en algunos equipos con Android 12.
- Modificado el mensaje de error que aparece cuando se ejecuta la versión de Google Play en un UHF U9000.

# 22092903
- Revertida corrección anterior. Se deja el fallo por el momento, para que el usuario no pueda usar la aplicación cuando instala la versión incorrecta. Los lectores UHF U9000 deben instalar la versión desde AppCenter, el resto puede usar la versión de la Google Play.

# 22092901
- Corrección. Mensaje de error al arrancar por incompatibilidad con el lector UHF U9000. Se ha filtrado el tipo de Abis para que no inicie este lector en equipos incompatibles.