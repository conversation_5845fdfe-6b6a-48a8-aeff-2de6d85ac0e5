apply plugin: 'com.android.application'

android {
    // No se va a dar soporte a versiones superiores a Android 12 (SDK 32).
    // Actualización 26/09/2023: Google Play no permite subir versiones con SDKs antiguos, por lo que se ha actualizado a 33.
    // Indican que esto pasará cada año.
    // Se sube a compileSdk 35 para bloquear la obligación de adaptar la app a edge-to-edge en Android 15.
    // El bloqueo está en themes.xml
    // A partir de SDK 36 ya no se puede bloquear y es obligatorio adaptar la app a edge-to-edge.
    compileSdkVersion 35
    useLibrary 'org.apache.http.legacy'
    defaultConfig {
        applicationId "com.movisat.ecosat"
        minSdkVersion 21
        // No se va a dar soporte a versiones superiores a Android 12 (SDK 32).
        targetSdkVersion 35

        // Versions de indra empiezan por 4 (+20 años).
        // Versiones U9000 UHF empiezan por 3 (+10 años).
        versionCode 25091501
        versionName "25.09.15.01"
        multiDexEnabled true
        vectorDrawables.useSupportLibrary = true

        // Google Play => Comentar la línea y versión con la fecha actual. AppCenter => Descomentarla y versión añadiendo 10 años.
        // Las versiones de producción que no van a Google Play (3XXXXXXX) se actualizan con AppCenter, se debe descomentar todo lo
        // relacionado con el actualizador. Buscar los comentario // APPCIRCLE_UPDATER

        // Obligatorio para 3XXXXXXX. Hay que descomentar la linea para que los lectores UHF puedan leer tags.
//        ndk { abiFilters "armeabi", "armeabi-v7a", "x86", "mips" }
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildToolsVersion '35.0.0'
    namespace 'com.movisat.ecosat'
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':api')
    // APPCIRCLE_UPDATER
//    implementation project(':app_update_appcircle')
    implementation project(':googlemapcluster_lib')
    implementation project(':log_provider_console_android')
    implementation project(':log')
    implementation project(':materialDesign')
    implementation project(':rfid_uhf_u9000')
    implementation project(':utils_android')
    implementation project(':utils')
    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.multidex:multidex:2.0.0'
    implementation 'androidx.preference:preference:1.0.0'
    implementation 'com.getbase:floatingactionbutton:1.10.1'
    implementation 'com.github.castorflex.smoothprogressbar:library:1.1.0'
    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation 'com.google.android.material:material:1.3.0'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'net.mm2d.preference:preference:0.3.0' // A partir de la versión 0.3.0 da error
    implementation 'org.apache.commons:commons-lang3:3.7'
    // noinspection DuplicatePlatformClasses
    // implementation 'org.apache.httpcomponents:httpclient:4.5.13' // Descomentar para que no muestre error en el IDE. Solo para debuggear, para compilar da error.
    implementation 'org.greenrobot:eventbus:3.0.0'
    implementation 'com.google.android.gms:play-services-location:21.3.0'

    testImplementation 'junit:junit:4.12'
}
