import static org.junit.Assert.assertEquals;

import com.movisat.database.FrecuenciaProcesadoState;
import com.movisat.database.Elemento;
import com.movisat.use_case.ElementCalculateProcessingFrequencyState;

import org.junit.Test;

public class FrecuenciaProcesadoStateTest {
    String fechaActual;
    String fechaUltima;
    double frecuencia;
    FrecuenciaProcesadoState estadoEsperado;
    FrecuenciaProcesadoState resul;

    @Test
    public void testExceeded() {
        // exceeded: el número de días es mayor o igual al collectionFrequencyInDays.
        estadoEsperado = FrecuenciaProcesadoState.EXCEEDED;
        fechaActual = "2023-01-01 00:00:00";
        fechaUltima = "2023-01-11 00:00:00"; // 10 días

        // mayor
        frecuencia = 9;
        resul = ElementCalculateProcessingFrequencyState.execute(fechaActual, fechaUltima, frecuencia);
        assertEquals(estadoEsperado, resul);
    }

    @Test
    public void testOverHalf() {
        // overHalf: el número de días es mayor o igual a la mitad de collectionFrequencyInDays.
        estadoEsperado = FrecuenciaProcesadoState.OVER_HALF;
        fechaActual = "2023-01-01 00:00:00";
        fechaUltima = "2023-01-11 00:00:00"; // 10 días

        // mayor
        frecuencia = 19; // 19/2 = 9.5
        resul = ElementCalculateProcessingFrequencyState.execute(fechaActual, fechaUltima, frecuencia);
        assertEquals(estadoEsperado, resul);

        // igual
        frecuencia = 20; // 20/2 = 10
        resul = ElementCalculateProcessingFrequencyState.execute(fechaActual, fechaUltima, frecuencia);
        assertEquals(estadoEsperado, resul);
    }

    @Test
    public void testNone() {
        // none: si no cumple las condiciones anteriores.
        estadoEsperado = FrecuenciaProcesadoState.NONE;
        fechaActual = "2023-01-01 00:00:00";
        fechaUltima = "2023-01-11 00:00:00"; // 10 días

        // menor
        frecuencia = 21; // 21/2 = 10.5
        resul = ElementCalculateProcessingFrequencyState.execute(fechaActual, fechaUltima, frecuencia);
        assertEquals(estadoEsperado, resul);
    }


    @Test
    public void testFechaActualEmpty() {
        // none: si fechaActual viene vacía.
        estadoEsperado = FrecuenciaProcesadoState.NONE;
        fechaActual = "";
        fechaUltima = "2023-01-11 00:00:00";
        frecuencia = 0;

        resul = ElementCalculateProcessingFrequencyState.execute(fechaActual, fechaUltima, frecuencia);
        assertEquals(estadoEsperado, resul);
    }

    @Test
    public void testFechaUltimaEmpty() {
        // none: si fechaUltima viene vacía.
        estadoEsperado = FrecuenciaProcesadoState.NONE;
        fechaActual = "2023-01-01 00:00:00";
        fechaUltima = "";
        frecuencia = 0;

        resul = ElementCalculateProcessingFrequencyState.execute(fechaActual, fechaUltima, frecuencia);
        assertEquals(estadoEsperado, resul);
    }
}