package com.environment;

public class Environment {
    /**
     * Indica si el dispositivo tiene un lector NFC válido.
     */
    public static boolean hasReaderNFC = false;

    /**
     * Indica si el dispositivo tiene un lector UHF válido.
     */
    public static boolean hasReaderUHFC71 = false;

    /**
     * Indica si el dispositivo tiene un lector RFID LF (125-134 MHz) válido.
     */
    public static boolean hasReaderLFChainway = false;

    /**
     * Indica si el dispositivo es un U9000.
     */
    public static boolean hasReaderUHFU9000 = false;

    /**
     * Indica si la versión tiene las características que se hicieron para Indra en la version de software 247.
     */
    public static boolean isSoftIndra = false;

    public static boolean isCompanyMadridContenur = false;

    /**
     * 0005793: Mejoras ASCAN ECOVIDRIO
     */
    public static boolean isVisibleAvisoFalso = false;

    /**
     * 0005793: Mejoras ASCAN ECOVIDRIO
     */
    public static boolean isVisibleFrecuenciaProcesado = false;

    /**
     * Indica si la versión va a utilizar tags largos, y si no los encuentra tags cortos.
     *
     * Esto solo aplica a los tag UHF, los 134 siempre serán de 6 dígitos.
     */
    public static boolean isTagUHFLongAndShort = false;

    /**
     * Indica si se tiene que mostrar el tag largo.
     * 
     * Se utiliza para UHF.
     */
    public static boolean isTagUHFExtended = false;

    /**
     * Indicates if the tag can be assigned to a container even if it does not exists.
     */
    public static VisibilityElementPlate visibilityElementPlate = VisibilityElementPlate.NO;

    /**
     * Indica si la versión tiene las características que se hicieron para Camacho en la version de software 202 o 207.
     */
    public static boolean isSoftCamacho = false;

    // TODO: REDUCIDOS: Eliminar esta lógica cuando el nuevo endpoint esté en todos los servidores
    /**
     * Indica si la API tiene el nuevo endpoin de elementos (reducidos).
     */
    public static boolean hasNewElementEndpoint = false;

    /**
     * Indica si se está ejecutando en un tablet según el resultado de isTablet() de MainActivity.
     */
    public static boolean isTablet;
}