package com.environment;
import com.movisat.ecosat.BuildConfig;

public class EnvironmentDebug {

    //================================================================================
    // Fields
    //================================================================================
    protected static Boolean isRelease = null;

    private static EnvironmentDebugData data = new EnvironmentDebugData();

    //================================================================================
    // Methods - Static
    //================================================================================
    
    // Habilita el modo debug solo si no se ha llamado previamente a getInstance().
    public static void enableDebugMode(EnvironmentDebugData debugData) {
        // Solo está disponible en modo DEBUG.
        if (!BuildConfig.DEBUG) isRelease = new Boolean(true);

        if (isRelease == null) {
            isRelease = new Boolean(false);
            if (debugData != null) data = debugData;
        }
    }

    public static EnvironmentDebugData getData() {
        // Definimos que está en modo release si no se ha asignado antes el debug mode.
        if (isRelease == null) {
            isRelease = new Boolean(true);
            data = new EnvironmentDebugData();
        }

        if (data == null) data = new EnvironmentDebugData();

        return data;
    }

    public static boolean isRelease() {
        getData();
        return isRelease;
    }

}