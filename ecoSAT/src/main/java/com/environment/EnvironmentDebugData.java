package com.environment;

public class EnvironmentDebugData {

    //================================================================================
    // Fields
    //================================================================================
    private String projectKey = null;
    private String urlServer = null;
    private Integer companyId = null;
    private Boolean canSynchronize = null;
    private String deviceId = null;
    private Integer mobileCode = null;
    private Boolean isSimulatedTagNFC = null;
    private String tipoSoft = null;
    private String menu = null;


    //================================================================================
    // Constructors
    //================================================================================
    public EnvironmentDebugData() {
    }

    public EnvironmentDebugData(String projectKey,
                                String urlServer,
                                Integer companyId,
                                Boolean canSynchronize,
                                String deviceId,
                                Integer mobileCode,
                                Boolean isSimulatedTagNFC,
                                String tipoSoft,
                                String menu) {
        this.projectKey = projectKey;
        this.urlServer = urlServer;
        this.companyId = companyId;
        this.canSynchronize = canSynchronize;
        this.deviceId = deviceId;
        this.mobileCode = mobileCode;
        this.isSimulatedTagNFC = isSimulatedTagNFC;
        this.tipoSoft = tipoSoft;
        this.menu = menu;
    }

    //================================================================================
    // Getters
    //================================================================================
    public String getProjectKey() {
        if (EnvironmentDebug.isRelease) return null;
        return projectKey;
    }

    public String getUrlServer() {
        if (EnvironmentDebug.isRelease) return null;
        return urlServer;
    }

    public Integer getCompanyId() {
        if (EnvironmentDebug.isRelease) return null;
        return companyId;
    }

    public Boolean getCanSynchronize() {
        if (EnvironmentDebug.isRelease) return null;
        return canSynchronize;
    }

    public String getDeviceId() {
        if (EnvironmentDebug.isRelease) return null;
        return deviceId;
    }

    public Integer getMobileCode() {
        if (EnvironmentDebug.isRelease) return null;
        return mobileCode;
    }

    public Boolean getIsSimulatedTagRead() {
        if (EnvironmentDebug.isRelease) return null;
        return isSimulatedTagNFC;
    }

    public String getTipoSoft() {
        if (EnvironmentDebug.isRelease) return null;
        return tipoSoft;
    }

    public String getMenu() {
        if (EnvironmentDebug.isRelease) return null;
        return menu;
    }
}
