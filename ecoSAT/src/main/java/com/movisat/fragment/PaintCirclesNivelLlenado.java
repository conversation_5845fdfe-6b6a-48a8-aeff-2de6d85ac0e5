package com.movisat.fragment;

import android.content.res.Resources;
import android.os.AsyncTask;

import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.model.Marker;
import com.movisat.database.DBElemento;
import com.movisat.database.DBSensoresNivelesLlenado;
import com.movisat.database.Elemento;
import com.movisat.database.ItemMapa;
import com.movisat.database.SensorNivelLLenado;
import com.movisat.ecosat.MainActivity;

import java.sql.Date;
import java.text.SimpleDateFormat;
import java.util.ArrayList;

public class PaintCirclesNivelLlenado extends
        AsyncTask<Integer, Elemento, Void> {

    private ArrayList<ItemMapa> elementos = null;
    private GoogleMap map = null;
    private ArrayList<Integer> listaCirculosNivelLlenado = null;
    private Boolean running = false;
    private GestionElementos gestionElementos = null;
    private Resources res = null;

    public PaintCirclesNivelLlenado(GestionElementos gestionElementos,
                                    GoogleMap map, ArrayList<ItemMapa> elementos) {
        this.elementos = elementos;
        this.map = map;
        this.gestionElementos = gestionElementos;
        this.res = MainActivity.getInstance().getResources();
    }

    @Override
    protected Void doInBackground(Integer... params) {
        if (running)
            return null;

        int empresa = params[0];

        DBSensoresNivelesLlenado sensoresManager = new DBSensoresNivelesLlenado();
        ArrayList<SensorNivelLLenado> sensores = sensoresManager.getSensoresSinProcesar(empresa);
        sensoresManager.close();

        if (sensores == null) {
            return null;
        }

        DBElemento elementoManager = new DBElemento();

        Elemento elemento = null;

        if (listaCirculosNivelLlenado == null)
            listaCirculosNivelLlenado = new ArrayList<Integer>();
        running = true;
        for (SensorNivelLLenado sensor : sensores) {

            // sensor = sensoresManager.getSensorBy(elemento.getId(),
            // MainActivity
            // .getInstance().getEmpresa());
            elemento = elementoManager.getByIdExterno(
                    sensor.getCodigoElemento(), empresa);

            if (elemento != null && elemento.hasLinkedVolumetric() ) {
                Date d = new Date(sensor.getFechaRegistro());
                SimpleDateFormat sdf = new SimpleDateFormat(
                        "yyyy-MM-dd kk:mm:ss.SSS");
                //elemento.setTag(res.getString(R.string.recogido) + ": " + sdf.format(d) + res.getString(R.string.fraccion) + ": "
                        //+ sensor.getNumeroFraccion());
                publishProgress(elemento);

            }
        }
        elementoManager.close();

        return null;
    }

    protected void onProgressUpdate(Elemento... progress) {
        Elemento elemento = progress[0];
        Marker marker;
        if (!listaCirculosNivelLlenado.contains(elemento.getId())) {
            marker = gestionElementos.getMarker(elemento);
            if (marker != null) {
                marker.setRotation(180);
                listaCirculosNivelLlenado.add(elemento.getId());
            } else {
                gestionElementos.updateElementoToClusterById(elemento.getId(),
                        elemento);
            }
        }
    }

    protected void onPostExecute() {
        running = false;
    }

}
