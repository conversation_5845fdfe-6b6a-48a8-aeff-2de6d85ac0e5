package com.movisat.fragment;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.drawable.BitmapDrawable;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.environment.Environment;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.GoogleMap.OnMapClickListener;
import com.google.android.gms.maps.GoogleMap.OnMarkerClickListener;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.gms.maps.model.Polyline;
import com.google.android.gms.maps.model.PolylineOptions;
import com.google.maps.android.SphericalUtil;
import com.google.maps.android.clustering.Cluster;
import com.google.maps.android.clustering.ClusterManager;
import com.google.maps.android.clustering.algo.GridBasedAlgorithm;
import com.google.maps.android.clustering.algo.PreCachingAlgorithmDecorator;
import com.google.maps.android.clustering.view.DefaultClusterRenderer;
import com.movisat.bll.SensoresManager;
import com.movisat.database.DBElemento;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBEstados;
import com.movisat.database.DBFlotaPosiciones;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBIncidenciaFoto;
import com.movisat.database.DBIncidenciaTipo;
import com.movisat.database.DBOperationsDone;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModelo;
import com.movisat.database.Estado;
import com.movisat.database.FlotaPosiciones;
import com.movisat.database.FlotaPosicionesHistorico;
import com.movisat.database.FrecuenciaProcesadoState;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaFoto;
import com.movisat.database.IncidenciaTipo;
import com.movisat.database.ItemMapa;
import com.movisat.database.Posicion;
import com.movisat.database.Tags;
import com.movisat.ecosat.AddInciActivity;
import com.movisat.ecosat.AppContext;
import com.movisat.ecosat.AsignarIncidenciasActivity;
import com.movisat.ecosat.DepositarElemActivity;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyBroadCastManager;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.NivelLlenadoActivity;
import com.movisat.ecosat.R;
import com.movisat.ecosat.SeleccionUsuarioIncidenciaActivity;
import com.movisat.ecosat.SettingsActivity;
import com.movisat.ecosat.SustituirElementoActivity;
import com.movisat.ecosat.UpdateEstadoIncidencia;
import com.movisat.events.onChangeLocation;
import com.movisat.helpers.ElementAreaBoundsValidator;
import com.movisat.log.Logg;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.synchronize.DBSynchro;
import com.movisat.tags.ITag;
import com.movisat.tags.TagUnknow;
import com.movisat.utilities.Config;
import com.movisat.utilities.GPS;
import com.movisat.utilities.HelperDates;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Photo;
import com.movisat.utilities.Utils;
import com.movisat.utils.Utilss;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

import java.sql.Date;
import java.util.ArrayList;
import java.util.Hashtable;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

public class GestionElementos extends DefaultClusterRenderer<ItemMapa>
      implements ClusterManager.OnClusterClickListener<ItemMapa>,
      ClusterManager.OnClusterInfoWindowClickListener<ItemMapa>,
      ClusterManager.OnClusterItemClickListener<ItemMapa>,
      ClusterManager.OnClusterItemInfoWindowClickListener<ItemMapa>,
      ClusterManager.OnClusterItemDragStartListener<ItemMapa>,
      ClusterManager.OnClusterItemDragListener<ItemMapa>,
      ClusterManager.OnClusterItemDragEndListener<ItemMapa>,
      ClusterManager.OnClusterCameraChangeListener, OnMapClickListener,
      OnMarkerClickListener {
   private static final String TAG = GestionElementos.class.getSimpleName();
   private static final long TIEMPO_REFRESCO_POSICION = 5000;
   private receiverGestionElementos receiver = null;
   private Toast toast = null;
   public static Marker mCrearElemento = null;
   public static Marker mDepositarElemento = null;
   public static Marker mCrearIncidencia = null;
   public static String mAsignarIncidenciaFake = null;
   public static Marker mCrearDiaBloqueo = null;
   private static GestionElementos instance = null;
   private CameraPosition oldCameraPosition = null;
   private static final String syncElem = "Elem_sync";
   public static List<Posicion> Posiciones = null;
   private static boolean isClickMap = true;
   // Tablas hash para almacenar los iconos de los modelos y
   // la visibilidad de cada uno
   private static Hashtable<Integer, BitmapDescriptor> modelIcons = null;
   private static Hashtable<Integer, Bitmap> modelBitmaps = new Hashtable<>();
   private static Hashtable<Integer, Boolean> modelVisible = null;
   public static boolean gps_enabled = false;
   // Lista de elementos en memoria que corresponde con los
   // que hay en los cluster
   private static ArrayList<ItemMapa> elemList = null;
   private final ArrayList<Integer> elemClenead = new ArrayList<>();
   private final ArrayList<Integer> elemCollected = new ArrayList<>();
   private ArrayList<Marker> marcadoresFlota = null;
   private static ArrayList<ItemMapa> flotaList = null;
   private final Handler handler = new Handler(Looper.getMainLooper());
   private Runnable runnable2;
   private DrawMapTask drawMapTask;
   // Lista de incidencias en memoria que corresponde con las
   // que hay en en mapa
   private static ArrayList<ItemMapa> inciList = new ArrayList<>();
   private static Hashtable<Integer, Boolean> inciVisible = null;
   private static Hashtable<Integer, Boolean> inciTipoVisible = null;
   private static ArrayList<ItemMapa> historicoRutaList = null;
   private static int iconWidth, iconHeight;
   public static GoogleMap map = null;
   private static ClusterManager<ItemMapa> elemCluster = null;
   private ItemMapa oldElemento = null;
   private static Elemento elementoSeleccionado = null;
   private Incidencia incidenciaSeleccionada = null;
   private float actualZoom = 6;
   // Para gestionar las posiciones GPS
   public static LocationManager locationManager = null;
   private LocationListener locationListener = null;
   public static GPSInfo ultGpsPos = null;
   private boolean gpsTrack = false;
   private SharedPreferences sharedPref;
   // Circulo para ver la posiciin GPS
   //private CircleOptions circlePosOptions = new CircleOptions();
   //private Circle circlePos = null;
   //private float acuGps = 50;
   //private int colorGps1 = Color.argb(200, 255, 0, 255);
   //private int colorGps2 = Color.argb(50, 255, 0, 255);
   //private int colorWfi1 = Color.argb(200, 100, 150, 255);
   //private int colorWfi2 = Color.argb(50, 100, 150, 255);
   // Temporizador para evitar la reentrada cuando se pulsa muchas veces
   // seguidas sobre la cartografia
   private static long timerClick = 0;
   // Mensajes para interactual con la interfaz de usuario desde otros hilos
   // private static final int MSG_REFRESH_CLUSTER = 1;
   // private static final int MSG_REFRESH_INCIDENCIAS = 2;
   // Nivel de zoom minimo para poder crear, modificar o borrar
   public static final int MINZOOM_CLICKABLE = 16;
   public static int MINZOOM_CLUSTER = 14;
   public static final String EVENT_GESTION_ELEMENTOS = "gestion.elementos.event";
   public static final String MSG_REFRESH_MAP_BR = "refresh.map.event";
   public static final String MSG_REFRESH_CLUSTER_BR = "refresh.cluster.event";
   public static final String MSG_REFRESH_INCIDENCIAS_BR = "refresh.incidencias.event";
   public static final String MSG_DELETE_ITEM_CLUSTER_BR = "delete.item.cluster.event";
   public static final String MSG_UPDATE_ITEM_CLUSTER_BR = "update.item.cluster.event";
   public static final String MSG_ADD_ITEM_CLUSTER_BR = "add.item.cluster.event";
   public static final String MSG_ADD_INCIDENCIA_FOTO_BR = "add.incidencia.foto.event";
   public static final String MSG_UPDATE_INCIDENCIA_BR = "update.incidencia.event";
   public static final String MSG_SET_MAP_TYPE_BR = "set.map.type.event";
   public static final String MSG_CENTER_GPS_POS_BR = "set.center.gps.pos.event";
   public static final String MSG_GPS_TRACK_BR = "set.gps.track.event";
   public static final String MSG_CENTER_MAP_BR = "set.center.map.event";
   public static final String MSG_SET_VISIBLE_ESTADO_INCIDENCIA_BR = "set.visible.estado.incidencia.event";
   public static final String MSG_TOGGLE_VISIBLE_ESTADO_INCIDENCIA_BR = "toggle.visible.estado.incidencia.event";
   public static final String MSG_SET_VISIBLE_TIPO_INCIDENCIA_BR = "set.visible.tipo.incidencia.event";
   public static final String MSG_TOGGLE_VISIBLE_TIPO_INCIDENCIA_BR = "toggle.visible.tipo.incidencia.event";
   public static final String MSG_SET_VISIBLE_MODELO_ELEMENTO_BR = "set.visible.modelo.elemento.event";
   public static final String MSG_TOGGLE_VISIBLE_MODELO_ELEMENTO_BR = "set.toggle.modelo.elemento.event";
   public static final String MSG_TOGGLE_CLICKMAP = "toggle.click.map.event";
   public static final String MSG_CREATE_MARKER_CREAR_ELEMENTO = "marker.create.element.event";
   public static final String MSG_REMOVE_MARKER_CREAR_ELEMENTO = "marker.remove.element.event";
   public static final String MSG_CREATE_MARKER_DEPOSITAR_ELEMENTO = "marker.create.depositar.element.event";
   public static final String MSG_REMOVE_MARKER_DEPOSITAR_ELEMENTO = "marker.remove.depositar.element.event";
   public static final String MSG_CLICK_FLOAT_BUTTON_CREAR_ELEMENTO = "click.float.button.create.elemento.event";
   public static final String MSG_CLICK_FLOAT_BUTTON_DEPOSITAR_ELEMENTO = "click.float.button.depositar.elemento.event";
   public static final String MSG_CREATE_MARKER_CREAR_INCIDENCIA = "marker.create.incidencia.event";
   public static final String MSG_REMOVE_MARKER_CREAR_INCIDENCIA = "marker.remove.incidencia.event";
   public static final String MSG_CLICK_FLOAT_BUTTON_CREAR_INCIDENCIA = "click.float.button.create.incidencia.event";
   public static final String MSG_CREATE_BUTTON_ASIGNAR_INCIDENCIA = "float.button.create.asignar.incidencia.event";
   public static final String MSG_REMOVE_BUTTON_ASIGNAR_INCIDENCIA = "float.button.remove.asignar.incidencia.event";
   public static final String MSG_CLICK_FLOAT_BUTTON_ASIGNAR_INCIDENCIA = "click.float.button.create.asignar.incidencia.event";
   public static final String MSG_REFRESH_MODELS = "click.refresh.models";
   public static final String MSG_UPDATE_ELEMENTO = "update.elemento.event";
   public static final String MSG_INCIDENCIA_ELEMENTO = "incidencia.elemento.event";
   public static final String MSG_REMOVE_MARKERS_FLOTA = "flota.clean.event";
   public static final String MSG_REFRESH_HISTORICO_RUTAS = "historico.rutas.event";
   private Polyline polyline = null;

   public synchronized static GestionElementos getInstance() {
      return instance;
   }

   public GestionElementos(GoogleMap map, ClusterManager<ItemMapa> clusterManager) {
      super(MainActivity.getInstance(), map, clusterManager);

      instance = this;

      try {

         receiver = new receiverGestionElementos();
         IntentFilter filter = new IntentFilter();
         filter.addAction(EVENT_GESTION_ELEMENTOS);
         filter.addAction(MSG_REFRESH_CLUSTER_BR);
         filter.addAction(MSG_REFRESH_MAP_BR);
         filter.addAction(MSG_REFRESH_INCIDENCIAS_BR);
         filter.addAction(MSG_DELETE_ITEM_CLUSTER_BR);
         filter.addAction(MSG_UPDATE_ITEM_CLUSTER_BR);
         filter.addAction(MSG_ADD_ITEM_CLUSTER_BR);
         filter.addAction(MSG_ADD_INCIDENCIA_FOTO_BR);
         filter.addAction(MSG_UPDATE_INCIDENCIA_BR);
         filter.addAction(MSG_SET_MAP_TYPE_BR);
         filter.addAction(MSG_CENTER_GPS_POS_BR);
         filter.addAction(MSG_GPS_TRACK_BR);
         filter.addAction(MSG_CENTER_MAP_BR);
         filter.addAction(MSG_SET_VISIBLE_ESTADO_INCIDENCIA_BR);
         filter.addAction(MSG_TOGGLE_VISIBLE_ESTADO_INCIDENCIA_BR);
         filter.addAction(MSG_SET_VISIBLE_TIPO_INCIDENCIA_BR);
         filter.addAction(MSG_TOGGLE_VISIBLE_TIPO_INCIDENCIA_BR);
         filter.addAction(MSG_SET_VISIBLE_MODELO_ELEMENTO_BR);
         filter.addAction(MSG_TOGGLE_VISIBLE_MODELO_ELEMENTO_BR);
         filter.addAction(MSG_TOGGLE_CLICKMAP);
         filter.addAction(MSG_CREATE_MARKER_CREAR_ELEMENTO);
         filter.addAction(MSG_REMOVE_MARKER_CREAR_ELEMENTO);
         filter.addAction(MSG_CREATE_MARKER_DEPOSITAR_ELEMENTO);
         filter.addAction(MSG_REMOVE_MARKER_DEPOSITAR_ELEMENTO);
         filter.addAction(MSG_CLICK_FLOAT_BUTTON_CREAR_ELEMENTO);
         filter.addAction(MSG_CLICK_FLOAT_BUTTON_DEPOSITAR_ELEMENTO);
         filter.addAction(MSG_CLICK_FLOAT_BUTTON_CREAR_INCIDENCIA);
         filter.addAction(MSG_CLICK_FLOAT_BUTTON_ASIGNAR_INCIDENCIA);
         filter.addAction(MSG_REFRESH_MODELS);
         filter.addAction(MSG_CREATE_MARKER_CREAR_INCIDENCIA);
         filter.addAction(MSG_REMOVE_MARKER_CREAR_INCIDENCIA);
         filter.addAction(MSG_CREATE_BUTTON_ASIGNAR_INCIDENCIA);
         filter.addAction(MSG_REMOVE_BUTTON_ASIGNAR_INCIDENCIA);
         filter.addAction(MSG_UPDATE_ELEMENTO);
         filter.addAction(MSG_INCIDENCIA_ELEMENTO);
         filter.addAction(MSG_REMOVE_MARKERS_FLOTA);
         filter.addAction(MSG_REFRESH_HISTORICO_RUTAS);

         sharedPref = PreferenceManager.getDefaultSharedPreferences(AppContext.getContext());

         // /Receiver
         LocalBroadcastManager.getInstance(AppContext.getContext())
               .registerReceiver(receiver, filter);

         GestionElementos.map = map;
         if (ActivityCompat.checkSelfPermission(MainActivity.getInstance(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(MainActivity.getInstance(), Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            try {
               map.setMyLocationEnabled(true);
               map.getUiSettings().setMyLocationButtonEnabled(false);
            } catch (Exception e) {

            }
         }
         elemCluster = clusterManager;

         // Asigno los eventos que quiero recibir del cluster y de los
         // elementos que hay dentro
         clusterManager.setOnClusterClickListener(this);
         clusterManager.setOnClusterInfoWindowClickListener(this);
         clusterManager.setOnClusterItemClickListener(this);
         clusterManager.setOnClusterItemInfoWindowClickListener(this);
         clusterManager.setOnClusterItemDragStartListener(this);
         clusterManager.setOnClusterItemDragListener(this);
         clusterManager.setOnClusterItemDragEndListener(this);
         clusterManager.setOnClusterCameraChangeListener(this);
         clusterManager.setRenderer(this);

         clusterManager.setAlgorithm(new PreCachingAlgorithmDecorator<ItemMapa>(
               new GridBasedAlgorithm<ItemMapa>()));

         // Redirecciono algunos eventos del mapa al cluster
         map.setOnCameraChangeListener(clusterManager);
         map.setOnInfoWindowClickListener(clusterManager);
         map.setOnMarkerDragListener(clusterManager);
         map.setOnMarkerClickListener(clusterManager);
         map.setOnMapClickListener(this);
         //map.setOnPolylineClickListener(this);

         isClickMap = true;
         // Calculo las dimensiones de los iconos en función de
         // la resolución de pantalla
         Display display = MainActivity.getInstance().getWindowManager()
               .getDefaultDisplay();

         Point size = new Point();
         display.getSize(size);

         if (size.x <= 320)
            iconWidth = iconHeight = 32;
         else if (size.x >= 1000)
            iconWidth = iconHeight = 96;
         else
            iconWidth = iconHeight = 48;

         marcadoresFlota = new ArrayList<Marker>();

         if (flotaList == null)
            flotaList = new ArrayList<ItemMapa>();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   public void onLogin() {
      // Cago en memoria la información de los modelos de elementos
      // básicamente los iconos y la visibilidad de cada uno
      loadModels();

      // Cago en memoria la información de los estados de incidencias
      // y la visibilidad de cada uno
      loadEstadosInci();
      
      // Cago en memoria la información de los tipos de incidencias
      // y la visibilidad de cada uno
      loadTiposInci();

      double lat = 0, lon = 0;
      //         actualZoom = 6;
      //
      //         // Recupero la ultima informacion del usuario en cuanto a posicion
      //         // del mapa y zoom
      //         lat = Double.parseDouble(Config.getInstance().getValueUsuario(
      //               "miLat", "0"));
      //         lon = Double.parseDouble(Config.getInstance().getValueUsuario(
      //               "miLon", "0"));
      //         actualZoom = Float.parseFloat(Config.getInstance().getValueUsuario(
      //               "miZoom", "6"));
      //
      //         // Si es la primera vez que entra el usuario centro en
      //         // la Puerta del Sol de Madrid
      //         if (lat == 0 && lon == 0) {
      //
      //            lat = 40.416876;
      //            lon = -3.703304;
      //
      //            Config.getInstance().setValueUsuario("miLat", "" + lat);
      //            Config.getInstance().setValueUsuario("miLon", "" + lon);
      //            Config.getInstance().setValueUsuario("miZoom", "" + actualZoom);
      //         }
      //
      //         String mapType = Config.getInstance().getValueUsuario("vistaMapa",
      //               "normal");
      //
      //         // Se establece el tipo de mapa
      //         if (mapType.equals("satelite"))
      //            setMapType(GoogleMap.MAP_TYPE_SATELLITE);
      //
      //         // Centro el mapa sobre la iltima posiciin del usuario
      //         centerMap(new LatLng(lat, lon), actualZoom);

      // Recupero la iltima posiciin guardada y su precisiin
      lat = Double.parseDouble(Config.getInstance().getValueUsuario(
            "ultLat", "0"));
      lon = Double.parseDouble(Config.getInstance().getValueUsuario(
            "ultLon", "0"));
            /*acuGps = Float.parseFloat(Config.getInstance().getValueUsuario(
                    "ultAcu", "50"));

            // Preparo el circulo para ver la posiciin GPS
            circlePosOptions.strokeWidth(3);
            circlePosOptions.radius(acuGps);*/

      // Creo la ultima posiciin y dibujo el circulo
      if (lat != 0 && lon != 0) {
         ultGpsPos = new GPSInfo(new LatLng(lat, lon), 0);
         //circlePosOptions.center(ultGpsPos.position);

         // drawGpsPos(false);
      }

      // Recupero el ultimo estado del seguimiento GPS
      gpsTrack = (Integer.parseInt(Config.getInstance().getValueUsuario(
            "segGps", "0")) > 0);

      setGpsTrack(gpsTrack);
      setActionOnLoadApp();
      GesElemMapFragment.getInstance().showAreas();
      GesElemMapFragment.getInstance().showNivelesLlenado();
   }

   public static ArrayList<ItemMapa> getElemList() {
      return elemList;
   }

   public static ArrayList<ItemMapa> getFlotaList() {
      return flotaList;
   }


    /*public static ArrayList<ItemMapa> getSearchList() {
        return searchList;
    }*/

   /*
    * Para subir al frente el marcador. Esto se usa para que no se solapen.
    */
   private void topMarkerGuias() {
      //TODO: Subir el foco del marcador hacia arriba
   }

   public BitmapDescriptor getModelIcons(Integer i) {
      return modelIcons.get(i);
   }

   private void setActionOnLoadApp() {
      // Para cuando se abre la primera vez la app y tiene marcado meni por
      // defecto que requiere acciones.
      if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_ELEMENTOS ||
            MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_ELEMENTOS_IMAGEN ||
            MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN) {
         addMarkerCrearElementos();
      }

      if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_DEPOSITAR_ELEMENTOS) {
         addMarkerDepositarElemento();
      }

      if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_INCIDENCIAS) {
         addMarkerCrearIncidencia();
      }

      if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_ASIGNAR_INCIDENCIAS) {
         addButtonAsignarIncidencia();
      }

      /*********************/
   }

   public synchronized static void addMarkerCrearElementos() {
      if (mCrearElemento == null) {

         GesElemMapFragment.getInstance().setVisibleCrearElemento();  // .smallCrearElemento.setVisibility(View.VISIBLE);
         BitmapDescriptor icon = BitmapDescriptorFactory
               .fromResource(R.drawable.ic_action_crosshair);
         MarkerOptions markerOptions = new MarkerOptions()
               .position(map.getCameraPosition().target).draggable(false)
               .icon(icon);

         mCrearElemento = map.addMarker(markerOptions);
         MyLoggerHandler.getInstance().info("Entrando en modo crear elementos...");
      }
   }

   public synchronized static boolean isNotNullCrearElementos() {
      return mCrearElemento != null;
   }

   public synchronized static void addMarkerDepositarElemento() {
      if (mDepositarElemento == null) {

         GesElemMapFragment.getInstance().setVisibleDepositarElemento();
         BitmapDescriptor icon = BitmapDescriptorFactory
               .fromResource(R.drawable.ic_action_crosshair);
         MarkerOptions markerOptions = new MarkerOptions()
               .position(map.getCameraPosition().target).draggable(false)
               .icon(icon);

         mDepositarElemento = map.addMarker(markerOptions);
         MyLoggerHandler.getInstance().info("Entrando en modo depositar elemento...");
      }
   }

   public synchronized static void addMarkerCrearIncidencia() {
      if (mCrearIncidencia == null) {

         GesElemMapFragment.getInstance().setVisibleCrearIncidencia(); // smallCrearIncidencia.setVisibility(View.VISIBLE);
         BitmapDescriptor icon = BitmapDescriptorFactory
               .fromResource(R.drawable.ic_action_crosshair);
         MarkerOptions markerOptions = new MarkerOptions()
               .position(map.getCameraPosition().target).draggable(false)
               .flat(true)
               .icon(icon);

         mCrearIncidencia = map.addMarker(markerOptions);
         MyLoggerHandler.getInstance().info("Entrando en modo crear incidencias...");
      }
   }

   public synchronized static void addButtonAsignarIncidencia() {
      if (mAsignarIncidenciaFake == null) {
         mAsignarIncidenciaFake = "Esto es divertido";
         GesElemMapFragment.getInstance().setVisibleAsignarIncidencia(); // smallCrearIncidencia.setVisibility(View.VISIBLE);
         MyLoggerHandler.getInstance().info("Entrando en modo asignar incidencias...");
      }
   }

   public synchronized static void removeMarkerCrearElementos() {
      if (mCrearElemento != null) {

         GesElemMapFragment.getInstance().removeButtonCrearElemento(); // .smallCrearElemento.setVisibility(View.GONE);
         mCrearElemento.remove();
         mCrearElemento = null;
         MyLoggerHandler.getInstance().info("Saliendo del modo crear elementos...");
      }
   }

   public synchronized static void removeMarkerDepositarElementos() {
      if (mDepositarElemento != null) {

         GesElemMapFragment.getInstance().removeButtonDepositarElemento();
         mDepositarElemento.remove();
         mDepositarElemento = null;
         MyLoggerHandler.getInstance().info("Saliendo del modo depositar elemento...");
      }
   }

   public synchronized static void removeMarkerCrearIncidencia() {
      if (mCrearIncidencia != null) {

         GesElemMapFragment.getInstance().removeButtonCrearIncidencia();
         mCrearIncidencia.remove();
         mCrearIncidencia = null;
         MyLoggerHandler.getInstance().info("Saliendo del modo crear incidencias...");
      }
   }

   public synchronized static void removeButtonAsignarIncidencia() {
      if (mAsignarIncidenciaFake != null) {
         mAsignarIncidenciaFake = null;
         GesElemMapFragment.getInstance().removeButtonAsignarIncidencia();
         MyLoggerHandler.getInstance().info("Saliendo del modo asignar incidencias...");
      }
   }

   static InfoDialog dialog = null;

   public synchronized void CrearElemento(final Tags tag, final ITag iTag) {

      try {
         final LatLng markerPos = mCrearElemento.getPosition();

         if (markerPos != null && !ElementAreaBoundsValidator.isInside(MainActivity.getInstance(), markerPos)) {
            return;
         }

         GesElemMapFragment.getInstance().setEnabledCrearElemento(false);
         Resources res = MainActivity.getInstance().getResources();
         mCrearElemento.setVisible(false);
         if ((int) map.getCameraPosition().zoom < MINZOOM_CLICKABLE) {

            Toast.makeText(
                  MainActivity.getInstance(),
                  MainActivity.getInstance().getResources()
                        .getString(R.string.ZoomNoDisponible),
                  Toast.LENGTH_SHORT).show();

            GesElemMapFragment.getInstance().setEnabledCrearElemento(true);
            mCrearElemento.setVisible(true);
            return;
         }

         if (dialog == null) {
            double lng =  mCrearElemento.getPosition().longitude;
            dialog = new InfoDialog(
                  MainActivity.getInstance(),
                  res.getString(R.string.atencion),
                  res.getString(R.string.questionCrearElemento),
                  InfoDialog.ICON_QUESTION,
                  new OnInfoDialogSelect() {
                     @Override
                     public void onSelectOption(int option) {

                        if (option == InfoDialog.BUTTON_YES) {
                           MainActivity.abrirAddElementActivity(iTag, tag);
                        }
                        dialog = null;
                     }
                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                  InfoDialog.POSITION_CENTER);
            dialog.show();
         }

         GesElemMapFragment.getInstance().setEnabledCrearElemento(true);
         mCrearElemento.setVisible(true);
      } catch (Throwable e) {
         e.printStackTrace();
      }
   }

   public synchronized static void DepositarElemento() {

      try {
         if (mDepositarElemento == null)
            return;
         if ((int) map.getCameraPosition().zoom < MINZOOM_CLICKABLE) {

            Toast.makeText(
                  MainActivity.getInstance(),
                  MainActivity.getInstance().getResources()
                        .getString(R.string.ZoomNoDisponible),
                  Toast.LENGTH_SHORT).show();

            GesElemMapFragment.getInstance().setEnabledDepositarElemento(true);
            mDepositarElemento.setVisible(true);
            return;
         }

         final LatLng markerPos = mDepositarElemento.getPosition();

         if (!ElementAreaBoundsValidator.isInside(MainActivity.getInstance(), markerPos)) {
            return;
         }

         GesElemMapFragment.getInstance().setEnabledDepositarElemento(false);
         Resources res = MainActivity.getInstance().getResources();
         mDepositarElemento.setVisible(false);

         // Se muestra un cuadro de diálogo para preguntar si se quiere depositar un elemento
         // en la posición indicada. Si se confirma, se lanza la actividad de depositar elemento.
         if (dialog == null) {
            dialog = new InfoDialog(
                  MainActivity.getInstance(),
                  res.getString(R.string.atencion),
                  res.getString(R.string.questionDepositarElemento),
                  InfoDialog.ICON_QUESTION,
                  new OnInfoDialogSelect() {
                     @Override
                     public void onSelectOption(int option) {
                        if (option == InfoDialog.BUTTON_YES) {
                           Intent i = new Intent(MainActivity
                                 .getInstance(), DepositarElemActivity.class);
                           i.putExtra("latitud", markerPos.latitude);
                           i.putExtra("longitud", markerPos.longitude);
                           MainActivity.getInstance().startActivity(i);
                        }
                        dialog = null;
                     }
                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                  InfoDialog.POSITION_CENTER);
            dialog.show();
         }

         GesElemMapFragment.getInstance().setEnabledDepositarElemento(true);
         mDepositarElemento.setVisible(true);
      } catch (Throwable e) {
         e.printStackTrace();
      }
   }

   public synchronized static void crearIncidencia() {
      Resources res = MainActivity.getInstance().getResources();
      GesElemMapFragment.getInstance().setEnabledCrearIncidencia(false); //.smallCrearIncidencia.setEnabled(false);
      mCrearIncidencia.setVisible(false);
      if ((int) map.getCameraPosition().zoom < MINZOOM_CLICKABLE) {

         Toast.makeText(
               MainActivity.getInstance(),
               MainActivity.getInstance().getResources()
                     .getString(R.string.ZoomNoDisponible),
               Toast.LENGTH_SHORT).show();

         GesElemMapFragment.getInstance().setEnabledCrearIncidencia(true);
         mCrearIncidencia.setVisible(true);
         return;
      }

      if (dialog == null) {

         // Si se pulsa más de una vez con un intervalo inferior a 3
         // segundos no hago nada para evitar la reentrada
         if (elementoSeleccionado == null) {
            dialog = new InfoDialog(
                  MainActivity.getInstance(),
                  res.getString(R.string.atencion),
                  res.getString(R.string.questionCrearIncidencia),
                  InfoDialog.ICON_QUESTION,
                  new OnInfoDialogSelect() {
                     @Override
                     public void onSelectOption(int option) {

                        if (option == InfoDialog.BUTTON_YES) {

                           Intent addIncidenciaActivity = new Intent(
                                 MainActivity.getInstance(),
                                 AddInciActivity.class);
                           addIncidenciaActivity
                                 .putExtra(
                                       "latitud",
                                       mCrearIncidencia
                                             .getPosition().latitude);
                           addIncidenciaActivity
                                 .putExtra(
                                       "longitud",
                                       mCrearIncidencia
                                             .getPosition().longitude);

                           MainActivity.getInstance().startActivity(
                                 addIncidenciaActivity);
                        }

                        dialog = null;
                     }
                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                  InfoDialog.POSITION_CENTER);
         } else {
            final int IdExterno = elementoSeleccionado.getIdExterno();
            final LatLng position = elementoSeleccionado.getPosition();
            String message;
            if (Environment.isSoftIndra)
               message = elementoSeleccionado.getNombre() + " "
                     + "\nCod físico: " + elementoSeleccionado.getCodFisico();
            else
               message = elementoSeleccionado.toString();

            dialog = new InfoDialog(MainActivity.getInstance(), res.getString(R.string.atencion),
                  res.getString(R.string.warningCrearIncidenciaSobreElemento)
                        + "\n" + message
                        + "\n" + res.getString(R.string.questionCrearIncidenciaConfirm),
                  InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
               @Override
               public void onSelectOption(int option) {
                  if (option == InfoDialog.BUTTON_YES) {

                     // sobre elemento.
                     Intent addIncidenciaActivity = new Intent(
                           MainActivity.getInstance(),
                           AddInciActivity.class);
                     addIncidenciaActivity.putExtra("elemento",
                           IdExterno);
                     addIncidenciaActivity.putExtra("latitud",
                           position.latitude);
                     addIncidenciaActivity.putExtra("longitud",
                           position.longitude);

                     addIncidenciaActivity.putExtra("usuario",
                           MainActivity.getInstance()
                                 .getUsuario());

                     MainActivity.getInstance().startActivity(
                           addIncidenciaActivity);
                  }

                  dialog = null;
               }
            }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                  InfoDialog.POSITION_CENTER);
         }

         dialog.show();
      }

      GesElemMapFragment.getInstance().setEnabledCrearIncidencia(true);
      mCrearIncidencia.setVisible(true);
   }

   public synchronized static void asignarIncidencia() {
      GesElemMapFragment.getInstance().setEnabledAsignarIncidencia(false);

      Intent asignarIncidenciaActivity = new Intent(MainActivity.getInstance(), AsignarIncidenciasActivity.class);
      MainActivity.getInstance().startActivity(asignarIncidenciaActivity);

      GesElemMapFragment.getInstance().setEnabledAsignarIncidencia(true);
   }

   /* @Override
    public void onPolylineClick(Polyline polyline) {

        Log.e("Polylinea: ",polyline.getPoints()+"");





        LayoutInflater inflater = MainActivity.getInstance()
                .getLayoutInflater();
        View layout = inflater.inflate(R.layout.toast_custom,
                (ViewGroup) MainActivity.getInstance()
                        .findViewById(R.id.toast_layout_root));


        for (ItemMapa i: historicoRutaList) {

            FlotaPosicionesHistorico f = (FlotaPosicionesHistorico) i;

            if (f.getPosition().equals(polyline.getTag())){

                TextView text = (TextView) layout.findViewById(R.id.text);
                text.setText(String.format("%s\n%s\n%s", "Posición ", "Fecha :"+((FlotaPosicionesHistorico) i).getFecha()," Velocidad: "+((FlotaPosicionesHistorico) i).getVelocidad()));

                if (toast != null)
                    toast.cancel();

                toast = new Toast(MainActivity.getInstance()
                        .getApplicationContext());
                toast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
                toast.setDuration(Toast.LENGTH_LONG);
                toast.setView(layout);
                toast.show();
                break;
            }
        }
    }*/

   final Object receiverGestionElementosLock = new Object();
   // Our handler for received Intents. This will be called whenever an Intent
   // with an action named "custom-event-name" is broadcasted.
   public class receiverGestionElementos extends BroadcastReceiver {
      @Override
      public void onReceive(Context context, Intent intent) {
         // Get extra data included in the Intent
         synchronized (receiverGestionElementosLock) {
            String action = intent.getAction();
            GesElemMapFragment fragmentElements = GesElemMapFragment.getInstance();

            if (action.equals(MSG_REFRESH_CLUSTER_BR)) {
               int minZoomCluster = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_CLUSTER, "14"));
               int zoomActual = getActualZoom();

               if (elemCluster != null)
                  elemCluster.cluster();

               if (zoomActual < minZoomCluster) {
                  GesElemMapFragment.getInstance().removeMarkersOperationsMode();
                  GesElemMapFragment.getInstance().removeMarkersNivelesLlenado();
                  GesElemMapFragment.getInstance().removeMarkersDiasBloqueo();
                  return;
               }

               if (!DBSynchro.getInstance().getSynchro()) {
                  fragmentElements.showNivelesLlenado();

                  // para la app versión 202 de Camacho, mostraré en el mapa los elementos con una X roja, que coincidan con el día de bloqueo.
                  if (Environment.isSoftCamacho)
                     fragmentElements.showDiasBloqueoMode();
               }
            }

            if (action.equals(MSG_REFRESH_MAP_BR)) {
               drawMap();
            }

            if (action.equals(MSG_REFRESH_INCIDENCIAS_BR)) {
               //refreshIncidencias();
               elemCluster.cluster();
               //refreshMap();
            }

            if (action.equals(MSG_DELETE_ITEM_CLUSTER_BR)) {
               int id = (Integer) intent.getExtras().get("id-item");
               deleteElementoToClusterById(id);
            }

            if (action.equals(MSG_UPDATE_ITEM_CLUSTER_BR)) {
               int id = (Integer) intent.getExtras().get("id-item");
               Elemento elemento = (Elemento) intent.getExtras().get(
                     "item");
               updateElementoToClusterById(id, elemento);
            }

            if (action.equals(MSG_ADD_ITEM_CLUSTER_BR)) {
               Elemento elemento = (Elemento) intent.getExtras().get("item");
               addElemento(elemento);
            }

            if (action.equals(MSG_ADD_INCIDENCIA_FOTO_BR)) {
               IncidenciaFoto foto = (IncidenciaFoto) intent.getExtras()
                     .get("item");
               int idFoto = addIncidenciaFoto(foto);
               Intent i = new Intent(
                     UpdateEstadoIncidencia.EVENT_UPDATE_ESTADO_INCIDENCIA);

               // Este broadcast llega a algún sitio? En UpdateEstadoIncidencia no se recibe...
               i.setAction(UpdateEstadoIncidencia.MSG_SET_ID_FOTO_BR);
               i.putExtra("id-foto", idFoto);
               i.putExtra("item", foto);
               // update-estado-incidencia-event
               // intent.

               context.sendBroadcast(i);
            }

            if (action.equals(MSG_UPDATE_INCIDENCIA_BR)) {
               Incidencia incidencia = (Incidencia) intent.getExtras()
                     .get("item");
               updateIncidencia(incidencia);
            }

            if (action.equals(MSG_SET_MAP_TYPE_BR)) {
               int typeMap = (Integer) intent.getExtras().get("type");
               setMapType(typeMap);
            }

            if (action.equals(MSG_CENTER_GPS_POS_BR)) {
               boolean isCenterGPS = (Boolean) intent.getExtras().get(
                     "is-center");
               centerGpsPos(isCenterGPS);
            }

            if (action.equals(MSG_GPS_TRACK_BR)) {
               boolean isTrackGPS = (Boolean) intent.getExtras().get("is-checked");
               setGpsTrack(isTrackGPS);
               GesElemMapFragment.notifyGpsTrackingStateChanged();
            }

            if (action.equals(MSG_CENTER_MAP_BR)) {

               LatLng posicion = (LatLng) intent.getExtras().get(
                     "position");

               int zoom = (Integer) intent.getExtras().get("zoom");
               String title = intent.getExtras().get("title").toString();
               boolean addMarker = intent.getExtras().getBoolean(
                     "addMarker");

               centerMap(posicion, zoom, addMarker, title);
            }

            if (action.equals(MSG_SET_VISIBLE_ESTADO_INCIDENCIA_BR)) {

               int id = (Integer) intent.getExtras().get("id-estado");
               boolean visible = (Boolean) intent.getExtras().get("is-visible");

               setVisibleEstadoIncidencia(id, visible);
            }

            if (action.equals(MSG_TOGGLE_VISIBLE_ESTADO_INCIDENCIA_BR)) {

               int id = (Integer) intent.getExtras().get("id-estado");
               boolean visible = isVisibleEstadoIncidencia(id);

               setVisibleEstadoIncidencia(id, !visible);
            }

            if (action.equals(MSG_SET_VISIBLE_TIPO_INCIDENCIA_BR)) {
                try {
                    int idExterno = (int) intent.getExtras().get("id-tipo");
                    boolean visible = (Boolean) intent.getExtras().get("is-visible");

                    setVisibleTipoIncidencia(idExterno, visible);
                } catch (Exception e) {
                    Toast.makeText(
                            AppContext.getContext(),
                            R.string.error_al_establecer_la_visibilidad_del_tipo_de_incidencia,
                            Toast.LENGTH_SHORT
                    ).show();
                }
            }

            if (action.equals(MSG_TOGGLE_VISIBLE_TIPO_INCIDENCIA_BR)) {
                try {
                    int idExterno = (int) intent.getExtras().get("id-tipo");
                    boolean visible = isVisibleTipoIncidencia(idExterno);

                    setVisibleTipoIncidencia(idExterno, !visible);
                } catch (Exception e) {
                    Toast.makeText(
                            AppContext.getContext(),
                            R.string.error_al_alternar_la_visibilidad_del_tipo_de_incidencia,
                            Toast.LENGTH_SHORT
                    ).show();
                }
            }

            if (action.equals(MSG_SET_VISIBLE_MODELO_ELEMENTO_BR)) {
               int id = (Integer) intent.getExtras().get("id-modelo");
               boolean visible = (Boolean) intent.getExtras().get("is-visible");

               setVisibleModeloElemento(id, visible);
            }

            if (action.equals(MSG_TOGGLE_VISIBLE_MODELO_ELEMENTO_BR)) {
               int id = (Integer) intent.getExtras().get("id-modelo");
               boolean visible = isVisibleModeloElemento(id);

               setVisibleModeloElemento(id, !visible);
            }

            if (action.equals(MSG_TOGGLE_CLICKMAP)) {
               if (!isClickMap) {
                  // map.setOnMarkerClickListener(instance);
                  map.setOnMapClickListener(instance);
                  isClickMap = true;
               } else {
                  // map.setOnMarkerClickListener(null);
                  map.setOnMapClickListener(null);
                  isClickMap = false;
               }
            }

            if (action.equals(MSG_CREATE_MARKER_CREAR_ELEMENTO)) {
               addMarkerCrearElementos();
            }

            if (action.equals(MSG_REFRESH_MODELS)) {
               loadModels();
            }

            if (action.equals(MSG_CREATE_MARKER_CREAR_INCIDENCIA)) {
               addMarkerCrearIncidencia();
            }

            if (action.equals(MSG_REMOVE_MARKER_CREAR_INCIDENCIA)) {
               removeMarkerCrearIncidencia();
            }

            if (action.equals(MSG_CREATE_BUTTON_ASIGNAR_INCIDENCIA)) {
               addButtonAsignarIncidencia();
            }

            if (action.equals(MSG_REMOVE_BUTTON_ASIGNAR_INCIDENCIA)) {
               removeButtonAsignarIncidencia();
            }

            if (action.equals(MSG_REMOVE_MARKER_CREAR_ELEMENTO)) {
               removeMarkerCrearElementos();
            }

            if (action.equals(MSG_CREATE_MARKER_DEPOSITAR_ELEMENTO)) {
               addMarkerDepositarElemento();
            }

            if (action.equals(MSG_REMOVE_MARKER_DEPOSITAR_ELEMENTO)) {
               removeMarkerDepositarElementos();
            }

            if (action.equals(MSG_CLICK_FLOAT_BUTTON_CREAR_ELEMENTO)) {
               Tags tag = null;
               ITag iTag = null;
               Bundle extras = intent.getExtras();
               if (extras != null) {
                  tag = (Tags) extras.get("tag");
                  iTag = (ITag) extras.get("iTag");
               }
               CrearElemento(tag, iTag);
            }

            if (action.equals(MSG_CLICK_FLOAT_BUTTON_DEPOSITAR_ELEMENTO)) {
               DepositarElemento();
            }

            if (action.equals(MSG_CLICK_FLOAT_BUTTON_CREAR_INCIDENCIA)) {
               crearIncidencia();
            }

            if (action.equals(MSG_CLICK_FLOAT_BUTTON_ASIGNAR_INCIDENCIA)) {
               asignarIncidencia();
            }

            if (action.equals(MSG_UPDATE_ELEMENTO)) {

               Elemento elemento = (Elemento) intent.getExtras().get("elemento");
               ItemMapa itemMapa = null;

               MainActivity.getInstance().updateElementoBusqueda(elemento);
               if (elemList != null) {
                  for (ItemMapa elem : elemList) {
                     if (elem != null && elem.getId() == elemento.getId()
                           && elemento.getTipoElemento().equals("E")) {
                        itemMapa = elem;
                        break;
                     }
                  }
               }

               if (itemMapa == null) {

                  if (elemList == null)
                     elemList = new ArrayList<ItemMapa>();

                  // Añado el elemento a la lista en memoria y al cluster
                  elemCluster.addItem(elemento);
                  elemList.add(elemento);
                  itemMapa = elemList.get(elemList.size() - 1);
               }

               itemMapa.setFromRfID(true);
               getInstance().onClusterItemClick(itemMapa);
            }

            if (action.equals(MSG_INCIDENCIA_ELEMENTO)) {

               Elemento elemento = (Elemento) intent.getExtras().get("elemento");
               elementoSeleccionado = elemento;

               crearIncidencia();
            }

            if (action.equals(MSG_REMOVE_MARKERS_FLOTA)) {

               removeMarkersFlota();
            }

            if (action.equals(MSG_REFRESH_HISTORICO_RUTAS)) {

               int rutaAMostrar = (int) intent.getExtras().get("rutaId");
               int tiempoParada = (int) intent.getExtras().get("tiempoParada");
               refreshHistoricoRuta(rutaAMostrar, tiempoParada);
            }
         }
      }

      private void refreshHistoricoRuta(int rutaId, int tiempoParada) {

         // INI - 30/01/2020 - DINIGO
         // Se elimina el objeto polylineOption de la clase, ya que no volver a instanciarlo provocaba que la ruta anterior no se eliminase. Cada vez que se mostraba una ruta se iban almacenando.
         PolylineOptions polylineOptions = new PolylineOptions();
         // FIN - 30/01/2020 - DINIGO

         // la primera ruta la pintaré en verde, las demás, en su caso, un color random.
         int colorRuta = Color.rgb(0, 146, 0);
         polylineOptions.color(colorRuta).width(18).geodesic(true);

         historicoRutaList = new ArrayList<ItemMapa>();
         DBFlotaPosiciones dbPosFlota = new DBFlotaPosiciones();
         historicoRutaList = dbPosFlota.getHistoricoRuta(MainActivity.getInstance()
               .getEmpresa(), MainActivity.getInstance().getUsuario(), rutaId);

         dbPosFlota.close();

         // marcadores para las posiciones
         MarkerOptions options = new MarkerOptions();
         options.draggable(false);

         // marcadores para las paradas
         MarkerOptions optionsParada = new MarkerOptions();
         optionsParada.draggable(false);
         optionsParada.icon(Utils.getBitmapDescriptorFromVectorDrawable(
               MainActivity.getInstance(), R.drawable.parada));

         // si no hay más de 2 posiciones de ruta, no puedo pintar, pues necesito al menos un inicio y fin
         if (historicoRutaList.size() > 2) {

            // pinto los iconos de inicio y fin de ruta
            options.position(historicoRutaList.get(0).getPosition());
            options.icon(Utils.getBitmapDescriptorFromVectorDrawable(
                  MainActivity.getInstance(), R.drawable.inicio_ruta));
            marcadoresFlota.add(map.addMarker(options));
            options.position(historicoRutaList.get(historicoRutaList.size() - 1).getPosition());
            options.icon(Utils.getBitmapDescriptorFromVectorDrawable(
                  MainActivity.getInstance(), R.drawable.fin_ruta));

            marcadoresFlota.add(map.addMarker(options));

            centerMap(historicoRutaList.get(0).getPosition(), actualZoom);

            FlotaPosicionesHistorico fph;
            for (ItemMapa i : historicoRutaList) {
               fph = (FlotaPosicionesHistorico) i;
               polylineOptions.add(i.getPosition());

               if (fph.getTiempoParada() >= (tiempoParada * 60000)) { //getTiempoParada()>= TimeUnit.MINUTES.toMillis(tiempoParada)){

                  optionsParada.position(fph.getPosition());
                  marcadoresFlota.add(map.addMarker(optionsParada));
               }
            }

            if (polyline != null) {
               polyline.remove();
            }

            polyline = map.addPolyline(polylineOptions);
            polyline.setClickable(false);
         }
            /*List<PatternItem> pattern = Arrays.asList(
                    new Gap(15), new Dash(15), new Gap(15));
            polyline.setPattern(pattern);
            polyline.setJointType(JointType.ROUND);*/

      }


        /*private void refreshIncidencias() {
            // Antes de nada borro todas las incidencias del mapa
            if (inciList != null)
                for (ItemMapa inci : inciList)
                    if (inci.getMarker() != null)
                        inci.getMarker().remove();

            inciList = null;

            // Recupero la configuraciin del usuario en cuanto
            // a incidencias visibles
            String filtro = Config.getInstance().getValueUsuario(
                    "inciVisibles", "ninguno");

            if (!filtro.equals("ninguno")) {

                // Cargo las incidencias
                DBIncidencia dbInci = new DBIncidencia();

                // Cargo silo las visibles
                if (filtro.equals("todos"))
                    inciList = dbInci.getAll(MainActivity.getInstance()
                            .getEmpresa(), MainActivity.getInstance().getUsuario());
                else if (!filtro.equals("ninguno"))
                    inciList = dbInci.getAllByEstado(MainActivity.getInstance()
                                    .getEmpresa(), MainActivity.getInstance().getUsuario(),
                            filtro);

                dbInci.close();
            }

            // Aiado todas las incidencias al mapa
            if (inciList != null) {
                elemCluster.addItems(inciList);
            }

        }*/
   }

   /**
    * Carga en memoria las tablas hash con los iconos y la visibilidad de cada
    * modelo
    */
   public static void loadModels() {

      try {

         // Obtengo todos los modelos y meto en una tabla hash
         // todos los iconos
         DBElementoModelo dbModelo = new DBElementoModelo();
         ArrayList<ElementoModelo> modelos = dbModelo.getAll(MainActivity
               .getInstance().getEmpresa());

         if (modelIcons != null)
            modelIcons.clear();

         if (modelVisible != null)
            modelVisible.clear();

         modelIcons = new Hashtable<Integer, BitmapDescriptor>(
               dbModelo.getCount(MainActivity.getInstance().getEmpresa()));
         modelVisible = new Hashtable<Integer, Boolean>(
               dbModelo.getCount(MainActivity.getInstance().getEmpresa()));

         dbModelo.close();

         // Recupero la configuraciin del usuario en cuanto a modelos visibles
         String visModels = Config.getInstance().getValueUsuario("modelVisibles", "todos");

         if (modelos == null)
            return;

         for (ElementoModelo modelo : modelos) {

            try {

               // Redimensiono el icono
               Bitmap image = Utils.GetBitmapToArrayBytes(modelo.getIcono());
               if (image == null)
                  image = BitmapFactory.decodeResource(MainActivity.getInstance().getResources(), R.drawable.modelo_sin_icono);

               int nh = (int) (image.getHeight() * (512.0 / image.getWidth()));
               Bitmap iconoModelo = Photo.getInstance().scaleBitmap(image, (float) 1.9); // scaleWithAspectRatio(MainActivity.getInstance(), image); // Bitmap.createScaledBitmap(image, 512, nh, true);

               int desiredHeight = 45;
               if (iconoModelo.getHeight() < desiredHeight) {
                  // Calcula el ancho proporcional manteniendo la relación de aspecto original y escalo
                  int desiredWidth = (int) ((float) desiredHeight / image.getHeight() * image.getWidth());
                  iconoModelo = Bitmap.createScaledBitmap(image, desiredWidth, desiredHeight, true);
               }

               BitmapDrawable bmp = new BitmapDrawable(MainActivity.getInstance().getResources(), iconoModelo);
               // Meto el icono en la tabla hash
               modelBitmaps.put(modelo.getIdExterno(), iconoModelo);
               modelIcons.put(modelo.getIdExterno(), BitmapDescriptorFactory.fromBitmap(bmp.getBitmap()));

               // Asigno la visibilidad del icono dependiendo de la
               // configuraciin del ususario
               if (visModels.equals("todos"))
                  modelVisible.put(modelo.getIdExterno(), true);
               else if (visModels.equals("ninguno"))
                  modelVisible.put(modelo.getIdExterno(), false);
               else {

                  modelVisible.put(modelo.getIdExterno(), false);

                  String arrayModels[] = visModels.split(",");

                  // Si esti en la lista lo pongo como visible
                  for (String id : arrayModels) {

                     if (Integer.parseInt(id) == modelo.getIdExterno()) {

                        modelVisible.put(modelo.getIdExterno(), true);

                        break;
                     }
                  }
               }
            } catch (Throwable e) {
               MyLoggerHandler.getInstance().error(e);
               e.printStackTrace();
            }
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Carga en memoria las tablas hash con la visibilidad de las incidencias
    * segin estados
    */
   public static void loadEstadosInci() {

      try {

         // Obtengo todos los estados y los meto en una tabla hash
         DBEstados dbEstados = new DBEstados();
         ArrayList<Estado> estados = dbEstados.getAll(MainActivity
               .getInstance().getEmpresa());

         if (inciVisible != null)
            inciVisible.clear();

         inciVisible = new Hashtable<Integer, Boolean>(
               dbEstados.getCount(MainActivity.getInstance().getEmpresa()));

         dbEstados.close();

         // Recupero la configuraciin del usuario en cuanto a modelos
         // visibles
         String visModels = Config.getInstance().getValueUsuario(
               "inciVisibles", "ninguno");
         if (estados == null)
            return;
         for (Estado estadoInci : estados) {

            try {

               // Asigno la visibilidad del dependiendo de la
               // configuraciin del ususario
               if (visModels.equals("todos"))
                  inciVisible.put(estadoInci.getIdExterno(), true);
               else if (visModels.equals("ninguno"))
                  inciVisible.put(estadoInci.getIdExterno(), false);
               else {

                  inciVisible.put(estadoInci.getIdExterno(), false);

                  String arrayModels[] = visModels.split(",");

                  // Si esti en la lista lo pongo como visible
                  for (String id : arrayModels) {

                     if (Integer.parseInt(id) == estadoInci
                           .getIdExterno()) {

                        inciVisible
                              .put(estadoInci.getIdExterno(), true);

                        break;
                     }
                  }
               }
            } catch (Throwable e) {
               MyLoggerHandler.getInstance().error(e);
               e.printStackTrace();
            }
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Carga en memoria las tablas hash con la visibilidad de las incidencias
    * según tipos
    */
   public static void loadTiposInci() {

      try {

         // Obtengo todos los tipos y los meto en una tabla hash
         DBIncidenciaTipo dbTipos = new DBIncidenciaTipo();
         ArrayList<IncidenciaTipo> tipos = dbTipos.getAll(MainActivity
               .getInstance().getEmpresa());

         if (inciTipoVisible != null)
            inciTipoVisible.clear();

         inciTipoVisible = new Hashtable<Integer, Boolean>();

         dbTipos.close();

         // Recupero la configuración del usuario en cuanto a tipos visibles
         String visTipos = Config.getInstance().getValueUsuario(
               "tiposInciVisibles", "todos");
         if (tipos == null)
            return;
         for (IncidenciaTipo tipoInci : tipos) {

            try {

               // Asigno la visibilidad del dependiendo de la
               // configuración del usuario
               if (visTipos.equals("todos")) {
                  inciTipoVisible.put(tipoInci.getIdExterno(), true);
               }
               else if (visTipos.equals("ninguno")) {
                  inciTipoVisible.put(tipoInci.getIdExterno(), false);
               } else {
                  inciTipoVisible.put(tipoInci.getIdExterno(), false);
                  String arrayTipos[] = visTipos.split(",");

                  // Si está en la lista lo pongo como visible
                  for (String idExterno : arrayTipos) {
                     if (Integer.parseInt(idExterno) == tipoInci.getIdExterno()) {
                        inciTipoVisible.put(tipoInci.getIdExterno(), true);
                        break;
                     }
                  }
               }
            } catch (Throwable e) {
               MyLoggerHandler.getInstance().error(e);
               e.printStackTrace();
            }
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Esta funciin hay que llamarla cuando se descargue el fragmento o se
    * cierre la aplicaciin
    */
   public void end() {

      try {

         // Guardo paquetes de posiciones para enviar

         // Cierro el listener GPS para que se descargue el controlador
         if (locationManager != null && locationListener != null)
            locationManager.removeUpdates(locationListener);

         locationManager = null;
         locationListener = null;

         map = null;

         instance = null;

         if (receiver != null)
            LocalBroadcastManager.getInstance(AppContext.getContext())
                  .unregisterReceiver(receiver);

         mCrearElemento = null;
         mCrearIncidencia = null;
         inciVisible = null;
         modelVisible = null;
         elementoSeleccionado = null;
      } catch (SecurityException se) {
         MyLoggerHandler.getInstance().error(se);
         se.printStackTrace();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Para ver desde dónde se llamaa un método
    * @return String con los métodos que han llamado a este
    */
   public static String getLastMethods() {
      String result = "";
      StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
      for (int i = 3; i < stackTrace.length; i++) {
         result += stackTrace[i].getMethodName() + " ";
      }
      return result;
   }

   /**
    * Pinta el mapa con los elementos
    */
   public void drawMap() {
      try {
         if (map == null) return;

         Logg.error(TAG, getLastMethods());

         if (runnable2 != null) {
            handler.removeCallbacks(runnable2);
         }

         if (drawMapTask != null) {
            drawMapTask.cancel(true);
         }

         runnable2 = () -> {
            LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
            String filtroElementos = Config.getInstance().getValueUsuario("modelVisibles", "todos");
            String filtroIncidencias = Config.getInstance().getValueUsuario("inciVisibles", "ninguno");
            String filtroTiposIncidencias = Config.getInstance().getValueUsuario("tiposInciVisibles", "todos");

            drawMapTask = new DrawMapTask(bounds, filtroElementos, filtroIncidencias, filtroTiposIncidencias);
            drawMapTask.executeOnExecutor(AsyncTask.SERIAL_EXECUTOR);
         };

         int delay = DBSynchro.getInstance().getSynchro() ? 300 : 100;
         handler.postDelayed(runnable2, delay);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   private class DrawMapTask extends AsyncTask<Void, Void, Boolean> {
      private LatLngBounds bounds;
      private String filtroElementos;
      private String filtroIncidencias;
      private String filtroTiposIncidencias;
      private Throwable error;
      private long inicio;

      public DrawMapTask(LatLngBounds bounds, String filtroElementos, String filtroIncidencias, String filtroTiposIncidencias) {
         this.bounds = bounds;
         this.filtroElementos = filtroElementos;
         this.filtroIncidencias = filtroIncidencias;
         this.filtroTiposIncidencias = filtroTiposIncidencias;
      }
      
      @Override
      protected Boolean doInBackground(Void... voids) {
         try {
            long inicio = System.currentTimeMillis();
            if (isCancelled()) return false;

            if (elemList == null)
               elemList = new ArrayList<>();
            else elemList.clear();

            if (!filtroElementos.equals("ninguno")) {
               ArrayList<Elemento> elementosTemp = new ArrayList<>(MainActivity.getInstance().getElementos());
               // Si no hay elementos en memoria, los cargo de la base de datos
               if (elementosTemp.isEmpty()) {
                  Log.e(TAG, "No hay elementos en memoria, los cargo de la base de datos");
                  DBElemento dbElem = new DBElemento(true);
                  elemList.addAll(dbElem.getByLatLon(MainActivity.getInstance().getEmpresa(),
                        bounds.southwest.latitude,
                        bounds.southwest.longitude,
                        bounds.northeast.latitude,
                        bounds.northeast.longitude,
                        filtroElementos,
                        Elemento.ESTADO_ACTIVO));
                  dbElem.close();
               } else {
                  Log.e(TAG, "Hay elementos en memoria, los filtro");
                  // Filtro los elementos según la visibilidad del modelo y en la zona visible del mapa
                  boolean addAllModels = filtroElementos.equals("todos");
                  for (Elemento elem : elementosTemp) {
                     if (elem != null && elem.getEstado() == Elemento.ESTADO_ACTIVO &&
                           elem.getPosition().latitude >= bounds.southwest.latitude &&
                           elem.getPosition().latitude <= bounds.northeast.latitude &&
                           elem.getPosition().longitude >= bounds.southwest.longitude &&
                           elem.getPosition().longitude <= bounds.northeast.longitude &&
                           (addAllModels || isVisibleModeloElemento(elem.getModelo()))) {
                        elemList.add(elem);
                     }
                  }
               }

               if (isCancelled()) return false;

               // Rellenamos las listas elemCollected y elemClenead
               SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
               int minZoomCluster = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_CLUSTER, "14"));
               int minZoomElements = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_MINZOOM_NOSHOWCLUSTER, "11"));
               int actualZoom = getActualZoom();
               elemCollected.clear();
               elemClenead.clear();

               if (actualZoom >= minZoomCluster && actualZoom >= minZoomElements && (!DBSynchro.getInstance().getSynchro())) {
                  java.util.Date now = new java.util.Date();
                  int hoursCheckCollected = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_LEVEL, "18").trim());
                  int hoursCheckCleaned = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_CLEAN, "18").trim());
                  long fromDateCollected = HelperDates.getInstance().addHours(now, -hoursCheckCollected).getTime();
                  long fromDateCleaned = HelperDates.getInstance().addHours(now, -hoursCheckCleaned).getTime();

                  DBOperationsDone dbOperationsDone = new DBOperationsDone();
                  elemCollected.addAll(dbOperationsDone.getElementIdsWithCollectOrLevelFromDateAndLatLon(
                        fromDateCollected,
                        bounds.southwest.latitude,
                        bounds.southwest.longitude,
                        bounds.northeast.latitude,
                        bounds.northeast.longitude
                  ));
                  elemClenead.addAll(dbOperationsDone.getElementIdsWithCleanFromDateAndLatLon(
                        fromDateCleaned,
                        bounds.southwest.latitude,
                        bounds.southwest.longitude,
                        bounds.northeast.latitude,
                        bounds.northeast.longitude
                  ));
                  dbOperationsDone.close();
               }
            }

            if (isCancelled()) return false;

            if (inciList == null)
               inciList = new ArrayList<>();
            else inciList.clear();

            if (!filtroIncidencias.equals("ninguno")) {
               DBIncidencia dbInci = new DBIncidencia(true);
               ArrayList<ItemMapa> items =dbInci.getByLatLon(MainActivity.getInstance().getEmpresa(),
                     MainActivity.getInstance().getUsuario(),
                     bounds.southwest.latitude,
                     bounds.southwest.longitude,
                     bounds.northeast.latitude,
                     bounds.northeast.longitude,
                       filtroIncidencias, filtroTiposIncidencias);
               if (items != null) {
                  inciList.addAll(items);
               } else {
                  Logg.error(TAG, "inciList es null");
               }
               dbInci.close();
            }

            if (isCancelled()) return false;
            Log.e(TAG, "DrawMapTask took " + (System.currentTimeMillis() - inicio) + " ms");
            return true;
         } catch (Throwable e) {
            error = e;
            return false;
         }
      }

      @Override
      protected void onPostExecute(Boolean success) {
         if (success) {
            elemCluster.clearItems();
            if (flotaList != null && !flotaList.isEmpty()) {
               elemCluster.addItems(flotaList);
            }
            if (elemList != null && !elemList.isEmpty()) {
               elemCluster.addItems(elemList);
            }
            if (inciList != null && !inciList.isEmpty()) {
               elemCluster.addItems(inciList);
            }
            MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
         } else if (error != null) {
            error.printStackTrace();
            Logg.error(TAG, "Error al dibujar el mapa: " + error);
         }
      }
   }

   /**
    * Centra el mapa sin cambiar el nivel de zoom actual
    */
   public void centerMap(LatLng pos) {

      try {

         map.animateCamera(CameraUpdateFactory.newLatLngZoom(pos,
               elemCluster.getZoom()));
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Devuelve el nivel de zoom actual
    */
   public int getActualZoom() {
      int res = 14;

      try {

         if (elemCluster != null)
            res = (int) elemCluster.getZoom();
      } catch (Throwable e) {
      }

      return res;
   }

   /**
    * Centra el mapa y establece nivel de zoom
    */
   public void centerMap(LatLng pos, float zoom) {
      centerMap(pos, zoom, false, "");
   }

   public void centerMap(LatLng pos, float zoom, boolean addMarker,
         String title) {

      try {

         map.animateCamera(CameraUpdateFactory.newLatLngZoom(pos, zoom));

         if (addMarker) {

//            map.clear();

            MarkerOptions options = new MarkerOptions();

            options.draggable(false);
            options.position(pos);
            if (!title.equals("")) {
               options.icon(Photo.getInstance().textAsBitmapDescriptor(title,
                     pxFromDp(15, MainActivity.getInstance())
                     , Color.RED));
            } else
               options.icon(Utils.getBitmapDescriptorFromVectorDrawable(
                     MainActivity.getInstance(), R.drawable.ic_marker_flota));
            Marker m = map.addMarker(options);
            m.setZIndex(Float.MAX_VALUE);
            m.setTag((Runnable) () -> {
               try {
                  marcadoresFlota.remove(m);
                  m.remove();
               } catch (Throwable e) {
                  MyLoggerHandler.getInstance().error(e);
               }
            });
            marcadoresFlota.add(m);
            Handler handler = new Handler();
            handler.postDelayed(runnable, 5000); //quitamos marcadores a los 5 segundos
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   protected Runnable runnable = new Runnable() {
      @Override
      public void run() {
         cleanMarcadoresFlota();
      }

      private void cleanMarcadoresFlota() {

         if (marcadoresFlota != null && marcadoresFlota.size() > 0) {
            for (Marker marker :
                  marcadoresFlota) {
               marker.remove();
            }

            marcadoresFlota.clear();
         }
      }
   };

   public static float pxFromDp(float dp, Context mContext) {
      return dp * mContext.getResources().getDisplayMetrics().density;
   }

   /**
    * Cambia el tipo de mapa (normal o satilite)
    */
   public void setMapType(int type) {

      try {

         map.setMapType(type);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Activa o desactiva el seguimiento GPS
    */
   public void setGpsTrack(boolean state) {

      try {

         gpsTrack = state;
         Resources res = MainActivity.getInstance().getResources();
         Config.getInstance().setValueUsuario("segGps", "" + (state ? 1 : 0));

         Toast.makeText(MainActivity.getInstance(),
               res.getString(R.string.seguimientoGPS) + " " +
                     (state ? res.getString(R.string.activado) : res.getString(R.string.desactivado)),
               Toast.LENGTH_LONG).show();

         if (state) {
            // Preparo un listener para recibir posiciones GPS y/o
            // Wifi cada 15 segundos o cada 10 metros recorridos
            locationManager = (LocationManager) MainActivity.getInstance()
                  .getSystemService(MainActivity.LOCATION_SERVICE);
            locationListener = new MyLocationListener();
            // SOLO POSICION GPS
            // para testear sin posición GPS puede establecerse LocationManager.NETWORK_PROVIDER
            locationManager.requestLocationUpdates(
                  LocationManager.GPS_PROVIDER, TIEMPO_REFRESCO_POSICION, 0,
                  locationListener);
            //drawCircleGpsPos(state);
         } else {
            if (locationManager != null && locationListener != null)
               locationManager.removeUpdates(locationListener);
            //removeCircleGpsPos();
         }
      } catch (SecurityException se) {
         MyLoggerHandler.getInstance().error(se);
         se.printStackTrace();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Centra el mapa sin cambiar el nivel de zoom actual en la iltima posiciin
    * GPS vilida
    */
   public void centerGpsPos(boolean gpsPos) {

      // TODO AQUI
      try {

         synchronized (this) {

            if (ultGpsPos != null) {
               long horas = Utils.getDateDiff(ultGpsPos.datetime,
                     System.currentTimeMillis(), TimeUnit.HOURS);
               if (horas > 1) {
                  Toast.makeText(
                        MainActivity.getInstance(),
                        R.string.posicionMasDeUnaHora,
                        Toast.LENGTH_LONG).show();
               }

               centerMap(ultGpsPos.position, actualZoom);
            } else
               Toast.makeText(MainActivity.getInstance(),
                           R.string.atencionGPSObsoleta, Toast.LENGTH_LONG)
                     .show();

            // Muestro el circulo de la posisiin
            //drawCircleGpsPos(gpsPos);
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   /**
    * Dibuja un circulo con la posiciin GPS
    */
    /*private void drawCircleGpsPos(boolean gpsPos) {

        try {

            synchronized (this) {
                if (ultGpsPos == null) return;
                if (circlePos != null)
                    circlePos.remove();
                circlePos = null;

                circlePosOptions.strokeColor(colorGps1);
                circlePosOptions.fillColor(colorGps2);
                circlePosOptions.center(ultGpsPos.position);
                circlePosOptions.zIndex(0);
                circlePos = map.addCircle(circlePosOptions);
                circlePos.setRadius(acuGps);
                circlePos.setCenter(ultGpsPos.position);
                circlePos.setClickable(false);
                circlePos.setTag("POSITION");
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }

    private void removeCircleGpsPos() {

        try {

            synchronized (this) {

                if (circlePos != null)
                    circlePos.remove();
                circlePos = null;
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }*/

   public Bitmap combineImagesForOperations(Bitmap background, Bitmap foreground) {
      Bitmap scaledForeground = Bitmap.createScaledBitmap(foreground, background.getWidth(), background.getHeight(), false);

      Bitmap result = Bitmap.createBitmap(background.getWidth(), background.getHeight(), background.getConfig());

      Canvas canvas = new Canvas(result);
      canvas.drawBitmap(background, 0, 0, null);
      canvas.drawBitmap(scaledForeground, 0, 0, null);

      return result;
   }


   public Bitmap combineImagesForCollectFreq(Bitmap background, Bitmap foreground) {
      // Crear un bitmap mutable con el tamaño del fondo
      int totalWidth = background.getWidth() + (int) (foreground.getWidth() * 0.2);
      int totalHeight = background.getHeight() + (int) (foreground.getHeight() * 0.2);
      Bitmap combined = Bitmap.createBitmap(totalWidth, totalHeight, background.getConfig());
      Canvas canvas = new Canvas(combined);
      // Dibujar la imagen de fondo
      float left = (float) (foreground.getWidth() * 0.2); // Lo desplazamos un 20% a la derecha
      canvas.drawBitmap(background, left, 0f, null);
      // Dibujar la imagen del frente en la posición deseada
      float top = background.getHeight() - (float) (foreground.getHeight() * 0.8); // Un 80% hacia arriba respecto al bg
      canvas.drawBitmap(foreground, 0f, top, null);
      return combined;
   }

   public void marcarLavado(int idElemento) {
      elemClenead.add(idElemento);
      setIconoMarker(idElemento);
   }

   public void marcarRecogida(int idElemento) {
      elemCollected.add(idElemento);
      setIconoMarker(idElemento);
   }

   public void marcarNivelLlenado(int idElemento) {
      elemCollected.add(idElemento);
      setIconoMarker(idElemento);
   }

   public void setIconoMarker(int idElemento) {
      ItemMapa elemento = null;
      for (ItemMapa elem : elemList) {
         if (elem != null && elem.getId() == idElemento) {
            elemento = elem;
            break;
         }
      }

      Bitmap icon = getIconoElemento((Elemento) elemento);
      getMarker(elemento).setIcon(BitmapDescriptorFactory.fromBitmap(icon));
   }

   public Bitmap getIconoElemento(Elemento e) {
      Bitmap icon = modelBitmaps.get(e.getModelo());

      if (elemClenead.contains(e.getId())) {
         Bitmap cleanIcon = BitmapFactory.decodeResource(MainActivity.getInstance().getResources(), R.drawable.ico_ok3);
         icon = combineImagesForOperations(icon, cleanIcon);
      }

      if (elemCollected.contains(e.getId())) {
         Bitmap colletOrLevelIcon = BitmapFactory.decodeResource(MainActivity.getInstance().getResources(), R.drawable.ico_ok2);
         icon = combineImagesForOperations(icon, colletOrLevelIcon);
      }

      // Añadir el icono de la frecuencia de procesado
      String showCollectionFreq = sharedPref.getString(SettingsActivity.KEY_PREF_SHOW_COLLECTION_FREQ, "0");
      if (showCollectionFreq.equals("1")) {
         FrecuenciaProcesadoState state = e.getFrecuenciaProcesadoState();
         Bitmap foreground = GesElemMapFragment.getInstance().getBitMapFrecuencia(state);
         if (foreground != null) {
            icon = combineImagesForCollectFreq(icon, foreground);
         }
      }

      return icon;
   }

   /**
    * Este método se ejecuta cada vez que un marcador sale del cluster
    */
   @Override
   protected void onBeforeClusterItemRendered(ItemMapa elemento,
         MarkerOptions markerOptions) {
      super.onBeforeClusterItemRendered(elemento, markerOptions);

      try {
         if (modelIcons.size() <= 0)
            loadModels();

         //Icono elemento
         if (elemento.getTipoElemento().equals("E")) {           // Elemento
            Elemento e = (Elemento) elemento;
            markerOptions.position(e.getPosition());
            markerOptions.draggable(true);
            markerOptions.zIndex(0.5f);
            Bitmap icon = getIconoElemento(e);
            markerOptions.icon(BitmapDescriptorFactory.fromBitmap(icon));
         } else if (elemento.getTipoElemento().equals("I")) {    // Incidencia

            Incidencia i = (Incidencia) elemento;
            Bitmap bmp = null;
            Bitmap image = null;

            if (i.getElemento() > 0) {
               image = BitmapFactory.decodeResource(
                     MainActivity.getInstance().getResources(),
                     R.drawable.icon_inci_elem);
               bmp = Photo.getInstance().scaleBitmap(image, (float) 0.5);
            } else {
               image = BitmapFactory.decodeResource(
                     MainActivity.getInstance().getResources(),
                     R.drawable.icon_incidencias);
               bmp = Photo.getInstance().scaleBitmap(image, (float) 0.5);
            }
            if (bmp != null)
               markerOptions
                     .position(i.getPosition())
                     .icon(BitmapDescriptorFactory.fromBitmap(bmp)).draggable(false).zIndex(1.0f);
         } else if (elemento.getTipoElemento().equals("F")) {    // Móvil de Flota
            FlotaPosiciones flotaPosiciones = (FlotaPosiciones) elemento;
            BitmapDescriptor bitmapDescriptor = Utils.getBitmapDescriptorFromVectorDrawable(
                  MainActivity.getInstance(), R.drawable.ic_marker_flota);

            if (bitmapDescriptor != null)
               markerOptions.position(flotaPosiciones.getPosition()).icon(bitmapDescriptor);
         } else if (elemento.getTipoElemento().equals("R")) {    // Histórico de posiciones de la ruta

            /* TODO aquí pinto las rutas */
                /*FlotaPosicionesHistorico f = (FlotaPosicionesHistorico)elemento;
                markerOptions.position(elemento.getPosition())
                        .icon(Utils.getBitmapDescriptorFromVectorDrawable(
                                MainActivity.getInstance(), R.drawable.punt_puntoazul)).title(f.getFecha());*/

         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Devuelve si esti visible o no un modelo
    */
   public static boolean isVisibleModeloElemento(int model) {

      try {
         if (modelVisible == null)
            loadModels();
         if (modelVisible != null) {
            return Boolean.TRUE.equals(modelVisible.get(model));
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }

      return false;
   }

   /**
    * Pone visible o no un modelo
    */
   public void setVisibleModeloElemento(int model, boolean visible) {

      try {

         modelVisible.put(model, visible);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   /**
    * Devuelve si esti visible o no un estado de incidencia
    */
   public static boolean isVisibleEstadoIncidencia(int id) {

      try {
         if (inciVisible == null)
            loadEstadosInci();
         else
            return Boolean.TRUE.equals(inciVisible.get(id));
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }

      return false;
   }

   /**
    * Pone visible o no un estado de incidencia
    */
   public void setVisibleEstadoIncidencia(int id, boolean visible) {

      try {
         if (inciVisible != null)
            inciVisible.put(id, visible);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Devuelve si está visible o no un tipo de incidencia
    */
   public static boolean isVisibleTipoIncidencia(int idExterno) {

      try {
         // Check if filter menu is enabled remotely
         MainActivity mainActivity = MainActivity.getInstance();
         if (mainActivity == null || !mainActivity.isValidMenu(MainActivity.MENU_FILTRAR_TIPOS, "")) {
            // Menu disabled remotely → show ALL incident types
            return true;
         }
         
         // Menu enabled remotely → proceed with local preference logic
         if (inciTipoVisible == null)
            loadTiposInci();
         else
            return Boolean.TRUE.equals(inciTipoVisible.get(idExterno));
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }

      return false;
   }

   /**
    * Pone visible o no un tipo de incidencia
    */
   public void setVisibleTipoIncidencia(int id, boolean visible) {

      try {
         if (inciTipoVisible != null)
            inciTipoVisible.put(id, visible);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Este mitodo se ejecuta cada vez que un marcador entra en el cluster
    */
   @Override
   protected void onBeforeClusterRendered(Cluster<ItemMapa> cluster,
         MarkerOptions markerOptions) {
      super.onBeforeClusterRendered(cluster, markerOptions);
   }

   /**
    * Este método se ejecuta cada vez que se van a crear los cluster si retorna
    * TRUE se crean los cluster, sino, se pintan todos los elementos sueltos
    */
   @Override
   protected boolean shouldRenderAsCluster(Cluster<ItemMapa> cluster) {
      boolean res = false;

      try {
         // TODO: En pruebas, hay que ver como se comporta 
         if (elemList.size() > 500 && cluster.getSize() > 9) {
            return true;
         }
         MINZOOM_CLUSTER = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_CLUSTER, "14"));
         // A un nivel de zoom mayor o igual a 16 se pintan todos los
         // elementos sueltos
         actualZoom = (int) elemCluster.getZoom();
         if (cluster.getSize() > 1 && actualZoom < MINZOOM_CLUSTER)
            res = true;
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }

      return res;
   }

   /**
    * Este mitodo se ejecuta cada vez que se pincha encima de la ventana de
    * informaciin de un elemento
    */
   @Override
   public void onClusterItemInfoWindowClick(ItemMapa elemento) {
   }

   final Object onClusterItemClickLock = new Object();
   /**
    * Este método se ejecuta cada vez que se pincha sobre un elemento
    */
   @Override
   public boolean onClusterItemClick(ItemMapa item) {

      Resources res = MainActivity.getInstance().getResources();
      synchronized (onClusterItemClickLock) {
         try {
            String SCalle;

            // Mostrar info
            elementoSeleccionado = null;
            incidenciaSeleccionada = null;
            FlotaPosiciones flotaElemSeleccionado = null;

            // Se obtiene el elemento, incidencia o elemento de flota seleccionado
            if (item.getTipoElemento().equals("E") && elemList != null) {          // Elemento
               for (ItemMapa elem : elemList) {
                  if (elem != null && elem.getId() == item.getId()) {

                     DBElemento dbElemento = new DBElemento();
                     elementoSeleccionado = dbElemento.getByIdInterno(elem.getId(), MainActivity.getInstance().getEmpresa());
                     dbElemento.close();
                     DBTags dbTags = new DBTags();
                     Tags tag = dbTags.getByMatricula(elementoSeleccionado.getMatricula(), elementoSeleccionado.getEmpresa());
                     dbTags.close();
                     if (tag != null)
                        elementoSeleccionado.setTag(tag.getTag());

                     String filtroIncidencias = Config.getInstance()
                           .getValueUsuario("inciVisibles", "ninguno");
                     String filtroTiposIncidencias = Config.getInstance()
                           .getValueUsuario("tiposInciVisibles", "todos");

                     if (!filtroIncidencias.equals("ninguno") || !filtroTiposIncidencias.equals("ninguno")) {
                        DBIncidencia dbIncidencia = new DBIncidencia();
                        ArrayList<Incidencia> listInci = dbIncidencia.getByElemId(elementoSeleccionado.getIdExterno(),
                                elementoSeleccionado.getEmpresa(), filtroIncidencias, filtroTiposIncidencias);
                        dbIncidencia.close();

                        if (listInci != null && !listInci.isEmpty()) {
                           for (Incidencia incidencia : listInci) {
                              double distancia = GPS.calcularDistancia(elementoSeleccionado.getPosition(), incidencia.getPosition());
                              if (distancia < 5) {
                                 incidenciaSeleccionada = incidencia;
                              }
                           }
                        }
                     }
                     break;
                  }
               }
            } else if (item.getTipoElemento().equals("I") && inciList != null) {     // Incidencia
               for (ItemMapa inci : inciList) {
                  if (inci != null && inci.getId() == item.getId()) {

                     incidenciaSeleccionada = (Incidencia) inci;

                            if (incidenciaSeleccionada.getElemento() != 0) {
                                DBElemento dbElemento = new DBElemento();
                                Elemento elemento = dbElemento.getByIdExterno(incidenciaSeleccionada.getElemento(),
                                        MainActivity.getInstance().getEmpresa());
                                dbElemento.close();
                                if (elemento != null && GPS.calcularDistancia(elemento.getPosition(), incidenciaSeleccionada.getPosition()) < 5)
                                    elementoSeleccionado = elemento;
                                if (elementoSeleccionado != null) {
                                    DBTags dbTags = new DBTags();
                                    Tags tag = dbTags.getByMatricula(elementoSeleccionado.getMatricula(), elementoSeleccionado.getEmpresa());
                                    dbTags.close();
                                    if (tag != null) elementoSeleccionado.setTag(tag.getTag());
                                }
                            }
                            break;
                        }
                    }
                } else if (item.getTipoElemento().equals("F")) {
                    for (ItemMapa flotaElem : flotaList) {
                        if (flotaElem != null && flotaElem.getId() == item.getId()) {  // Flota

                     flotaElemSeleccionado = (FlotaPosiciones) flotaElem;
                     break;
                  }
               }
            }

            if (mCrearIncidencia != null) {
               if (elementoSeleccionado != null)
                  mCrearIncidencia.setPosition(elementoSeleccionado
                        .getPosition());
            }

            if (MainActivity.getInstance().getIdItemMenu() <= 0) {
               LayoutInflater inflater = MainActivity.getInstance()
                     .getLayoutInflater();
               View layout = inflater.inflate(R.layout.toast_custom,
                     (ViewGroup) MainActivity.getInstance()
                           .findViewById(R.id.toast_layout_root));

               TextView text = (TextView) layout.findViewById(R.id.text);

               String text_aux = "";
               if (elementoSeleccionado != null && incidenciaSeleccionada != null) {
                  boolean mostrarOperaciones = Utils.parseStringToInt(Config.getInstance().getValue("mostrarInfoOperaciones")) > 0;
                  text_aux = "ELEMENTO: \n";
                  text_aux += elementoSeleccionado.toString(mostrarOperaciones);
                  text_aux += "\n\nINCIDENCIA: \n";
                  text_aux += incidenciaSeleccionada.toString();
                  text.setText(text_aux);
               } else {
                  if (elementoSeleccionado != null) {
                     boolean mostrarOperaciones = Utils.parseStringToInt(Config.getInstance().getValue("mostrarInfoOperaciones")) > 0;
                     text.setText(elementoSeleccionado.toString(mostrarOperaciones));
                     if (Environment.isSoftIndra)
                        text.setText(getElementDialogForIndra());
                  } else if (incidenciaSeleccionada != null)
                     text.setText(incidenciaSeleccionada.toString());
                  else if (flotaElemSeleccionado != null)
                     text.setText(String.format("%s\n%s", flotaElemSeleccionado.getFecha(), flotaElemSeleccionado.getDescripcion()));
               }
               text.setTextColor(MainActivity.getInstance().getResources()
                     .getColor(R.color.white_color));

               if (toast != null)
                  toast.cancel();

               toast = new Toast(MainActivity.getInstance().getApplicationContext());
               toast.setView(layout);
               toast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
               toast.setDuration(Toast.LENGTH_LONG);
               toast.show();
            }

            // Si se pulsa más de una vez con un intervalo inferior a 3
            // segundos no hago nada para evitar la reentrada
            if (SystemClock.uptimeMillis() - timerClick > 3000) {

               int idItemMenu = MainActivity.getInstance().getIdItemMenu();
               Intent intent;

               switch (idItemMenu) {

                  case MainActivity.MENU_PARQUES_JARDINES_MEDIDA:
                     // Recoger papelera
                     oldElemento = elementoSeleccionado;
                     if (elementoSeleccionado != null &&
                           !MainActivity.getInstance().isCrearIncidenciasActiveInMedida
                           && !MainActivity.getInstance().isCrearElementosActiveInMedida) {
                        recogerPapelera();
                     }
                     break;

                  case MainActivity.MENU_NIVEL_LLENADO_MAPA:
                  case MainActivity.MENU_NIVEL_LLENADO_MAPA_JEFE:
                     // Nueva forma de nivel de llenado.
                     oldElemento = elementoSeleccionado;

                     if (elementoSeleccionado != null) {
                        //Mostramos pantalla de introducir nivel de llenado.
                        NivelLlenadoActivity.getInstance().setElemento(elementoSeleccionado);
                        NivelLlenadoActivity.getInstance().setFromRfID(item.getFromRfID());
                        intent = new Intent(MainActivity.getInstance(), NivelLlenadoActivity.class);

                        if (idItemMenu == MainActivity.MENU_NIVEL_LLENADO_MAPA_JEFE)
                           intent.putExtra("version_jefe", true);

                        MainActivity.getInstance().startActivity(intent);
                     }
                     break;

                  case MainActivity.MENU_NIVEL_LLENADO_PARCIAL:
                  case MainActivity.MENU_NIVEL_LLENADO_PARCIAL_JEFE:

                     NivelLlenadoActivity.getInstance().setElemento(elementoSeleccionado);
                     intent = new Intent(MainActivity.getInstance(), NivelLlenadoActivity.class);

                     if (idItemMenu == MainActivity.MENU_NIVEL_LLENADO_PARCIAL_JEFE)
                        intent.putExtra("version_jefe", true);

                     intent.putExtra("procesado", true);
                     intent.putExtra("vaciado_parcial", true);
                     MainActivity.getInstance().startActivity(intent);

                     break;

                  case MainActivity.MENU_LECTURA_LLENADO:
                  case MainActivity.MENU_LECTURA_LLENADO_JEFE:

                     NivelLlenadoActivity.getInstance().setElemento(elementoSeleccionado);
                     intent = new Intent(MainActivity.getInstance(), NivelLlenadoActivity.class);

                     if (idItemMenu == MainActivity.MENU_LECTURA_LLENADO_JEFE)
                        intent.putExtra("version_jefe", true);

                     intent.putExtra("procesado", false);
                     intent.putExtra("vaciado_parcial", false);
                     MainActivity.getInstance().startActivity(intent);

                     break;

                  case MainActivity.MENU_CREAR_ELEMENTOS:
                  case MainActivity.MENU_CREAR_ELEMENTOS_IMAGEN:
                  case MainActivity.MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN:
                     if (mCrearElemento != null && item.getMarker().equals(mCrearElemento)) {
                        mCrearElemento.setPosition(elementoSeleccionado.getPosition());
                        CrearElemento(null, null);
                     }

                     break;

                  case MainActivity.MENU_LAVAR_ELEMENTO_MAPA:
                     oldElemento = elementoSeleccionado;

                     if (elementoSeleccionado != null) {
                        String infoElemento = elementoSeleccionado.toString();
                        if (Environment.isSoftIndra)
                           infoElemento = getElementDialogForIndra();

                        new InfoDialog(
                              MainActivity.getInstance(),
                              res.getString(R.string.atencion),
                              res.getString(R.string.warningMarcarLavado) + "\n"
                                    + infoElemento + "\n"
                                    + res.getString(R.string.estaSeguro2),
                              InfoDialog.ICON_QUESTION,
                              new OnInfoDialogSelect() {
                                 Resources res = MainActivity.getInstance().getResources();

                                 @Override
                                 public void onSelectOption(int option) {
                                    if (option == InfoDialog.BUTTON_YES) {
                                       // Enviamos evento de lavado por la API.
                                       if (SensoresManager.getInstance().sendSensorLavado(elementoSeleccionado)) {
                                          DBElemento dbElemento = new DBElemento();
                                          elementoSeleccionado.setFechaUltLavado(Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));
                                          dbElemento.update(elementoSeleccionado);
                                          dbElemento.close();
                                          DBTags dbTags = new DBTags();
                                          Tags tag = dbTags.getByMatricula(elementoSeleccionado.getMatricula(), elementoSeleccionado.getEmpresa());
                                          dbTags.close();
                                          if (tag != null)
                                             elementoSeleccionado.setTag(tag.getTag());
                                          String infoElemento = elementoSeleccionado.toString();
                                          if (Environment.isSoftIndra)
                                             infoElemento = getElementDialogForIndra();

                                          MainActivity.getInstance().showMessage(
                                                res.getString(R.string.elemento) + "\n"
                                                      + infoElemento + "\n" + res.getString(R.string.marcadoLavado));
                                       }
                                    }
                                 }
                              }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                              InfoDialog.POSITION_CENTER).show();
                     }
                     break;

                  case MainActivity.MENU_RECOGIDA_ELEMENTOS:

                     oldElemento = elementoSeleccionado;

                     if (elementoSeleccionado != null) {
                        String infoElemento = elementoSeleccionado.toString();
                        if (Environment.isSoftIndra)
                           infoElemento = getElementDialogForIndra();

                        String message = "Elemento recogido: " + infoElemento;
                        new InfoDialog(
                              MainActivity.getInstance(),
                              res.getString(R.string.atencion),
                              res.getString(R.string.desea_recoger_elemento) + "\n" + infoElemento + "\n" + res.getString(R.string.estaSeguro2),
                              InfoDialog.ICON_QUESTION,
                              new OnInfoDialogSelect() {
                                 @Override
                                 public void onSelectOption(int option) {
                                    if (option == InfoDialog.BUTTON_YES) {
                                       // Envio el procesado del elemento
                                       if (elementoSeleccionado != null) {
                                          SensoresManager.getInstance().sendSensorProcesado(elementoSeleccionado);
                                          DBElemento dbElemento = new DBElemento();
                                          elementoSeleccionado.setFechaUltRecogida(Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));
                                          dbElemento.update(elementoSeleccionado);
                                          dbElemento.close();
                                          MainActivity.getInstance().showMessage(message);
                                          refreshNivelLlenado();
                                       } else
                                          MainActivity.getInstance().showMessage(R.string.noEncontradoElemento);

                                       //Insertamos operación realizada
                                    }
                                 }
                              }, InfoDialog.BUTTON_NO
                              | InfoDialog.BUTTON_YES,
                              InfoDialog.POSITION_CENTER).show();
                     }
                     break;

                  case MainActivity.MENU_MODIFICAR_BORRAR_ELEMENTOS:
                  case MainActivity.MENU_MODIFICAR_ELEMENTOS:
                     // Compruebo que está seleccionada la opción de
                     // modificar/borrar elementos en el submenú
                     oldElemento = elementoSeleccionado;

                     if (elementoSeleccionado != null) {
                        if (elementoSeleccionado.getIdExterno() < 1) {
                           MainActivity.getInstance().showMessage(
                                 "El elemento " + elementoSeleccionado.getNombre() + " no se encuentra sincronizado.", Toast.LENGTH_LONG);
                           break;
                        }
                        if (elementoSeleccionado.getEstado() == Elemento.ESTADO_INACTIVO) {
                           MainActivity.getInstance().showMessage(
                                 "El elemento " + elementoSeleccionado.getNombre() + " se encuentra borrado.", Toast.LENGTH_LONG);
                           break;
                        }
                        int PERMISOS = (idItemMenu == MainActivity.MENU_MODIFICAR_BORRAR_ELEMENTOS)
                              ? InfoDialog.BUTTON_MODIFY | InfoDialog.BUTTON_DELETE
                              : InfoDialog.BUTTON_MODIFY;

                        new InfoDialog(MainActivity.getInstance(), res.getString(R.string.atencion),
                              getDataElemento(elementoSeleccionado),
                              InfoDialog.ICON_QUESTION,
                              new OnInfoDialogSelect() {
                                 @Override
                                 public void onSelectOption(int option) {
                                    if (elementoSeleccionado == null)
                                       return;
                                    Resources res = MainActivity.getInstance().getResources();
                                    if (option == InfoDialog.BUTTON_MODIFY) {
                                       int idExterno = elementoSeleccionado.getIdExterno();
                                       int idInterno = elementoSeleccionado.getId();
                                       final Tags tag = new DBTags().getByElementIdInternalOrExternal(idExterno, idInterno, elementoSeleccionado.getEmpresa());
                                       ITag iTag = null;
                                       if (tag != null)
                                          iTag = new TagUnknow(tag.getMatricula(), tag.getTag(), Utilss.now());
                                       MainActivity.abrirAddElementActivityUpdate(tag, iTag, idExterno, idInterno, elementoSeleccionado.getPosition());
                                    } else if (option == InfoDialog.BUTTON_DELETE) {
                                       new InfoDialog(
                                             MainActivity
                                                   .getInstance(),
                                             res.getString(R.string.atencion),
                                             res.getString(R.string.confirmDeleteElemento),
                                             InfoDialog.ICON_QUESTION,
                                             new OnInfoDialogSelect() {
                                                @Override
                                                public void onSelectOption(int option) {
                                                   if (option == InfoDialog.BUTTON_YES) {

                                                      // El elemento se borra cuando recibo
                                                      // el OK del servidor
                                                      // deleteElemento(elementoSeleccionado);

                                                      // Se marca como inactivo para no mostrarlo
                                                      elementoSeleccionado.setEstado(Elemento.ESTADO_INACTIVO);
                                                      DBElemento dbElemento = new DBElemento();
                                                      dbElemento.update(elementoSeleccionado);
                                                      dbElemento.close();
                                                      MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

                                                      // Guardo la información para enviar
                                                      // en la bandeja de salida
                                                      DBPacket dbp = new DBPacket();
                                                      dbp.insert(new Packet(
                                                            Packet.ELEMENTO_BORRAR,
                                                            Packet.PRIORIDAD_NORMAL,
                                                            elementoSeleccionado));

                                                      //Sacar el tag que tiene ese elemento a borrar
                                                      DBTags tagsManagers = new DBTags();
                                                      Tags tag;
                                                      // Si el elemento tiene id externo (está sincronizado), se busca por ese
                                                      // Si no, se busca por el id interno
                                                      tag = tagsManagers.getByElementIdInternalOrExternal(
                                                            elementoSeleccionado.getIdExterno(),
                                                            elementoSeleccionado.getId(),
                                                            elementoSeleccionado.getEmpresa());

                                                      if (tag != null) {
                                                         tag.setIdExternoElemento(0);
                                                         tagsManagers.update(tag);
                                                      }

                                                      dbp.close();

                                                      //sincro
                                                      if (MainActivity.getInstance().isNetworkAvailable()) {
                                                         if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                                                            DBSynchro.getInstance().forceSync();
                                                         }
                                                      }
                                                   }
                                                }
                                             },
                                             InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                                             InfoDialog.POSITION_CENTER)
                                             .show();
                                    }
                                 }
                              }, PERMISOS, InfoDialog.POSITION_CENTER)
                              .show();
                     }
                     break;
                  case MainActivity.MENU_NAVEGAR_ELEMENTO:
                     if (elementoSeleccionado != null) {
                        //PMARCO Mantis 5408. Error al abrir el waze
                        String uri = String.format(Locale.ENGLISH,
                              "https://www.google.com/maps/dir/?api=1&travelmode=driving&destination=%f,%f",
                              elementoSeleccionado.getPosition().latitude,
                              elementoSeleccionado.getPosition().longitude);
                        Intent intent_maps = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
                        intent_maps.setPackage("com.google.android.apps.maps");
                        MainActivity.getInstance().startActivity(intent_maps);

                        // solamente si el waze está instalado, intentaré abrirlo, en caso contrario, muestro mensaje para que se instale
                        /*if (!Utils.isPaqueteInstalado(MainActivity.getInstance(), "com.waze")) {

                           Toast.makeText(
                                   MainActivity.getInstance(),
                                   "No tiene la aplicación WAZE instalada en el dispositivo. Para poder
                                   navegar al elemento, necesita realizar la instalación de la misma.",
                                   Toast.LENGTH_LONG).show();

                        } else {

                           new InfoDialog(
                                    MainActivity.getInstance(),
                                    res.getString(R.string.atencion),
                                    "¿Desea navegar al elemento seleccionado?",
                                    InfoDialog.ICON_QUESTION,
                                    new OnInfoDialogSelect() {

                                       @Override
                                       public void onSelectOption(int option) {
                                             if (option == InfoDialog.BUTTON_YES) {
                                                try {
                                                   String url = "https://waze.com/ul?ll=" + elementoSeleccionado.getPosition().latitude + "," + elementoSeleccionado.getPosition().longitude + "&navigate=yes"; // Cieza
                                                   Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));


                                                   intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                                   intent.setPackage("com.waze");

                                                   try {
                                                         MainActivity.getInstance().startActivity(intent);
                                                   } catch (ActivityNotFoundException ex) {
                                                         // Chrome browser presumably not installed so allow user to choose instead
                                                         intent.setPackage(null);
                                                         MainActivity.getInstance().startActivity(intent);
                                                   }


                                                } catch (Throwable e) {
                                                   Toast.makeText(
                                                            MainActivity.getInstance(),
                                                            "Problemas al intentar navegar al elemento. Compruebe que la app de navegación está instalada.",
                                                            Toast.LENGTH_SHORT).show();
                                                }
                                             }
                                       }
                                    }, InfoDialog.BUTTON_YES
                                    | InfoDialog.BUTTON_NO,
                                    InfoDialog.POSITION_CENTER).show();
                        }*/
                     }
                     break;
                  case MainActivity.MENU_SUSTITUIR_ELEMENTO:
                     // Compruebo que está seleccionada la opción de
                     // modificar/borrar elementos en el submenú
                     oldElemento = elementoSeleccionado;

                     if (elementoSeleccionado != null) {

                        new InfoDialog(
                              MainActivity.getInstance(),
                              res.getString(R.string.atencion),
                              "¿Desea realizar la sustitución de este elemento?",
                              InfoDialog.ICON_QUESTION,
                              new OnInfoDialogSelect() {
                                 @Override
                                 public void onSelectOption(int option) {
                                    if (option == InfoDialog.BUTTON_YES) {

                                       DBElemento db = new DBElemento();
                                       Elemento elem = db.getByIdInterno(
                                             elementoSeleccionado.getId(),
                                             MainActivity.getInstance()
                                                   .getEmpresa());

                                       Intent i = new Intent(MainActivity.getInstance(), SustituirElementoActivity.class);
                                       i.putExtra("elem_id", elem.getId());
                                       i.putExtra("elem_idext", elem.getIdExterno());
                                       i.putExtra("elem_nombre", elem.getNombre());
                                       MainActivity.getInstance().startActivity(i);
                                    }
                                 }
                              }, InfoDialog.BUTTON_YES
                              | InfoDialog.BUTTON_NO,
                              InfoDialog.POSITION_CENTER).show();
                     }
                     break;

                  case MainActivity.MENU_MODIFICAR_INCIDENCIAS:
                     // Compruebo que está seleccionada la opción de
                     // modificar incidencias en el submenú
                     if (incidenciaSeleccionada != null) {

                        new InfoDialog(
                              MainActivity.getInstance(),
                              res.getString(R.string.atencion),
                              res.getString(R.string.questionModificarEstadoIncidencia),
                              InfoDialog.ICON_QUESTION,
                              new OnInfoDialogSelect() {
                                 @Override
                                 public void onSelectOption(int option) {
                                    if (option == InfoDialog.BUTTON_YES) {

                                       // sobre elemento.
                                       Intent updateEstadoIncidenciaActivity = new Intent(
                                             MainActivity.getInstance(),
                                             UpdateEstadoIncidencia.class);

                                       updateEstadoIncidenciaActivity.putExtra(
                                             "incidencia", incidenciaSeleccionada.getId());

                                       updateEstadoIncidenciaActivity.putExtra(
                                             "incidenciaExt", incidenciaSeleccionada.getIdExterno());

                                       updateEstadoIncidenciaActivity.putExtra(
                                             "usuario", MainActivity.getInstance().getUsuario());

                                       MainActivity.getInstance().startActivity(
                                             updateEstadoIncidenciaActivity);
                                    }
                                 }
                              }, InfoDialog.BUTTON_YES
                              | InfoDialog.BUTTON_NO,
                              InfoDialog.POSITION_CENTER).show();
                     }
                     break;
                  case MainActivity.MENU_ASIGNAR_INCIDENCIAS:
                     Intent seleccionUsuarioIncidencia = new Intent(MainActivity.getInstance(),
                           SeleccionUsuarioIncidenciaActivity.class);
                     seleccionUsuarioIncidencia.putExtra("incidencia", incidenciaSeleccionada.getId());
                     seleccionUsuarioIncidencia.putExtra("incidenciaExt", incidenciaSeleccionada.getIdExterno());
                     MainActivity.getInstance().startActivity(seleccionUsuarioIncidencia);
                     break;
                  case MainActivity.MENU_NAVEGAR_INCIDENCIA:
                     if (incidenciaSeleccionada != null) {
                        String uri = String.format(Locale.ENGLISH,
                              "https://www.google.com/maps/dir/?api=1&travelmode=driving&destination=%f,%f",
                              incidenciaSeleccionada.getPosition().latitude,
                              incidenciaSeleccionada.getPosition().longitude);
                        Intent intent_maps = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
                        intent_maps.setPackage("com.google.android.apps.maps");
                        MainActivity.getInstance().startActivity(intent_maps);
                     }
                     break;
                  default:
                     break;
               }

               timerClick = SystemClock.uptimeMillis();
            }
         } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
         }
      }

      drawMap();
      return false;
   }

   /**
    * Este mitodo se ejecuta cada vez que se pincha encima de la ventana de
    * informaciin de un cluster
    */
   @Override
   public void onClusterInfoWindowClick(Cluster<ItemMapa> cluster) {
   }

   /**
    * Este mitodo se ejecuta al empezar a mover un elemento.
    */
   @Override
   public void onClusterItemDragStart(ItemMapa elemento) {

      try {

         // Guardo el elemento original cuando se empieza a mover
         oldElemento = elemento;
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Este mitodo se ejecuta mientras se mueve un elemento
    */
   @Override
   public void onClusterItemDrag(ItemMapa elemento) {
      try {
         if (elemento instanceof Elemento) {
            if (MainActivity.getInstance().getIdItemMenu() != MainActivity.MENU_MODIFICAR_BORRAR_ELEMENTOS
                  && MainActivity.getInstance().getIdItemMenu() != MainActivity.MENU_MODIFICAR_ELEMENTOS)
               elemento.getMarker().setPosition(oldElemento.getPosition());
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Este mitodo se ejecuta cuando se deja de mover un elemento.
    */
   @Override
   public void onClusterItemDragEnd(final ItemMapa elemento) {

      Resources res = MainActivity.getInstance().getResources();
      try {

         if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_MODIFICAR_BORRAR_ELEMENTOS
               || MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_MODIFICAR_ELEMENTOS) {

            if (elemento instanceof Elemento) {
               elementoSeleccionado = (Elemento) elemento;

               if (elementoSeleccionado.getIdExterno() < 1) {
                  elementoSeleccionado.getMarker().setPosition(oldElemento.getPosition());
                  MainActivity.getInstance().showMessage(
                        "El elemento " + elementoSeleccionado.getNombre() + " no se encuentra sincronizado.", Toast.LENGTH_LONG);
                  return;
               }

               String sCalle = "";
               // getInfoLatLng(
               // elementoSeleccionado.getMarker().getPosition().latitude,
               // elementoSeleccionado.getMarker().getPosition().longitude);
               new InfoDialog(
                     MainActivity.getInstance(),
                     res.getString(R.string.atencion),
                     res.getString(R.string.questionMoverElemento)
                           + sCalle,
                     InfoDialog.ICON_QUESTION,
                     new OnInfoDialogSelect() {
                        @Override
                        public void onSelectOption(int option) {

                           if (option == InfoDialog.BUTTON_YES) {

                              DBElemento db = new DBElemento();
                              Elemento elem = db.getByIdInterno(
                                    elementoSeleccionado.getId(),
                                    MainActivity.getInstance()
                                          .getEmpresa());
                              db.close();

                              // Cambio las coordenadas al elemento
                              elem.setPosition(
                                    elementoSeleccionado
                                          .getMarker()
                                          .getPosition().latitude,
                                    elementoSeleccionado
                                          .getMarker()
                                          .getPosition().longitude);

                              // Actualizo la BD
                              updateElemento(elem);

                              // Guardo la informaciin para enviar en
                              // la bandeja de salida
                              DBPacket dbp = new DBPacket();
                              dbp.insert(new Packet(
                                    Packet.ELEMENTO_MODIFICAR,
                                    Packet.PRIORIDAD_NORMAL, elem));
                              dbp.close();

                              centerMap(elem.getPosition());
                           } else {

                              // Restauro las coordenadas originales
                              elementoSeleccionado.getMarker()
                                    .setPosition(
                                          oldElemento
                                                .getPosition());
                           }
                        }
                     }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                     InfoDialog.POSITION_CENTER).show();
            }
         } else {

            // Restauro las coordenadas originales
            elemento.getMarker().setPosition(oldElemento.getPosition());
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   String getElementDialogForIndra() {
      if (elementoSeleccionado == null)
         return "";

      DBElementoModelo dbElementoModelo = new DBElementoModelo();
      ElementoModelo modelo = dbElementoModelo.getByID(
            elementoSeleccionado.getModelo(),
            MainActivity.getInstance().getEmpresa());
      return elementoSeleccionado.getNombre() + "\n" +
            (modelo == null ? "" : modelo.getNombre()) + "\n" +
            "CodFisico. " + elementoSeleccionado.getCodFisico() + "\n" +
            "Tag. " + elementoSeleccionado.getTag();
   }

   /**
    * Este mitodo se ejecuta cada vez que se pincha encima de un cluster
    */
   @Override
   public boolean onClusterClick(Cluster<ItemMapa> cluster) {

      try {
         // Descomentar si queremos hacer que vuelva a vibrar.
         // MainActivity.getInstance().vibratorManager
         // .vibrate(MilisegundosVibrar);

         int NumElementos = 0;
         int NumIncidencias = 0;

         for (ItemMapa items : cluster.getItems()) {
            if (items.getTipoElemento().equals("E"))
               NumElementos++;
            else if (items.getTipoElemento().equals("I"))
               NumIncidencias++;
         }

         Toast.makeText(
               MainActivity.getInstance(),
               "Contiene " + NumElementos + " elementos y "
                     + NumIncidencias + " incidencias",
               Toast.LENGTH_SHORT).show();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }

      return false;
   }

   /**
    * Este mitodo se ejecuta cada vez que se cambia de zoom o se mueve la
    * cartografia
    */
   @Override
   public void onClusterCameraChange(CameraPosition cameraPosition) {
      try {
         if (oldCameraPosition != null && oldCameraPosition.equals(cameraPosition))
            return;

         oldCameraPosition = cameraPosition;

         // Guardo en la configuraciin del usuario el centro de la cartografia y el nivel de zoom
         if (MainActivity.getInstance().getUsuario() > 0) {
            Config.getInstance().setValueUsuario("miLat", "" + cameraPosition.target.latitude);
            Config.getInstance().setValueUsuario("miLon", "" + cameraPosition.target.longitude);
            Config.getInstance().setValueUsuario("miZoom", "" + cameraPosition.zoom);
         }

         actualZoom = elemCluster.getZoom();

         int minZoomCluster = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_MINZOOM_NOSHOWCLUSTER, "11"));
         if (actualZoom < minZoomCluster) {
            if (elemList != null && elemList.size() > 0) {
               // Cuando dejan de mostrarse los marcadores de elementos también
               // se dejan de mostrar los submarcadores asociados
               elemCluster.clearItems();
               elemCluster.cluster();
               elemList.clear();
            }
            return;
         }

         if (elementoSeleccionado != null) {
            if (mCrearElemento != null) {
               if (!elementoSeleccionado.getPosition().equals(mCrearElemento.getPosition())) {
                  elementoSeleccionado = null;
               }
            } else if (mCrearIncidencia != null) {
               if (!elementoSeleccionado.getPosition().equals(mCrearIncidencia.getPosition())) {
                  elementoSeleccionado = null;
               }
            }
         }

         if (mCrearElemento != null) {
            mCrearElemento.setPosition(cameraPosition.target);
         }

         if (mDepositarElemento != null) {
            mDepositarElemento.setPosition(cameraPosition.target);
         }

         if (mCrearIncidencia != null) {
            mCrearIncidencia.setPosition(cameraPosition.target);
         }

         GesElemMapFragment.getInstance().setVisibleMarkersAndAreas();

         drawMap();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }
   }

   /**
    * Evento que se ejecuta cuando se pulsa sobre el mapa
    */
   public void onMapClick(LatLng position) {

      synchronized (position) {

         final LatLng pos = position;
         // String infoGeo = "";
         String SCalle = "";
         // Descomentar si querememos que vuelva a vibrar.
         // MainActivity.getInstance().vibratorManager.vibrate(MilisegundosVibrar);
         try {
            elementoSeleccionado = null;

            // Crear elementos?
            if ((MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_ELEMENTOS ||
                  MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_ELEMENTOS_IMAGEN ||
                  MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN)
                  && mCrearElemento != null) {

               mCrearElemento.setPosition(position);
               // mCrearElemento.showInfoWindow();

               // Crear incidencias
            } else if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_CREAR_INCIDENCIAS
                  && mCrearIncidencia != null) {

               mCrearIncidencia.setPosition(position);
               // mCrearIncidencia.showInfoWindow();

               // Depositar elemento
            } else if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_DEPOSITAR_ELEMENTOS
                  && mDepositarElemento != null) {

               mDepositarElemento.setPosition(position);
            } else {

               // boolean isOnRoute = PolyUtil.isLocationOnPath(position, polylineOptions.getPoints(), false, 25);

               FlotaPosicionesHistorico fph = null;

               double distanceMin = Double.MAX_VALUE;

               if (historicoRutaList != null)
                  for (ItemMapa i : historicoRutaList) {

                     double distanceInMeters = SphericalUtil.computeDistanceBetween(position, i.getPosition());

                     // me kedo con el punto más cercano de la polilínea
                     if (distanceInMeters < distanceMin) {
                        distanceMin = distanceInMeters;
                        fph = (FlotaPosicionesHistorico) i;
                     }
                  }

               if (fph != null) {
                  LayoutInflater inflater = MainActivity.getInstance()
                        .getLayoutInflater();
                  View layout = inflater.inflate(R.layout.toast_custom,
                        (ViewGroup) MainActivity.getInstance()
                              .findViewById(R.id.toast_layout_root));

                  TextView text = (TextView) layout.findViewById(R.id.text);
                  text.setText(String.format("%s\n%s\n%s", "Posición ", "Fecha :" + fph.getFecha(), " Velocidad: " + fph.getVelocidad()));

                  if (toast != null)
                     toast.cancel();

                  toast = new Toast(MainActivity.getInstance()
                        .getApplicationContext());
                  toast.setView(layout);
                  toast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
                  toast.setDuration(Toast.LENGTH_LONG);
                  toast.show();
               }
            }
         } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
         }
      }
   }

   // private boolean isClickMarker = false;

   /**
    * Las incidencias son tratadas como marcadores. Las sacamos fuera de los
    * cluster. Este metodo se ejecuta cuando pulsamos encima de una incidencia.
    */
   public boolean onMarkerClick(final Marker marker) {

      Log.e("", marker.getId());

      return false;
   }

   private void recogerPapelera() {
      final Resources res = MainActivity.getInstance().getResources();
      boolean isPapelera = true;
      //De momento no sabmos que vamos hacer elementoSeleccionado.isInsideArea(codigoArea, MainActivity.getInstance().getEmpresa());
      boolean isDentroArea = true;
      if (isPapelera && isDentroArea) {
         new InfoDialog(
               MainActivity.getInstance(),
               res.getString(R.string.atencion),
               res.getString(R.string.warningRecogerPapelera) + " "
                     + elementoSeleccionado.toString()
                     + res.getString(R.string.estaSeguro),
               InfoDialog.ICON_QUESTION,
               new OnInfoDialogSelect() {
                  @Override
                  public void onSelectOption(int option) {

                     if (option == InfoDialog.BUTTON_YES) {

                        long fechaMedida = System.currentTimeMillis();

                        String fechaM = Utils.datetimeToString(
                              new Date(fechaMedida),
                              "yyyy-MM-dd HH:mm:ss");
                        String papelerasMedidas = Config
                              .getInstance()
                              .getValueUsuario("papelerasMedidas", "");
                        if (papelerasMedidas.equals(""))
                           Config.getInstance().setValueUsuario(
                                 "papelerasMedidas",
                                 elementoSeleccionado.getIdExterno()
                                       + ";" + fechaM);
                        else
                           Config.getInstance().setValueUsuario(
                                 "papelerasMedidas",
                                 papelerasMedidas
                                       + ";"
                                       + elementoSeleccionado
                                       .getIdExterno()
                                       + ";" + fechaM);

                        Toast.makeText(
                              MainActivity.getInstance(),
                              res.getString(R.string.elemento)
                                    + " " + elementoSeleccionado
                                    .toString()
                                    + " " + res.getString(R.string.marcadoRecogido),
                              Toast.LENGTH_LONG).show();
                     }
                  }
               }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
               InfoDialog.POSITION_CENTER).show();
      }
   }

   /**
    * Devuelve un mensaje con la direccion dadas unas coordenadas
    */
   /* public String getAddress(LatLng posicion) {

        try {

            Geocoder gc = new Geocoder(MainActivity.getInstance()
                    .getApplicationContext(), Locale.getDefault());

            ArrayList<Address> direcciones = null;
            direcciones = (ArrayList<Address>) gc.getFromLocation(
                    posicion.latitude, posicion.longitude, 3);

            if (direcciones != null) {

                StringBuilder sb = new StringBuilder();
                sb.append(direcciones.get(0).getAddressLine(0) + " - "
                        + direcciones.get(0).getLocality());

                return sb.toString();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return "";
    }*/

   /**
    * Devuelve un String con los datos del elemento seleccionado en el mapa
    */
   public String getDataElemento(Elemento elemento) {

      try {
         Resources res = MainActivity.getInstance().getResources();

         Elemento elem = null;
         DBElemento dbElemento = new DBElemento();
         elem = dbElemento.getByIdInterno(elemento.getId(), elemento.getEmpresa());
         dbElemento.close();

         DBTags dbTags = new DBTags();
         Tags tag = dbTags.getByElementIdInternalOrExternal(elemento.getIdExterno(), elemento.getId(), elemento.getEmpresa());
         dbTags.close();

         if (tag != null)
            elemento.setTag(tag.getTag());

         String modelo = "";
         if (Environment.isSoftIndra) {
            DBElementoModelo dbElementoModelo = new DBElementoModelo();
            ElementoModelo elementoModelo = dbElementoModelo.getByID(elem.getModelo(), elem.getEmpresa());
            dbElementoModelo.close();
            modelo = elementoModelo.getNombre();
         }

         StringBuilder sb = new StringBuilder();
         sb.append(res.getString(R.string.elemento) + ": "
               + elemento.getNombre()
               + "\n");

         if (!Environment.isSoftIndra)
            sb.append((!elemento.getMatricula().equals("") ? res.getString(R.string.matricula) + ": "
                  + elemento.getMatricula() + "\n"
                  + "Tag: " + elemento.getTag() + "\n" : ""));
         else
            sb.append("Mod: " + modelo + "\n"
                  + "Cod. Físico: " + elem.getCodFisico() + "\n"
                  + "Tag: " + elemento.getTag() + "\n");

         sb.append("\n" + res.getString(R.string.queDeseaHacer));

         return sb.toString();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }

      return "";
   }

   /**
    * Devuelve un String con los datos del elemento seleccionado en el mapa
    */
   public synchronized static String getInfoGIS(double Lat, double Lng) {

      //String sCalle = "- CALLE NO ENCONTRADA -";
      String sCalle = "";
      try {

         ClientWebSvc sweb = new ClientWebSvc();

         JSONObject calle = sweb.callAPI("/api/gis/calle/" + Lat + "/" + Lng, null, true);

         if (calle.getInt("via_id") != -1) {
            sCalle = String.format("%s\r\n%s (%d-%d) (%d-%d)\r\n(%s)",
                  calle.get("Municipio").toString(),
                  calle.get("NombreCalle").toString(), calle
                        .getInt("via_id"), calle.getInt("via_ih"),
                  calle.getInt("via_dd"), calle.getInt("via_dh"), calle
                        .get("NombreNucleo").toString());
         }
         return sCalle;
      } catch (Throwable e) {

         e.printStackTrace();
         //return "Calle no encontrada";
      }

      return sCalle;
   }

   /**
    * Devuelve un String con los datos del elemento seleccionado en el mapa
    */
   public static synchronized String getMunicipioBy(double Lat, double Lng) {

      try {

         ClientWebSvc sweb = new ClientWebSvc();
         sweb.getToken();

         JSONObject calle = sweb.callAPI(
               "/api/gis/calle/" + Lat + "/" + Lng, null, true);

         String sCalle = "";

         sCalle = calle.get("Municipio").toString();

         return sCalle;
      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
         return "";
      }
   }

   /**
    * Devuelve un String con los datos del elemento seleccionado en el mapa
    */
   public static synchronized String getMunicipioBy(double Lat, double Lng, Context
         context) {

      try {

         ClientWebSvc sweb = new ClientWebSvc();

         JSONObject calle = sweb.callAPI(
               "/api/gis/calle/" + Lat + "/" + Lng, null, context);

         String sCalle = "";

         sCalle = calle.get("Municipio").toString();

         return sCalle;
      } catch (Throwable e) {
         return "";
      }
   }

   static PaintCirclesNivelLlenado taskPaint = null;

   private void refreshNivelLlenado() {
      if (taskPaint != null)
         taskPaint.cancel(true);

      taskPaint = new PaintCirclesNivelLlenado(this, map, elemList);
      taskPaint.execute(MainActivity.getInstance().getEmpresa());
   }

   /**
    * Añade el elemento en BD y en el cluster
    */
   public int addElemento(Elemento elemento) {
      int id = elemento.getId();
      try {

         if (id == 0) {
            // Inserto el elemento en la BD
            DBElemento dbElemento = new DBElemento();
            id = (int) dbElemento.insert(elemento);
            dbElemento.close();
         }

         synchronized (syncElem) {

            if (elemList == null)
               elemList = new ArrayList<ItemMapa>();
            // if (elemCluster == null)
            // elemCluster = clusterManager;

            // Añado el elemento a la lista en memoria y al cluster
            elemCluster.addItem(elemento);

            elemList.add(elemento);
            MainActivity.getInstance().addElementoBusqueda(elemento);

            // Por si se llama desde un hilo paso el mensaje al
            // manejador para que cree de nuevo el cluster
            MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }

      return id;
   }

   /**
    * Añade el elemento solo a la lista en memoria y al cluster pero no crea el
    * cluster. Este mitodo solo es llamado desde el proceso de sincronización
    */
   public void addElementoToCluster(ItemMapa elemento) {

      try {

         synchronized (syncElem) {

            if (elemList == null)
               elemList = new ArrayList<ItemMapa>();

            // Aiado el elemento a la lista en memoria y al cluster
            elemCluster.addItem(elemento);
            elemList.add(elemento);
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   /**
    * Actualiza el elemento en BD y en el cluster
    */
   public void updateElemento(Elemento elemento) {

      try {

         // Actualizo la BD
         DBElemento dbElemento = new DBElemento();
         dbElemento.update(elemento);
         dbElemento.close();

         synchronized (syncElem) {

            // Quito el anterior elemento de la lista en memoria y del
            // cluster
            elemCluster.removeItem(oldElemento);
            elemList.remove(oldElemento);

            // Añado el nuevo elemento a la lista en memoria y al cluster
            elemCluster.addItem(elemento);
            elemList.add(elemento);
            MainActivity.getInstance().updateElementoBusqueda(elemento);

            // Por si se llama desde un hilo paso el mensaje al
            // manejador para que cree de nuevo el cluster
            // handler.sendEmptyMessage(MSG_REFRESH_CLUSTER);

            MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   /**
    * Actualiza el elemento pero solo en la lista en memoria y en el cluster
    * pero no crea el cluster. Este método solo es llamado desde el proceso de
    * sincronización
    */
   public void updateElementoToClusterById(int id, ItemMapa elemento) {

      try {

         synchronized (syncElem) {

            if (elemList != null) {

               // Busco el elemento en la lista en memoria por el id
               // definitivo o temporal
               for (ItemMapa elem : elemList) {

                  if (elem.getId() == id) {

                     // Quito el elemento antiguo e inserto el nuevo
                     // tanto en la lista en memoria como en el cluster
                     elemCluster.removeItem(elem);
                     elemList.remove(elem);

                     elemCluster.addItem(elemento);
                     elemList.add(elemento);
                     MainActivity.getInstance().updateElementoBusqueda((Elemento) elemento);

                     MyBroadCastManager.getInstance()
                           .sendBroadCastRefreshCluster();

                     break;
                  }
               }
            }
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   /**
    * Elimina un elemento en BD y en el cluster
    */
   private void deleteElemento(Elemento elemento) {

      try {

         // Borro el elemento de la BD
         DBElemento dbElemento = new DBElemento();
         dbElemento.delete(elemento);
         dbElemento.close();

         synchronized (syncElem) {

            // Quito el elemento de la lista en memoria y del cluster
            elemCluster.removeItem(elemento);
            elemList.remove(elemento);

            // Por si se llama desde un hilo paso el mensaje al
            // manejador para que cree de nuevo el cluster
            // handler.sendEmptyMessage(MSG_REFRESH_CLUSTER);
            MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   /**
    * Borra el elemento pero solo en la lista en memoria y en el cluster pero
    * no crea el cluster. Este método salo es llamado desde el proceso de
    * sincronización
    */
   public void deleteElementoToClusterById(int id) {

      try {

         synchronized (syncElem) {

            if (elemList != null) {

               // Busco el elemento en la lista en memoria por el id
               // definitivo o temporal
               for (ItemMapa item : elemList) {

                  if (item.getId() == id) {

                     // Quito el elemento +tanto en la lista en memoria
                     // como en el cluster
                     elemCluster.removeItem(item);
                     elemList.remove(item);
                     MainActivity.getInstance().removeElementoBusqueda((Elemento) item);

                     // Por si se llama desde un hilo paso el mensaje al
                     // manejador para que cree de nuevo el cluster
                     MyBroadCastManager.getInstance()
                           .sendBroadCastRefreshCluster();

                     break;
                  }
               }
            }
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   /**
    * Crea el cluster. Este mitodo es llamado desde el proceso de
    * sincronizaciin
    */
   public void refreshCluster() {

      try {

         synchronized (syncElem) {

            // Por si se llama desde un hilo paso el mensaje al
            // manejador para que cree de nuevo el cluster
            MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   /**
    * Repinta de nuevo todas las incidencias. Este mitodo es llamado desde el
    * proceso de sincronizaciin
    */
   public void refreshIncidencias() {

      try {

         synchronized (syncElem) {

            // Por si se llama desde un hilo paso el mensaje al
            // manejador para que cree de nuevo el cluster
            MyBroadCastManager.getInstance().sendBroadCastRefreshIncidencias();
            // handler.sendEmptyMessage(MSG_REFRESH_INCIDENCIAS);
         }
      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   /**
    * Actualiza la incidencia en BD
    */
   public void updateIncidencia(Incidencia incidencia) {
      try {
         // 01/07/2024 - Comentado por problemas de sincronización
//         synchronized (syncElem) {
//            DBIncidencia dbIncidencia = new DBIncidencia();
//            if (!dbIncidencia.update(incidencia)) {
//               int id = (int) dbIncidencia.insert(incidencia);
//               incidencia.setId(id);
//            }
//            dbIncidencia.close();
//         }
         // Añado la incidencia al mapa
         DBIncidencia dbIncidencia = new DBIncidencia();
         ItemMapa itemMapa = dbIncidencia.getItemMapaByIdInterno(incidencia.getId());
         dbIncidencia.close();
         if (itemMapa != null) {
            inciList.add(itemMapa);
            elemCluster.addItem(itemMapa);
         }

         // Refresco el mapa
         refreshIncidencias();
      } catch (Throwable e) {
         e.printStackTrace();
      }
   }

   public int addIncidenciaFoto(IncidenciaFoto fotoIncidencia) {
      int id = fotoIncidencia.getId();

      try {

         // Inserto la incidencia en la BD
         DBIncidenciaFoto db = new DBIncidenciaFoto();
         if (!db.update(fotoIncidencia))
            id = (int) db.insert(fotoIncidencia);
         db.close();
      } catch (Throwable e) {

         e.printStackTrace();
      }

      return id;
   }

   /**
    * Limpiar flota
    */
   private void removeMarkersFlota() {

      if (historicoRutaList != null && historicoRutaList.size() > 0) {
         historicoRutaList.clear();

         map.clear();
      }

      if (flotaList != null && flotaList.size() > 0) {
         flotaList.clear();
      }

      drawMap();
   }

   /**
    * Recibe las posiciones del GPS
    */
   private class MyLocationListener implements LocationListener {
      public void onLocationChanged(Location loc) {
         try {
            synchronized (this) {
               if (loc != null) {
                  gps_enabled = loc.getProvider().equals(LocationManager.GPS_PROVIDER);

                  ultGpsPos = new GPSInfo(new LatLng(loc.getLatitude(), loc.getLongitude()), loc.getTime());

                  // Guardo la ultima posicion y su precision
                  Config.getInstance().setValueUsuario("ultLat", "" + loc.getLatitude());
                  Config.getInstance().setValueUsuario("ultLon", "" + loc.getLongitude());
                  Config.getInstance().setValue("ultFechaGPS", "" + loc.getTime());

                  // Centro el mapa si está activo el seguimiento GPS
                  if (gpsTrack) {
                     long horas = Utils.getDateDiff(ultGpsPos.datetime, System.currentTimeMillis(), TimeUnit.HOURS);
                     if (horas > 1) {
                        Toast.makeText(
                              MainActivity.getInstance(),
                              R.string.posicionMasDeUnaHora,
                              Toast.LENGTH_LONG).show();
                     }
                     centerGpsPos(gps_enabled);
                  }

                  // Aqui vamos a llamar a un evento del eventbus
                  EventBus.getDefault().post(new onChangeLocation(loc));
               }
            }
         } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
         }
      }

      private void WriteInfoGPS_Logger(Location loc) {
         //Guardamos información de posición actualizada.
            /*MyLoggerHandler.getInstance().info(
                    String.format("Posición GPS seguimiento %s. Coord. (Lat: %s, Lng: %s)",
                            Utils.datetimeToString(new Date(ultGpsPos.datetime)
                                    , "dd/MM/yyyy HH:mm:ss"), "" + loc.getLatitude(), "" + loc.getLongitude()));*/
      }

      @Override
      public void onProviderDisabled(String provider) {
      }

      @Override
      public void onProviderEnabled(String provider) {
      }

      @Override
      public void onStatusChanged(String provider, int status, Bundle extras) {
      }
   }

   public class GPSInfo {
      private LatLng position;
      private long datetime;

      public GPSInfo(LatLng position, long datetime) {
         this.position = position;
         this.datetime = datetime;
      }

      public long getDatetime() {
         return datetime;
      }

      public void setDatetime(long datetime) {
         this.datetime = datetime;
      }

      public LatLng getPosition() {
         return position;
      }

      public void setPosition(LatLng position) {
         this.position = position;
      }
   }


    /*class asyncPaint extends AsyncTask<Void, String, Boolean> {

        ProgressDialog pDialog;
        int rutaId, tiempoParada;

        public asyncPaint(int rutaId, int tiempoParada) {

            this.rutaId = rutaId;
            this.tiempoParada = tiempoParada;
        }


        @Override
        protected Boolean doInBackground(Void... voids) {

            try {

                historicoRutaList = new ArrayList<ItemMapa>();
                DBFlotaPosiciones dbPosFlota = new DBFlotaPosiciones();
                historicoRutaList = dbPosFlota.getHistoricoRuta(MainActivity.getInstance()
                        .getEmpresa(), MainActivity.getInstance().getUsuario(), this.rutaId);
                dbPosFlota.close();

            } catch (Throwable e){
                Log.e("", e.getMessage());
                return false;
            }

            return true;
        }

        protected void onPreExecute() {
            // para el progress dialog
            pDialog = new ProgressDialog(MainActivity.getInstance());
            pDialog.setMessage("Pintando la ruta, por favor espere");
            pDialog.setIndeterminate(true);
            pDialog.setCancelable(false);
            pDialog.show();
        }

        @Override
        protected void onPostExecute(Boolean result) {

            // recorremos las polilíneas y añadimos marcadores de paradas, según el tiempo establecido
            boolean hayParada = false;
            String fechaUltPos;



            for (ItemMapa i: historicoRutaList){

                FlotaPosicionesHistorico fph = (FlotaPosicionesHistorico)i;
                /*if (hayParada && fph.getVelocidad()>0){

                    // viene de una parada, añado marcador de la misma y duración
                    hayParada=false;



                } else if (fph.getVelocidad()==0){

                    hayParada=true;
                    fechaUltPos = fph.getFecha();

                }



                polylineOptions.add(i.getPosition());
                polyline= map.addPolyline(polylineOptions);
                polyline.setTag(i.getIdExterno());
                polyline.setClickable(true);
            }

            pDialog.dismiss();

        }
    }*/
}
