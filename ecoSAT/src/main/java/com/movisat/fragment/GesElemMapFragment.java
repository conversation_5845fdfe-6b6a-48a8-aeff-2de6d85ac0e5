package com.movisat.fragment;

import static com.movisat.ecosat.R.layout.button_float;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Point;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.Xml;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.environment.Environment;
import com.gc.materialdesign.utils.Utils;
import com.gc.materialdesign.views.ButtonFloat;
import com.getbase.floatingactionbutton.FloatingActionButton;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailabilityLight;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.Priority;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.MapFragment;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.Projection;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.Circle;
import com.google.android.gms.maps.model.CircleOptions;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.gms.maps.model.Polygon;
import com.google.android.gms.maps.model.PolygonOptions;
import com.google.maps.android.clustering.ClusterManager;
import com.movisat.adapter.IconTextAdapter;
import com.movisat.database.Area;
import com.movisat.database.DBArea;
import com.movisat.database.DBElemento;
import com.movisat.database.DBSensoresNivelesLlenado;
import com.movisat.database.Elemento;
import com.movisat.database.FrecuenciaProcesadoState;
import com.movisat.database.ItemMapa;
import com.movisat.database.SensorNivelLLenado;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyBroadCastManager;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;
import com.movisat.ecosat.SettingsActivity;
import com.movisat.log.Logg;
import com.movisat.managers.TagReaderManager;
import com.movisat.managers.UHFManager;
import com.movisat.services.PositionService;
import com.movisat.utilities.Config;
import com.movisat.utils.IFuncVoid;

import org.xmlpull.v1.XmlPullParser;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.function.BiConsumer;

public class GesElemMapFragment extends MapFragment implements
      ActionBar.OnNavigationListener, OnMapReadyCallback {
   private static final String TAG = "GesElemMapFragment";

   private float lastZoom = 0;
   private int lastOptionBar = 0, lastOptBarZoom = 0;
   private FloatingActionButton smallCrearElemento = null;
   private FloatingActionButton smallDepositarElemento = null;
   private FloatingActionButton smallCrearIncidencia = null;
   private FloatingActionButton smallAsignarIncidencia = null;
   private FloatingActionButton leerTag = null;
   private FloatingActionButton gpsLocationButton = null;
   public GestionElementos gestionElementos = null;

   private final HashSet<Marker> markersOperationsDones = new HashSet<>();
   private final ArrayList<Marker> markersDiasBloqueo = new ArrayList<>();
   private final ArrayList<Marker> markersNivelesLlenado = new ArrayList<>();

   private final int CIRCLE = 0;
   private final int SQUARE = 1;
   private final int POLYGON = 2;

   private boolean isReadingTag = false;
   private final CountDownTimer readTagTimer = new CountDownTimer(3000, 3000) {
      @Override
      public void onTick(long millisUntilFinished) {
      }

      @Override
      public void onFinish() {
         isReadingTag = false;
      }
   };

   private int MAX_ZOOM_VISIBLE_AREAS = 17;
   private final int MAX_ZOOM_VISIBLE_PUNTOS_CONTROL = 19;
   private final int MAX_GRUPOS_VISIBLES_ZOOM = 2;
   // public static ButtonFloat buttonFloat = null;

   // private LocationManager locationManager = null;


   // Action bar
   // private ActionBar actionBar = null;

   // Title navigation Spinner data
   // private ArrayList<IconTextItem> navSpinner;
   private String[] spinnerOptions;

   // Navigation adapter
   private IconTextAdapter titleNavigationAdapter;
   private static GesElemMapFragment instance = null;

    private GoogleMap map = null;

   private ClusterManager<ItemMapa> mClusterManager = null;

   public static GesElemMapFragment getInstance() {
      return instance;
   }

   @Override
   public void onCreate(Bundle savedInstanceState) {
      super.onCreate(savedInstanceState);

      setHasOptionsMenu(true);
      displayMetrics = getResources().getDisplayMetrics();

   }

   /**
    * Esta funciin hay que llamarla cuando se descargue el fragmento o se
    * cierre la aplicacion
    */
   public void end() {

      try {
//            smallCrearElemento = null;
//            smallCrearIncidencia = null;
         gestionElementos.end();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         e.printStackTrace();
      }

   }

   @Override
   public void onActivityCreated(Bundle savedInstanceState) {
      super.onActivityCreated(savedInstanceState);

      try {

         instance = this;

         getMapAsync(this);

         // TODO: AORTIZ
         // El método getMap() ha desaparecido rn la última actualización de Google
         // Ahora se llama a getMapAsync()
            /*
            map = getMap();
            map.getUiSettings().setZoomControlsEnabled(true);
            map.getUiSettings().setMapToolbarEnabled(false);

            //TODO: FAROCA
            map.setInfoWindowAdapter(new GoogleMap.InfoWindowAdapter() {
                @Override
                public View getInfoWindow(Marker marker) {
                    // Empty info window.
                    return new View(MainActivity.getInstance().getApplicationContext());
                }

                @Override
                public View getInfoContents(Marker marker) {
                    return null;
                }
            });

            createButtonFloat();
            gestionElementos = new GestionElementos(map,
                    new ClusterManager<ItemMapa>(MainActivity.getInstance(),
                            map));

            showAreas();
            */

      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

   }

   @Override
   public synchronized void onMapReady(GoogleMap theMap) {
      try {
         // TODO: AORTIZ
         map = theMap;
         map.getUiSettings().setZoomControlsEnabled(true);
         map.getUiSettings().setMapToolbarEnabled(false);
         //Aumentar zoom del mapa
         map.setMaxZoomPreference(27.0f);
         map.setIndoorEnabled(false);
         map.setBuildingsEnabled(false);

         createButtonFloat();
         mClusterManager = new ClusterManager<>(MainActivity.getInstance(), map);

         gestionElementos = new GestionElementos(map, mClusterManager);

         // Inciamos el singleton que cacheará los cambios de posición.
         PositionService.getInstance();

      } catch (Throwable e) {
         e.printStackTrace();
      }
   }

   public synchronized void showDiasBloqueoMode() {
      try {
         if (map != null) {
            if (markersDiasBloqueo.size() > 0)
               removeMarkersDiasBloqueo();

            SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
            int minZoomCluster = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_CLUSTER, "14"));
            if (GestionElementos.getInstance().getActualZoom() >= minZoomCluster) {
               LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;

               Calendar calendar = Calendar.getInstance();
               int day = calendar.get(Calendar.DAY_OF_WEEK);

               // Se recupera el elemento asociado a la operación
               DBElemento dbElemento = new DBElemento();
               final ArrayList<Elemento> elementos = dbElemento.getByDiasBloqueo(day, MainActivity.getInstance().getEmpresa(), bounds.southwest.latitude,
                     bounds.southwest.longitude,
                     bounds.northeast.latitude,
                     bounds.northeast.longitude);

               dbElemento.close();

               for (final Elemento elemento : elementos) {

                  if (elemento == null || elemento.getEstado() != Elemento.ESTADO_ACTIVO || isNotElementoVisible(elemento))
                     continue;

                  MarkerOptions options = new MarkerOptions();
                  options.position(elemento.getPosition());

                  BitmapDescriptor bitmap = BitmapDescriptorFactory.fromResource(R.drawable.ico_ko);
                  options.icon(bitmap);
                  options.zIndex(Integer.MAX_VALUE);

                  Marker m = map.addMarker(options);

                  // Estos marcadores acompañan a elementos, pero no se incluyen en el
                  // cluster. Al estar superpuestos, muchas veces interceptan la pulsación
                  // sobre el elemento y no hacen nada. Para evitar esto, se incluye un
                  // Runnable en el Tag que se ejecutará en el método onMarkerClick() de
                  // ClusterManager.java. Este runnable ejecuta una pulsación sobre el
                  // elemento, que tendrá su marcador asociado en el mapa.
                  m.setTag(new Runnable() {
                     @Override
                     public void run() {
                        try {
                           gestionElementos.onClusterItemClick(elemento);
                        } catch (Throwable e) {
                        }
                     }
                  });

                  markersDiasBloqueo.add(m);
               }
            }
         }

      } catch (Throwable e) {
         e.printStackTrace();
      }

   }

   // public synchronized void showOperationsDone() {
   //    try {
   //       if (map == null) return;
   //       SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
   //       int minZoomCluster = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_CLUSTER, "14"));
   //       int minZoomElements = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_MINZOOM_NOSHOWCLUSTER, "11"));
   //       int actualZoom = GestionElementos.getInstance().getActualZoom();

   //       if (actualZoom >= minZoomCluster && actualZoom >= minZoomElements) {
   //          Date now = new Date();
   //          int hoursCheckCollected = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_LEVEL, "18").trim());
   //          int hoursCheckCleaned = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_CLEAN, "18").trim());
   //          long fromDateCollected = HelperDates.getInstance().addHours(now, -hoursCheckCollected).getTime();
   //          long fromDateCleaned = HelperDates.getInstance().addHours(now, -hoursCheckCleaned).getTime();
   //          LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;

   //          DBOperationsDone dbOperationsDone = new DBOperationsDone();
   //          ArrayList<OperationsDone> operationsDone = dbOperationsDone.getFromDateLatLonAndOperation(fromDateCollected,
   //                bounds.southwest.latitude,
   //                bounds.southwest.longitude,
   //                bounds.northeast.latitude,
   //                bounds.northeast.longitude,
   //                OperationsEnum.COLLECT.getKey()
   //          );
   //          operationsDone.addAll(dbOperationsDone.getFromDateLatLonAndOperation(fromDateCollected,
   //                bounds.southwest.latitude,
   //                bounds.southwest.longitude,
   //                bounds.northeast.latitude,
   //                bounds.northeast.longitude,
   //                OperationsEnum.LEVEL.getKey()
   //          ));
   //          operationsDone.addAll(dbOperationsDone.getFromDateLatLonAndOperation(fromDateCleaned,
   //                bounds.southwest.latitude,
   //                bounds.southwest.longitude,
   //                bounds.northeast.latitude,
   //                bounds.northeast.longitude,
   //                OperationsEnum.CLEAN.getKey()
   //          ));
   //          dbOperationsDone.close();

   //          /* Mantis 6028: Optimización, al mover el mapa los marcadores parpadeaban y a veces se quedaban sin mostrar */
   //          /* Mantis 6034: Habría que usar getIcon para distinquir operación y setVisible para ocultar según filtro de elementos */
   //          // Eliminar duplicados en operationsDone para no volver a pintarlos
   //          // ArrayList<OperationsDone> operationsDoneCopy = new ArrayList<>(operationsDone);
   //          // for (OperationsDone operationDone : operationsDoneCopy) {
   //          //     for (Marker marker : markersOperationsDones) {
   //          //         if (operationDone.getLat() == marker.getPosition().latitude &&
   //          //               operationDone.getLng() == marker.getPosition().longitude) {
   //          //             operationsDone.remove(operationDone);
   //          //             break;
   //          //         }
   //          //     }
   //          // }

   //          // // Elementos a eliminar en markersOperationsDones que no están en operationsDone,
   //          // // es decir, están fuera de la zona visible del mapa
   //          // ArrayList<Marker> markersToRemove = new ArrayList<>();
   //          // for (Marker marker : markersOperationsDones) {
   //          //     boolean found = false;
   //          //     for (OperationsDone operationDone : operationsDoneCopy) {
   //          //         if (operationDone.getLat() == marker.getPosition().latitude &&
   //          //               operationDone.getLng() == marker.getPosition().longitude) {
   //          //             found = true;
   //          //             break;
   //          //         }
   //          //     }
   //          //     if (!found) {
   //          //         markersToRemove.add(marker);
   //          //     }
   //          // }

   //          // // Eliminar los marcadores de markersOperationsDones y del mapa
   //          // for (Marker marker : markersToRemove) {
   //          //     marker.remove();
   //          //     markersOperationsDones.remove(marker);
   //          // }
   //          /* Mantis 6028: Fin */
   //          final HashSet<Marker> oldMarkers = new HashSet<>(markersOperationsDones);
   //          DBElemento dbElemento = new DBElemento();
   //          final int empresa = MainActivity.getInstance().getEmpresa();
   //          for (OperationsDone operationDone : operationsDone) {
   //             final Elemento elemento = dbElemento.getByIdInterno(operationDone.getIdInternoElemento(), empresa);
   //             // Si no hay un elemento asociado a la operación o éste no está activo, no se muestra en el mapa
   //             if (elemento == null || elemento.getEstado() != Elemento.ESTADO_ACTIVO || isNotElementoVisible(elemento))
   //                continue;

   //             // Se crea el marcador
   //             MarkerOptions options = new MarkerOptions();
   //             options.position(elemento.getPosition());
   //             options.title(operationDone.toString());
   //             options.zIndex(Integer.MAX_VALUE);
   //             if (operationDone.getCodeOperation() == OperationsEnum.COLLECT.getKey()
   //                   || operationDone.getCodeOperation() == OperationsEnum.LEVEL.getKey())
   //                options.icon(BitmapDescriptorFactory.fromResource(R.drawable.ico_ok2));
   //             if (operationDone.getCodeOperation() == OperationsEnum.CLEAN.getKey())
   //                options.icon(BitmapDescriptorFactory.fromResource(R.drawable.ico_ok3));

   //             Marker m = map.addMarker(options);

   //             // Estos marcadores acompañan a elementos, pero no se incluyen en el
   //             // cluster. Al estar superpuestos, muchas veces interceptan la pulsación
   //             // sobre el elemento y no hacen nada. Para evitar esto, se incluye un
   //             // Runnable en el Tag que se ejecutará en el método onMarkerClick() de
   //             // ClusterManager.java. Este runnable ejecuta una pulsación sobre el
   //             // elemento, que tendrá su marcador asociado en el mapa.
   //             m.setTag((Runnable) () -> {
   //                try {
   //                   gestionElementos.onClusterItemClick(elemento);
   //                } catch (Throwable e) {
   //                   Logg.error(TAG, e.toString());
   //                }
   //             });
   //             markersOperationsDones.add(m);
   //          }
   //          dbElemento.close();
   //          removeMarkers(oldMarkers);
   //          markersOperationsDones.removeAll(oldMarkers);
   //       }
   //    } catch (Throwable e) {
   //       e.printStackTrace();
   //    }
   // }

   public synchronized void showNivelesLlenado() {
      try {
         if (map == null)
            return;
         SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
         int showLevels = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_SHOW_LEVELS, "0"));
         int minZoomCluster = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_CLUSTER, "14"));
         int actualZoom = GestionElementos.getInstance().getActualZoom();

         if (showLevels == 0 || actualZoom < minZoomCluster) {
            removeMarkersNivelesLlenado();
            return;
         }

         DBSensoresNivelesLlenado dbSensoresNivelesLlenado = new DBSensoresNivelesLlenado();
         ArrayList<SensorNivelLLenado> niveles = dbSensoresNivelesLlenado.getSensores(MainActivity.getInstance().getEmpresa());
         dbSensoresNivelesLlenado.close();
         if (niveles.isEmpty())
            return;
            
         String modelosVisibles = Config.getInstance().getValueUsuario("modelVisibles", "todos");
         if (modelosVisibles.equals("ninguno"))
            return;

         LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
         // TODO: Operación costosa, obtener los elementos del mapa o añadir el bitmap como los ticks
         DBElemento dbElemento = new DBElemento();
         ArrayList<ItemMapa> elems = dbElemento.getByLatLon(
               MainActivity.getInstance().getEmpresa(),
               bounds.southwest.latitude,
               bounds.southwest.longitude,
               bounds.northeast.latitude,
               bounds.northeast.longitude,
               modelosVisibles, Elemento.ESTADO_ACTIVO);

         dbElemento.close();

         ArrayList<Marker> markersNivelesLlenadoNew = new ArrayList<>();
         for (ItemMapa elem : elems) {
            Elemento elemento = null;
            if (elem instanceof Elemento)
               elemento = (Elemento) elem;
            else
               return;

            if (!elemento.hasLinkedVolumetric())
               continue;

            //Obtener porcentaje
            for (SensorNivelLLenado nivel : niveles) {

               if (nivel != null && nivel.getCodigoElemento() == elemento.getIdExterno()) {

                  //icono nivel de llenado
                  MarkerOptions options = new MarkerOptions();
                  options.position(elemento.getPosition());

                  BitmapDescriptor bitmap = null;
                  int resource = getIconNivelLlenado(nivel.getNumeroFraccion());
                  //bitmap = BitmapDescriptorFactory.fromResource(resource);
                  Bitmap bmp = BitmapFactory.decodeResource(getResources(), resource);

                  Bitmap bmp2 = com.movisat.utilities.Utils.writeTextOnDrawable(MainActivity.getInstance(), bmp,
                        String.valueOf(nivel.getNumeroFraccion()), 14,
                        nivel.getNumeroFraccion() <= 50 ? Color.BLACK : Color.WHITE, Paint.Align.CENTER);

                  bmp2 = Bitmap.createScaledBitmap(bmp2, 75, 75, false);

                  try {
                     bitmap = BitmapDescriptorFactory.fromBitmap(bmp2);
                  } catch (RuntimeException e) {
                     continue;
                  }

                  options.anchor(0f, 1f);

                  options.icon(bitmap);
                  options.zIndex(Integer.MAX_VALUE);

                  Marker m = map.addMarker(options);
                  Elemento finalElemento = elemento;
                  m.setTag((Runnable) () -> {
                     try {
                        gestionElementos.onClusterItemClick(finalElemento);
                     } catch (Throwable e) {
                     }
                  });
                  markersNivelesLlenadoNew.add(m);
               }
            }
         }
         markersNivelesLlenado.removeAll(markersNivelesLlenadoNew);
         removeMarkers(markersNivelesLlenado);
         markersNivelesLlenado.clear();
         markersNivelesLlenado.addAll(markersNivelesLlenadoNew);
      } catch (Throwable e) {
         e.printStackTrace();
      }
   }

   public static LatLng getLatLongOffSet(GoogleMap mapa, LatLng target, int despX, int despY) {

      try {
         if (mapa != null) {
            Projection projection = mapa.getProjection();
            if (projection != null) {
               Point screenLocation = projection.toScreenLocation(target);
               screenLocation.x += despX;
               screenLocation.y += despY;
               LatLng offsetTarget = projection.fromScreenLocation(screenLocation);
               if (offsetTarget != null)
                  return offsetTarget;
            }
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
      return null;


   }

   private int getFraccion(String value) {
      String num = value.split(",")[0].replaceAll("Fracción: ", "");
      //FALTA CALCULAR PORCENTAJE
      return Integer.parseInt(num);
   }

   /**
    * Indica si el elemento es visible en función de la visibilidad del modelo.
    */
   private boolean isNotElementoVisible(Elemento elemento) {
      if (elemento == null) return true;
      try {
         String modelosVisibles = Config.getInstance().getValueUsuario("modelVisibles", "todos");

         if (modelosVisibles.equals("todos")) return false;
         if (modelosVisibles.equals("ninguno")) return true;

         String[] arrayModels = modelosVisibles.split(",");
         // Si está en la lista lo pongo como visible
         for (String id : arrayModels) {
            if (Integer.parseInt(id) == elemento.getModelo())
               return false;
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
      return true;
   }

//    public synchronized void showFrecuenciaProcesado() {
//           if (map != null) {
//               SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
//               int showCollectionFreq = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_SHOW_COLLECTION_FREQ, "0"));
//               int minZoomCluster = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_CLUSTER, "14"));
//               int zoomActual = GestionElementos.getInstance().getActualZoom();
//               if (showCollectionFreq == 0 || zoomActual < minZoomCluster) {
//                  removeFrecuenciaProcesado();
//                  return;
//               }
//               // Se obtienen los elementos visibles en el mapa
//               DBElemento dbElemento = new DBElemento();
//               LatLngBounds bounds = map.getProjection().getVisibleRegion().latLngBounds;
//               ArrayList<ItemMapa> elems = dbElemento.getByLatLon(
//                     MainActivity.getInstance().getEmpresa(),
//                     bounds.southwest.latitude,
//                     bounds.southwest.longitude,
//                     bounds.northeast.latitude,
//                     bounds.northeast.longitude,
//                     "");
//               dbElemento.close();
//
//               HashSet<Marker> markersToRemove = new HashSet<>(markersFrecuenciaProcesado);
//               ArrayList<Marker> markersToAdd = new ArrayList<>();
//
//               for (ItemMapa elem : elems) {
//                   Elemento elemento = null;
//                   if (elem instanceof Elemento)
//                       elemento = (Elemento) elem;
//                   if (isNotElementoVisible(elemento)) continue;
//                   if (elemento.getFechaUltRecogida().equals("")) continue;
//
//                   FrecuenciaProcesadoState state = elemento.getFrecuenciaProcesadoState();
//                   if (state == FrecuenciaProcesadoState.NONE) continue; // No se muestra nada
//
//                   int iconR = getIconFrecuenciaProcesado(state);
//                   Bitmap bmp = BitmapFactory.decodeResource(getResources(), iconR);
//                   bmp = Bitmap.createScaledBitmap(bmp, 25, 25, false);
//                   BitmapDescriptor bitmap = BitmapDescriptorFactory.fromBitmap(bmp);
//
//                   MarkerOptions options = new MarkerOptions();
//                   options.icon(bitmap);
//                   options.position(elemento.getPosition());
//                   options.anchor(1f, 1f);
//                   options.zIndex(Integer.MAX_VALUE);
//
//                   Marker m = map.addMarker(options);
//                   Elemento finalElemento = elemento;
//                   m.setTag((Runnable) () -> {
//                       try {
//                           gestionElementos.onClusterItemClick(finalElemento);
//                       } catch (Throwable e) {
//                           Logg.error(TAG, e.toString());
//                       }
//                   });
//                   markersToAdd.add(m);
//                   markersToRemove.remove(m);
//               }
//               for (Marker marker : markersToRemove) {
//                  marker.remove();
//                  markersFrecuenciaProcesado.remove(marker);
//               }
//               markersFrecuenciaProcesado.addAll(markersToAdd);
//           }
//       }

   public static int getIconNivelLlenado(int porcentaje) {
      try {
         if (porcentaje == 0) return R.drawable.circulo_blanco;
         if (porcentaje >= 1 && porcentaje <= 25) return R.drawable.circulo_verde;
         if (porcentaje >= 26 && porcentaje <= 50) return R.drawable.circulo_amarillo;
         if (porcentaje >= 51 && porcentaje <= 75) return R.drawable.circulo_naranja;
         if (porcentaje >= 76 && porcentaje <= 100) return R.drawable.circulo_rojo;
         if (porcentaje > 100) return R.drawable.circulo_negro;
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
      return 0;
   }

   public Bitmap getBitMapFrecuencia(FrecuenciaProcesadoState state) {
      if (state == FrecuenciaProcesadoState.NONE)
         return null;
      int iconR = getIconFrecuenciaProcesado(state);
      Bitmap bmp = BitmapFactory.decodeResource(getResources(), iconR);
      return Bitmap.createScaledBitmap(bmp, 25, 25, false);
   }

   public static int getIconFrecuenciaProcesado(FrecuenciaProcesadoState frecuencia) {
      try {
         if (frecuencia == FrecuenciaProcesadoState.EXCEEDED)
            return R.drawable.circulo_rojo;
         if (frecuencia == FrecuenciaProcesadoState.OVER_HALF)
            return R.drawable.circulo_amarillo;
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
      return 0;
   }

   public synchronized void removeMarkersOperationsMode() {
      removeMarkers(markersOperationsDones);
      markersOperationsDones.clear();
   }

   public synchronized void removeMarkersNivelesLlenado() {
      removeMarkers(markersNivelesLlenado);
   }

   public synchronized void removeMarkersDiasBloqueo() {
      removeMarkers(markersDiasBloqueo);
   }

//   public synchronized void removeFrecuenciaProcesado() {
//      removeMarkers(markersFrecuenciaProcesado);
//      markersFrecuenciaProcesado.clear();
//   }

   private void removeMarkers(Iterable<Marker> it) {
      for (Marker m : it) {
         m.remove();
      }
   }

   @Override
   public void onResume() {
      super.onResume();
      showNivelesLlenado();

      if (Environment.isSoftCamacho)
         showDiasBloqueoMode();

      updateGpsButtonAppearance();
      
      // Reposicionar botón GPS en caso de cambios de configuración
      repositionGpsButton();

//        createButtonFloat();
//        MyLoggerHandler.getInstance().info("Regenerando boton float");
//        Log.i("GESTION_MAPA","Regenerando boton float");
   }

   @Override
   public void onPause() {
      super.onPause();
//        smallCrearElemento = null;
//        smallCrearIncidencia = null;
//        MyLoggerHandler.getInstance().info("Pausando botones float");
//        Log.i("GESTION_MAPA", "Pausando boton float");
   }

   private synchronized void createButtonFloat() {
      if (smallCrearElemento == null) {
         //MyLoggerHandler.getInstance().info("Generando boton float elemento");
         //Log.i("GESTION_MAPA", "Generando boton float elemento");
         RelativeLayout layout = (RelativeLayout) MainActivity.getInstance()
               .findViewById(R.id.content_frame);
         Resources res = getActivity().getResources();
         XmlPullParser parser = res.getXml(R.layout.button_float);
         AttributeSet attributes = Xml.asAttributeSet(parser);

         smallCrearElemento = new FloatingActionButton(getActivity());
         smallCrearElemento.setSize(FloatingActionButton.SIZE_NORMAL);
         smallCrearElemento.setColorNormalResId(R.color.theme_principal);
         smallCrearElemento.setColorPressedResId(R.color.theme_principal_hover);
         smallCrearElemento.setIcon(R.drawable.ic_action_ok);
         //smallCrearElemento.setVerticalScrollbarPosition();
         smallCrearElemento.setStrokeVisible(false);

         if (Environment.hasReaderLFChainway || Environment.hasReaderUHFC71
               || Build.MODEL.equals("PDA")) {
            leerTag = new FloatingActionButton(getActivity());
            leerTag.setSize(FloatingActionButton.SIZE_NORMAL);
            leerTag.setColorNormalResId(R.color.theme_principal);
            leerTag.setColorPressedResId(R.color.theme_principal_hover);
            leerTag.setIcon(R.drawable.ic_uhf_reader_white);
            leerTag.setStrokeVisible(false);
            leerTag.setVisibility(View.VISIBLE);
            android.view.View.OnClickListener clickFloatButtonLeerTag = new MyClickFloatButtonLeerTag();
            leerTag.setOnClickListener(clickFloatButtonLeerTag);
            layout.addView(leerTag);


            try {
               RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) leerTag.getLayoutParams();
               params.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
               params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
               leerTag.setLayoutParams(params);
               
               // Reposicionar el botón GPS cuando leerTag se hace visible
               repositionGpsButton();
            } catch (Exception e) {
               e.printStackTrace();
            }
         }
//            smallCrearElemento.setBackgroundColor(res
//                    .getColor(R.color.theme_principal));
//            smallCrearElemento.setDrawableIcon(res
//              /      .getDrawable(R.drawable.ic_action_ok));
         // small.set
         // small.isShow = false;

         // small.setVisibility(getView().GONE);
         smallCrearElemento.setVisibility(View.GONE);
         android.view.View.OnClickListener clickFloatButtonCrearElemento = new MyClickFloatButtonCrearElemento();
         smallCrearElemento
               .setOnClickListener(clickFloatButtonCrearElemento);
         //setPositionButtonFloat(smallCrearElemento);

         layout.addView(smallCrearElemento);
      }

      if (smallDepositarElemento == null) {
         //MyLoggerHandler.getInstance().info("Generando boton float elemento");
         //Log.i("GESTION_MAPA", "Generando boton float elemento");
         RelativeLayout layout = (RelativeLayout) MainActivity.getInstance()
               .findViewById(R.id.content_frame);
         Resources res = getActivity().getResources();
//            XmlPullParser parser = res.getXml(R.layout.button_float);
//            AttributeSet attributes = Xml.asAttributeSet(parser);

         smallDepositarElemento = new FloatingActionButton(getActivity());
         smallDepositarElemento.setSize(FloatingActionButton.SIZE_NORMAL);
         smallDepositarElemento.setColorNormalResId(R.color.theme_principal);
         smallDepositarElemento.setColorPressedResId(R.color.theme_principal_hover);
         smallDepositarElemento.setIcon(R.drawable.ic_action_depositar);
         smallDepositarElemento.setStrokeVisible(false);
         smallDepositarElemento.setVisibility(View.GONE);
         android.view.View.OnClickListener clickFloatButtonDepositarElemento = new MyClickFloatButtonDepositarElemento();
         smallDepositarElemento.setOnClickListener(clickFloatButtonDepositarElemento);

         layout.addView(smallDepositarElemento);
      }

      if (smallCrearIncidencia == null) {
         //MyLoggerHandler.getInstance().info("Generando boton float incidencia");
         //Log.i("GESTION_MAPA", "Generando boton float incidencia");
         RelativeLayout layout = (RelativeLayout) MainActivity.getInstance()
               .findViewById(R.id.content_frame);
         Resources res = getActivity().getResources();
         XmlPullParser parser = res.getXml(button_float);
         AttributeSet attributes = Xml.asAttributeSet(parser);

         smallCrearIncidencia = new FloatingActionButton(getActivity());
         smallCrearIncidencia.setColorNormalResId(R.color.theme_complementary);
         smallCrearIncidencia.setColorPressedResId(R.color.theme_triad);
         smallCrearIncidencia.setIcon(R.drawable.ic_action_ok);
         smallCrearIncidencia.setVisibility(View.GONE);
         android.view.View.OnClickListener clickFloatButton = new MyClickFloatButtonCrearIncidencia();
         smallCrearIncidencia.setOnClickListener(clickFloatButton);
//            setPositionButtonFloat(smallCrearIncidencia);

         layout.addView(smallCrearIncidencia);
      }

      if (smallAsignarIncidencia == null) {
         //MyLoggerHandler.getInstance().info("Generando boton float incidencia");
         //Log.i("GESTION_MAPA", "Generando boton float incidencia");
         RelativeLayout layout = (RelativeLayout) MainActivity.getInstance()
               .findViewById(R.id.content_frame);
         Resources res = getActivity().getResources();
         XmlPullParser parser = res.getXml(button_float);
         AttributeSet attributes = Xml.asAttributeSet(parser);

         smallAsignarIncidencia = new FloatingActionButton(getActivity());
         smallAsignarIncidencia.setColorNormalResId(R.color.theme_complementary);
         smallAsignarIncidencia.setColorPressedResId(R.color.theme_triad);
         smallAsignarIncidencia.setIcon(R.drawable.ic_action_asignar);
         smallAsignarIncidencia.setVisibility(View.GONE);
         View.OnClickListener clickFloatButton = new MyClickFloatButtonAsignarIncidencia();
         smallAsignarIncidencia.setOnClickListener(clickFloatButton);
//            setPositionButtonFloat(smallAsignarIncidencia);

         layout.addView(smallAsignarIncidencia);
      }

      if (gpsLocationButton == null) {
         RelativeLayout layout = (RelativeLayout) MainActivity.getInstance()
               .findViewById(R.id.content_frame);

         gpsLocationButton = new FloatingActionButton(getActivity());
         gpsLocationButton.setSize(FloatingActionButton.SIZE_NORMAL);
         gpsLocationButton.setColorNormalResId(R.color.theme_principal);
         gpsLocationButton.setColorPressedResId(R.color.theme_principal_hover);
         gpsLocationButton.setIcon(R.drawable.ic_location);
         gpsLocationButton.setStrokeVisible(false);
         gpsLocationButton.setVisibility(View.VISIBLE);

         View.OnClickListener clickGpsButton = new MyClickFloatButtonGpsLocation();
         gpsLocationButton.setOnClickListener(clickGpsButton);

         updateGpsButtonAppearance();

         View.OnLongClickListener longClickGpsButton = new MyLongClickFloatButtonGpsLocation();
         gpsLocationButton.setOnLongClickListener(longClickGpsButton);

         layout.addView(gpsLocationButton);

         // Usar función de reposicionamiento dinámico
         repositionGpsButton();
      }
   }

   private DisplayMetrics displayMetrics;
   private boolean isPortrait;

   int _dp(float pixels) {
      return (int) (pixels * displayMetrics.density);
   }

   float _sp(float pixels) {
      return (pixels * displayMetrics.scaledDensity);
   }

   /**
    * Calcula el margen inferior dinámico para el botón GPS según botones visibles
    */
   private int calculateGpsButtonBottomMargin() {
      int buttonHeight = Utils.dpToPx(56, getResources());
      int spacing = Utils.dpToPx(12, getResources());
      int totalOffset = 0;
      
      // Verificar si leerTag está visible y añadir offset
      if (leerTag != null && leerTag.getVisibility() == View.VISIBLE) {
         totalOffset += buttonHeight + spacing;
      }
      
      // Agregar offset por otros botones visibles en la esquina inferior izquierda si los hay
      // (para futuras expansiones)
      
      return totalOffset;
   }

   /**
    * Reposiciona el botón GPS dinámicamente según otros botones visibles
    */
   private void repositionGpsButton() {
      if (gpsLocationButton != null) {
         try {
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) gpsLocationButton.getLayoutParams();
            params.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
            params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
            
            int bottomMargin = calculateGpsButtonBottomMargin();
            params.setMargins(0, 0, 0, bottomMargin);
            
            gpsLocationButton.setLayoutParams(params);
         } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
         }
      }
   }

   /**
    * Obtiene la ubicación actual y la devuelve en el hilo principal.
    * Compatibilidad: API 21-34
    *
    * @param activity Activity que mostrará los diálogos de permiso o ajustes
    * @param callback Lambda/Interfaz con lat y lon (p.e. (lat,lon)->{ ... })
    */
   public static void getCurrentLocation(
      @NonNull Activity activity,
      @NonNull BiConsumer<Double, Double> callback
   ) {

      // El usuario puede denegar el permiso de ubicación en cualquier momento,
      // debemos asegurarnos que lo tenemos
      if (ContextCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_COARSE_LOCATION)
              != PackageManager.PERMISSION_GRANTED &&
              ContextCompat.checkSelfPermission(activity, Manifest.permission.ACCESS_FINE_LOCATION)
                      != PackageManager.PERMISSION_GRANTED) {

         ActivityCompat.requestPermissions(activity,
                 new String[]{
                         Manifest.permission.ACCESS_FINE_LOCATION,
                         Manifest.permission.ACCESS_COARSE_LOCATION
                 },
                 1001);
         return;
      }

      Toast.makeText(activity, R.string.obteniendo_ubicacion_actual, Toast.LENGTH_SHORT).show();

      boolean gmsOk = GoogleApiAvailabilityLight.getInstance()
              .isGooglePlayServicesAvailable(activity) == ConnectionResult.SUCCESS;

      if (gmsOk) {
         FusedLocationProviderClient fused = LocationServices
                 .getFusedLocationProviderClient(activity);

         // Posición cacheada
         fused.getLastLocation()
                 .addOnSuccessListener(activity, loc -> {
                    if (loc != null) {
                       callback.accept(loc.getLatitude(), loc.getLongitude());
                    }
                 });

         // Posición actualizada
         fused.getCurrentLocation(Priority.PRIORITY_HIGH_ACCURACY, null)
                 .addOnSuccessListener(activity, loc -> {
                    if (loc != null) {
                       callback.accept(loc.getLatitude(), loc.getLongitude());
                    }
                 });

      } else {
         LocationManager lm = (LocationManager) activity
                 .getSystemService(Context.LOCATION_SERVICE);

         if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) { // API 30+
            lm.getCurrentLocation(
                    LocationManager.GPS_PROVIDER,
                    null,
                    activity.getMainExecutor(),
                    loc -> {
                       if (loc != null) {
                          callback.accept(loc.getLatitude(), loc.getLongitude());
                       }
                    });

         } else { // API 21-29
            LocationListener ll = new LocationListener() {
               @Override
               public void onLocationChanged(@NonNull Location loc) {
                  callback.accept(loc.getLatitude(), loc.getLongitude());
                  lm.removeUpdates(this);
               }
            };

            lm.requestSingleUpdate(LocationManager.GPS_PROVIDER, ll, Looper.getMainLooper());
         }
      }
   }

   public void setPositionButtonFloat(ButtonFloat small) {
      RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
            ActionBar.LayoutParams.WRAP_CONTENT, ActionBar.LayoutParams.WRAP_CONTENT);
      params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
      params.setMargins(Utils.dpToPx(_dp(5), getResources()),
            Utils.dpToPx(_dp(5), getResources()),
            Utils.dpToPx(_dp(5), getResources()),
            Utils.dpToPx(_dp(5), getResources()));
      small.setLayoutParams(params);
   }

   private void doIfTimeLapsed(IFuncVoid function) {
      if (!isReadingTag) {
         isReadingTag = true;
         function.execute();
         readTagTimer.start();
      }

   }

   public static boolean isVisibleGrupo(int IdGrupo) {
      DBArea db = new DBArea();
      List<Area> areas = db.getAreasBy(IdGrupo);

      db.close();

      if (areas == null || areas.size() == 0) return false;

      for (Area item : areas) {
         if (isVisibleArea(item.getCodigo()))
            return true;
      }

      return false;
   }

   public void removeButtonCrearIncidencia() {
      synchronized (smallCrearIncidencia) {
         if (smallCrearIncidencia != null) {
            smallCrearIncidencia.setVisibility(View.GONE);
//                smallCrearIncidencia = null;
         }
      }
   }

   public void removeButtonAsignarIncidencia() {
      synchronized (smallAsignarIncidencia) {
         if (smallAsignarIncidencia != null) {
            smallAsignarIncidencia.setVisibility(View.GONE);
//                smallCrearIncidencia = null;
         }
      }
   }

   public void removeButtonCrearElemento() {
      synchronized (smallCrearElemento) {
         if (smallCrearElemento != null) {
            smallCrearElemento.setVisibility(View.GONE);
//                smallCrearElemento = null;
//                smallCrearElemento.dispose
         }
      }
   }

   public void removeButtonDepositarElemento() {
      synchronized (smallDepositarElemento) {
         if (smallDepositarElemento != null) {
            smallDepositarElemento.setVisibility(View.GONE);
         }
      }
   }

   public void setVisibleCrearElemento() {
      synchronized (smallCrearElemento) {
         if (smallCrearElemento == null) createButtonFloat();
         smallCrearElemento.setVisibility(View.VISIBLE);
         if (!smallCrearElemento.isEnabled()) smallCrearElemento.setEnabled(true);
         //Log.i(Help.GESTION_ELEMENTOS, "Estableciendo a visible botón crear elemento");
         MyLoggerHandler.getInstance().info("Estableciendo a visible botón crear elemento");
      }
   }

   public void setVisibleDepositarElemento() {
      synchronized (smallDepositarElemento) {
         if (smallDepositarElemento == null) createButtonFloat();
         smallDepositarElemento.setVisibility(View.VISIBLE);
         //Log.i(Help.GESTION_ELEMENTOS, "Estableciendo a visible botón depositar elemento");
         MyLoggerHandler.getInstance().info("Estableciendo a visible botón depositar elemento");
      }
   }

   public void setVisibleCrearIncidencia() {
      synchronized (smallCrearIncidencia) {
         if (smallCrearIncidencia == null) createButtonFloat();
         smallCrearIncidencia.setVisibility(View.VISIBLE);
         //Log.i(Help.GESTION_ELEMENTOS, "Estableciendo a visible botón crear incidencia");
         MyLoggerHandler.getInstance().info("Estableciendo a visible botón crear incidencia");
      }
   }

   public void setVisibleAsignarIncidencia() {
      synchronized (smallAsignarIncidencia) {
         if (smallAsignarIncidencia == null) createButtonFloat();
         smallAsignarIncidencia.setVisibility(View.VISIBLE);
         //Log.i(Help.GESTION_ELEMENTOS, "Estableciendo a visible botón crear incidencia");
         MyLoggerHandler.getInstance().info("Estableciendo a visible botón crear incidencia");
      }
   }

   public void setEnabledCrearElemento(boolean enabledCrearElemento) {
      synchronized (smallCrearElemento) {
         if (smallCrearElemento == null) createButtonFloat();
         smallCrearElemento.setEnabled(enabledCrearElemento);
      }
   }

   public void setEnabledDepositarElemento(boolean enabledDepositarElemento) {
      synchronized (smallDepositarElemento) {
         if (smallDepositarElemento == null) createButtonFloat();
         smallDepositarElemento.setEnabled(enabledDepositarElemento);
      }
   }

   public void setEnabledCrearIncidencia(boolean enabledCrearIncidencia) {
      synchronized (smallCrearIncidencia) {
         if (smallCrearIncidencia == null) createButtonFloat();
         smallCrearIncidencia.setEnabled(enabledCrearIncidencia);
      }
   }

   public void setEnabledAsignarIncidencia(boolean enabledAsignarIncidencia) {
      synchronized (smallAsignarIncidencia) {
         if (smallAsignarIncidencia == null) createButtonFloat();
         smallAsignarIncidencia.setEnabled(enabledAsignarIncidencia);
      }
   }

   public static class MyClickFloatButtonCrearElemento implements
         android.view.View.OnClickListener {

      @Override
      public void onClick(View v) {
         MyBroadCastManager.getInstance().sendBroadCrearElemento(null, null);
      }

   }

   public class MyClickFloatButtonLeerTag implements
         android.view.View.OnClickListener {

      public final String _TAG = "MyClickFloatButtonLeerTag";

      @Override
      public void onClick(View v) {
         Logg.info(_TAG, "Botón lectura TAG pulsado");

         doIfTimeLapsed(() -> {
            if (Build.MODEL.equals("PDA")) {
               Logg.info(_TAG, "U9000 - Leyendo 134 o UHF");
               TagReaderManager.read134orUHF((buffer, size) -> MainActivity.getInstance().onReaded134TagData(buffer, size));
            }
            // Botón de escaneo UHF (solo para los lectores UHF CHAINWAY)
            else if (Environment.hasReaderUHFC71) {
               if (UHFManager.get().isReading()) {
                  Logg.info(_TAG, "C71 - El servicio estaba leyendo, se detiene");
                  UHFManager.get().stopReadLoop();
               } else {
                  Logg.info(_TAG, "C71 - Leyendo UHF");
                  // Se inicia la lectura UHF si el usuario está identificado en la aplicación
                  MainActivity mainActivity = MainActivity.getInstance();
                  if (mainActivity != null && mainActivity.getUsuario() > 0) {
                     UHFManager.get().readSingleTag(3000);
                  }
               }
            }
         });


      }

   }

   public void updateGpsButtonAppearance() {
      if (gpsLocationButton != null) {
         try {
            boolean isGpsTracking = Integer.parseInt(
                  Config.getInstance().getValueUsuario("segGps", "0")
            ) > 0;

            if (isGpsTracking) {
               gpsLocationButton.setColorNormalResId(R.color.theme_complementary);
               gpsLocationButton.setColorPressedResId(R.color.theme_triad);
               gpsLocationButton.setIcon(R.drawable.ic_location_filled);
            } else {
               gpsLocationButton.setColorNormalResId(R.color.theme_principal);
               gpsLocationButton.setColorPressedResId(R.color.theme_principal_hover);
               gpsLocationButton.setIcon(R.drawable.ic_location);
            }
            
            // Mantener posición correcta al cambiar apariencia
            repositionGpsButton();
         } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
         }
      }
   }

   public static void notifyGpsTrackingStateChanged() {
      GesElemMapFragment instance = GesElemMapFragment.getInstance();
      if (instance != null) {
         instance.updateGpsButtonAppearance();
      }
   }

   public static class MyClickFloatButtonGpsLocation implements
           View.OnClickListener {

      @Override
      public void onClick(View v) {
         try {
            // Verificar el estado actual del seguimiento GPS
            boolean currentGpsState = Integer.parseInt(
                  Config.getInstance().getValueUsuario("segGps", "0")
            ) > 0;
            
            if (currentGpsState) {
               // Verificar si el menú de seguimiento GPS está habilitado desde intranet
               MainActivity mainActivity = MainActivity.getInstance();
               if (mainActivity != null && mainActivity.isValidMenu(MainActivity.MENU_SEGUIMIENTO_GPS, "")) {
                  // El seguimiento GPS está activo y el menú está habilitado, desactivarlo
                  Config.getInstance().setValueUsuario("segGps", "0");
                  MyBroadCastManager.getInstance().sendBroadCastEnabledSeguimientoGPS(false);
                  
                  // Actualizar apariencia del botón
                  GesElemMapFragment instance = GesElemMapFragment.getInstance();
                  if (instance != null) {
                     instance.updateGpsButtonAppearance();
                  }
                  
                  // Actualizar checkbox del menú lateral y adaptador
                  if (mainActivity.adapterMenuDerecho != null) {
                     mainActivity.adapterMenuDerecho.notifyDataSetChanged();
                  }
               }
            } else {
               // El seguimiento GPS no está activo, centrar el mapa manualmente en la ubicación actual
               getCurrentLocation(MainActivity.getInstance(), (lat, lon) -> {
                  if (lat != null && lon != null) {
                     LatLng userLocation = new LatLng(lat, lon);
                     GestionElementos.getInstance().centerMap(userLocation);
                  }
               });
            }
         } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
         }
      }
   }

   public static class MyLongClickFloatButtonGpsLocation implements
           View.OnLongClickListener {

      @Override
      public boolean onLongClick(View v) {
         try {
            // Verificar si el menú de seguimiento GPS está habilitado desde intranet
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity == null || !mainActivity.isValidMenu(MainActivity.MENU_SEGUIMIENTO_GPS, "")) {
               // El menú de seguimiento GPS está deshabilitado desde intranet, no hacer nada
               return true;
            }
            
            // Verificar el estado actual del seguimiento GPS
            boolean currentGpsState = Integer.parseInt(
                  Config.getInstance().getValueUsuario("segGps", "0")
            ) > 0;
            
            if (!currentGpsState) {
               // El seguimiento GPS no está activo, activarlo
               Config.getInstance().setValueUsuario("segGps", "1");
               MyBroadCastManager.getInstance().sendBroadCastEnabledSeguimientoGPS(true);
               
               // Actualizar apariencia del botón
               GesElemMapFragment instance = GesElemMapFragment.getInstance();
               if (instance != null) {
                  instance.updateGpsButtonAppearance();
               }
               
               // Actualizar checkbox del menú lateral y adaptador
               if (mainActivity.adapterMenuDerecho != null) {
                  mainActivity.adapterMenuDerecho.notifyDataSetChanged();
               }
            }
            // Si el seguimiento GPS ya está activo, no hacer nada en pulsación larga
         } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
         }
         return true;
      }
   }

   public static class MyClickFloatButtonDepositarElemento implements
         android.view.View.OnClickListener {

      @Override
      public void onClick(View v) {
         MyBroadCastManager.getInstance().sendBroadDepositarElemento();
      }

   }

   public static class MyClickFloatButtonCrearIncidencia implements
         android.view.View.OnClickListener {

      @Override
      public void onClick(View v) {
         MyBroadCastManager.getInstance().sendBroadCrearIncidencia();
      }

   }

   public static class MyClickFloatButtonAsignarIncidencia implements
         android.view.View.OnClickListener {

      @Override
      public void onClick(View v) {
         MyBroadCastManager.getInstance().sendBroadAsignarIncidencia();
      }

   }

   /**
    * Rellena las opciones del submeni
    */
   private void setSubMenuOptions() {

      try {

         for (int i = 0; i < spinnerOptions.length; i++)
            // navSpinner.add(new IconTextItem(Integer
            // .parseInt(spinnerOptions[i].split(";")[0]),
            // spinnerOptions[i].split(";")[1], spinnerOptions[i]
            // .split(";")[2], Integer
            // .parseInt(spinnerOptions[i].split(";")[3])));

            // Recupero el iltimo estado del seguimiento GPS y pongo el
            // estado del item del menu (id=1030) acorde con el estado actual del
            // seguimiento GPS
            GesElemMapFragment.getInstance().setItemSubmenuState(
                  1030,
                  Integer.parseInt(Config.getInstance().getValueUsuario(
                        "segGps", "0")) > 0);

         // Recupero la iltima opciin del submeni de usuario y la selecciono
         lastOptionBar = Integer.parseInt(Config.getInstance()
               .getValueUsuario("ultOpcSubmenu", "0"));

         // actionBar.setSelectedNavigationItem(lastOptionBar);

      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

   }

   public synchronized void invalidateOptionMenuPrincipal() {

      synchronized (this) {
         setHasOptionsMenu(true);

      }

   }

   /*
    * @Override public void onCreateOptionsMenu(Menu menu, MenuInflater
    * inflater) { super.onCreateOptionsMenu(menu, inflater);
    *
    * try {
    *
    * // Compruebo el nivel de zoom actual para ver si hay que quitar o //
    * poner determinadas opciones en el submenu float zoom =
    * map.getCameraPosition().zoom;
    *
    * if (zoom >= GestionElementos.MINZOOM_CLICKABLE && lastZoom <
    * GestionElementos.MINZOOM_CLICKABLE) {
    *
    * // Meto todas las opciones del submenu //navSpinner.clear();
    * //setSubMenuOptions();
    *
    * //actionBar.setSelectedNavigationItem(lastOptBarZoom);
    *
    * } else {
    *
    * if (zoom < GestionElementos.MINZOOM_CLICKABLE && (lastZoom >=
    * GestionElementos.MINZOOM_CLICKABLE || lastZoom == 0)) {
    *
    * lastOptBarZoom = 0;
    *
    * // Quito las opciones del submeni que silo se pueden ver a // zoom bajo
    * for (int i = 0, j = 0; i < navSpinner.size(); i++, j++) {
    *
    * if (Integer.parseInt(spinnerOptions[j].split(";")[4]) > 0) {
    *
    * navSpinner.remove(i); i--;
    *
    * // Guardo la opciin seleccionada actualmente para // volver a
    * seleccionarla cuando se vuelva a hacer // zoom + if (Integer
    * .parseInt(spinnerOptions[j].split(";")[0]) == lastOptionBar)
    * lastOptBarZoom = lastOptionBar; } }
    *
    * //actionBar.setSelectedNavigationItem(0); }
    *
    * }
    *
    * lastZoom = zoom;
    *
    * } catch (Throwable e) { MyLoggerHandler.getInstance().error(e); }
    *
    * }
    */

   /*
    * @Override public boolean onNavigationItemSelected(int itemPosition, long
    * itemId) {
    *
    *
    *
    *
    * return false; }
    */
   // public ActionBar getSupportActionBar() {
   // return actionBar;
   // }

   // public void setActionBar(ActionBar actionBar) {
   // this.actionBar = actionBar;
   // }

   /**
    * Permite cambiar el estado de un item del meni
    */
   public void setItemSubmenuState(int itemId, boolean state) {

      try {

         /*
          * for (int i = 0; i < navSpinner.size(); i++) {
          *
          * // Busco el item por el ID if (navSpinner.get(i).getId() ==
          * itemId) {
          *
          * // Cambio es estado navSpinner.get(i).setState(state);
          *
          * break; }
          *
          * }
          */

      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

   }

   @Override
   public boolean onNavigationItemSelected(int itemPosition, long itemId) {
      return false;
   }

   private static List<MyPolygon> listaPoligonosArea = new LinkedList<MyPolygon>();
   private static List<MyCircle> listaCirculosArea = new LinkedList<MyCircle>();
   private static HashMap<Integer, MyGrupo> IdsGrupos = new HashMap<Integer, MyGrupo>();
   private Polygon poligono = null;
   private Circle circle = null;

   /*
    * private class MyPolyline { private int idRuta; private Polyline polyline;
    *
    * public MyPolyline(int idRuta, Polyline polyline) { this.idRuta = idRuta;
    * this.polyline = polyline; }
    *
    * }
    */
   private class MyCircle {
      private int idArea;
      private Circle circle;

      public MyCircle(int idRuta, Circle circle) {
         this.idArea = idRuta;
         this.circle = circle;
      }

   }

   private class MyPolygon {
      private int idArea;
      private Polygon polygon;
      private List<Marker> marcadores;

      public MyPolygon(int idRuta, Polygon polygon, List<Marker> marcadores) {
         this.idArea = idRuta;
         this.polygon = polygon;
         this.marcadores = marcadores;
      }

   }

   private class MyGrupo {
      private int idGrupo;


      public MyGrupo(int idGrupo) {
         this.idGrupo = idGrupo;
      }
   }

   public static boolean isVisibleGrupoArea(int idGrupo) {

      boolean isVisible = false;
      DBArea db = new DBArea();
      List<Area> areas = db.getAreasBy(idGrupo);
      db.close();
      if (areas == null)
         return false;
      for (Area area : areas) {
         isVisible = isVisibleArea(area.getCodigo());
         if (isVisible)
            return true;
      }

      return false;
   }

   public static boolean isVisibleArea(int codigo) {

      boolean isVisible = false;
      for (MyPolygon item : listaPoligonosArea) {
         if (item.idArea == codigo) {
            isVisible = true;
            break;
         }
      }

      for (MyCircle item : listaCirculosArea) {
         if (item.idArea == codigo) {
            isVisible = true;
            break;
         }
      }

      return isVisible;
   }

   public void showAreas() {
      String gruposAreasVisibles = Config.getInstance().getValueUsuario("gruposAreasVisibles", "ninguno");
      int grupoAreaMedir = Integer.parseInt(Config.getInstance().getValue("grupoMedir", "0"));

      if (gruposAreasVisibles.equals("todos")) {
         showAreasByGrupo(-1, true, Color.GREEN);
      } else if (!gruposAreasVisibles.equals("ninguno")) {
         // Areas concretas
         String[] codigos = gruposAreasVisibles.split(",");


         int idGrupo = -1;
         for (String codigo : codigos) {
            idGrupo = Integer.parseInt(codigo);
            if (idGrupo == grupoAreaMedir)
               showAreasByGrupo(grupoAreaMedir, true, Color.MAGENTA);
            else showAreasByGrupo(grupoAreaMedir, false, Color.GREEN);
         }
      }

      boolean isMedidaIniciada = Config.getInstance().getValue("medidaIniciada", "0").equals("1");

      if (isMedidaIniciada) {
         showAreasByGrupo(grupoAreaMedir, true, Color.MAGENTA);
      }
   }

   public synchronized void showAreasByGrupo(int idGrupo, boolean visible, int color) {
      DBArea db = new DBArea();
      List<Area> areas = null;

      MyGrupo g = new MyGrupo(idGrupo);

      if (visible) {
         //añadimos el grupo a la lista
         IdsGrupos.put(idGrupo, g);
      } else {
         //Quitamos el grupo.
         IdsGrupos.remove(idGrupo);
      }
      if (idGrupo != -1)
         areas = db.getAreasBy(idGrupo);
      else areas = db.getAll(true);
      db.close();
      for (Area area : areas) {
         showArea(area.getCodigo(), visible, color);
      }
   }


   public synchronized void showArea(int idArea, boolean visible, int color) {

      try {

         synchronized (this) {
            double lat = 0, lon = 0;

            int count = 0;
            if (idArea > 0) {

               DBArea dbAreas = new DBArea();
               Area area = dbAreas.getBy(idArea, MainActivity.getInstance().getEmpresa());
               dbAreas.close();
               if (area.getForma() == POLYGON || area.getForma() == SQUARE) { // Poligono
                  deletePoligono(idArea);
                  lat = 0;
                  lon = 0;
                  count = 0;

                  if (visible) {

                     MarkerOptions markerOptions = null;
                     List<Marker> marcadores = new ArrayList<Marker>();

                     PolygonOptions optPoligono = new PolygonOptions().strokeColor(color);
                     Iterator<LatLng> it = area.getListaPuntos().iterator();

                     Iterator<LatLng> puntosControl = area.getListaPuntosControl().iterator();
                     LatLng punto = null;


                     if (puntosControl != null) {
                        while (puntosControl.hasNext()) {
                           punto = puntosControl.next();
                           markerOptions = new MarkerOptions();
                           markerOptions.position(punto);
                           markerOptions.draggable(false);
                           markerOptions
                                 .icon(BitmapDescriptorFactory
                                       .fromResource(R.drawable.ic_action_bullet));
                           marcadores
                                 .add(map.addMarker(markerOptions));
                        }
                     }

                     while (it.hasNext()) {

                        punto = it.next();
                        optPoligono.add(punto);

                        lat += punto.latitude;
                        lon += punto.longitude;
                        count++;
                     }

                     poligono = map.addPolygon(optPoligono);

                     MyPolygon myPol = new MyPolygon(idArea, poligono,
                           marcadores);

                     listaPoligonosArea.add(myPol);

                     setVisibleMarkersAndAreas();
                  }

               } else if (area.getForma() == CIRCLE) { // Circulo
                  deleteCircle(idArea);
                  lat = 0;
                  lon = 0;
                  count = 0;
                  if (visible) {

                     CircleOptions optCircle = new CircleOptions()
                           .strokeColor(color);

                     LatLng centro = area.getPuntoCentro();
                     int radio = area.getRadioLado();
                     lat = centro.latitude;
                     lon = centro.longitude;
                     count++;
                     optCircle.center(centro);
                     optCircle.radius(radio);

                     circle = map.addCircle(optCircle);

                     MyCircle myCir = new MyCircle(idArea, circle);

                     listaCirculosArea.add(myCir);
                     setVisibleCirculos();
                  }

               }

               if (lat != 0 && lon != 0) {

                  lat /= count;
                  lon /= count;
                  MyBroadCastManager.getInstance()
                        .sendBroadCastCenterMapBy(new LatLng(lat, lon),
                              18, "", false);
               }

            }

         }

      } catch (Throwable e) {

         e.printStackTrace();
      }

   }


   private void setVisibleCirculos() {

      setMaxZoomAreas();

      for (int i = listaCirculosArea.size() - 1; i >= 0; i--) {
         if (IdsGrupos.size() >= MAX_GRUPOS_VISIBLES_ZOOM) {
            Circle circle = listaCirculosArea.get(i).circle;
            circle.setVisible(map.getCameraPosition().zoom > MAX_ZOOM_VISIBLE_AREAS);
         }
      }

   }

   private void setMaxZoomAreas() {
      SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
      int maxZoomAreas = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_ZOOM_ZONAS, "" + MAX_ZOOM_VISIBLE_AREAS));

      MAX_ZOOM_VISIBLE_AREAS = maxZoomAreas;
   }


   protected synchronized void setVisibleMarkersAndAreas() {

      try {

         boolean isMedidaIniciada = Config.getInstance().getValue(
               "medidaIniciada", "0").equals("1");

         setMaxZoomAreas();

         for (int i = listaPoligonosArea.size() - 1; i >= 0; i--) {


            List<Marker> marcadores = listaPoligonosArea.get(i).marcadores;

            for (Marker marker : marcadores) {
               marker.setVisible(map.getCameraPosition().zoom > MAX_ZOOM_VISIBLE_PUNTOS_CONTROL);
            }
            MyPolygon area = listaPoligonosArea.get(i);
            if (!isMedidaIniciada) {
               if (IdsGrupos.size() >= MAX_GRUPOS_VISIBLES_ZOOM) {
                  area.polygon.setVisible(map.getCameraPosition().zoom > MAX_ZOOM_VISIBLE_AREAS);
               } else {
                  area.polygon.setVisible(true);
               }
            } else {
               area.polygon.setVisible(true);
            }
         }

      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   private void deletePoligono(int idArea) {

      try {

         synchronized (this) {

            // Si idArea es 0 se borran todos los poligonos
            for (int i = listaPoligonosArea.size() - 1; i >= 0; i--) {

               // Borro el poligono del mapa y lo quito de la lista
               if (idArea == 0
                     || idArea == listaPoligonosArea.get(i).idArea) {

                  List<Marker> marcadores = listaPoligonosArea.get(i).marcadores;

                  for (Marker marker : marcadores) {
                     marker.remove();
                  }
                  listaPoligonosArea.get(i).polygon.remove();
                  listaPoligonosArea.remove(i);

                  break;
               }
            }

            // Borro todos los poligonos de la lista
            if (idArea == 0)
               listaPoligonosArea.clear();

         }

      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   private void deleteCircle(int idArea) {

      try {

         synchronized (this) {

            // Si idArea es 0 se borran todos los poligonos
            for (int i = listaCirculosArea.size() - 1; i >= 0; i--) {

               // Borro el poligono del mapa y lo quito de la lista
               if (idArea == 0
                     || idArea == listaCirculosArea.get(i).idArea) {

                  listaCirculosArea.get(i).circle.remove();
                  listaCirculosArea.remove(i);

                  break;
               }
            }

            // Borro todos los poligonos de la lista
            if (idArea == 0)
               listaCirculosArea.clear();

         }

      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

   private void deleteSquare(int idArea) {

      try {

         synchronized (this) {

            // Si idArea es 0 se borran todos los poligonos
            for (int i = listaCirculosArea.size() - 1; i >= 0; i--) {

               // Borro el poligono del mapa y lo quito de la lista
               if (idArea == 0
                     || idArea == listaCirculosArea.get(i).idArea) {

                  listaCirculosArea.get(i).circle.remove();
                  listaCirculosArea.remove(i);

                  break;
               }
            }

            // Borro todos los poligonos de la lista
            if (idArea == 0)
               listaCirculosArea.clear();

         }

      } catch (Throwable e) {

         e.printStackTrace();
      }
   }

}