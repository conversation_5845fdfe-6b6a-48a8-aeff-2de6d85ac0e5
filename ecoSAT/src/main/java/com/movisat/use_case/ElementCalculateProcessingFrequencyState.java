package com.movisat.use_case;

import com.movisat.database.FrecuenciaProcesadoState;
import com.movisat.utils.Utilss;

public class ElementCalculateProcessingFrequencyState {
    public static FrecuenciaProcesadoState execute(String fechaActual, String fechaUltRecogida, double frecuenciaProcesado) {
        if (Utilss.isNullOrEmpty(fechaUltRecogida))
            return FrecuenciaProcesadoState.NONE;
        if (Utilss.isNullOrEmpty(fechaActual))
            return FrecuenciaProcesadoState.NONE;
        if (frecuenciaProcesado == 0)
            return FrecuenciaProcesadoState.NONE;

        double diffDays = Utilss.getDiffInDays(fechaUltRecogida, fechaActual);
        if (diffDays >= frecuenciaProcesado)
            return FrecuenciaProcesadoState.EXCEEDED;
        if (diffDays >= frecuenciaProcesado / 2)
            return FrecuenciaProcesadoState.OVER_HALF;
        return FrecuenciaProcesadoState.NONE;
    }
}
