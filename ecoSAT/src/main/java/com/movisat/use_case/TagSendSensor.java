package com.movisat.use_case;

import android.content.Intent;

import com.environment.Environment;
import com.environment.VisibilityElementPlate;
import com.movisat.database.SensorTag;
import com.movisat.database.Tags;
import com.movisat.ecosat.TagNotFoundListActivity;
import com.movisat.ecosat.MainActivity;
import com.movisat.log.Logg;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.tags.ITag;

public class TagSendSensor {

    private static final String TAG = "TagSendSensor";
    private static String lastTag = "";

    public static void execute(ITag iTag, Tags tags) {

        try {
            boolean isActiveTagMenu = MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_LEER_TAG;

            if (!isActiveTagMenu) {
                // We only send the sensor if the tag not exists in the database.
                if (tags != null) return;
                // If the tag not exists, we must send the sensor but not if the elemVerMatricula has the value 2
                // (MODIFY_ELEMENT_EVEN_IF_TAG_DOES_NOT_EXIST), in this case the tag will send in other package.
                if (Environment.visibilityElementPlate == VisibilityElementPlate.MODIFY_ELEMENT_EVEN_IF_TAG_NOT_EXIST)
                    return;
            }

            MainActivity.getInstance().runOnUiThread(() -> {
                Logg.info(TAG, "Enviando sensor de tag: " + iTag);
            });

            if (!iTag.isValid()) {
                Logg.info(TAG, "El tag no es válido: " + iTag);
                MainActivity.getInstance().showMessage("El tag leído no es válido");
                return;
            }

            if (!lastTag.equals(iTag.get()) || isActiveTagMenu) {
                lastTag = iTag.get();
                sendSensorTag(iTag);
                Logg.info(TAG, "Sensor TAG (36) insertado: " + iTag);
            }

            if (isActiveTagMenu) {
                MainActivity.getInstance().showMessage("Tag leído: " + iTag.get());
                return;
            }

            TagNotFoundListActivity lt = TagNotFoundListActivity.getInstance();
            if (lt == null) {
                Intent intent = new Intent(MainActivity.getInstance(), TagNotFoundListActivity.class);
                intent.putExtra("tag", iTag.get());
                MainActivity.getInstance().showMessage("El elemento con el identificador digital leído no se encuentra actualmente en la base de datos del dispositivo");
                MainActivity.getInstance().startActivity(intent);
            } else {
                lt.addTag(iTag.get());
            }
        } catch (Throwable e) {
            Logg.error(TAG, "iTag: " + iTag + ". Error: " + e.getMessage());
        }
    }


    private static void sendSensorTag(ITag tag) {
        try {
            SensorTag s = new SensorTag(tag.get(), tag.Fecha.getTime());
            DBPacket dbp = new DBPacket();
            dbp.insert(new Packet(Packet.SENSOR_TAG, Packet.PRIORIDAD_NORMAL, s));
            dbp.close();
        } catch (Exception e) {
            Logg.error(TAG, "[sendSensorTag] " + e.getMessage());
        }

    }
}
