package com.movisat.outbox;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.log.Logg;
import com.movisat.utilities.Database;

import java.util.ArrayList;

public class DBPacket {
    private final String TABLE_NAME = "bandeja_salida";
    private SQLiteDatabase db = null;

    public DBPacket() {

        try {

            db = Database.getConnection(
                    MainActivity.getInstance().getDatabasePath("ecosat.sqlite")
                            .getPath(), SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            db = null;
        }

    }

    public boolean isPendienteEnvioNivelesLlenado() {
        int total = (getCount(Packet.SENSOR_NIVEL_LLENADO) + getCount(Packet.SENSOR_PESO_VERTEDERO));
        return total > 0;
    }

    public synchronized void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public synchronized long insert(Packet reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            // El campo id no se asigna por que es autoincremental,
            // el campo dtime tampoco por que es un timestamp
            values.put("empresa", reg.getEmpresa());
            values.put("usuario", reg.getUsuario());
            values.put("prioridad", reg.getPrioridad());
            values.put("tipo", reg.getTipo());
            values.put("datos", reg.getDatos());


            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            Logg.error("[" + getClass()  + "] Error SQLITE INSERT, tabla: BANDEJA_SALIDA... \n" + e.getMessage() );
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public synchronized boolean update(Packet reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put("empresa", reg.getEmpresa());
            values.put("usuario", reg.getUsuario());
            values.put("prioridad", reg.getPrioridad());
            values.put("tipo", reg.getTipo());
            values.put("datos", reg.getDatos());


            if (db.update(TABLE_NAME, values, "id=" + reg.getId(), null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public synchronized boolean delete(Packet reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, "id=" + reg.getId(), null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public synchronized int getCount(int tipo) {
        int res = 0;
        Cursor cur;
        String where = null;
        try {

            if (tipo != 0) {
                where = "tipo=" + tipo;
            }
            if ((cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                    where, null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public synchronized int getCount() {
        return getCount(0);
    }

    public synchronized void deleteAll() {

        try {

            db.delete(TABLE_NAME, null, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public synchronized Packet getByID(int id) {
        Cursor cur;
        Packet res = null;

        try {

            if ((cur = db.query(TABLE_NAME, (new String[]{"id", "empresa",
                    "usuario", "prioridad", "tipo", "dtime", "datos"}), "id="
                    + id, null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Packet(cur.getInt(0), cur.getInt(1),
                            cur.getInt(2), cur.getInt(3), cur.getInt(4),
                            cur.getLong(5), cur.getBlob(6));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public synchronized Packet getByPrioridad(int prioridad) {
        Cursor cur;
        Packet res = null;

        try {

            if ((cur = db.query(TABLE_NAME, (new String[]{"id", "empresa",
                            "usuario", "prioridad", "tipo", "dtime", "datos"}),
                    "prioridad=" + prioridad, null, null, null, "id ASC")) != null) {

                if (cur.moveToFirst())
                    res = new Packet(cur.getInt(0), cur.getInt(1),
                            cur.getInt(2), cur.getInt(3), cur.getInt(4),
                            cur.getLong(5), cur.getBlob(6));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public synchronized ArrayList<Packet> getAll() {
        Cursor cur = null;
        ArrayList<Packet> res = null;

        try {
            if ((cur = db.query(TABLE_NAME, (new String[]{"id", "empresa",
                            "usuario", "prioridad", "tipo", "dtime", "datos"}), null,
                    null, null, null, "prioridad ASC, id ASC")) != null) {
                if (cur.moveToFirst()) {
                    res = new ArrayList<Packet>();
                    do {
                        res.add(new Packet(cur.getInt(0), cur.getInt(1), cur
                                .getInt(2), cur.getInt(3), cur.getInt(4), cur
                                .getLong(5), cur.getBlob(6)));
                    } while (cur.moveToNext());
                }
                cur.close();
            }

        } catch (Throwable e) {
            if (cur != null && !cur.isClosed())
                cur.close();
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return res;
    }
}
