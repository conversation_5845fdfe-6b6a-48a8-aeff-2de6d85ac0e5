package com.movisat.outbox;

import android.provider.Settings.Secure;
import android.util.Base64;

import com.environment.Environment;
import com.movisat.database.DBIncidencia;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoSustitucion;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaEstado;
import com.movisat.database.IncidenciaFoto;
import com.movisat.database.InfoSustituir;
import com.movisat.database.MedidaZonas;
import com.movisat.database.Posicion;
import com.movisat.database.SensorLavado;
import com.movisat.database.SensorNivelLLenado;
import com.movisat.database.SensorPesoVertedero;
import com.movisat.database.SensorTag;
import com.movisat.database.Vehiculo;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.log.Logg;
import com.movisat.utilities.Phone;
import com.movisat.utilities.Utils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.sql.Date;
import java.util.List;

public class Packet {
    // Tipos de paquetes
    public static final int ELEMENTO_CREAR = 1;
    public static final int ELEMENTO_MODIFICAR = 2;
    public static final int ELEMENTO_BORRAR = 3;
    public static final int INCIDENCIA_CREAR = 4;
    public static final int INCIDENCIA_NUEVO_ESTADO = 5;
    public static final int INCIDENCIA_INSERTAR_FOTO = 6;
    public static final int SENSOR_NIVEL_LLENADO = 7;
    public static final int ENVIO_POSICIONES = 8;
    public static final int SENSOR_LAVADO = 9;
    public static final int SENSOR_PESO_VERTEDERO = 10;
    public static final int MEDIDA_ZONAS_VERDES = 11;
    public static final int SENSOR_TAG = 12;
    public static final int ELEMENTO_SUSTITUIR = 13;
    public static final int ELEMENTO_DEPOSITAR = 14;
    public static final int ELEMENTO_RETIRAR = 15;
    public static final int ELEMENTO_CREAR_CON_NOMBRE = 16;
    public static final int VEHICULO_ASOCIAR = 17;
    public static final int SENSOR_NIVEL_LLENADO_SIN_PROCESAR = 18;
    public static final int ELEMENTO_SUSTITUIR_CON_BAJA = 19;
    public static final int INCIDENCIAS_PROPIETARIOS = 20;
    public static final int ELEMENTO_CREAR_IMAGEN = 21;
    public static final int ELEMENTO_BORRAR_IMAGEN = 22;
    public static final int INCIDENCIA_ASIGNAR_USUARIO = 23;

    public static final int SENSOR_ELEMENTO_RECOGIDO = 80;

    // Niveles de prioridad
    public static final int PRIORIDAD_MAXIMA = 0;
    public static final int PRIORIDAD_NORMAL = Integer.MAX_VALUE / 2;
    public static final int PRIORIDAD_MINIMA = Integer.MAX_VALUE;

    private int id;
    private int empresa;
    private int usuario;
    private int prioridad;
    private int tipo;
    private long dTime;
    byte[] datos;

    /**
     * Constructor usado al recuperar registros de la bandeja de salida
     */
    public Packet(int id, int empresa, int usuario, int prioridad, int tipo,
                  long dTime, byte[] datos) {

        setId(id);
        setEmpresa(empresa);
        setUsuario(usuario);
        setPrioridad(prioridad);
        setTipo(tipo);
        setDTime(dTime);
        setDatos(datos);

    }

    /**
     * Constructor usado para al crear registros en la bandeja de salida
     */
    public Packet(int tipo, int prioridad, Object obj) {
        JSONObject json = null;

        try {

            setEmpresa(MainActivity.getInstance().getEmpresa());
            setUsuario(MainActivity.getInstance().getUsuario());
            setPrioridad(prioridad);
            setTipo(tipo);
            DBIncidencia dbIncidencia;
            Incidencia inci;

            String android_id = Secure.getString(MainActivity.getInstance()
                    .getContentResolver(), Secure.ANDROID_ID);

            String idSynchro = System.currentTimeMillis() + "/" + android_id;

            switch (tipo) {
                case SENSOR_LAVADO:
                    json = new JSONObject();
                    SensorLavado s = (SensorLavado) obj;

                    json.put("CodigoElemento", s.getCodigoElemento());
                    json.put("CodigoMovil", s.getCodigoMovil());
                    json.put("IdEmpresa", s.getEmpresa());
                    json.put("FechaRegistro", Utils.datetimeToString(
                            new Date(s.getFechaRegistro()), "yyyy-MM-dd HH:mm:ss"));

                    setDatos(json.toString().getBytes());

                    break;
                case MEDIDA_ZONAS_VERDES:
                    json = new JSONObject();
                    MedidaZonas medida = (MedidaZonas) obj;
                    json.put("CodigoGrupo", medida.getCodigoGrupo());
                    json.put("IdEmpresa", medida.getIdEmpresa());
                    json.put("FechaInicioMedida", Utils.datetimeToString(
                            new Date(medida.getFechaInicioMedida()), "yyyy-MM-dd HH:mm:ss"));
                    json.put("FechaFinMedida", Utils.datetimeToString(
                            new Date(medida.getFechaFinMedida()), "yyyy-MM-dd HH:mm:ss"));
                    json.put("PapelerasRecogidas", medida.getPapelerasRecogidas());
                    json.put("IdUsuario", medida.getIdUsuario());
                    json.put("IdEmpleado", null);

                    setDatos(json.toString().getBytes());
                    break;

                case SENSOR_NIVEL_LLENADO:
                    json = new JSONObject();
                    SensorNivelLLenado sensor = (SensorNivelLLenado) obj;

                    json.put("CodigoElemento", sensor.getCodigoElemento());
                    json.put("CodigoMovil", sensor.getCodigoMovil());
                    json.put("IdEmpresa", sensor.getEmpresa());
                    json.put("NumeroFraccion", sensor.getNumeroFraccion());
                    json.put("FechaRegistro", Utils.datetimeToString(new Date(
                            sensor.getFechaRegistro()), "yyyy-MM-dd HH:mm:ss"));
                    json.put("VaciadoParcial", sensor.getVaciadoParcial());
                    setDatos(json.toString().getBytes());
                    break;

                case SENSOR_PESO_VERTEDERO:
                    json = new JSONObject();
                    SensorPesoVertedero pesoVertedero = (SensorPesoVertedero) obj;

                    json.put("codigo_movil", pesoVertedero.getCodigoMovil());
                    json.put("id_empresa", pesoVertedero.getEmpresa());
                    json.put("pesaje_vertedero", pesoVertedero.getPesoVertedero());
                    json.put("planchada_id", pesoVertedero.getPlanchada());
                    json.put("desde", Utils.datetimeToString(
                            new Date(pesoVertedero.getFechaRegistroIni()),
                            "yyyy-MM-dd HH:mm:ss"));
                    json.put("hasta", Utils.datetimeToString(
                            new Date(pesoVertedero.getFechaRegistroFin()),
                            "yyyy-MM-dd HH:mm:ss"));
                    json.put("margen", 0);

                    setDatos(json.toString().getBytes());
                    break;
                case ENVIO_POSICIONES:

                    JSONArray arrayJson = new JSONArray();

                    List<Posicion> posiciones = (List<Posicion>) obj;

                    for (Posicion posicion : posiciones) {
                        json = new JSONObject();
                        json.put("lng", posicion.getLng());
                        json.put("lat", posicion.getLat());
                        json.put("h", posicion.getAltura());
                        json.put("r", posicion.getRumbo());
                        json.put("v", posicion.getVelocidad());

                        json.put("f", Utils.datetimeToString(
                                new Date(posicion.getFecha()),
                                "yyyy-MM-dd HH:mm:ss"));

                        arrayJson.put(json);
                    }

                    setDatos(arrayJson.toString().getBytes());

                    break;

                case ELEMENTO_CREAR:
                case ELEMENTO_CREAR_CON_NOMBRE:
                    json = new JSONObject();
                    Elemento elemNew = (Elemento) obj;

                    json.put("idInterno", elemNew.getId());
                    json.put("IdSincro", idSynchro);
                    json.put("IdEmpresa", elemNew.getEmpresa());
                    json.put("CodModelo", elemNew.getModelo());
                    json.put("Matricula", elemNew.getMatricula());
                    json.put("Descripcion", elemNew.getDescripcion());
                    json.put("CodigoFisico", elemNew.getCodFisico());
                    json.put("Nombre", elemNew.getNombre());
                    json.put("LatitudInt", (int) (elemNew.getPosition().latitude * 3600000));
                    json.put("LongitudInt", (int) (elemNew.getPosition().longitude * 3600000));
                    json.put("Estado", elemNew.getEstado());
                    json.put("VaciaBajoDemanda", elemNew.getVaciaBajoDemanda());
                    if (elemNew.getTag() != null && !elemNew.getTag().isEmpty())
                        json.put("_Tag", elemNew.getTag());

                    // envío el nivel de cuánto crítico es el elemento.
                    if (elemNew.getEstadoCritico() != "") {
                        json.put("NivelCritico", elemNew.getEstadoCritico());
                    }

                    if (elemNew.getFechaCreacion() != "")
                        json.put("FechaInsercion", elemNew.getFechaCreacion());

                    JSONArray jss = new JSONArray();
                    for (int i = 1; i < 8; i++) {
                        if (elemNew.getDiaBloqueo(i) > 0) {
                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Dia", i);
                            jsonObj.put("TipoBloqueo", elemNew.getDiaBloqueo(i));
                            jss.put(jsonObj);
                        }
                    }

                    if (jss.length() > 0)
                        json.put("DiasBloqueo", jss);


                    // envío el id del elemento al que se asemeje
                    if (elemNew.getIdElementoSimilar() > 0) {
                        json.put("ElementoSimilar", elemNew.getIdElementoSimilar());
                    }

                    // envío el tipo de zona del elemento y de agrupación
                    json.put("IdZona", elemNew.getTipoZona());

                    // envío la indicación de si es rotativo o no
                    json.put("Rotativo", elemNew.esRotativo());


                    // Si se debe modificar el nombre y no está vacío, se añade un campo extra para
                    // que el nombre no se genere de forma automática en el servidor
                    if (tipo == ELEMENTO_CREAR_CON_NOMBRE) {
                        setTipo(ELEMENTO_CREAR);
                        if (!elemNew.getNombre().equals(""))
                            json.put("MantenerNombre", 1);
                    }

                    setDatos(json.toString().getBytes());

                    break;

                case ELEMENTO_MODIFICAR:
                    json = new JSONObject();
                    Elemento elemUpdate = (Elemento) obj;

                    json.put("idInterno", elemUpdate.getId());
                    json.put("Codigo", elemUpdate.getIdExterno());
                    json.put("IdSincro", idSynchro);
                    json.put("IdEmpresa", elemUpdate.getEmpresa());
                    json.put("CodModelo", elemUpdate.getModelo());
                    json.put("Matricula", elemUpdate.getMatricula());
                    json.put("CodigoFisico", elemUpdate.getCodFisico());
                    json.put("Descripcion", elemUpdate.getDescripcion());
                    json.put("Nombre", elemUpdate.getNombre());
                    json.put("LatitudInt",
                            (int) (elemUpdate.getPosition().latitude * 3600000));
                    json.put("LongitudInt",
                            (int) (elemUpdate.getPosition().longitude * 3600000));
                    json.put("Estado", elemUpdate.getEstado());
                    json.put("VaciaBajoDemanda", elemUpdate.getVaciaBajoDemanda()); // TODO: ¿Esto es correcto?
                    if (elemUpdate.getTag() != null && !elemUpdate.getTag().isEmpty())
                        json.put("_Tag", elemUpdate.getTag());

                    /* para app 202 */
                    // envío el nivel de cuánto crítico es el elemento.
                    if (elemUpdate.getEstadoCritico() != "") {
                        json.put("NivelCritico", elemUpdate.getEstadoCritico());
                    }

                    jss = new JSONArray();
                    for (int i = 1; i < 8; i++) {
                        if (elemUpdate.getDiaBloqueo(i) > 0) {
                            JSONObject jsonObj = new JSONObject();
                            jsonObj.put("Dia", i);
                            jsonObj.put("TipoBloqueo", elemUpdate.getDiaBloqueo(i));
                            jss.put(jsonObj);
                        }
                    }

                    if (jss.length() > 0)
                        json.put("DiasBloqueo", jss);


                    // envío el id del elemento al que se asemeje
                    if (elemUpdate.getIdElementoSimilar() > 0) {
                        json.put("ElementoSimilar", elemUpdate.getIdElementoSimilar());
                    }

                    // envío el tipo de zona del elemento y de agrupación
                    json.put("IdZona", elemUpdate.getTipoZona());

                    // envío la indicación de si es rotativo o no
                    json.put("Rotativo", elemUpdate.esRotativo());

                    setDatos(json.toString().getBytes());

                    break;

                case ELEMENTO_BORRAR:
                    json = new JSONObject();
                    Elemento elemDelete = (Elemento) obj;

                    json.put("idInterno", elemDelete.getId());
                    json.put("IdSincro", idSynchro);
                    json.put("Codigo", elemDelete.getIdExterno());
                    json.put("IdEmpresa", elemDelete.getEmpresa());

                    setDatos(json.toString().getBytes());

                    break;

                case INCIDENCIA_CREAR:
                    json = new JSONObject();
                    Incidencia incidenciaNew = (Incidencia) obj;

                    json.put("IdSincro", idSynchro);
                    json.put("idInterno", incidenciaNew.getId());
                    json.put("IdEmpresa", incidenciaNew.getEmpresa());
                    json.put("Incidencia", incidenciaNew.getModelo());
                    json.put("Fecha", Utils.datetimeToString(new Date(
                            incidenciaNew.getFechaUltimoEstado() * 1000), "yyyy-MM-dd HH:mm:ss"));
                    json.put("LatitudInt",
                            (int) (incidenciaNew.getPosition().latitude * 3600000));
                    json.put("LongitudInt",
                            (int) (incidenciaNew.getPosition().longitude * 3600000));
                    json.put("Motivo", incidenciaNew.getMotivo());
                    json.put("Tipo", incidenciaNew.getTipo());
                    json.put("Usuario", incidenciaNew.getUsuario());
                    json.put("Observaciones", incidenciaNew.getObserv());
                    if (incidenciaNew.getElemento() != 0)
                        json.put("Elemento", incidenciaNew.getElemento());

                    // Añado la informaciin geografica
                    /*
                     * if (MainActivity.getInstance().mapaElementos != null)
                     * json.put("InfoGeo", GestionElementos.getInstance()
                     * .getAddress(incidenciaNew.getPosition()));
                     */

                    setDatos(json.toString().getBytes());

                    break;

                case ELEMENTO_CREAR_IMAGEN:
                    json = new JSONObject();
                    Elemento elemImagen = (Elemento) obj;


                    json.put("Fecha", Utils.datetimeToString(new Date(
                            System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss"));
                    // Codificamos la imagen en base64
                    String imagenElem = Base64
                            .encodeToString(elemImagen.getFoto(), 0);
                    json.put("Imagen", imagenElem);
                    json.put("IdInterno", elemImagen.getId());
                    json.put("Codigo", elemImagen.getIdExterno());
                    // DINIGO - 08/02/2021 - Mantis 0004987: Cuando se introducen fotos en Elementos
                    json.put("Sustituir", elemImagen.tieneQueBorrarImagenesAnterioresEnServidor);
                    // DINIGO - 08/02/2021 - FIN

                    setDatos(json.toString().getBytes());


                    break;

                //PMARCO MANTIS 5322 llamada para borrar las imagenes asociadas a un elemento
                case ELEMENTO_BORRAR_IMAGEN:
                    json = new JSONObject();
                    Elemento elemBorrarImg = (Elemento) obj;

                    json.put("Fecha", Utils.datetimeToString(new Date(
                            System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss"));
                    json.put("Codigo", elemBorrarImg.getIdExterno());
                    json.put("IdInterno", elemBorrarImg.getId());

                    setDatos(json.toString().getBytes());

                    break;

                case INCIDENCIA_INSERTAR_FOTO:
                    json = new JSONObject();
                    dbIncidencia = new DBIncidencia();

                    IncidenciaFoto incidenciaFoto = (IncidenciaFoto) obj;
                    inci = dbIncidencia.getByIdInterno(
                            incidenciaFoto.getIncidenciaInterno(),
                            incidenciaFoto.getEmpresa());
                    dbIncidencia.close();
                    if (inci.getId() == 0)
                        break;

                    json.put("IdSincro", idSynchro);
                    json.put("Id", incidenciaFoto.getId());
                    json.put("idInterno", incidenciaFoto.getIncidenciaInterno());
                    json.put("IdEmpresa", incidenciaFoto.getEmpresa());
                    json.put("IncidenciaH", inci.getId());
                    json.put("Fecha", Utils.datetimeToString(new Date(
                            incidenciaFoto.getFecha()), "yyyy-MM-dd HH:mm:ss"));

                    // Aquí se guarda la ruta y nombre fichero, luego a la hora de enviar la transformo a base 64
                    json.put("ImagenBase64", incidenciaFoto.getImageBase64());
                    json.put("EsSolucion", "0");
                    setDatos(json.toString().getBytes());

                    break;

                case INCIDENCIA_NUEVO_ESTADO:
                    json = new JSONObject();
                    dbIncidencia = new DBIncidencia();
                    IncidenciaEstado incidenciaEstado = (IncidenciaEstado) obj;

                    //inci = dbIncidencia.getByIdInterno(incidenciaEstado.getIdIncidencia(),
                    //      incidenciaEstado.getEmpresa());

                    json.put("idInterno", incidenciaEstado.getId());
                    json.put("IdSincro", idSynchro);
                    json.put("IdEmpresa", incidenciaEstado.getEmpresa());
                    json.put("IdIncidenciah", incidenciaEstado.getIdIncidenciaExterno());
                    json.put("IdIncidenciahInterno", incidenciaEstado.getIdIncidencia());
                    json.put("Fecha", incidenciaEstado.getFecha());
                    json.put("Estado", incidenciaEstado.getIdEstado());
                    if (Environment.isVisibleAvisoFalso)
                        json.put("EsAvisoFalso", incidenciaEstado.esAvisoFalso() == 1);
                    String observacion = incidenciaEstado.getObservacion();
                    if (observacion != null && !observacion.isEmpty())
                        json.put("TextoCambioEstado", incidenciaEstado.getObservacion());
                    json.put("Usuario", MainActivity.getInstance().getUsuario());
                    setDatos(json.toString().getBytes());
                    dbIncidencia.close();

                    break;

                case INCIDENCIA_ASIGNAR_USUARIO:
                    json = new JSONObject();
                    Incidencia incidencia = (Incidencia) obj;

                    json.put("solicitanteId", MainActivity.getInstance().getUsuario());
                    json.put("propietarioId", incidencia.getPropietario());
                    json.put("incidenciaId", incidencia.getIdExterno());
                    json.put("incidenciaIdInterno", incidencia.getId());

                    setDatos(json.toString().getBytes());

                    break;

                case SENSOR_ELEMENTO_RECOGIDO:
                    json = new JSONObject();
                    Elemento elem = (Elemento) obj;

                    json.put("imei", Phone.getInstance().getIMEI());
                    json.put("Sensor", SENSOR_ELEMENTO_RECOGIDO);
                    json.put("Lectura", elem.getIdExterno());

                    //Enviar procesado con misma fecha que el sensor tag
                    java.util.Date fecha = MainActivity.fechaProcesado;
                    if (fecha != null)
                        json.put("Fecha", Utils.datetimeToString(fecha, "yyyy-MM-dd HH:mm:ss"));
                    else
                        json.put("Fecha", Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));

                    setDatos(json.toString().getBytes());

                    break;

                case SENSOR_TAG:
                    json = new JSONObject();
                    SensorTag tag = (SensorTag) obj;

                    json.put("imei", Phone.getInstance().getIMEI());
                    json.put("Sensor", tag.getSensor());
                    json.put("Lectura", tag.getLectura());
                    json.put("Fecha", Utils.datetimeToString(new Date(
                            tag.getFecha()), "yyyy-MM-dd HH:mm:ss"));

                    setDatos(json.toString().getBytes());

                    break;

                case ELEMENTO_SUSTITUIR:
                    json = new JSONObject();
                    InfoSustituir infoSustituir = (InfoSustituir) obj;

                    // Se incluyen los id internos de los elementos para poder recuperarlos
                    // justo antes del envío, y poder comprobar así que sus id externos son válidos
                    json.put("idInternoElementoDepositado", infoSustituir.getElementoDepositado().getId());
                    json.put("idInternoElementoRetirado", infoSustituir.getElementoRetirado().getId());
                    json.put("CodigoElementoDepositado", infoSustituir.getElementoDepositado().getIdExterno());
                    json.put("CodigoElementoRetirado", infoSustituir.getElementoRetirado().getIdExterno());
                    json.put("EstadoRetirado", infoSustituir.getElementoRetirado().getEstado());
                    json.put("Fecha", Utils.datetimeToString(infoSustituir.getFecha(), "yyyy-MM-dd HH:mm:ss"));

                    setDatos(json.toString().getBytes());

                    break;

                case ELEMENTO_DEPOSITAR:
                    json = new JSONObject();
                    InfoSustituir infoDepositar = (InfoSustituir) obj;

                    json.put("idInternoElemento", infoDepositar.getElementoDepositado().getId());
                    json.put("CodigoElemento", infoDepositar.getElementoDepositado().getIdExterno());
                    json.put("Fecha", Utils.datetimeToString(infoDepositar.getFecha(), "yyyy-MM-dd HH:mm:ss"));
                    json.put("Lat", infoDepositar.getElementoDepositado().getPosition().latitude);
                    json.put("Lng", infoDepositar.getElementoDepositado().getPosition().longitude);

                    setDatos(json.toString().getBytes());

                    break;

                case ELEMENTO_RETIRAR:
                    json = new JSONObject();
                    InfoSustituir infoRetirar = (InfoSustituir) obj;

                    json.put("idInternoElemento", infoRetirar.getElementoRetirado().getId());
                    json.put("CodigoElemento", infoRetirar.getElementoRetirado().getIdExterno());
                    json.put("EstadoRetirado", infoRetirar.getElementoRetirado().getEstado());
                    json.put("Fecha", Utils.datetimeToString(infoRetirar.getFecha(), "yyyy-MM-dd HH:mm:ss"));
                    json.put("MotivoBaja", infoRetirar.getMotivo());

                    setDatos(json.toString().getBytes());

                    break;

                case VEHICULO_ASOCIAR:
                    json = new JSONObject();
                    Vehiculo vehiculo = (Vehiculo) obj;

                    json.put("Imei", Phone.getInstance().getIMEI());
                    json.put("Vehiculo", vehiculo.getCodigo());
                    json.put("Empresa", vehiculo.getEmpresa());
                    json.put("Tipo", vehiculo.getTipo());
                    json.put("Fecha", Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));

                    setDatos(json.toString().getBytes());

                    break;

                case SENSOR_NIVEL_LLENADO_SIN_PROCESAR:
                    json = new JSONObject();
                    SensorNivelLLenado sensor_no_proc = (SensorNivelLLenado) obj;

                    json.put("CodigoElemento", sensor_no_proc.getCodigoElemento());
                    json.put("CodigoMovil", sensor_no_proc.getCodigoMovil());
                    json.put("IdEmpresa", sensor_no_proc.getEmpresa());
                    json.put("Fecha", Utils.datetimeToString(new Date(
                            sensor_no_proc.getFechaRegistro()), "yyyy-MM-dd HH:mm:ss"));
                    json.put("Fraccion", sensor_no_proc.getNumeroFraccion());
                    json.put("VaciadoParcial", sensor_no_proc.getVaciadoParcial());
                    setDatos(json.toString().getBytes());
                    break;

                case ELEMENTO_SUSTITUIR_CON_BAJA:
                    json = new JSONObject();
                    ElementoSustitucion elemento = (ElementoSustitucion) obj;
                    json.put("Fecha", Utils.datetimeToString(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss"));
                    json.put("CodigoElementoRetirado", elemento.getIdElementoRetirado());
                    json.put("ElementoDepositado", elemento.getElementoDepositado());
                    json.put("CodigoElementoDepositado", elemento.getIdElementoDepositado());

                    setDatos(json.toString().getBytes());
                    break;
            }

            Logg.info("PACKET", "[PAQUETE CREADO] [" + tipo + "] " + json);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }

    public void setId(int id) {

        this.id = id;
    }

    public void setEmpresa(int empresa) {

        this.empresa = empresa;
    }

    public void setUsuario(int usuario) {

        this.usuario = usuario;
    }

    public void setPrioridad(int prioridad) {

        this.prioridad = prioridad;
    }

    public void setTipo(int tipo) {

        this.tipo = tipo;
    }

    public void setDTime(long dTime) {

        this.dTime = dTime;
    }

    public void setDatos(byte[] datos) {

        this.datos = datos;
    }

    public int getId() {

        return id;
    }

    public int getEmpresa() {

        return empresa;
    }

    public int getUsuario() {

        return usuario;
    }

    public int getPrioridad() {

        return prioridad;
    }

    public int getTipo() {

        return tipo;
    }

    public long getDTime() {

        return dTime;
    }

    public byte[] getDatos() {

        return datos;
    }

}