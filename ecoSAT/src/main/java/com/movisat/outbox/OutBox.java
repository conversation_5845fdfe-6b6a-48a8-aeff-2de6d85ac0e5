package com.movisat.outbox;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import com.movisat.database.DBElemento;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBIncidenciaEstado;
import com.movisat.database.DBIncidenciaFoto;
import com.movisat.database.Elemento;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaEstado;
import com.movisat.database.IncidenciaFoto;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyBroadCastManager;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;
import com.movisat.events.OnIncidenciaAsignada;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.synchronize.DBSynchro;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Phone;
import com.movisat.utilities.Utils;
import com.movisat.utils.Utilss;
import com.movisat.utils_android.UtilssAndroid;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;

public class OutBox extends Thread {
    private static final String TAG = "OutBox";

    private static OutBox instance = null;
    private static String sync = "OutBox_sync";
    private volatile boolean running = false;
    private static final int TIME_SLEEP = 5;
    static public ClientWebSvc wsc;
    private static volatile boolean isSending = false;

    private OutBox() {

        wsc = new ClientWebSvc();
    }

    /**
     * Inicia el proceso de envio
     */
    public static boolean init() {
        boolean res = false;

        synchronized (sync) {
            if (instance == null || !instance.running) {
                instance = new OutBox();
                instance.start();
                res = true;
            } else
                res = instance.running;
        }
        return res;
    }

    /**
     * Finaliza el proceso de envio.
     */
    public static void end() {
        synchronized (sync) {
            // Finalizo el hilo de sincronizaciin
            if (instance != null) {
                instance.interrupt();
                instance.running = false;
            }
        }
    }

    public static boolean isSending() {
        return instance.isSending;
    }

    /**
     * Hilo principal del proceso de envio
     */
    public void run() {

        try {

            int id, idExterno, empresa;
            DBPacket dbp = null;
            DBElemento dbElem;
            DBIncidencia dbIncidencia;
            DBIncidenciaEstado dbIncidenciaEstado;
            Incidencia incidencia;
            IncidenciaEstado estado;
            Elemento elem;
            JSONObject jsonData, res = null;
            JSONArray arrayData;
            String url;

            running = true;

            while (running || !interrupted()) {
                try {
                    // Mientras se está sincronizando no se envía nada
                    if (DBSynchro.getInstance().getSynchro()) {
                        // if (DBSynchro.running) {

                        for (int i = 0; i < 5 && running; i++)
                            sleep(1000);
                        continue;
                    }

                    // Se comprueba si está desactivado el envío (se utiliza para evitar el envío
                    // de datos al cargar una base de datos de otro equipo (falseando su IMEI, con
                    // el valor de configuración "fakeDevice" = "1").
                    if (Config.getInstance().getValue("disableOutbox", "0").equals("1")) {
                        sleep(10000);
                        continue;
                    }

                    // Obtengo todos los paquetes ordenados por prioridad + id
                    dbp = new DBPacket();
                    if (dbp == null) {
                        sleep(2000);
                        continue;
                    }

                    ArrayList<Packet> packets = dbp.getAll();
                    dbp.close();

                    // Envío todos los paquetes
                    if (packets == null) {
                        sleep(2000);
                        continue;
                    }

                    if (!UtilssAndroid.isInternetAvailable()) continue;

                    isSending = true;
                    for (Packet packet : packets) {

                        if (packet.getDatos() != null) {

                            // Envio la información y si se puede enviar
                            // a continuación borro el registro. Si falla el
                            // envío hay que salir y empezar otra vez por el
                            // principio
                            switch (packet.getTipo()) {
                                case Packet.MEDIDA_ZONAS_VERDES:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    //Para registrar el vehiculo que envia los datos.
                                    jsonData.put("IMEI", Phone.getInstance()
                                            .getIMEI());

                                    // Creo la ruta para llamar al servicio web
                                    url = "/api/zonasverdes/insert/medida";

                                    // Envío los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);
                                    if (res != null && !res.isNull("Id")) {
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    break;

                                case Packet.ELEMENTO_CREAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    // Recupero el identificador del elemento y la empresa
                                    id = jsonData.getInt("idInterno");

                                    // Quitamos el idInterno
                                    jsonData.remove("idInterno");

                                    empresa = jsonData.getInt("IdEmpresa");

                                    // Paso el campo código al servidor a 0 para que
                                    // sepa que se trata de un elemento nuevo
                                    jsonData.put("Codigo", 0);
                                    //jsonData.put("FechaInsercion", Utils.datetimeToString(new Date(), "yyyy-MM-dd HH:mm:ss"));

                                    // Creo la ruta para llamar al servicio web
                                    url = "/moviles/elementos/insert/" + Phone.getInstance().getIMEI();

                                    // Envío los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Compruebo la respuesta del servidor, si ha
                                    // ido bien recupero el código y el nombre que
                                    // el servidor ha asignado al nuevo elemento
                                    if (res != null && !res.isNull("Codigo")) {

                                        // Antes de nada compruebo si el sistema me
                                        // ha dicho de borrar el elemento.
                                        if (res.getBoolean("Borrado")) {

                                            dbElem = new DBElemento();
                                            elem = dbElem.getByIdInterno(id, empresa);
                                            dbElem.delete(elem);
                                            // Lo elimino de la lista de elementos para su búsqueda
                                            if (MainActivity.getInstance() != null) {
                                                MainActivity.getInstance().removeElementoBusqueda(elem);
                                            }

                                            MyBroadCastManager
                                                    .getInstance()
                                                    .sendBroadCastDeleteItemCluster(elem.getId());

                                            // Borro el registro de la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                            dbElem.close();

                                        } else {
                                            // Asigno el cidigo definitivo que ha
                                            // asignado el servidor al elemento
                                            int IdExterno = res.getInt("Codigo");
                                            dbElem = new DBElemento();
                                            elem = dbElem.getByIdInterno(id, empresa);

                                            // Lo he encontrado por idInterno.
                                            if (elem != null) {
                                                elem.setIdExterno(IdExterno);
                                                elem.setNombre(res.getString("Nombre"));
                                                dbElem.update(elem);

                                                // Lo añado a la lista de elementos para su búsqueda
                                                if (MainActivity.getInstance() != null) {
                                                    MainActivity.getInstance().addElementoBusqueda(elem);
                                                }

                                                // Actualizo el elemento en la lista en
                                                // memoria y en el cluster
                                                MyBroadCastManager
                                                        .getInstance()
                                                        .sendBroadCastUpdateItemCluster(elem);
                                            }

                                            dbp = new DBPacket();
                                            // Borro el registro de la bandeja de salida
                                            dbp.delete(packet);
                                            dbp.close();
                                            dbElem.close();

                                        }

                                    } else {
                                        if (res != null
                                                && !res.isNull("ErrNum")
                                                && (res.getInt("ErrNum") == 400)) {
                                            // Borro el registro de la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                    }
                                    break;

                                case Packet.ELEMENTO_MODIFICAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    // Recupero el identificador del elemento y la empresa
                                    id = jsonData.getInt("idInterno");

                                    // Quitamos el idInterno
                                    jsonData.remove("idInterno");

                                    empresa = jsonData.getInt("IdEmpresa");

                                    int codElem = 0;
                                    try {
                                        codElem = jsonData.getInt("Codigo");
                                    } catch (Throwable e) {
                                        Log.e("Error", e.getMessage());
                                    }

                                    Elemento auxElem2 = null;

                                    // Si no hay código de elemento hay que añadirlo antes de enviar
                                    if (codElem == 0) {
                                        dbElem = new DBElemento();
                                        auxElem2 = dbElem.getByIdInterno(id, empresa);
                                        if (auxElem2 != null)
                                            codElem = auxElem2.getIdExterno();
                                        dbElem.close();

                                        if (codElem > 0) {

                                            jsonData.put("Codigo", codElem);
                                            jsonData.put("Nombre", auxElem2.getNombre());
                                        } else {

                                            // Solo borramos el packet cuando no exista el elemento,
                                            // así se evita que se pierdan acciones realizadas que
                                            // se envían antes que el propio elemento y no consiguen
                                            // recuperar su código.
                                            if (auxElem2 == null) {
                                                // Si no se puede asignar el código borro el registro
                                                dbp = new DBPacket();
                                                dbp.delete(packet);
                                                dbp.close();
                                            }

                                            break;
                                        }
                                    }

                                    // Creo la ruta para llamar al servicio web
                                    url = "/moviles/elementos/update/"
                                            + Phone.getInstance().getIMEI();

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Compruebo la respuesta del servidor, si ha
                                    // ido bien borro el registro de la bandeja de
                                    // salida
                                    checkResult(res, packet, url);
                                    if (res != null && !res.isNull("Codigo")) {
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();

                                        if (MainActivity.getInstance() != null && auxElem2 != null) {
                                            MainActivity.getInstance().updateElementoBusqueda(auxElem2);
                                        }
                                    } else {
                                        if (res != null
                                                && !res.isNull("ErrNum")
                                                && (res.getInt("ErrNum") == 400)) {

                                            // Borro el registro de la bandeja de
                                            // salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                    }

                                    break;

                                case Packet.ELEMENTO_BORRAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    // Recupero el identificador del elemento y la
                                    // empresa
                                    id = jsonData.getInt("idInterno");
                                    idExterno = jsonData.getInt("Codigo");

                                    // Si no hay código de elemento hay que añadirlo antes de enviar
                                    if (idExterno < 1) {
                                        codElem = 0;
                                        empresa = jsonData.getInt("IdEmpresa");
                                        dbElem = new DBElemento();
                                        Elemento auxElem = dbElem.getByIdInterno(id, empresa);
                                        if (auxElem != null)
                                            codElem = auxElem.getIdExterno();
                                        dbElem.close();

                                        if (codElem > 0) {
                                            jsonData.put("Codigo", codElem);
                                        } else {

                                            // Solo borramos el packet cuando no exista el elemento,
                                            // así se evita que se pierdan acciones realizadas que
                                            // se envían antes que el propio elemento y no consiguen
                                            // recuperar su código.
                                            if (auxElem == null) {
                                                // Si no se puede asignar el código borro el registro
                                                // 13/02/2023 - Se comentan estas dos lineas ya que al crear y borrar un elemento
                                                // sin internet, el idExterno es 0 y no tiene idInterno porque se ha borrado de SQLite. Hay que esperar
                                                // a que se sincronice la creación del elemento y entonces ya se podrá obtener por id interno o externo.
                                                // dbp = new DBPacket();
                                                // dbp.delete(packet);
                                                dbp.close();
                                            }

                                            break;
                                        }
                                    }

                                    // Quitamos el idInterno
                                    jsonData.remove("idInterno");

                                    empresa = jsonData.getInt("IdEmpresa");

                                    int codigo = jsonData.getInt("Codigo");

                                    // Creo la ruta para llamar al servicio web
                                    url = "/moviles/elementos/delete/" + codigo;

                                    // Creo el json nuevo para enviar ya que es
                                    // distinto al que se recibe de la bandeja
                                    jsonData = new JSONObject();
                                    jsonData.put("IdEmpresa", empresa);
                                    jsonData.put("imei", Phone.getInstance()
                                            .getIMEI());

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Compruebo la respuesta del servidor, si ha
                                    // ido bien borro el registro de la bandeja de
                                    // salida y si se ha producido un error porque
                                    // el elemento esti asociado a una ruta tambiin
                                    if (res != null && !res.isNull("ErrNum")) {
                                        int errorCode = res.getInt("ErrNum");
                                        if (errorCode == 404 || errorCode == 400) {
                                            // Borramos.
                                            DBElemento db = new DBElemento();
                                            elem = new Elemento(id, idExterno,
                                                    empresa, "", 0, "", 0, 0, 0, "", "", 0, "");
                                            db.delete(elem);
                                            db.close();

                                            // Lo borro de la lista de elementos para su búsqueda
                                            if (MainActivity.getInstance() != null) {
                                                MainActivity.getInstance().removeElementoBusqueda(elem);
                                            }

                                            // Borro el elemento del cluster
                                            MyBroadCastManager
                                                    .getInstance()
                                                    .sendBroadCastDeleteItemCluster(
                                                            elem.getId());
                                        } else {
                                            showMessage(res.getString("ErrDes")
                                                    + " (" + res.getInt("ErrNum")
                                                    + ")");
                                        }
                                    } else {
                                        // Borro el elemento de la base de datos
                                        DBElemento db = new DBElemento();
                                        elem = new Elemento(id, idExterno, empresa,
                                                "", 0, "", 0,
                                                0, 0, "", "", 0, "");
                                        db.delete(elem);
                                        db.close();

                                        // Lo borro de la lista de elementos para su búsqueda
                                        if (MainActivity.getInstance() != null) {
                                            MainActivity.getInstance().removeElementoBusqueda(elem);
                                        }

                                        // Borro el elemento del cluster
                                        MyBroadCastManager.getInstance()
                                                .sendBroadCastDeleteItemCluster(
                                                        elem.getId());
                                    }

                                    // DINIGO - 12/04/2021 - Mantis 0005143: Fallo al sincronizar EcoSAT Móvil
                                    // Comprobamos que el servidor haya enviado una respuesta antes de borrar el registro de la bandeja de salida.
                                    // Se estaba borrando aún cuando no tenía conexión a Internet y nunca se llegaba a borrar en el servidor.
                                    if (res != null) {
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.ELEMENTO_CREAR_IMAGEN:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    if (jsonData != null) {
                                        int idElemento = jsonData.getInt("IdInterno");

                                        DBElemento dbElemento = new DBElemento();
                                        Elemento elemento = dbElemento.getByIdInterno(idElemento, packet.getEmpresa());
                                        if (elemento != null) {
                                            dbElemento.close();
                                            if (elemento.getIdExterno() > 0) {
                                                //jsonData.remove("Codigo");
                                                jsonData.put("Codigo", elemento.getIdExterno());
                                            } else {
                                                break;
                                            }
                                        } else {
                                            elemento = dbElemento.getByIdExterno(idElemento, packet.getEmpresa());
                                            dbElemento.close();
                                            if (elemento != null)
                                                jsonData.put("Codigo", idElemento);
                                            else
                                                break;
                                        }
                                    }

                                    // Creo la ruta para llamar al servicio web
                                    url = "/moviles/elementos/imagen/insert/"
                                            + Phone.getInstance().getIMEI();

                                    try {
                                        // Envio los datos al servidor
                                        res = wsc.sendPacket(url, jsonData, packet);

                                        if (res != null
                                                && !res.isNull("message")
                                                && (res.getString("message")
                                                .equals("OK"))) {

                                            // Borro el registro de la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                    } catch (Throwable e) {
                                        MyLoggerHandler.getInstance().error(e);
                                    }
                                    break;

                                case Packet.ELEMENTO_BORRAR_IMAGEN:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    if (jsonData != null) {
                                        int idElemento = jsonData
                                                .getInt("IdInterno");

                                        DBElemento dbElemento = new DBElemento();
                                        Elemento elemento = dbElemento
                                                .getByIdInterno(idElemento, packet.getEmpresa());
                                        dbElemento.close();
                                        if (elemento != null) {
                                            if (elemento.getIdExterno() > 0) {
                                                //jsonData.remove("Codigo");
                                                jsonData.put("Codigo",
                                                        elemento.getIdExterno());
                                            } else {
                                                break;
                                            }
                                        } else
                                            break;
                                    }

                                    url = "/moviles/elementos/imagen/deleteall/"
                                            + Phone.getInstance().getIMEI();

                                    try {
                                        // Envio los datos al servidor
                                        res = wsc.sendPacket(url, jsonData, packet);

                                        if (res != null
                                                && !res.isNull("message")
                                                && (res.getString("message")
                                                .equals("OK"))) {

                                            // Borro el registro de la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                    } catch (Throwable e) {
                                        MyLoggerHandler.getInstance().error(e);
                                    }

                                    break;

                                case Packet.SENSOR_PESO_VERTEDERO:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    // Creo la ruta para llamar al servicio web
                                    url = "/moviles/sensores/insert/pesaje/vertedero/"
                                            + Phone.getInstance().getIMEI();

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    if (res != null && !res.isNull("Id")) {
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    if (res == null) {
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.INCIDENCIA_CREAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    id = jsonData.getInt("idInterno");

                                    // Quitamos el idInterno
                                    jsonData.remove("idInterno");

                                    // Quitamos el codigo para que no tengamos el
                                    // problema de recoger los datos en el servidor.
                                    // jsonData.remove("Codigo");
                                    empresa = jsonData.getInt("IdEmpresa");

                                    // URL API Crear incidencia
                                    url = "/moviles/incidencias/insert/"
                                            + Phone.getInstance().getIMEI();

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Compruebo la respuesta del servidor, si ha
                                    // ido bien borro el registro de la bandeja de
                                    // salida
                                    if (res != null && !res.isNull("Id")) {
                                        // Asigno el código definitivo que ha
                                        // asignado el servidor a la incidencia
                                        dbIncidencia = new DBIncidencia();
                                        incidencia = dbIncidencia.getByIdInterno(id, empresa);
                                        // int incidenciaId = res.getInt("Id");

                                        if (incidencia != null) {
                                            incidencia.setIdExterno(res.getInt("Id"));

                                            // Actualizo la incidencia en la base de datos
                                            if (!dbIncidencia.update(incidencia)) {
                                                // Si no se ha podido actualizar lo intento de nuevo
                                                break;
                                            }

                                            // Actualizo el elemento en el mapa
                                            MyBroadCastManager.getInstance().sendBroadCastRefreshIncidencia(incidencia);

                                            // Se asigna el id externo al estado "Abierto" que se creó con la incidencia
                                            // La fecha de la incidencia debe coincidir con la del estado creado
                                            dbIncidenciaEstado = new DBIncidenciaEstado();
                                            IncidenciaEstado incidenciaEstado = dbIncidenciaEstado
                                                    .getEstadoIncidenciaByDate(
                                                            incidencia.getId(),
                                                            incidencia.getEmpresa(),
                                                            res.getString("Fecha"),
                                                            false);
                                            if (incidenciaEstado != null) {
                                                incidenciaEstado.setIdIncidenciaExterno(res.getInt("Id"));
                                                dbIncidenciaEstado.update(incidenciaEstado, false);
                                            }
                                            dbIncidenciaEstado.close();

                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                            dbIncidencia.close();
                                        }
                                    } else {
                                        if (res != null
                                                && !res.isNull("ErrNum")
                                                && (res.getInt("ErrNum") == 400)) {

                                            // Borro el registro de la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                    }
                                    break;

                                case Packet.INCIDENCIA_NUEVO_ESTADO:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    url = "/moviles/incidencias/estados/update/"
                                            + Phone.getInstance().getIMEI();
                                    id = jsonData.getInt("idInterno");
                                    int incidenciaHId = jsonData.getInt("IdIncidenciah");
                                    int incidenciaHIdInterno = jsonData.getInt("IdIncidenciahInterno");
                                    empresa = jsonData.getInt("IdEmpresa");

                                    // Si no se puede rellenar el id externo de la incidencia no se envía
                                    if (incidenciaHId == 0) {

                                        // Recupero la incidencia para asignar el campo "incidenciaExterno"
                                        dbIncidencia = new DBIncidencia();
                                        incidencia = dbIncidencia.getByIdInterno(incidenciaHIdInterno, empresa);
                                        dbIncidencia.close();

                                        incidenciaHId = incidencia.getIdExterno();

                                        if (incidenciaHId == 0) {
                                            break;
                                        }
                                        jsonData.put("IdIncidenciah", incidenciaHId);
                                    }

                                    // Quitamos el idInterno
                                    jsonData.remove("idInterno");
                                    jsonData.remove("IdIncidenciahInterno");


                                    // Quitamos el Id para que concuerde con el json
                                    // que enviamos al servidor.
                                    // jsonData.remove("Id");

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Compruebo la respuesta del servidor, si ha
                                    // ido bien borro el registro de la bandeja de
                                    // salida
                                    if (res != null && !res.isNull("Id")) {

                                        // Asigno el codigo definitivo que ha
                                        // asignado el servidor a la incidencia
                                        dbIncidenciaEstado = new DBIncidenciaEstado();
                                        estado = dbIncidenciaEstado.getByIdInterno(id, empresa);
                                        estado.setIdExterno(res.getInt("Id"));
                                        estado.setIdIncidenciaExterno(res.getInt("IdIncidenciah"));

                                        if (dbIncidenciaEstado.update(estado, false)) {
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                        dbIncidenciaEstado.close();

                                    } else {

                                        if (res != null
                                                && !res.isNull("ErrNum")
                                                && (res.getInt("ErrNum") == 400)) {

                                            // Borro el registro de la bandeja de
                                            // salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                    }

                                    break;

                                case Packet.INCIDENCIA_ASIGNAR_USUARIO:
                                    url = "/moviles/incidencias/asignar/" + Phone.getInstance().getIMEI();
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    int propietarioId = jsonData.getInt("propietarioId");
                                    int incidenciaId = jsonData.getInt("incidenciaId");

                                    if (incidenciaId < 1) {
                                        dbIncidencia = new DBIncidencia();
                                        int incidenciaIdInterno = jsonData.getInt("incidenciaIdInterno");
                                        incidencia = dbIncidencia.getByIdInterno(incidenciaIdInterno, MainActivity.getInstance().getEmpresa());
                                        dbIncidencia.close();
                                        incidenciaId = incidencia.getIdExterno();
                                        if (incidenciaId < 1) {
                                            jsonData.put("incidenciaId", incidenciaId);
                                            jsonData.remove("incidenciaIdInterno");
                                        } else {
                                            break;
                                        }
                                    }

                                    res = wsc.sendPacket(url, jsonData, packet);

                                    if (res != null
                                            && !res.isNull("Propietario")
                                            && (res.getInt("Propietario") == propietarioId)) {

                                        // Borro el registro de la bandeja desalida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();

                                        // Actualizo el propietario de la incidencia
                                        dbIncidencia = new DBIncidencia();
                                        incidencia = dbIncidencia.getByIdExterno(incidenciaId, MainActivity.getInstance().getEmpresa());
                                        incidencia.setPropietario(propietarioId);
                                        dbIncidencia.setNombrePropietario(incidencia);
                                        dbIncidencia.update(incidencia);
                                        dbIncidencia.close();

                                        // Evento para actualizar la incidencia en la lista
                                        EventBus.getDefault().post(new OnIncidenciaAsignada(incidencia));
                                    }

                                    break;

                                case Packet.ENVIO_POSICIONES:
                                    arrayData = new JSONArray(new String(packet.getDatos()));

                                    String modoRuta =
                                            Config.getInstance().getValue("modo_ruta", "peaton");


                                    // URL API Crear incidencia
                                    url = "/moviles/insert/posiciones/"
                                            + Phone.getInstance().getIMEI() + "/" + (modoRuta.equals("") ? "peaton" : modoRuta);

                                    // Envio los datos al servidor
                                    try {

                                        res = wsc.sendPacket(url, arrayData);

                                    } catch (Throwable ex) {
                                        MyLoggerHandler.getInstance().error(ex);
                                    }

                                    if (res != null
                                            && !res.isNull("message")
                                            && (res.getString("message")
                                            .equals("OK"))) {

                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.SENSOR_LAVADO:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    if (jsonData != null) {
                                        int idElemento = jsonData
                                                .getInt("CodigoElemento");
                                        empresa = jsonData.getInt("IdEmpresa");
                                        DBElemento dbElemento = new DBElemento();
                                        Elemento elemento = dbElemento
                                                .getByIdInterno(idElemento, empresa);
                                        dbElemento.close();
                                        if (elemento != null) {
                                            if (elemento.getIdExterno() > 0) {
                                                jsonData.remove("CodigoElemento");
                                                jsonData.put("CodigoElemento",
                                                        elemento.getIdExterno());
                                            } else {
                                                break;
                                            }
                                        }
                                    }

                                    // URL API
                                    url = "/moviles/sensores/insert/lavado/contenedor/"
                                            + Phone.getInstance().getIMEI();

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    if (res != null
                                            && !res.isNull("message")
                                            && (res.getString("message")
                                            .equals("OK"))) {

                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.SENSOR_NIVEL_LLENADO:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    if (jsonData != null) {
                                        int idElemento = jsonData.getInt("CodigoElemento");
                                        empresa = jsonData.getInt("IdEmpresa");
                                        DBElemento dbElemento = new DBElemento();
                                        Elemento elemento = dbElemento
                                                .getByIdInterno(idElemento, empresa);
                                        dbElemento.close();
                                        if (elemento != null) {
                                            if (elemento.getIdExterno() > 0) {
                                                jsonData.remove("CodigoElemento");
                                                jsonData.put("CodigoElemento",
                                                        elemento.getIdExterno());
                                            } else {
                                                break;
                                            }
                                        } else {
                                            // Borro el registro de la bandeja de salida porque el
                                            // elemento no existe y no se podrá sincronizar hasta
                                            // que se vacíe la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                            break;
                                        }
                                    }

                                    // URL API
                                    url = "/moviles/sensores/insert/nivel/llenado/"
                                            + Phone.getInstance().getIMEI();

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    if (res != null
                                            && !res.isNull("message")
                                            && (res.getString("message")
                                            .equals("OK"))) {

                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    // Borramos paquete porque algo esta fallando al
                                    // enviarlo.
                                    if (res != null &&
                                            !res.isNull("ErrNum")
                                            && res.getInt("ErrNum") == 400) {
                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    break;

                                case Packet.SENSOR_NIVEL_LLENADO_SIN_PROCESAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    if (jsonData != null) {
                                        int idElemento = jsonData.getInt("CodigoElemento");
                                        empresa = jsonData.getInt("IdEmpresa");
                                        DBElemento dbElemento = new DBElemento();
                                        Elemento elemento = dbElemento
                                                .getByIdInterno(idElemento, empresa);
                                        dbElemento.close();
                                        if (elemento != null) {
                                            if (elemento.getIdExterno() > 0) {
                                                jsonData.remove("CodigoElemento");
                                                jsonData.put("CodigoElemento",
                                                        elemento.getIdExterno());
                                            } else {
                                                break;
                                            }
                                        } else {
                                            // Borro el registro de la bandeja de salida porque el
                                            // elemento no existe y no se podrá sincronizar hasta
                                            // que se vacíe la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                            break;
                                        }
                                    }

                                    // URL API
                                    url = "/api/sensores/insert/sensor/lectura/llenado/fraccion";

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    if (res != null
                                            && !res.isNull("message")
                                            && (res.getString("message")
                                            .equals("OK"))) {

                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    // Borramos paquete porque algo esta fallando al
                                    // enviarlo.
                                    if (!res.isNull("ErrNum")
                                            && res.getInt("ErrNum") == 400) {
                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.ELEMENTO_SUSTITUIR_CON_BAJA:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    int idElementoRetirado = 0, idElementoDepositado = 0;
                                    String nombreElementoDepositado = "";

                                    DBElemento dbElemento_sus = new DBElemento();
                                    Elemento elemRetirado = null, elemDepositado = null;

                                    try {
                                        idElementoRetirado = jsonData.getInt("CodigoElementoRetirado");
                                        elemRetirado = dbElemento_sus.getByIdExterno(idElementoRetirado, MainActivity.getInstance().getEmpresa());
                                        nombreElementoDepositado = jsonData.getString("ElementoDepositado");

                                        idElementoDepositado = jsonData.getInt("CodigoElementoDepositado");
                                        elemDepositado = dbElemento_sus.getByIdExterno(idElementoDepositado, MainActivity.getInstance().getEmpresa());

                                    } catch (Throwable e) {
                                        MyLoggerHandler.getInstance().error(e);
                                    }


                                    if (idElementoRetirado <= 0 || elemRetirado == null) {
                                        // Borro el registro de la bandeja de salida porque el
                                        // elemento no existe y no se podrá sincronizar hasta
                                        // que se vacíe la bandeja de salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                        break;
                                    }

                                    // URL API
                                    url = "/moviles/elementos/sustituir/recycling/" + Phone.getInstance().getIMEI();

                                    try {
                                        // Envio los datos al servidor
                                        res = wsc.sendPacket(url, jsonData, packet);

                                        if (res != null && !res.isNull("Codigo")) {

                                            int lunes = 0, martes = 0, miercoles = 0, jueves = 0, viernes = 0, sabado = 0, domingo = 0;
                                            int vacia_bajo_demanda = 0;
                                            int rotativo = 0;

                                            if (!res.isNull("DiasBloqueo")) {

                                                JSONArray jss = res.getJSONArray("DiasBloqueo");

                                                for (int i = 0; i < jss.length(); i++) {
                                                    JSONObject object = jss.getJSONObject(i);
                                                    int dia = object.getInt("Dia");
                                                    int tipoBloqueo = object.getInt("TipoBloqueo");

                                                    if (dia == 1) lunes = tipoBloqueo;
                                                    else if (dia == 2) martes = tipoBloqueo;
                                                    else if (dia == 3) miercoles = tipoBloqueo;
                                                    else if (dia == 4) jueves = tipoBloqueo;
                                                    else if (dia == 5) viernes = tipoBloqueo;
                                                }
                                            }

                                            if (!res.isNull("VaciaBajoDemanda"))
                                                vacia_bajo_demanda = res.getInt("VaciaBajoDemanda");
                                            if (!res.isNull("Rotativo"))
                                                rotativo = res.getInt("Rotativo");

                                            int tipoZona = 0;
                                            if (!res.isNull("IdZona"))
                                                tipoZona = res.getInt("IdZona");

                                            int tieneImagen = 0;

                                            tieneImagen = (!res.has("TieneImagen")
                                                    || res.isNull("TieneImagen"))
                                                    ? 0 : res.getInt("TieneImagen");

                                            Elemento nuevo_elemento = new Elemento(0, res.getInt("Codigo"), res.getInt("IdEmpresa"),
                                                    res.getString("Nombre"), res.getInt("CodModelo"), res.getString("Matricula"),
                                                    res.getInt("Estado"), elemRetirado.getPosition().latitude, elemRetirado.getPosition().longitude,
                                                    res.getString("Descripcion"), res.getString("NivelCritico"), res.getInt("ElementoSimilar"),
                                                    lunes, martes, miercoles, jueves, viernes, sabado, domingo, tipoZona, rotativo, vacia_bajo_demanda, tieneImagen, null, "", 0, res.getString("IMEI_WellNess"));

                                            dbElemento_sus.insert(nuevo_elemento);
                                            // Lo añado a la lista de elementos para su búsqueda
                                            if (MainActivity.getInstance() != null) {
                                                MainActivity.getInstance().addElementoBusqueda(nuevo_elemento);
                                            }

                                            // Borro el registro de la bandeja de salida
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();

                                            // si todo ha ido bien elimino el elemento que acabo de sustituir
                                            dbElemento_sus.delete(elemRetirado);
                                            // Lo borro de la lista de elementos para su búsqueda
                                            if (MainActivity.getInstance() != null) {
                                                MainActivity.getInstance().removeElementoBusqueda(elemRetirado);
                                            }

                                            // si el elemento depositado, existe, lo elimino, pues se habrá creado en el punto de ubicación del que acabamos de sustituir
                                            if (elemDepositado != null) {
                                                dbElemento_sus.delete(elemDepositado);
                                                // Lo borro de la lista de elementos para su búsqueda
                                                if (MainActivity.getInstance() != null) {
                                                    MainActivity.getInstance().removeElementoBusqueda(elemDepositado);
                                                }
                                            }

                                            MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

                                        } else if (!res.isNull("ErrNum")
                                                && res.getInt("ErrNum") == 400) {

                                            // Borramos paquete porque algo esta fallando al  enviarlo.
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();
                                        }
                                    } catch (Throwable e) {
                                        Log.e("OutBox", e.getMessage());

                                        // Borro el registro de la bandeja de salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();

                                    }

                                    dbElemento_sus.close();
                                    break;

                                case Packet.INCIDENCIA_INSERTAR_FOTO:
                                    jsonData = new JSONObject(new String(packet.getDatos()));
                                    // Ahora enviamos las fotos
                                    url = "/moviles/incidencias/imagen/insert/";
                                    id = jsonData.getInt("Id");

                                    int idIncidencia = jsonData.getInt("IncidenciaH");

                                    empresa = jsonData.getInt("IdEmpresa");
                                    dbIncidencia = new DBIncidencia();
                                    incidencia = dbIncidencia.getByIdInterno(idIncidencia, empresa);
                                    dbIncidencia.close();

                                    // Si todavia no lo hemos enviado al servidor
                                    // pasamos de enviar la foto.
                                    if (incidencia != null) {
                                        if (incidencia.getIdExterno() <= 0)
                                            break;

                                        jsonData.put("IncidenciaH", incidencia.getIdExterno());

                                        String imageBase64 = jsonData.getString("ImagenBase64");

                                        // Añadimos compatibilidad con lo que existía previamente.
                                        if (Utilss.isFilled(imageBase64) && Utilss.isPathImage(imageBase64)) {
                                            try {
                                                imageBase64 = Utils.imagePathToBase64(jsonData.getString("ImagenBase64"));

                                                jsonData.remove("ImagenBase64");
                                                jsonData.put("ImagenBase64", imageBase64);
                                            } catch (Throwable t) {
                                                imageBase64 = "";
                                            }
                                        }

                                        // sino existe la imagen a enviar (se ha borrado o es inaccesible, elimino el paquete
                                        if (imageBase64 == null || imageBase64.length() == 0) {
                                            dbp = new DBPacket();
                                            dbp.delete(packet);
                                            dbp.close();

                                        } else {

                                            // Envio los datos al servidor
                                            res = wsc.sendPacket(url, jsonData, packet);

                                            // Compruebo la respuesta del servidor, si ha ido bien
                                            // borro el registro de la bandeja de salida
                                            if (res != null && !res.isNull("Id")) {
                                                // Asigno el cidigo definitivo que ha
                                                // asignado el servidor a la incidencia
                                                DBIncidenciaFoto dbIncidenciaFoto = new DBIncidenciaFoto();
                                                IncidenciaFoto incidenciaFoto = dbIncidenciaFoto
                                                        .getById(id, empresa);

                                                // Se introducen los id externos
                                                if (incidenciaFoto != null) {
                                                    incidenciaFoto.setIdExterno(res.getInt("Id"));
                                                    incidenciaFoto.setIncidenciaExterno(res.getInt("IncidenciaH"));
                                                    if (dbIncidenciaFoto.update(incidenciaFoto)) {
                                                        dbp = new DBPacket();
                                                        dbp.delete(packet);
                                                        dbp.close();
                                                    }
                                                }
                                                dbIncidenciaFoto.close();

                                            } else {
                                                if (res != null && !res.isNull("ErrNum") && (res.getInt("ErrNum") == 400)) {
                                                    // Borro el registro de la bandeja de salida
                                                    dbp = new DBPacket();
                                                    dbp.delete(packet);
                                                    dbp.close();
                                                }
                                            }
                                        }
                                    } else {
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.SENSOR_TAG:
                                case Packet.SENSOR_ELEMENTO_RECOGIDO:
                                    jsonData = new JSONObject(new String(
                                            packet.getDatos()));

                                    // URL API
                                    url = "/api/sensores/insert/sensor";

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    if (res != null
                                            && !res.isNull("message")
                                            && (res.getString("message")
                                            .equals("OK"))) {
                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    // Borramos paquete porque algo esta fallando al
                                    // enviarlo.
                                    if (!res.isNull("ErrNum")
                                            && res.getInt("ErrNum") == 400) {
                                        // Borro el registro de la bandeja de
                                        // salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.ELEMENTO_SUSTITUIR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    // URL API
                                    url = "/moviles/elementos/sustituir/"
                                            + Phone.getInstance().getIMEI();

                                    Elemento elementoDepositado2 = null, elementoRetirado = null;
                                    // *** Se comprueba que el elemento DEPOSITADO existe ***
                                    idExterno = jsonData.getInt("CodigoElementoDepositado");

                                    if (idExterno <= 0) {
                                        // Se obtiene el id interno del elemento depositado
                                        id = jsonData.getInt("idInternoElementoDepositado");

                                        // Se obtiene el elemento
                                        DBElemento dbElemento = new DBElemento();
                                        elementoDepositado2 = dbElemento.getByIdInterno(id, MainActivity.getInstance().getEmpresa());
                                        dbElemento.close();

                                        idExterno = elementoDepositado2.getIdExterno();

                                        // Si el id externo no es válido, no se envía el paquete
                                        if (elementoDepositado2 == null || idExterno <= 0)
                                            break;

                                        jsonData.put("CodigoElementoDepositado", idExterno);
                                    }

                                    // *** Se comprueba que el elemento RETIRADO existe ***
                                    idExterno = jsonData.getInt("CodigoElementoRetirado");

                                    if (idExterno <= 0) {
                                        // Se obtiene el id interno del elemento retirado
                                        id = jsonData.getInt("idInternoElementoRetirado");

                                        // Se obtiene el elemento
                                        DBElemento dbElemento = new DBElemento();
                                        elementoRetirado = dbElemento.getByIdInterno(id, MainActivity.getInstance().getEmpresa());
                                        dbElemento.close();

                                        idExterno = elementoRetirado.getIdExterno();

                                        // Si el id externo no es válido, no se envía el paquete
                                        if (elementoRetirado == null || idExterno <= 0)
                                            break;

                                        jsonData.put("CodigoElementoDepositado", idExterno);
                                    }

                                    jsonData.remove("idInternoElementoDepositado");
                                    jsonData.remove("idInternoElementoRetirado");

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Se comprueba el resultado
                                    boolean isOk = checkResult(res, packet, url);

                                    // recargo la lista de elementos para su búsqueda en la ventana principal
                                    if (MainActivity.getInstance() != null && isOk) {
                                        if (elementoDepositado2 != null && elementoRetirado != null) {
                                            MainActivity.getInstance().updateElementoBusqueda(elementoDepositado2);
                                            MainActivity.getInstance().updateElementoBusqueda(elementoRetirado);
                                        }
                                    }
                                    break;

                                case Packet.ELEMENTO_DEPOSITAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    // URL API
                                    url = "/moviles/elementos/depositar/"
                                            + Phone.getInstance().getIMEI();

                                    idExterno = jsonData.getInt("CodigoElemento");

                                    // Se comprueba que el id externo sea válido
                                    if (idExterno <= 0) {
                                        // Se obtiene el id interno del elemento
                                        id = jsonData.getInt("idInternoElemento");

                                        // Se obtiene el elemento
                                        DBElemento dbElemento = new DBElemento();
                                        Elemento elementoDepositado = dbElemento.getByIdInterno(id, MainActivity.getInstance().getEmpresa());
                                        dbElemento.close();

                                        // Si el id externo no es válido, no se envía el paquete
                                        if (elementoDepositado == null)
                                            break;

                                        idExterno = elementoDepositado.getIdExterno();

                                        if (idExterno <= 0)
                                            break;

                                        jsonData.put("CodigoElemento", idExterno);
                                    }

                                    jsonData.remove("idInternoElemento");

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Se comprueba el resultado
                                    checkResult(res, packet, url);
                                    break;

                                case Packet.ELEMENTO_RETIRAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    // URL API
                                    url = "/moviles/elementos/retirar/" + Phone.getInstance().getIMEI();

                                    Elemento eleRetirado = null;

                                    idExterno = jsonData.getInt("CodigoElemento");

                                    // Se comprueba que el id externo sea válido
                                    if (idExterno <= 0) {
                                        // Se obtiene el id interno del elemento
                                        id = jsonData.getInt("idInternoElemento");

                                        // Se obtiene el elemento
                                        DBElemento dbElemento = new DBElemento();
                                        eleRetirado = dbElemento.getByIdInterno(id, MainActivity.getInstance().getEmpresa());
                                        dbElemento.close();

                                        idExterno = eleRetirado.getIdExterno();

                                        // Si el id externo no es válido, no se envía el paquete
                                        if (eleRetirado == null || idExterno <= 0)
                                            break;

                                        jsonData.put("CodigoElemento", idExterno);
                                    }

                                    jsonData.remove("idInternoElemento");

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    // Se comprueba el resultado
                                    boolean isSend = checkResult(res, packet, url);

                                    // recargo la lista de elementos para su búsqueda en la ventana principal
                                    if (MainActivity.getInstance() != null && isSend && eleRetirado != null) {
                                        MainActivity.getInstance().updateElementoBusqueda(eleRetirado);
                                    }
                                    break;

                                case Packet.VEHICULO_ASOCIAR:
                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    // URL API
                                    url = "/moviles/asociar/vehiculo";

                                    // Envio los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);

                                    if (res != null && !res.isNull("message")) {
                                        // Borro el registro de la bandeja de salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    // Borramos paquete porque algo está fallando al enviarlo
                                    if (!res.isNull("ErrNum")
                                            && res.getInt("ErrNum") == 400) {
                                        // Borro el registro de la bandeja de salida
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }
                                    break;

                                case Packet.INCIDENCIAS_PROPIETARIOS:

                                    jsonData = new JSONObject(new String(packet.getDatos()));

                                    //Para registrar el vehiculo que envia los datos.
                                    jsonData.put("Imei", Phone.getInstance()
                                            .getIMEI());

                                    url = Config.getInstance().getValue("webSvc", "")
                                            + "/moviles/incidencias/propietarios/modificados";

                                    // Envío los datos al servidor
                                    res = wsc.sendPacket(url, jsonData, packet);
                                    if (res != null && !res.isNull("Id")) {
                                        dbp = new DBPacket();
                                        dbp.delete(packet);
                                        dbp.close();
                                    }

                                    break;

                                default:
                                    // Las órdenes desconocidas se borran de la bandeja de salida
                                    dbp = new DBPacket();
                                    dbp.delete(packet);
                                    dbp.close();
                            }

                        } else {
                            // Borro el registro de la bandeja de salida
                            dbp = new DBPacket();
                            dbp.delete(packet);
                            dbp.close();
                        }
                    }
                    isSending = false;

                    // Espero durante el tiempo establecido salvo
                    // que se finalice
                    for (int i = 0; i < TIME_SLEEP && running; i++)
                        sleep(1000);

                } catch (Throwable e) {
                    MyLoggerHandler.getInstance().error(e);
                    // Espero durante el tiempo establecido salvo
                    // que se finalice
                    for (int i = 0; i < TIME_SLEEP && running; i++)
                        sleep(1000);
                }
            }

            running = false;

            if (dbp != null)
                dbp.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }

    private void showMessage(String text) {
        try {
            Message msg = new Message();
            Bundle b = new Bundle();

            b.putString("message", text);
            msg.setData(b);

            handler.sendMessage(msg);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    /**
     * Manejador de mensajes para interactuar con la interfaz de usuario
     */
    static final Handler handler = new Handler() {

        @Override
        public void handleMessage(final Message message) {
            try {
                new InfoDialog(MainActivity.getInstance(), MainActivity
                        .getInstance().getString(R.string.atencion), message
                        .getData().getString("message"), InfoDialog.ICON_INFO,
                        new OnInfoDialogSelect() {
                            @Override
                            public void onSelectOption(int option) {
                            }
                        }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER)
                        .show();
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
                e.printStackTrace();
            }
        }
    };

    /**
     * Comprueba el resultado devuelto por la API y realiza las acciones por defecto con el
     * paquete de salida.
     *
     * @param res    JSON devuelto.
     * @param packet Paquete de la bandeja de salida asociado a la consulta.
     */
    private boolean checkResult(JSONObject res, Packet packet, String url) throws Throwable {
        if (res == null) return false;

        DBPacket dbp;

        if (!res.isNull("message") && res.getString("message").equals("OK")) {
            // Borro el registro de la bandeja de salida
            dbp = new DBPacket();
            dbp.delete(packet);
            dbp.close();
            return true;
        } else if (!res.isNull("ErrNum")) {
            // Borramos paquete porque algo esta fallando al enviarlo.
            if (res.getInt("ErrNum") == 400 || res.getInt("ErrNum") == 404) {
                dbp = new DBPacket();
                dbp.delete(packet);
                dbp.close();
            } else if (res.getInt("ErrNum") >= 500) {
                dbp = new DBPacket();
                dbp.delete(packet);
                //Pongo al final de la cola
                dbp.insert(packet);
                dbp.close();
            }
        }
        return false;
    }

}
