package com.movisat.apis;

import com.movisat.api.Api;
import com.movisat.api.ApiResponse;
import com.movisat.utils.ICallBack;

import org.json.JSONException;

import java.util.HashMap;

public class LoginIndraApi {

    public void execute(String url, Request request, String token, final ICallBack<Boolean> callback) {


        try {
            new Api().post(
                   url + "/api/usuarios/login/minsait",
                   ApiParams.getHeader(token),
                   request.getBody(),
                   new ICallBack<ApiResponse<String>>() {
                       @Override
                       public void execute(ApiResponse<String> response) {
                           if (response.isError) callback.execute(false);
                           else if (!response.body.toString().contains("OK")) callback.execute(false);
                           else callback.execute(true);
                       }
                   }
           );
        } catch (JSONException e) {
            e.printStackTrace();
            callback.execute(false);
        }


    }

    public static class Request {
        private String login;
        private String password;
        private int companyId;

        public Request(String login, String password, int companyId) {
            this.login = login;
            this.password = password;
            this.companyId = companyId;
        }

        HashMap<String, Object> getBody() {
            return new HashMap<String, Object>() {
                {
                    put("Login", login);
                    put("Password", password);
                    put("Empresa", companyId);
                }
            };
        }
    }


}
