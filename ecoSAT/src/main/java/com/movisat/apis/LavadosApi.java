package com.movisat.apis;

import com.movisat.api.Api;
import com.movisat.ecosat.MainActivity;
import com.movisat.log.Logg;
import com.movisat.utils.ICallBack;

import org.json.JSONArray;
import org.json.JSONException;

import java.util.HashMap;

public class LavadosApi {

    public void execute(String url, LavadosApi.Request request, String token, final ICallBack<JSONArray> callback) {
        try {
            url += "/api/sensores/lavados/" + MainActivity.getInstance().getEmpresa();
            Logg.warning("LavadosApi", "PETICIÓN: " + url);
            new Api().post(
                  url,
                  ApiParams.getHeader(token),
                  request.getBody(),
                  response -> {
                      if (response.isError) callback.execute(null);
                          //else if (!response.body.toString().contains("OK")) callback.execute("");
                      else {
                          try {
//                                JSONObject jsonobject = new JSONObject(response.body);
//                                JSONArray jsonArray = jsonobject.getJSONArray("");
                              JSONArray jsonArray = new JSONArray(response.body);
                              Logg.warning("LavadosApi", "RESPUESTA: " + jsonArray.length());
                              callback.execute(jsonArray);

                          } catch (JSONException e) {
                              e.printStackTrace();
                              callback.execute(null);
                          }
                      }
                  }
            );
        } catch (JSONException e) {
            e.printStackTrace();
            callback.execute(null);
        }
    }

    public static class Request {
        private String imei;
        private String desde;
        private String hasta;

        public Request(String imei, String desde, String hasta) {
            this.imei = imei;
            this.desde = desde;
            this.hasta = hasta;
        }

        HashMap<String, Object> getBody() {
            return new HashMap<String, Object>() {
                {
                    put("imei", imei);
                    put("desde", desde);
                    put("hasta", hasta);
                }
            };
        }
    }
}
