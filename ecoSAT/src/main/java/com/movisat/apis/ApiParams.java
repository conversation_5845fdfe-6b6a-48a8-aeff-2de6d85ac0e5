package com.movisat.apis;

import java.util.HashMap;

public class ApiParams {

    public static HashMap<String, String> getHeader(String token) {
        return new HashMap<String, String>() {
            {
                put("User-Agent", "java-Android");
                put("Content-type", "application/json");
                //put("Accept-encoding", "gzip");
                put("Authorization", "Bearer " + token);
            }
        };
    }
}
