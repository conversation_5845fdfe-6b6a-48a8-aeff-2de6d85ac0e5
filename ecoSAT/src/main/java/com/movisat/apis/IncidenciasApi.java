package com.movisat.apis;

import com.movisat.api.Api;
import com.movisat.log.Logg;
import com.movisat.utils.ICallBack;

import org.json.JSONArray;
import org.json.JSONException;

import java.util.HashMap;

public class IncidenciasApi {

    public void getHistoricoImagenes(String baseUrl, Request request, String token, final ICallBack<JSONArray> callback) {
        String url = baseUrl + "/api/incidencias/historico/imagenes/get";
        Logg.warning("IncidenciasApi", "PETICIÓN: " + url);
        try {
            new Api().post(
                    url,
                    ApiParams.getHeader(token),
                    request.getBody(),
                    response -> {
                        if (response.isError) {
                            callback.execute(null);
                        } else {
                            try {
                                JSONArray jsonArray = new JSONArray(response.body);
                                Logg.warning("IncidenciasApi", "RESPUESTA: " + jsonArray.length());
                                callback.execute(jsonArray);
                            } catch (JSONException e) {
                                callback.execute(null);
                            }
                        }
                    }
            );
        } catch (JSONException e) {
            callback.execute(null);
        }
    }

    public static class Request {
        private int empresa;
        private Integer idIncidencia; // interno
        private Integer idIncidenciaExterno; // externo
        private Integer idImagenExterno; // optional
        private String fecha; // ISO-8601 optional
        private String imagenBase64; // optional

        public Request(int empresa, Integer idIncidencia, Integer idIncidenciaExterno,
                       Integer idImagenExterno, String fecha, String imagenBase64) {
            this.empresa = empresa;
            this.idIncidencia = idIncidencia;
            this.idIncidenciaExterno = idIncidenciaExterno;
            this.idImagenExterno = idImagenExterno;
            this.fecha = fecha;
            this.imagenBase64 = imagenBase64;
        }

        HashMap<String, Object> getBody() {
            return new HashMap<String, Object>() {
                {
                    // Keep keys aligned with server sample payload
                    put("idIncidencia", idIncidencia);
                    put("empresa", empresa);
                    put("IdIncidencia", idIncidenciaExterno);
                    put("IdIncidenciaExterno", idIncidenciaExterno);
                    put("IdImagenExterno", idImagenExterno);
                    put("Fecha", fecha);
                    put("Imagen", imagenBase64);
                }
            };
        }
    }
}

