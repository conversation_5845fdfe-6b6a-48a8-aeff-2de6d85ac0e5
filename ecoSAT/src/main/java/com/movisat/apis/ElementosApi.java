package com.movisat.apis;

import android.net.Uri;

import com.movisat.api.Api;
import com.movisat.log.Logg;
import com.movisat.utils.ICallBack;

import org.json.JSONArray;
import org.json.JSONException;

/**
 * API client for Element images at a location point.
 * Endpoint: GET /api/elementos/imagen/punto-ubicacion/{codigo}/{empresa}
 * Optional query params: desde, hasta
 */
public class ElementosApi {

    public void getImagenesPuntoUbicacion(String baseUrl,
                                          int codigo,
                                          int empresa,
                                          String token,
                                          String desde,
                                          String hasta,
                                          final ICallBack<JSONArray> callback) {
        // Build base URL
        String url = baseUrl + "/api/elementos/imagen/punto-ubicacion/" + codigo + "/" + empresa;

        // Append optional date range if provided
        Uri.Builder builder = Uri.parse(url).buildUpon();
        boolean hasQuery = false;
        if (desde != null && desde.length() > 0) {
            builder.appendQueryParameter("desde", desde);
            hasQuery = true;
        }
        if (hasta != null && hasta.length() > 0) {
            builder.appendQueryParameter("hasta", hasta);
            hasQuery = true;
        }
        if (hasQuery) url = builder.build().toString();

        Logg.warning("ElementosApi", "PETICIÓN: " + url);
        try {
            new Api().get(
                    url,
                    ApiParams.getHeader(token),
                    response -> {
                        if (response.isError) {
                            callback.execute(null);
                        } else {
                            try {
                                JSONArray jsonArray = new JSONArray(response.body);
                                Logg.warning("ElementosApi", "RESPUESTA: " + jsonArray.length());
                                callback.execute(jsonArray);
                            } catch (JSONException e) {
                                callback.execute(null);
                            }
                        }
                    }
            );
        } catch (Throwable t) {
            callback.execute(null);
        }
    }
}

