package com.movisat.apis;


import com.movisat.api.Api;
import com.movisat.log.Logg;
import com.movisat.utils.ICallBack;

import org.json.JSONArray;
import org.json.JSONException;

public class ElementosApi {

    public void getImagenesPuntoUbicacion(String baseUrl,
                                          int codigo,
                                          int empresa,
                                          String token,
                                          String desde,
                                          String hasta,
                                          final ICallBack<JSONArray> callback) {

        String url = baseUrl + "/api/elementos/imagen/punto-ubicacion/codigo/" + codigo + "/" + empresa;

        StringBuilder urlBuilder = new StringBuilder(url);
        boolean hasQuery = false;

        if (desde != null && !desde.isEmpty()) {
            urlBuilder.append("?desde=").append(desde);
            hasQuery = true;
        }
        if (hasta != null && !hasta.isEmpty()) {
            urlBuilder.append(hasQuery ? "&hasta=" : "?hasta=").append(hasta);
        }
        url = urlBuilder.toString();

        Logg.info("ElementosApi", "PETICIÓN: " + url);
        try {
            new Api().get(
                    url,
                    ApiParams.getHeader(token),
                    response -> {
                        if (response.isError) {
                            callback.execute(null);
                        } else {
                            try {
                                JSONArray jsonArray = new JSONArray(response.body);
                                Logg.info("ElementosApi", "RESPUESTA: " + jsonArray.length());
                                callback.execute(jsonArray);
                            } catch (JSONException e) {
                                callback.execute(null);
                            }
                        }
                    }
            );
        } catch (Throwable t) {
            callback.execute(null);
        }
    }
}
