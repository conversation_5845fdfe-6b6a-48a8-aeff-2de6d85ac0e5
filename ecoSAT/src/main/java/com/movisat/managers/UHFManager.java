package com.movisat.managers;

import android.os.AsyncTask;
import android.util.Log;

import com.movisat.ecosat.MyLoggerHandler;
import com.rscja.deviceapi.RFIDWithUHF;

import java.util.Calendar;
import java.util.Date;


/**
 * Lee TAGs UHF.
 *
 * Esta clase constituye un wrapper para el manejo del lector UHF de los dispositivos de mano
 * CHAINWAY C71.
 * <br>
 * Presenta el patrón <PERSON>, obteniendo la instancia del manager mediante
 * {@link UHFManager#get() UHFManager.get()}. Una vez obtenido, es necesario inicializar el
 * lector UHF mediante {@link #init()}. Para liberarlo después de su uso se debe llamar a
 * {@link #release()}.
 * <br>
 * Existen dos modos de operación para la lectura de tags:
 * <ul>
 *     <li> <b>Lectura continua</b>: Se inicia con {@link #startReadLoop()} y se finaliza con
 *          {@link #stopReadLoop()}. Mientras dure la lectura, todos los tags detectados son
 *          notificados.
 *     <li> <b>Lectura única</b>: Se inicia con {@link #readSingleTag(int)}
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-02-26
 */
public class UHFManager {

    public static final byte FREQ_MODE_CHINA_STANDARD_920_925_MHZ = 0;
    public static final byte FREQ_MODE_CHINA_STANDARD_840_845_MHZ = 1;
    public static final byte FREQ_MODE_ETSI_STANDARD_865_868_MHZ = 2;
    public static final byte FREQ_MODE_FIXED_915_MHZ = 3;
    public static final byte FREQ_MODE_USA_STANDARD_902_928_MHZ = 4;

    public static final int SCAN_KEYCODE = 139;

    private static final String TAG = "UHF";
    public static Object OnTagReadListener;
    private volatile boolean mReading = false;

    private static UHFManager mInstance = null;

    private RFIDWithUHF mReader;
    private Date mStopReadDate;

    private OnStatusChangeListener mOnStatusChangedListener;
    private OnTagReadListener mOnTagReadListener;


    /**
     * Devuelve la referencia al manejador del lector UHF.
     *
     * @return Referencia al manejador del lector UHF.
     */
    public static UHFManager get() {
        if (mInstance == null)
            mInstance = new UHFManager();
        return mInstance;
    }


    /**
     * Devuelve el objeto RFIDWithUHF para realizar operaciones a más bajo nivel.
     *
     * @return Objeto RFIDWithUHF.
     */
    public RFIDWithUHF getReader() {
        try {
            if (mReader == null)
                mReader = RFIDWithUHF.getInstance();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return mReader;
    }


    /**
     * Inicializa el lector UHF.
     *
     * @return true si la inicialización ha sido correcta, false en otro caso.
     */
    public boolean init() {
        try {
            getReader();
            mReader.init();
            mReader.setEPCTIDMode(true);

            if (mReader == null || mReader.getHardwareType() == null)
                throw new Exception("El dispositivo no tiene un lector UHF válido.");

        } catch (Throwable e) {
            return false;
        }

        return isInitialized();
    }


    /**
     * Des-inicializa y libera el lector UHF.
     */
    public void release() {
        try {
            if (mReader != null)
                mReader.free();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }


    /**
     * Indica si el controlador del lector UHF está inicializado.
     *
     * @return true si el controlador está inicializado, false en otro caso.
     */
    public boolean isInitialized() {
        return mReader != null && mReader.isPowerOn();
    }


    /**
     * Indica si el lector UHF está en estado de lectura.
     *
     * @return true si el lector UHF está en estado de lectura, false si está detenido.
     */
    public boolean isReading() {
        return mReading;
    }


    /**
     * Conmuta el estado del proceso de lectura (lo inicia si está detenido, o lo detiene si está
     * iniciado).
     */
    public synchronized void toggleReadStatus() {
        if (!isInitialized()) return;

        if (mReading)
            stopReadLoop();
        else
            startReadLoop();
    }


    /**
     * Inicia el proceso de lectura continua.
     */
    public synchronized void startReadLoop() {
        try {
            if (mReading || !isInitialized()) return;

            if (mReader.startInventoryTag(0, 0)) {
                mReading = true;
                mStopReadDate = null;
                new TagReaderTask().execute();
                if (mOnStatusChangedListener != null) mOnStatusChangedListener.onReadStarted();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }


    /**
     * Detiene el proceso de lectura continua.
     */
    public synchronized void stopReadLoop() {
        try {
            if (!mReading) return;

            mReading = false;
            mReader.stopInventory();
            if (mOnStatusChangedListener != null) mOnStatusChangedListener.onReadStopped();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }


    /**
     * Inicia el proceso de lectura de un único tag UHF. Este proceso es asíncrono, y se detiene
     * inmediatamente cuando se lee un tag o si se vence el timeout. La lectura del tag y la
     * detención del proceso de lectura se detecta mediante los callbacks habituales (establecidos
     * mediante {@link #setOnTagReadListener(OnTagReadListener)} y
     * {@link #setOnStatusChangedListener(OnStatusChangeListener)}).
     *
     * @param millis Timeout de lectura en milisegundos.
     */
    public void readSingleTag(int millis) {
        try {
            startReadLoop();
            Calendar timeout = Calendar.getInstance();
            timeout.add(Calendar.MILLISECOND, millis);
            mStopReadDate = timeout.getTime();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }


    /**
     * Establece un listener para detectar el cambio de estado de lectura del lector UHF.
     *
     * @param onStatusChangedListener Listener OnStatusChangeListener.
     */
    public void setOnStatusChangedListener(OnStatusChangeListener onStatusChangedListener) {
        mOnStatusChangedListener = onStatusChangedListener;
    }


    /**
     * Establece un listener para detectar las lecturas realizadas por el lector UHF.
     *
     * @param onTagReadListener Listener OnTagReadListener.
     */
    public void setOnTagReadListener(OnTagReadListener onTagReadListener) {
        mOnTagReadListener = onTagReadListener;
    }


    /**
     * Tarea asíncrona para la lectura de tags UHF.
     * Realiza lecturas continuas y las notifica a través del listener del objeto UHFManager.
     * Si se ha establecido un tiempo de lectura, la tarea acabará cuando este tiempo haya pasado
     * o cuando se lea un solo tag UHF.
     */
    private static class TagReaderTask extends AsyncTask<Void, String, Void> {

        @Override
        protected Void doInBackground(Void... voids) {
            try {
                UHFManager uhfManager = UHFManager.get();
                if (uhfManager == null) return null;

                String tid;
                String epc;
                String[] res;
                while (uhfManager.mReading) {

                    // Si la fecha de detención ha expirado, se detiene la tarea
                    if (uhfManager.mStopReadDate != null &&
                            uhfManager.mStopReadDate.before(new Date()))
                        break;

                    res = uhfManager.getReader().readTagFromBuffer();
                    if (res != null) {
                        tid = res[0];
                        epc = res[1];
                        if (tid.length() != 0 && !tid.equals("0000000000000000") &&
                                !tid.equals("000000000000000000000000")) {

                            MyLoggerHandler.getInstance().info("TID:" + tid);
                            publishProgress(tid, epc);

                            // Si existe fecha de detención, solo se espera leer un tag.
                            // Como ya se ha hecho, se detiene la tarea.
                            if (uhfManager.mStopReadDate != null) break;
                        }
                    }
                }
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
            }
            return null;
        }

        @Override
        protected void onProgressUpdate(String... values) {
            super.onProgressUpdate(values);
            UHFManager uhfManager = UHFManager.get();
            if (uhfManager != null && uhfManager.mOnTagReadListener != null) {
                Log.i(TAG, "Tag leído: " + values[0].toString() + " - " + values[1].toString());
                uhfManager.mOnTagReadListener.onTagRead(values[0], values[1], new Date());
            }
        }

        @Override
        protected void onPostExecute(Void aVoid) {
            super.onPostExecute(aVoid);
            UHFManager.get().stopReadLoop();
        }
    }


    /**
     * Listener para detectar el cambio de estado de lectura del lector UHF.
     */
    public interface OnTagReadListener {
        /**
         * Se llama cuando un tag UHF ha sido leído.
         *
         * @param tid      TID del tag.
         * @param epc      EPC del tag.
         * @param readDate Fecha de lectura del tag.
         */
        void onTagRead(String tid, String epc, Date readDate);
    }


    /**
     * Listener para detectar las lecturas realizadas por el lector UHF.
     */
    public interface OnStatusChangeListener {
        /**
         * Se llama cuando se inicia el proceso de lectura.
         */
        void onReadStarted();

        /**
         * Se llama cuando se detiene el proceso de lectura.
         */
        void onReadStopped();
    }
}
