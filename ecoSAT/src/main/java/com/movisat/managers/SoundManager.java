package com.movisat.managers;

import android.content.Context;
import android.media.SoundPool;

import com.movisat.ecosat.R;

public class SoundManager {

   private static SoundManager instance;
   private SoundPool soundpool;
   private final int soundID;

   private SoundManager(Context context) {
      soundpool = new SoundPool.Builder().setMaxStreams(1).build();
      soundID = soundpool.load(context, R.raw.rfid_beep, 1);
   }

   public static SoundManager getInstance(Context context) {
      if (instance == null) {
         instance = new SoundManager(context.getApplicationContext());
      }
      return instance;
   }

   public void play() {
      soundpool.play(soundID, 1, 1, 0, 0, 1);
   }

   public void release() {
      if (soundpool != null) {
         soundpool.release();
         soundpool = null;
      }
   }
}
