package com.movisat.managers;

import android.os.CountDownTimer;
import android.util.Log;

import com.movisat.database.DBLecturas;
import com.movisat.database.Lectura;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.log.Logg;
import com.movisat.utils.LFByteUtils;
import com.movisat.utils.LFPowerUtils;
import com.movisat.utils.Utilss;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.function.BiConsumer;

import android_serialport_api.SerialPort;
import android_serialport_api.SerialPortTool;

/**
 * This class manages the U9000LF134 RFID reader.
 * It provides methods to read tags and handle the data.
 */
public class U9000LF134Manager {
    private static final String TAG = "U9000LF134Manager";
    private static final String PATH = "/dev/ttyS3";
    private static final int BAUDRATE = 9600;
    private static U9000LF134Manager mInstance = null;
    private static int reading_time = 3000;
    private final CountDownTimer readTagTimer;
    private volatile boolean isReading = false;
    private SerialPortTool serialPortTool;
    private InputStream mInputStream;
    private OutputStream mOutputStream;
    private byte[] buffer;
    private int size;
    private BiConsumer<byte[], Integer> onReaded134TagData;

    /**
     * Constructor for the U9000LF134Manager class.
     * Initializes the readTagTimer.
     */
    private U9000LF134Manager() {
        readTagTimer = new CountDownTimer(reading_time, reading_time) {
            @Override
            public void onTick(long millisUntilFinished) {
                isReading = true;
                startReading();
            }

            @Override
            public void onFinish() {
                isReading = false;
                finishReading();
                cancel();
            }
        };
    }

    /**
     * Returns the singleton instance of the U9000LF134Manager class.
     *
     * @return The U9000LF134Manager instance.
     */
    public static synchronized U9000LF134Manager get() {
        if (mInstance == null)
            mInstance = new U9000LF134Manager();
        return mInstance;
    }

    /**
     * Starts reading a tag.
     *
     * @param onReaded134TagData The callback function to handle the tag data.
     * @param time               The time to read the tag in milliseconds.
     */
    public void readTag(BiConsumer<byte[], Integer> onReaded134TagData, int time) {
        if (isReading) {
            Logg.info(TAG, "[readTag] isReading");
            return;
        }
        reading_time = time;
        readTagTimer.start();
        this.onReaded134TagData = onReaded134TagData;
    }

    /**
     * IsReading getter.
     */
    public boolean isReading() {
        return isReading;
    }

    /**
     * Initializes the RFID reader and starts reading.
     */
    private void startReading() {
        try {
            LFPowerUtils.power("1");
            serialPortTool = new SerialPortTool();
            SerialPort mSerialPort = serialPortTool.getSerialPort(PATH, BAUDRATE);
            mInputStream = mSerialPort.getInputStream();
            mOutputStream = mSerialPort.getOutputStream();
            new ReadThread().start();
        } catch (Exception e) {
            Log.e(TAG, "[startReading] Error: " + e);
            Logg.info(TAG, "[startReading] Error: " + e);
            MyLoggerHandler.getInstance().error(e);
        }
    }

    /**
     * Stops reading and closes the RFID reader.
     */
    private void finishReading() {
        Logg.info(TAG, "[finishReading] Finish...");
        try {
            onReaded134TagData.accept(buffer, size);
            saveData();
            Logg.info(TAG, "[finishReading] Closing streams");
            mOutputStream.close();
            mInputStream.close();
            serialPortTool.closeSerialPort();
        } catch (Exception e) {
            Log.e(TAG, "[finishReading] Error: " + e);
            Logg.info(TAG, "[finishReading] Error: " + e);
            MyLoggerHandler.getInstance().error(e);
        } finally {
            LFPowerUtils.power("0");
        }
    }

    private void saveData() {
        if (size > 0) {
            byte[] id = new byte[size];
            System.arraycopy(buffer, 0, id, 0, size);
            if (LFByteUtils.hasValue(id)) {
                String tagRead;
                if (MainActivity.getInstance().getEmpresa() == 661) { // Lipasam
                    tagRead = "00400000" + LFByteUtils.showLIPASAMResultASCII(id);
                    // if (!activityName.equals("AddElemActivity"))
                    // tagRead = LFByteUtils.swapString(tagRead);
                } else {
                    tagRead = LFByteUtils.showResultASCII(id);
                }
                String raw = LFByteUtils.showALLResultASCII(id);

                Lectura lectura = new Lectura();
                lectura.setRaw(raw);
                lectura.setTag(tagRead);
                lectura.setFecha(Utilss.getFormattedNow("yyyy-MM-dd HH:mm:ss"));
                lectura.setTipo("134");
                Logg.info(TAG, "[saveData] " + lectura);

                DBLecturas dbLecturas = new DBLecturas();
                dbLecturas.insert(lectura);
                dbLecturas.close();
            }
        }
    }

    /**
     * Checks if the tag data is valid.
     * https://forum.arduino.cc/t/fdx-b-134-2-khz-rfid-reader/912859?page=4
     *
     * @return True if the tag data is valid, false otherwise.
     */
    public boolean isValidData() {
        if (size != 30)
            return false;
        if (buffer[0] != 0x02)
            return false;

        boolean calculatedChecksum;
        boolean invertedChecksum;

        byte XOR = buffer[1];
        for (int x = 2; x <= 26; x++) {
            XOR ^= buffer[x];
        }
        calculatedChecksum = XOR == buffer[27];

        byte inverted = (byte) (~XOR);
        invertedChecksum = inverted == buffer[28];
        Logg.info(TAG, "[isValidData] checksum: " + (calculatedChecksum && invertedChecksum ? "Valid" : "Invalid"));

        return calculatedChecksum && invertedChecksum;
    }

    /**
     * Thread that reads the tag data.
     */
    private class ReadThread extends Thread {
        @Override
        public void run() {
            super.run();
            try {
                Logg.info(TAG, "[ReadThread] Start reading");
                int readingNumber = 1;
                size = 0;
                do {
                    Logg.info(TAG, "[ReadThread] Reading " + readingNumber);
                    buffer = new byte[30];
                    readingNumber++;
                    if (mInputStream.available() > 0) {
                        size = mInputStream.read(buffer);
                        Logg.info(TAG, "[ReadThread] Reading done: " + Arrays.toString(Arrays.copyOfRange(buffer, 0, size)));
                    }
                    Thread.sleep(200);
                } while (!isValidData() && isReading);
            } catch (Exception e) {
                Logg.info(TAG, "[ReadThread] Stop reading. Exception type: " + e.getClass().getSimpleName());
            }
            if (size <= 0) {
                Logg.info(TAG, "[ReadThread] No data readed");
            }
            readTagTimer.onFinish();
        }
    }
}
