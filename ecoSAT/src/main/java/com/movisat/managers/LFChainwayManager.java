package com.movisat.managers;

import android.os.AsyncTask;
import android.util.Log;

import com.rscja.deviceapi.RFIDWithLF;

import java.util.Calendar;
import java.util.Date;


/**
 * 
 * Esta clase constituye un wrapper para el manejo del lector RFID LF (125-134 MHz) de los
 * dispositivos de mano CHAINWAY C4050.
 * <br>
 * Presenta el patr<PERSON>, obteniendo la instancia del manager mediante
 * {@link LFChainwayManager#get() LFManager.get()}. Una vez obtenido, es necesario inicializar el
 * lector RFID mediante {@link #init()}. Para liberarlo después de su uso se debe llamar a
 * {@link #release()}.
 * <br>
 * Existen dos modos de operación para la lectura de tags:
 * <ul>
 *     <li>
 *          <b>Lectura continua</b>: Se inicia con {@link #startReadLoop()} y se finaliza con
 *          {@link #stopReadLoop()}. Mientras dure la lectura, todos los tags detectados son
 *          notificados.
 *     </li>
 *     <li>
 *          <b>Lectura única</b>: Se inicia con {@link #readSingleTag(int)}
 *     </li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since   2019-04-24
 */
public class LFChainwayManager {

    public static final int[] SCAN_KEYCODES = {136, 137, 139};

    private static final String TAG = "RFID";
    private volatile boolean mReading = false;

    private static LFChainwayManager mInstance = null;

    private RFIDWithLF mReader;
    private Date mStopReadDate;

    private OnStatusChangeListener mOnStatusChangedListener;
    private OnTagReadListener mOnTagReadListener;


    /**
     * Devuelve la referencia al manejador del lector RFID.
     * @return Referencia al manejador del lector RFID.
     */
    public static LFChainwayManager get() {
        if (mInstance == null)
            mInstance = new LFChainwayManager();
        return mInstance;
    }


    /**
     * Devuelve el objeto RFIDWithRFID para realizar operaciones a más bajo nivel.
     * @return Objeto RFIDWithRFID.
     */
    public RFIDWithLF getReader() {
        try {
            if (mReader == null)
                mReader = RFIDWithLF.getInstance();
        } catch (Throwable e) {
            Log.e(TAG, e.getMessage());
        }
        return mReader;
    }


    /**
     * Inicializa el lector RFID.
     * @return true si la inicialización ha sido correcta, false en otro caso.
     */
    public boolean init() {
        try {
            getReader();
            mReader.init();

            //if (mReader == null || mReader.getHardwareVersion() == null)
            if (mReader == null)
                throw new Exception("El dispositivo no tiene un lector RFID válido.");

        } catch (Throwable e) {
            return false;
        }

        return isInitialized();
    }


    /**
     * Des-inicializa y libera el lector RFID.
     */
    public void release() {
        try {
            if (mReader != null)
                mReader.free();
        } catch (Throwable e) {
            Log.e(TAG, e.getMessage());
        }
    }


    /**
     * Indica si el controlador del lector RFID está inicializado.
     * @return true si el controlador está inicializado, false en otro caso.
     */
    public boolean isInitialized() {
        return mReader != null && mReader.isPowerOn();
    }


    /**
     * Indica si el lector RFID está en estado de lectura.
     * @return true si el lector RFID está en estado de lectura, false si está detenido.
     */
    public boolean isReading() {
        return mReading;
    }


    /**
     * Conmuta el estado del proceso de lectura (lo inicia si está detenido, o lo detiene si está
     * iniciado).
     */
    public synchronized void toggleReadStatus() {
        if (!isInitialized()) return;

        if (mReading)
            stopReadLoop();
        else
            startReadLoop();
    }


    /**
     * Inicia el proceso de lectura continua.
     */
    public synchronized void startReadLoop() {
        try {
            if (mReading || !isInitialized()) return;

            mReading = true;
            mStopReadDate = null;
            new TagReaderTask().execute();
            if (mOnStatusChangedListener != null) mOnStatusChangedListener.onReadStarted();

        } catch (Throwable e) {
            Log.e(TAG, e.getMessage());
        }
    }


    /**
     * Detiene el proceso de lectura continua.
     */
    public synchronized void stopReadLoop() {
        try {
            if (!mReading) return;

            mReading = false;
            if (mOnStatusChangedListener != null) mOnStatusChangedListener.onReadStopped();

        } catch (Throwable e) {
            Log.e(TAG, e.getMessage());
        }
    }


    /**
     * Inicia el proceso de lectura de un único tag RFID. Este proceso es asíncrono, y se detiene
     * inmediatamente cuando se lee un tag o si se vence el timeout. La lectura del tag y la
     * detención del proceso de lectura se detecta mediante los callbacks habituales (establecidos
     * mediante {@link #setOnTagReadListener(OnTagReadListener)} y
     * {@link #setOnStatusChangedListener(OnStatusChangeListener)}).
     * @param millis Timeout de lectura en milisegundos.
     */
    public void readSingleTag(int millis) {
        try {
            startReadLoop();
            Calendar timeout = Calendar.getInstance();
            timeout.add(Calendar.MILLISECOND, millis);
            mStopReadDate = timeout.getTime();
        } catch (Throwable e) {
            Log.e(TAG, e.getMessage());
        }
    }


    /**
     * Establece un listener para detectar el cambio de estado de lectura del lector RFID.
     * @param onStatusChangedListener Listener OnStatusChangeListener.
     */
    public void setOnStatusChangedListener(OnStatusChangeListener onStatusChangedListener) {
        mOnStatusChangedListener = onStatusChangedListener;
    }


    /**
     * Establece un listener para detectar las lecturas realizadas por el lector RFID.
     * @param onTagReadListener Listener OnTagReadListener.
     */
    public void setOnTagReadListener(OnTagReadListener onTagReadListener) {
        mOnTagReadListener = onTagReadListener;
    }


    /**
     * Tarea asíncrona para la lectura de tags RFID.
     * Realiza lecturas continuas y las notifica a través del listener del objeto LFManager.
     * Si se ha establecido un tiempo de lectura, la tarea acabará cuando este tiempo haya pasado
     * o cuando se lea un solo tag RFID.
     */
    private static class TagReaderTask extends AsyncTask<Void, String, Void> {

        @Override
        protected Void doInBackground(Void... voids) {
            try {
                LFChainwayManager lfChainwayManager = LFChainwayManager.get();
                if (lfChainwayManager == null) return null;

                String tag;
                while (lfChainwayManager.mReading) {

                    // Si la fecha de detención ha expirado, se detiene la tarea
                    if (lfChainwayManager.mStopReadDate != null &&
                            lfChainwayManager.mStopReadDate.before(new Date()))
                        break;

                    tag = lfChainwayManager.getReader().readDataWithIDCard(0);
                    if (tag != null) {
                        if (tag.length() > 0) {

                            Log.i(TAG, "RFID LF:" + tag);
                            publishProgress(tag);

                            // Si existe fecha de detención, solo se espera leer un tag.
                            // Como ya se ha hecho, se detiene la tarea.
                            if (lfChainwayManager.mStopReadDate != null) break;
                        }
                    }
                }
            } catch (Throwable e) {
                Log.e(TAG, e.getMessage());
            }
            return null;
        }

        @Override
        protected void onProgressUpdate(String... values) {
            super.onProgressUpdate(values);
            LFChainwayManager lfChainwayManager = LFChainwayManager.get();
            if (lfChainwayManager != null && lfChainwayManager.mOnTagReadListener != null)
                lfChainwayManager.mOnTagReadListener.onTagRead(values[0], new Date());
        }

        @Override
        protected void onPostExecute(Void aVoid) {
            super.onPostExecute(aVoid);
            LFChainwayManager.get().stopReadLoop();
        }
    }


    /**
     * Listener para detectar el cambio de estado de lectura del lector RFID.
     */
    public interface OnTagReadListener {
        /**
         * Se llama cuando un tag RFID ha sido leído.
         * @param tag       ID del tag.
         * @param readDate  Fecha de lectura del tag.
         */
        void onTagRead(String tag, Date readDate);
    }


    /**
     * Listener para detectar las lecturas realizadas por el lector RFID.
     */
    public interface OnStatusChangeListener {
        /** Se llama cuando se inicia el proceso de lectura. */
        void onReadStarted();

        /** Se llama cuando se detiene el proceso de lectura. */
        void onReadStopped();
    }
}
