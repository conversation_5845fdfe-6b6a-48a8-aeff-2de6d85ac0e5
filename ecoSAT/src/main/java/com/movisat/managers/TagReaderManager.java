package com.movisat.managers;

import android.os.Handler;

import com.movisat.ecosat.MainActivity;
import com.movisat.log.Logg;
import com.movisat.rfid_uhf_u9000.TagNotFoundToast;
import com.movisat.rfid_uhf_u9000.U9000UHFManager;
import com.movisat.utilities.ReadingTagToast;

import java.util.function.BiConsumer;

public class TagReaderManager {
    static final String _TAG = "TagReaderManager";

    public static void read134orUHF(BiConsumer<byte[], Integer> onReaded134TagData) {
        if (!MainActivity.getInstance().hasSincro()) return;
        if (U9000UHFManager.get().isInit()) {
            Logg.info(_TAG, "[read134orUHF] Leyendo UHF");
            ReadingTagToast.get().showToast(200);
            // Esperamos 200ms para que el toast se muestre
            new Handler().postDelayed(() -> {
                U9000UHFManager.get().readTag();
            }, 200);
        } else {
            if (U9000LF134Manager.get().isReading()) {
                Logg.info(_TAG, "[read134orUHF] Ya está leyendo 134");
                return;
            }
            Logg.info(_TAG, "[read134orUHF] Leyendo 134");
            MainActivity.getInstance().cancelToast();
            TagNotFoundToast.get().cancel();
            ReadingTagToast.get().showToast(3000);
            U9000LF134Manager.get().readTag(onReaded134TagData, 3000);
        }
    }
}
