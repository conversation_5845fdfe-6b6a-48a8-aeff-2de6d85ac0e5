package com.movisat.managers;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.widget.Toast;

import com.environment.Environment;
import com.movisat.bll.SensoresManager;
import com.movisat.database.DBElemento;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBIncidenciaEstado;
import com.movisat.database.DBIncidenciaModelo;
import com.movisat.database.DBIncidenciaMotivo;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaEstado;
import com.movisat.database.IncidenciaModelo;
import com.movisat.database.IncidenciaMotivo;
import com.movisat.database.Tags;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyBroadCastManager;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;
import com.movisat.fragment.GestionElementos;
import com.movisat.log.Logg;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.tags.ITag;
import com.movisat.tags.Tag134;
import com.movisat.tags.TagUHF;
import com.movisat.use_case.TagSendSensor;
import com.movisat.utilities.Config;
import com.movisat.utilities.Utils;
import com.movisat.utils.Utilss;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.Set;
import java.util.UUID;

/**
 * Lee TAGs UHF
 */
public class DiscoveryManager {

    private static DiscoveryManager instance;
    private BluetoothDevice device;
    private BluetoothAdapter adapter;
    private BluetoothSocket socket;
    private InputStream in;
    private OutputStream out;
    private boolean connected;
    private String mac;

    public static final int INCIDENCIA_ABIERTA = 5;

    private int motivo;

    public static DiscoveryManager getInstance() {
        if (instance == null)
            instance = new DiscoveryManager();
        return instance;
    }

    private DiscoveryManager() {
        adapter = BluetoothAdapter.getDefaultAdapter();
    }

    public void onStart() {

        try {

            MyLoggerHandler.getInstance().info("Conectando con dispositivo -> " + device.getName());

            socket = device.createRfcommSocketToServiceRecord(
                    UUID.fromString("00001101-0000-1000-8000-00805F9B34FB"));
            socket.connect();
            in = socket.getInputStream();
            out = socket.getOutputStream();

            MyLoggerHandler.getInstance().info("Dispositivo -> " + device.getName() + " conectado");

            connected = true;

        } catch (Throwable e) {

            onStop();
        }
    }

    public void onStop() {

        try {

            MyLoggerHandler.getInstance().info("Dispositivo -> " + device.getName() + " desconectado");

            if (in != null)
                in.close();
            in = null;

            if (out != null)
                out.close();
            out = null;

            if (socket != null)
                socket.close();
            socket = null;

            connected = false;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public boolean checkBond() {

        boolean res = false;

        try {
            Set<BluetoothDevice> pairedDevices = adapter.getBondedDevices();
            if (pairedDevices != null && pairedDevices.size() > 0) {

                for (BluetoothDevice pairDev : pairedDevices) {

                    if (pairDev.getBondState() == BluetoothDevice.BOND_BONDED) {

                        if (pairDev.getName().startsWith("Discovery")) {
                            device = pairDev;
                            res = true;
                        }
                    }
                }
            }
        } catch (Exception e) {
            Logg.error("DiscoveryManager", "Error al inciar el bluetooth: " + e.getMessage());
        }

        return res;
    }

    public void run() {

        if (!connected) {

            connected = true;

            new Thread() {

                @Override
                public void run() {

                    try {

                        onStart();

                        if (connected) {

                            byte[] bBuf = new byte[1];
                            String sBuf = "";
                            long timeout = 0;

                            // Activo el almacenamiento de los TAG en memoria y el envío en tipo real
                            out.write("$200C00010101\r\n".getBytes());
                            Thread.sleep(250);


                            // Establezco la potencia de lectura al máximo
                            out.write("$1404010A\r\n".getBytes());
                            Thread.sleep(1000);

                            // $00;e200001733010092260012e4;2014-01-01,00:04:54;00.000000N,00.000000E<CR><LF>
                            // $00;e2000017330100902590107d;2018-04-09,11:55:23;38.002976N,01.144863W<CR><LF>

                            //out.write("$100300\r\n".getBytes());
                            //Thread.sleep(250);

                            while (connected) {

                                if (in.available() > 0 && in.read(bBuf) == 1) {

                                    timeout = 0;

                                    switch (bBuf[0]) {
                                        case 0x0D:
                                            break;

                                        case 0x0A:
                                            if (sBuf.startsWith("$00;") || sBuf.startsWith("$01;") || sBuf.startsWith("$02;")) {

                                                try {

                                                    MyLoggerHandler.getInstance().info("Trama recibida -> " + sBuf);

                                                    String[] auxString = sBuf.split(";");
                                                    int button = Integer.parseInt(auxString[0].substring(1));
                                                    String strFecha = auxString[2].replace(",", " ");
                                                    String tag = auxString[1].substring(auxString[1].length()
                                                            - 8, auxString[1].length()).toUpperCase();

                                                    DBTags dbTags = new DBTags();
                                                    final ITag iTag = Environment.hasReaderUHFU9000 ? new TagUHF("", tag, Utilss.now()) : new Tag134("", tag, Utilss.now());
                                                    Tags tagR = dbTags.getByTag(iTag, MainActivity.getInstance().getEmpresa());
                                                    dbTags.close();

                                                    MainActivity.getInstance().runOnUiThread(new Thread(new Runnable() {
                                                        @Override
                                                        public void run() {
                                                            TagSendSensor.execute(iTag, tagR);
                                                        }
                                                    }));

                                                    if (tagR != null) {
                                                        final ITag iTag2 = Environment.hasReaderUHFU9000 ? new TagUHF(tagR.getMatricula(), tag, Utilss.now()) : new Tag134(tagR.getMatricula(), tag, Utilss.now());

                                                        DBElemento dbElemento = new DBElemento();
                                                        Elemento elemento = dbElemento.getElementoByTag(tagR);
                                                        dbElemento.close();


                                                        if (elemento == null)
                                                            showToast(R.string.elem_no_existe);
                                                        else {


                                                            switch (button) {
                                                                case 0:
                                                                    //recogida
                                                                    if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_LAVAR_ELEMENTO
                                                                            || MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_LAVAR_ELEMENTO_MAPA) {
                                                                        SensoresManager.getInstance().sendSensorLavado(elemento);
                                                                        dbElemento = new DBElemento();
                                                                        elemento.setFechaUltLavado(Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));
                                                                        dbElemento.update(elemento);
                                                                        dbElemento.close();
                                                                        MainActivity.getInstance().runOnUiThread(new Thread(new Runnable() {
                                                                            @Override
                                                                            public void run() {
                                                                                MainActivity.getInstance().showMessage(R.string.lavado_tag, Toast.LENGTH_LONG);
                                                                            }
                                                                        }));
                                                                        GestionElementos.getInstance().drawMap();
                                                                    } else if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_RECOGIDA_ELEMENTOS) {
                                                                        SensoresManager.getInstance().sendSensorProcesado(elemento);
                                                                        dbElemento = new DBElemento();
                                                                        elemento.setFechaUltRecogida(Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));
                                                                        dbElemento.update(elemento);
                                                                        dbElemento.close();
                                                                        MainActivity.getInstance().runOnUiThread(new Thread(new Runnable() {
                                                                            @Override
                                                                            public void run() {
                                                                                MainActivity.getInstance().showMessage(R.string.recogido_tag, Toast.LENGTH_LONG);
                                                                            }
                                                                        }));
                                                                        GestionElementos.getInstance().drawMap();
                                                                    }

                                                                    break;
                                                                case 1:
                                                                    //incidencia 1
                                                                    motivo = Integer.parseInt(Config.getInstance().getValueEmpresa("Entrada6", "0"));
                                                                    break;
                                                                case 2:
                                                                    //incidencia 2
                                                                    motivo = Integer.parseInt(Config.getInstance().getValueEmpresa("Entrada7", "0"));
                                                                    break;
                                                            }

                                                            if (button > 0) {
                                                                int modelo;
                                                                int tipo;

                                                                DBIncidenciaMotivo dbIncidenciaMotivo = new DBIncidenciaMotivo();
                                                                IncidenciaMotivo motivo_Obj = dbIncidenciaMotivo.getByID(motivo, MainActivity.getInstance().getEmpresa());
                                                                dbIncidenciaMotivo.close();

                                                                if (motivo_Obj != null) {

                                                                    DBIncidenciaModelo dbIncidenciaModelo = new DBIncidenciaModelo();
                                                                    IncidenciaModelo modelo_Obj = dbIncidenciaModelo.getByID(motivo_Obj.getModelo(),
                                                                            MainActivity.getInstance().getEmpresa());
                                                                    dbIncidenciaModelo.close();

                                                                    if (modelo_Obj != null) {

                                                                        Incidencia inci = new Incidencia(
                                                                              0,
                                                                              0,
                                                                              MainActivity.getInstance().getEmpresa(),
                                                                              modelo_Obj.getTipo(),
                                                                              modelo_Obj.getIdExterno(),
                                                                              motivo, "", 1,
                                                                              System.currentTimeMillis() / 1000,
                                                                              elemento.getIdExterno(),
                                                                              elemento.getPosition().latitude,
                                                                              elemento.getPosition().longitude,
                                                                              MainActivity.getInstance().getUsuario(),
                                                                              0,
                                                                              0);

                                                                        DBIncidencia dbIncidencia = new DBIncidencia();
                                                                        inci.setId((int) dbIncidencia.insert(inci));
                                                                        dbIncidencia.close();

                                                                        String strDate = Utils.secondsToDatetimeString(inci.getFechaUltimoEstado(),
                                                                                "yyyy-MM-dd HH:mm:ss");

                                                                        // Se inserta el estado de incidencia como abierta. En el servidor,
                                                                        // al mandar el paquete de creación de incidencia, se marca
                                                                        // automáticamente como abierta, así que hay que hacerlo aquí también.
                                                                        IncidenciaEstado estado = new IncidenciaEstado(0, 0,
                                                                                inci.getEmpresa(), inci.getId(), inci.getIdExterno(),
                                                                                1, strDate, 0, ""
                                                                        );

                                                                        DBIncidenciaEstado dbIncidenciaEstado = new DBIncidenciaEstado();
                                                                        estado.setId((int) dbIncidenciaEstado.insert(estado));
                                                                        dbIncidenciaEstado.close();

                                                                        showToast(R.string.incidencia_introducida);

                                                                        // Actualizamos la incidencia en el mapa
                                                                        MyBroadCastManager.getInstance().sendBroadCastRefreshIncidencia(inci);

                                                                        // Guardo la información para enviar en la
                                                                        // bandeja de salida
                                                                        DBPacket dbp = new DBPacket();
                                                                        Packet paquete = new Packet(
                                                                                Packet.INCIDENCIA_CREAR,
                                                                                Packet.PRIORIDAD_NORMAL, inci);

                                                                        dbp.insert(paquete);
                                                                        dbp.close();
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                } catch (NumberFormatException e) {
                                                    MyLoggerHandler.getInstance().error(e);
                                                }
                                            }

                                            sBuf = "";
                                            break;

                                        default:
                                            sBuf += new String(bBuf);
                                    }
                                } else {

                                    // Después de 5 segundos sin recibir nada pido la MAC
                                    // del dispositivo para detectar pérdidas de conexión
                                    if (timeout > 0 && Utils.getTime() - timeout > 5)
                                        out.write("$100300\r\n".getBytes());

                                    if (timeout == 0)
                                        timeout = Utils.getTime();

                                    Thread.sleep(1000);
                                }
                            }
                        }

                    } catch (Throwable e) {
                        e.printStackTrace();
                    }

                    onStop();
                }
            }.start();
        } else {
            onStop();
        }
    }

    public void showToast(int text) {
        MainActivity.getInstance().runOnUiThread(new Thread(new Runnable() {
            @Override
            public void run() {
                MainActivity.getInstance().showMessage(text, Toast.LENGTH_LONG);
            }
        }));
    }

    public String getMAC() {
        if (device != null)
            return device.getAddress();
        else
            return "";
    }
}
