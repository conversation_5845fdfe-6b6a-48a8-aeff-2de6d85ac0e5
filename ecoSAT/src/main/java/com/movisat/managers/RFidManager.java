package com.movisat.managers;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.Set;
import java.util.UUID;

import com.movisat.application.EcoSATApplication;
import com.movisat.ecosat.MainActivity;
import com.movisat.tags.Tag134;
import com.movisat.utilities.Utils;

import android.bluetooth.*;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import com.movisat.events.OnReadedTag;

import org.greenrobot.eventbus.EventBus;

/**
 * Lee TAGs UHF
 */
public class RFidManager {
    private static RFidManager instance = null;
    private BluetoothAdapter adapter = null;
    private BluetoothSocket socket = null;
    private InputStream in = null;
    private OutputStream out = null;
    private BroadcastReceiver receiver = null;
    private volatile boolean connected = false, stop = false;
    private static String modelo = "";

    public static synchronized RFidManager start(String modelo) {
        if (instance == null)
            instance = new RFidManager(modelo);
        return instance;
    }

    private RFidManager(String modelo) {
        try {
            RFidManager.modelo = modelo;

            if ((adapter = BluetoothAdapter.getDefaultAdapter()) != null) {
                receiver = new BroadcastReceiver() {
                    public void onReceive(Context context, Intent intent) {
                        if (intent != null) {
                            String action = intent.getAction();
                            if (action != null && action.equals(BluetoothDevice.ACTION_FOUND)) {
                                BluetoothDevice dev = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                                if (dev != null) {
                                    String devName = dev.getName();
                                    if (devName != null && devName.startsWith(RFidManager.modelo)) {
                                        openDevice(dev);
                                    }
                                }
                            }
                        }
                    }
                };
                EcoSATApplication.getInstance().
                        registerReceiver(receiver, new IntentFilter(BluetoothDevice.ACTION_FOUND));
            }

            if (adapter == null || !adapter.isEnabled()) {
                Intent intent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                MainActivity.getInstance().startActivity(intent);
            }

            initialize();

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public static synchronized void stop() {
        try {
            if (instance != null) {
                instance.stop = true;

                instance.closeDevice();

                if (instance.adapter != null && instance.adapter.isDiscovering())
                    instance.adapter.cancelDiscovery();
                instance.adapter = null;

                if (instance.receiver != null)
                    EcoSATApplication.getInstance().unregisterReceiver(instance.receiver);
                instance.receiver = null;
            }

            instance = null;

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void initialize() {
        try {
            new Thread(new Runnable() {
                public void run() {
                    while (!stop) {
                        try {
                            if (adapter != null && adapter.isEnabled()) {
                                while (!stop && !connected) {
                                    if (!pairDevice()) {
                                        if (adapter.isDiscovering())
                                            adapter.cancelDiscovery();
                                        adapter.startDiscovery();
                                        for (int i = 0; i < 30 && !stop; i++)
                                            Thread.sleep(1000);
                                    }
                                }
                                if (!stop && connected)
                                    Thread.sleep(1000);
                            } else
                                Thread.sleep(1000);
                        } catch (Throwable e) {
                            e.printStackTrace();
                        }
                    }
                }
            }).start();

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Intenta conectar con un dispositivo emparejado
     *
     * @return true si hay un dispositivo emparejado, false si no lo hay
     */
    public boolean pairDevice() {

        try {

            Set<BluetoothDevice> pairedDevices = adapter.getBondedDevices();

            if (pairedDevices != null && pairedDevices.size() > 0) {

                for (BluetoothDevice dev : pairedDevices) {

                    if (dev != null && dev.getName().startsWith(modelo)
                            && dev.getBondState() == BluetoothDevice.BOND_BONDED) {

                        openDevice(dev);

                        return true;
                    }
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return false;
    }

    public synchronized void openDevice(BluetoothDevice device) {

        try {

            if (adapter.isDiscovering())
                adapter.cancelDiscovery();
            closeDevice();

            socket = device.createRfcommSocketToServiceRecord(
                    UUID.fromString("00001101-0000-1000-8000-00805F9B34FB"));

            socket.connect();

            in = socket.getInputStream();
            out = socket.getOutputStream();

            connected = true;

            new Thread(new Runnable() {
                public void run() {

                    try {

                        byte[] bBuf = new byte[1];
                        String sBuf = "";

                        try {
                            // Sincronizo la fecha y hora del lector con la del dispositivo
                            //[XSCLOCK|yy|mm|dd|hh|nn|ss]
                            String sync = "[XSCLOCK|" +
                                    Utils.datetimeToString(new Date(System.currentTimeMillis()), "yy|MM|dd|HH|mm|ss") + "]";
                            out.write(sync.getBytes());

                        } catch (Throwable e) {
                            e.printStackTrace();
                        }

                        while (!stop && connected) {
                            if (in.read(bBuf) == 1) {
                                switch (bBuf[0]) {
                                    case 0x0D:
                                        break;

                                    case 0x0A:
                                        if (!sBuf.startsWith("[")) {
                                            try {

                                                String[] auxString = sBuf.split(",");
                                                String strFecha = auxString[1].replace(".", "-");
                                                strFecha = "20" + strFecha.substring(6, 8) + "-" +
                                                        strFecha.substring(3, 5) + "-" + strFecha.substring(0, 2);
                                                String strHora = auxString[2];
                                                String tag = auxString[6].substring(auxString[6].length()
                                                        - 6, auxString[6].length());

                                                Date fecha = Utils.StringToDateTime(strFecha + " " + strHora);

                                                Tag134 tag134 = new Tag134("", tag, fecha);
                                                EventBus.getDefault().post(new OnReadedTag(tag134));

                                            } catch (Throwable e) {
                                                e.printStackTrace();
                                            }
                                        }

                                        sBuf = "";
                                        break;

                                    default:
                                        sBuf += new String(bBuf);
                                }
                            } else
                                Thread.sleep(1000);
                        }
                    } catch (Throwable e) {
                        e.printStackTrace();
                    }

                    closeDevice();
                }
            }).start();

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public synchronized void closeDevice() {
        try {
            if (in != null)
                in.close();
            in = null;

            if (out != null)
                out.close();
            out = null;

            if (socket != null)
                socket.close();
            socket = null;

            connected = false;
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }
}
