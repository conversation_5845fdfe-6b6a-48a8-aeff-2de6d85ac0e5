package com.movisat.managers;

import android.os.Looper;
import android.os.Message;
import com.movisat.database.Posicion;
import com.movisat.ecosat.IntranetNew;
import com.movisat.ecosat.MainActivity;
import com.movisat.services.MyLocationService;
import com.movisat.utilities.Config;
import com.movisat.utilities.MD5;
import com.movisat.utilities.Phone;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;


/**
 * Created by aortiz on 14/10/2015.
 */
public class IntranetManager extends Thread {
    private static final int MESSAGE_PERMISOS = 1;
    private final int MESSAGE_NEED_UPDATE = 2;
    private static IntranetManager instance = null;
    private volatile boolean terminate = false;
    private volatile boolean forze = false;
    private volatile boolean updating = false;

    // Datos de conexión con la Intranet
    private final String INTRANET_URL = "seguro.movisat.com";
    private final int INTRANET_PUERTO = 80;


    public static synchronized IntranetManager getInstance() {
        try {
            if (instance == null) {
                instance = new IntranetManager();
                instance.start();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return instance;
    }

    public synchronized void finalize() {
        terminate = true;
        instance = null;
    }

    public void forzarConexion() {
        forze = true;
    }

    @Override
    public void run() {
        int TIMER_CONECTAR_INTRANET = 900;

        try {

            while (!terminate) {
//                while(!BaseActivity.permisosAplicacion && !terminate){
//                    sleep(100);
//                }
//                if(terminate)
//                    break;
                forze = false;

                // Siempre espero que conecte antes el hilo que recibe los datos del
                // servicio restApi para que actualize el tipo de software que se
                // ha configurado en la Intranet
                if (IntranetNew.haveConnected) {

                    connectToIntranet();                    
                    MainActivity.getInstance().startEnvironment();
                    TIMER_CONECTAR_INTRANET = 900;

                } else
                    TIMER_CONECTAR_INTRANET = 2;
                for (int i = 0; i < TIMER_CONECTAR_INTRANET && !terminate && !forze; i++)
                    Thread.sleep(1000);
            }

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private boolean connectToIntranet() {
        boolean res = false;
        int i, tipoSoftware;
        String auxBuf, webPage, glsType, updUrl, updUser, updPassw, updSoft, updFiles, updVer;
        byte[] bufBin = new byte[2048];
        Socket auxSock = null;
        InputStream auxIn = null;
        OutputStream auxOut = null;

        try {

            // Lo siguiente comprueba si ya existe un Looper para el subproceso actual, si no,
            // crea uno, evitando así el RuntimeException.
            if (Looper.myLooper() == null) {
                Looper.prepare();
            }

            String imei = Phone.getInstance().getIMEI();
            MyLocationService locationService = MyLocationService.getInstance();
            Posicion infoGps = null;
            if (locationService != null)
                infoGps = locationService.getLastPosicion();

            try {

                webPage = "/dracos/datos.php";
                glsType = "14";

                // Recupero el tipo de software, si no existe por defecto pongo el 204 que
                // corresponde con EcoSAT móvil total
                String tipoSoft = Config.getInstance().getValue("tipoSoft", "204");
                String version = Config.getInstance().getValue("versionSoftware", "16010101");

                // Por si se ha almacenado la "versión name" quito los puntos
                version = version.replaceAll("\\.", "");

                // Realizo la llamada a la Intranet
                auxSock = new Socket();
                auxSock.connect(new InetSocketAddress(INTRANET_URL, INTRANET_PUERTO), 15000);

                auxIn = auxSock.getInputStream();
                auxOut = auxSock.getOutputStream();

                auxOut.write(("POST " + webPage + " HTTP/1.1\r\n").getBytes());
                auxOut.write(("Content-Type: application/x-www-form-urlencoded; charset=utf-8\r\n")
                        .getBytes());
                auxOut.write(("Accept: application/octet-stream\r\n").getBytes());
                auxOut.write(("Cache-Control: no-cache\r\n").getBytes());
                auxOut.write(("Pragma: no-cache\r\n").getBytes());

                auxOut.write(("Pragma: no-cache\r\n").getBytes());

                auxOut.write(("User-Agent: Draco Android/" + version + imei + "\r\n").getBytes());
                auxOut.write(("Host: " + INTRANET_URL + "\r\n").getBytes());

                // Concateno todos los campos para calcular el MD5
                auxBuf = glsType
                        + "0"
                        + "00.00.00"
                        + "00"
                        + ""
                        + "0"
                        + "0"
                        + imei
                        + version
                        + "1"
                        + "0"
                        + tipoSoft
                        + (infoGps != null ? "" + (infoGps.getFecha() / 1000) : "0")
                        + version
                        + version
                        + version;

                MD5 md5 = new MD5();

                md5.update(auxBuf.getBytes());
                md5.update("intra06dracos".getBytes());

                auxBuf = "TipoGLS=" + glsType
                        + "&" + "SerCD=0"
                        + "&" + "SerVer=00.00.00"
                        + "&" + "SerRev=00"
                        + "&" + "GlsIP=" + ""
                        + "&" + "GlsPuerto=" + "0"
                        + "&" + "GlsNS=" + "0"
                        + "&" + "GlsIMEI=" + imei
                        + "&" + "GlsVer=" + version
                        + "&" + "VerProto=1"
                        + "&" + "SerNS=" + "0"
                        + "&" + "GlsSOFT=" + tipoSoft
                        + "&" + "GPSFecha=" + (infoGps != null ? "" + (infoGps.getFecha() / 1000) : "0")
                        + "&" + "nucleoVer=" + version
                        + "&" + "interfazVer=" + version
                        + "&" + "gestionVer=" + version
                        + "&" + "Firma=" + md5.toString();

                auxOut.write(("Connection: keep-alive\r\nContent-Length: "
                        + auxBuf.length() + "\r\n\r\n").getBytes());
                auxOut.write(auxBuf.getBytes());

                // Leo la respuesta del servidor Web
                while (!res) {

                    try {
                        auxSock.setSoTimeout(15000);
                        for (i = 0; auxIn.read(bufBin, i, 1) == 1 && i < bufBin.length; i++) {
                            if (bufBin[i] == 0x0A)
                                break;
                        }
                        if (bufBin[i] != 0x0A)
                            break;
                    } catch (Throwable e) {
                        e.printStackTrace();
                        break;
                    }

                    for (int j = 0; j < i; j++) {
                        if (bufBin[j] > 128 || (bufBin[j] < 32 && bufBin[j] != 13 && bufBin[j] != 10))
                            bufBin[j] = 0x3F; // ?
                    }

                    auxBuf = new String(bufBin, 0, i - 1);

                    if (auxBuf.startsWith("<BODY>EST=AC")) { // Necesita actualización
                        try {
                            // ejemplo actualización:  "<BODY>EST=AC,,TSOFT=1,VER=18010101</BODY>";
                            int indice=auxBuf.indexOf("VER=");
                            String version_upd="";
                            String version_actual="";

                            version_upd    = auxBuf.substring(indice+4, auxBuf.length()-7); // +4 DE 'VER=' Y -7 DE </BODY>
                            version_actual = Config.getInstance().getValue("versionSoftware","0");

                            // compruebo que la versión a telecargar, no esté ya en el equipo, en ese caso, no le muestro mensaje y sigue su uso.
                            if (Integer.parseInt(version_upd)>Integer.parseInt(version_actual)) {
                                Message msg = new Message();
                                msg.what = MESSAGE_NEED_UPDATE;
                                MainActivity.getInstance().handler.sendMessage(msg);
                            }

                        } catch (Throwable e) {
                            e.printStackTrace();
                        }

                    } else if (auxBuf.startsWith("<BODY>EST=")) {  // Respuesta positiva
                        res = true;

                        if (auxBuf.indexOf("CONF=") > 0) {
                            getConfigVar(auxBuf.substring(auxBuf.indexOf("CONF=") + 5));
                        }
                    }
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }

            try {

                if (auxIn != null)
                    auxIn.close();

                if (auxOut != null)
                    auxOut.close();

                if (auxSock != null)
                    auxSock.close();

            } catch (Throwable e) {
                e.printStackTrace();
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    //TODO: Mantis 4961. Intentar mejorar la obtención de las variables.
    private boolean getConfigVar(String buf) {
        String var, val;
        int i = 0, j;
        boolean reset = false;

        try {

            while ((j = buf.indexOf("?", i)) > -1) {

                var = buf.substring(i, j);

                for (i = j + 1, val = ""; i < buf.length()
                        && buf.charAt(i) != '&' && buf.charAt(i) != '<'; i++)
                    val += buf.charAt(i);

                if (i < buf.length())
                    i++;

                if (val.length() < 1)
                    val = "";

                // aquí almacena en base de datos lo que ha obtenido
                Config.getInstance().setValue(var, val);
            }

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return reset;
    }


}
