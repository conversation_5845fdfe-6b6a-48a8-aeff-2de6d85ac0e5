package com.movisat.managers;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;

import com.movisat.database.DBElemento;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.InfoSustituir;
import com.movisat.database.Tags;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyBroadCastManager;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;
import com.movisat.fragment.GestionElementos;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.synchronize.DBSynchro;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import java.util.Date;

public class DepositManager {

    public static boolean depositElement(Elemento element, Tags tag, double lat, double lng, boolean create, boolean fromMainActivity) {

        try {
            if (element == null) return false;

            boolean success = createOrDeposit(element, tag, create, lat, lng);

            if (!success) return false;

            if (fromMainActivity) {
                // Actualizo la BD y el cluster
                GestionElementos.getInstance().updateElemento(element);
                GestionElementos.getInstance().centerMap(element.getPosition());
            }

            //sincro
            if (MainActivity.getInstance().isNetworkAvailable()) {
                if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                    DBSynchro.getInstance().forceSync();
                }
            }

            // Se actualiza el mapa
            MyBroadCastManager.getInstance().sendBroadCastRefreshMap();


        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
            return false;
        }

        return true;
    }

    private static boolean createOrDeposit(Elemento element, Tags tag, boolean create, double lat, double lng) {
        DBElemento dbElemento = new DBElemento();

        // Se abre la bandeja de salida para mandar al servidor el elementos modificado/creado
        DBPacket dbPacket = new DBPacket();

        // Se establece el elemento como activo y se le asigna la ubicación objetivo
        element.setEstado(Elemento.ESTADO_ACTIVO);
        element.setPosition(lat, lng);

        if (create) {
            // Si hay que crear el elemento, se inserta en la base de datos local
            int id = (int) dbElemento.insert(element);
            if (id == 0) return false;
            element.setId(id);

            // Se establece el id interno del elemento creado en su tag
            tag.setIdInternoElemento(element.getId());
            DBTags dbTags = new DBTags();
            dbTags.update(tag);
            dbTags.close();

            dbPacket.insert(new Packet(Packet.ELEMENTO_CREAR, Packet.PRIORIDAD_NORMAL, element));
        } else {
            boolean update = dbElemento.update(element);
            if (!update) return false;

            dbPacket.insert(new Packet(Packet.ELEMENTO_DEPOSITAR, Packet.PRIORIDAD_NORMAL, new InfoSustituir(null, element, new Date())));
        }
        dbElemento.close();
        dbPacket.close();
        return true;
    }

    public static void depositAlertElemInGarage(Elemento element, Context context, Tags tag, double lat, double lng) {

        InfoDialog dialog = new InfoDialog(
                MainActivity.getInstance(),
                context.getString(R.string.atencion),
                context.getString(R.string.elem_taller_depositar_cambiar_pu),
                InfoDialog.ICON_QUESTION,
                new OnInfoDialogSelect() {
                    @Override
                    public void onSelectOption(int option) {
                        if (option == InfoDialog.BUTTON_YES)
                            depositElement(element, tag, lat, lng, false, true);
                    }

                }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                InfoDialog.POSITION_CENTER);
        dialog.show();
    }
}
