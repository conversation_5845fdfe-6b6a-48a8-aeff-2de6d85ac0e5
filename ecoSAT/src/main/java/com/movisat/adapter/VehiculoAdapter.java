package com.movisat.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.movisat.database.Vehiculo;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;

import java.util.ArrayList;
import java.util.List;

public class VehiculoAdapter extends BaseAdapter {

    private Context mContext;
    private List<Vehiculo> mItems;
    private int mIndexSeleccionado = -1;


    public VehiculoAdapter(Context context, List<Vehiculo> items) {
        mContext = context;
        mItems = items;
    }

    public List<Vehiculo> getItems() {
        return mItems;
    }

    public void setItems(List<Vehiculo> items) {
        mItems = items;
    }

    public int getIndexSeleccionado() {
        return mIndexSeleccionado;
    }

    public void setIndexSeleccionado(int indexSeleccionado) {
        mIndexSeleccionado = indexSeleccionado;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return mItems.size();
    }

    @Override
    public Vehiculo getItem(int position) {
        return mItems.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        try {
            if (convertView == null) {
                LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(
                        Context.LAYOUT_INFLATER_SERVICE);
                convertView = inflater.inflate(R.layout.item_text_layout, null);
            }

            Resources res = mContext.getResources();

            TextView txtName = (TextView) convertView.findViewById(R.id.textItem);

            Vehiculo vehiculo = mItems.get(position);
            txtName.setText(vehiculo.getNombre());

            if (mIndexSeleccionado == position) {
                convertView.setBackgroundColor(res.getColor(R.color.theme_principal));
                txtName.setTextColor(res.getColor(R.color.white_color));
            } else  if (position == 0) {
                convertView.setBackgroundColor(res.getColor(R.color.blue_400));
                txtName.setTextColor(res.getColor(R.color.white_color));
            } else {
                convertView.setBackgroundColor(res.getColor(R.color.default_color));
                txtName.setTextColor(res.getColor(R.color.black_color));
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return convertView;
    }


    /**
     * Devuelve el índice del vehículo con los parámetros especificados, o -1 si no existe.
     */
    public int getIndexVehiculo(int codigo, int empresa, int tipo) {

        int index = -1;

        try {
            Vehiculo vehiculo = getVehiculo(codigo, empresa, tipo);
            if (vehiculo != null)
                index = mItems.indexOf(vehiculo);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return index;
    }


    /**
     * Devuelve el vehículo con los parámetros especificados, o null si no existe.
     */
    public Vehiculo getVehiculo(int codigo, int empresa, int tipo) {

        Vehiculo vehiculo = null;

        try {
            for (Vehiculo v : mItems) {
                if (v.getCodigo() == codigo && v.getEmpresa() == empresa && v.getTipo() == tipo) {
                    vehiculo = v;
                    break;
                }
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return vehiculo;
    }
}
