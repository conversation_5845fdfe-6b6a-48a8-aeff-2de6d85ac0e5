package com.movisat.adapter;

import android.content.Context;
import android.content.res.Resources;
import androidx.annotation.NonNull;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;

public class SelectionAdapter<T> extends ArrayAdapter<T> {

    private int mIndexSeleccionado = -1;


    public SelectionAdapter(Context context, int resource, T[] objects) {
        super(context, resource, objects);
    }

    public int getIndexSeleccionado() {
        return mIndexSeleccionado;
    }

    public void setIndexSeleccionado(int indexSeleccionado) {
        mIndexSeleccionado = indexSeleccionado;
        notifyDataSetChanged();
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @NonNull
    @Override
    public View getView(int position, View convertView, @NonNull ViewGroup parent) {

        View view = super.getView(position, convertView, parent);

        try {
            Resources res = getContext().getResources();

            TextView text1 = view.findViewById(android.R.id.text1);

            if (mIndexSeleccionado == position) {
                view.setBackgroundColor(res.getColor(R.color.theme_principal));
                text1.setTextColor(res.getColor(R.color.white_color));
            } else {
                view.setBackgroundColor(res.getColor(R.color.default_color));
                text1.setTextColor(res.getColor(R.color.black_color));
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return view;
    }
}
