package com.movisat.adapter;

public class IconTextItem {
	private int id;
	private String title;
	private String icon;
	private int type;
	private boolean state;

	public IconTextItem(int id, String title, String icon, int onOff) {

		setId(id);
		setTitle(title);
		setIcon(icon);
		setType(onOff);
		setState(false);
	}

	public void setId(int id) {
		this.id = id;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public void setType(int val) {
		this.type = val;
	}

	public void setState(boolean state) {
		this.state = state;
	}

	public int getId() {
		return id;
	}

	public String getTitle() {
		return title;
	}

	public String getIcon() {
		return icon;
	}

	public int getType() {
		return type;
	}

	public boolean getState() {
		return state;
	}

}
