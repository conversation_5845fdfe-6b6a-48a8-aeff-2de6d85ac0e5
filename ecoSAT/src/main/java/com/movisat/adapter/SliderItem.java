package com.movisat.adapter;


/**
 * Clase que representa un elemento en el menu del Slider lateral
 * <AUTHOR>
 *
 */
public class SliderItem {
    private String imagen;
    private String title;
    private long id;
    
    public SliderItem() {
        this.title = "";
        this.imagen = "";
      }
 
    public SliderItem(String imagen, String title) {
        this.imagen = imagen;
        this.title = title;
    }
 
     
    public String getImagen() {
        return imagen;
    }
 
    public void setImagen(String imagen) {
        this.imagen = imagen;
    }
 
    public String getTitle() {
        return title;
    }
 
    public void setTitle(String title) {
        this.title = title;
    }

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}
    
}
