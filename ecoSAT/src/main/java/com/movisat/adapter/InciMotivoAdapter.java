package com.movisat.adapter;

import java.util.ArrayList;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.movisat.database.IncidenciaMotivo;
import com.movisat.ecosat.R;

public class InciMotivoAdapter extends BaseAdapter {
    private Activity activity;
    private ArrayList<IncidenciaMotivo> items;

    public InciMotivoAdapter(Activity activity,
                             ArrayList<IncidenciaMotivo> items) {
        this.activity = activity;
        this.items = items;
    }

    @Override
    public int getCount() {
        return items != null ? items.size() : 0;
    }

    @Override
    public Object getItem(int position) {
        return items != null ? items.get(position) : null;
    }

    @Override
    public long getItemId(int position) {
        return items != null ? items.get(position).getId() : 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        if (convertView == null) {

            LayoutInflater inflater = (LayoutInflater) activity
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater.inflate(R.layout.item_text_layout, null);
        }

        TextView item = (TextView) convertView.findViewById(R.id.textItem);
        if (items != null)
            item.setText(items.get(position).getNombre());
        else
            item.setText("");

        return convertView;
    }
}
