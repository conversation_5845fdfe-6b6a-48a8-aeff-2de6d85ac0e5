package com.movisat.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.util.TypedValue;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.movisat.database.ElementoModelo;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.R;
import com.movisat.fragment.GestionElementos;
import com.movisat.utilities.Utils;

import java.util.ArrayList;

public class ElemModelAdapter extends BaseAdapter {
    private int iconWidth, iconHeight;
    private Activity activity;
    private ArrayList<ElementoModelo> items;
    private int VERSION_CAMACHO = 1;
    private int version_actual;

    public ElemModelAdapter(Activity activity, ArrayList<ElementoModelo> items, int version) {
        this.activity = activity;
        this.items = items;
        this.version_actual = version;

        // Calculo las dimensiones de los iconos en función de
        // la resolución de pantalla
        Display display = MainActivity.getInstance().getWindowManager()
                .getDefaultDisplay();


        Point size = new Point();
        display.getSize(size);

        if (size.x <= 320)
            iconWidth = iconHeight = 64;
        else if (size.x >= 1000)
            iconWidth = iconHeight = 128;
        else
            iconWidth = iconHeight = 96;

		/*if (display.getWidth() <= 320)
            iconWidth = iconHeight = 64;
		else
			iconWidth = iconHeight = 96;*/
    }

    @Override
    public int getCount() {
        return items.size();
    }

    @Override
    public Object getItem(int position) {
        return items.get(position);
    }

    @Override
    public long getItemId(int position) {
        return items.get(position).getIdExterno();
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        if (convertView == null) {

            LayoutInflater inflater = (LayoutInflater) activity
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater
                    .inflate(R.layout.item_icon_text_layout, null);
        }

        BitmapDrawable image;

        if (items.get(position).getIcono() != null && items.get(position).getIcono().length > 256) {
            image = (BitmapDrawable) Utils.convertArrayByteToDrawable(items.get(position).getIcono());
        } else {
            Bitmap bmp = BitmapFactory.decodeResource(MainActivity.getInstance().getResources(), R.drawable.modelo_sin_icono);
            image = new BitmapDrawable(MainActivity.getInstance().getResources(), bmp);
        }

        image = Utils.resizeImage(image.getBitmap(), iconWidth, iconHeight, 0);

        TextView item = convertView.findViewById(R.id.textItem);
        ElementoModelo modelo = items.get(position);
        boolean isVisible = GestionElementos.isVisibleModeloElemento(modelo.getIdExterno());

        item.setText(modelo.getNombre());
        item.setTextColor(isVisible ? Color.BLACK : Color.GRAY);
        item.setTypeface(null, isVisible ? Typeface.NORMAL : Typeface.ITALIC);

        item.setCompoundDrawablesWithIntrinsicBounds(image, null, null, null);
        item.setCompoundDrawablePadding(24);
        item.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);

        TextView itemDistance = convertView.findViewById(R.id.textDistance);
        itemDistance.setVisibility(View.GONE);

        return convertView;
    }
}
