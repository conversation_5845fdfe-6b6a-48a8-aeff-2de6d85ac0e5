package com.movisat.adapter;

import java.util.ArrayList;
import java.util.List;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.TextView;

import com.movisat.database.Area;
import com.movisat.database.ElementoModelo;
import com.movisat.ecosat.R;
import com.movisat.ecosat.MainActivity;
import com.movisat.fragment.GesElemMapFragment;
import com.movisat.fragment.GestionElementos;
import com.movisat.utilities.Utils;

public class AreasAdapter extends BaseAdapter implements Filterable {
	private int iconWidth, iconHeight;
	private Activity activity;
	private List<Area> items;
	private List<Area> itemsFilter;

	public AreasAdapter(Activity activity, List<Area> items) {
		this.activity = activity;
		this.items = items;
		this.itemsFilter =items;
	}

	@Override
	public int getCount() {
		return itemsFilter.size();
	}

	@Override
	public Object getItem(int position) {
		return itemsFilter.get(position);
	}

	@Override
	public long getItemId(int position) {
		return itemsFilter.get(position).getCodigo();
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {

		if (convertView == null) {

			LayoutInflater inflater = (LayoutInflater) activity
					.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
			convertView = inflater
					.inflate(R.layout.item_icon_text_layout, null);
		}

		Area area = itemsFilter.get(position);

		Drawable icon = null;
		if (area.getForma() == 2) // Poligono
			icon = convertView.getResources().getDrawable(
					R.drawable.ic_action_estrella);
		else if (area.getForma() == 0) // círculo
			icon = convertView.getResources().getDrawable(
					R.drawable.ic_action_circulo);
		else
			// Cuadrado
			icon = convertView.getResources().getDrawable(
					R.drawable.ic_action_cuadrado);

		TextView item = (TextView) convertView.findViewById(R.id.textItem);
		item.setCompoundDrawablesWithIntrinsicBounds(icon, null, null, null);

		item.setText(itemsFilter.get(position).getNombre());

		item.setCompoundDrawablePadding(10);

		boolean isVisible = GesElemMapFragment.isVisibleArea(area.getCodigo());

		item.setTextColor(isVisible ? Color.BLACK : Color.GRAY);

		item.setTypeface(null, isVisible ? Typeface.NORMAL : Typeface.ITALIC);

		return convertView;
	}

	@Override
	public Filter getFilter() {
		return new Filter() {
			private List<Area> myData;
			private List<Area> orig = null;

			@SuppressWarnings("unchecked")
			@Override
			protected void publishResults(CharSequence constraint,
					FilterResults results) {
				itemsFilter = (List<Area>) results.values;
				notifyDataSetChanged();
			}

			@Override
			protected FilterResults performFiltering(CharSequence constraint) {
				FilterResults filteredResults = getFilteredResults(constraint);
				return filteredResults;
			}

			private FilterResults getFilteredResults(CharSequence constraint) {
				final FilterResults oReturn = new FilterResults();
				List<Area> results = new ArrayList<Area>();
				if (orig == null)
					orig = items;
				if (constraint != null) {
					if (orig != null && orig.size() > 0) {
						for (final Area g : orig) {
							if (g.getNombre().toLowerCase()
									.contains(constraint.toString()))
								results.add(g);
						}
					}
					oReturn.values = results;
					oReturn.count = results.size();
				}
				return oReturn;
			}
		};
	}

}
