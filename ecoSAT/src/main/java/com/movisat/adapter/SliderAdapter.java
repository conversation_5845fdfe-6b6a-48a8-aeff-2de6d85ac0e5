package com.movisat.adapter;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

public class SliderAdapter extends BaseAdapter {
	private ImageView imgIcon = new ImageView(MainActivity.getInstance());
	private Activity activity;
	private ArrayList<SliderItem> items;

	public SliderAdapter(Activity activity, ArrayList<SliderItem> items) {
		this.activity = activity;
		this.items = items;
	}

	@Override
	public int getCount() {
		return items.size();
	}

	@Override
	public Object getItem(int arg0) {
		return items.get(arg0);
	}

	@Override
	public long getItemId(int position) {
		return items.get(position).getId();
	}

	@Override
	public View getView(int position, View contentView, ViewGroup parent) {

		try {

			if (contentView == null) {

				LayoutInflater inflater = (LayoutInflater) activity
						.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
				contentView = inflater.inflate(R.layout.slider_item_layout, null);
			}

			imgIcon.setImageResource(MainActivity
					.getInstance()
					.getResources()
					.getIdentifier(items.get(position).getImagen(), "drawable",
							MainActivity.getInstance().getPackageName()));

			TextView title = (TextView) contentView.findViewById(R.id.titleItemSlider);

			/*
			 * title.setBackgroundColor(contentView.getResources().getColor(
			 * R.color.theme_complementary));
			 */
			title.setTextColor(contentView.getResources().getColor(R.color.white_color));
			title.setText(items.get(position).getTitle());
			title.setCompoundDrawablesWithIntrinsicBounds(
					imgIcon.getDrawable(), null, null, null);
			title.setCompoundDrawablePadding(15);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return contentView;
	}

}
