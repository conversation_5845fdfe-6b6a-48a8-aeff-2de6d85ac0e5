package com.movisat.adapter;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.movisat.database.FlotaPosiciones;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.R;
import com.movisat.utilities.HelperDates;
import com.movisat.utilities.Utils;

import java.util.ArrayList;

public class TagModelAdapter extends BaseAdapter {
    private Activity activity;
    public ArrayList<String> getItems() {
        return items;
    }
    public void setItems(ArrayList<String> items) {
        this.items = items;
    }
    private ArrayList<String> items;

    public TagModelAdapter(Activity activity, ArrayList<String> items) {
        this.activity = activity;
        this.items = items;
    }

    @Override
    public int getCount() {
        return items.size();
    }

    @Override
    public Object getItem(int position) {
        return items.get(position);
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if(items != null) {
            String posicion = items.get(position);

            if (convertView == null) {
                LayoutInflater inflater = (LayoutInflater) activity
                        .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                convertView = inflater
                        .inflate(R.layout.item_text_layout, null);
            }

            TextView item = (TextView) convertView.findViewById(R.id.textItem);
            item.setText(posicion);
            item.setTextSize(16);
            item.setGravity(Gravity.CENTER_HORIZONTAL);
        }

        return convertView;
    }

}
