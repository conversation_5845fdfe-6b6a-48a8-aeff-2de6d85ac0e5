package com.movisat.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.TextView;

import com.movisat.database.Area;
import com.movisat.database.DBGrupoAreas;
import com.movisat.database.GrupoAreas;
import com.movisat.ecosat.GruposActivity;
import com.movisat.ecosat.R;
import com.movisat.fragment.GesElemMapFragment;
import com.movisat.utilities.Config;

import java.util.ArrayList;
import java.util.List;

public class GrupoAreasAdapter extends BaseAdapter implements Filterable {
    private int iconWidth, iconHeight;
    private Activity activity;
    //private List<GrupoAreas> items;
    private List<GrupoAreas> itemsFilter;
    private int empresa = 0;

    public GrupoAreasAdapter(Activity activity, int empresa) {
        this.activity = activity;
        DBGrupoAreas dbGrupoAreas = new DBGrupoAreas();
        //Cargar las ultimas 50 mas clickeadas
        ArrayList<GrupoAreas> grupos = dbGrupoAreas.getLast50MoreClicks(empresa);
        dbGrupoAreas.close();
        this.itemsFilter = (grupos == null) ? new ArrayList<GrupoAreas>() : grupos;
        this.empresa = empresa;

    }

    @Override
    public int getCount() {
        return itemsFilter.size();
    }

    @Override
    public Object getItem(int position) {
        return itemsFilter.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    private List<Integer> codigosGruposSeleccionados = null;

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {

        if (codigosGruposSeleccionados == null)
            codigosGruposSeleccionados = new ArrayList<Integer>();

        if (convertView == null) {

            LayoutInflater inflater = (LayoutInflater) activity
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater
                    .inflate(R.layout.item_icon_text_layout, null);
        }

        GrupoAreas nombreGrupo = itemsFilter.get(position);

        Drawable icon = null;

        icon = convertView.getResources().getDrawable(
                R.drawable.ic_action_estrella);

        TextView item = (TextView) convertView.findViewById(R.id.textItem);
        TextView distance = (TextView) convertView.findViewById(R.id.textDistance);
        if (nombreGrupo.getDistance() > 0) {
//            if (nombreGrupo.getDistance() > 100)
//                distance.setText("> 100 mts");
//            else distance.setText(String.format("%d mts", nombreGrupo.getDistance()));
            if (nombreGrupo.getDistance() > 100)
                distance.setText("> 100 mts");
            else distance.setText(String.format("%d mts", nombreGrupo.getDistance()));
            distance.setVisibility(View.VISIBLE);
        } else {

            distance.setVisibility(View.GONE);
        }

        item.setCompoundDrawablesWithIntrinsicBounds(icon, null, null, null);

        item.setText(nombreGrupo.getNombre());

        item.setCompoundDrawablePadding(10);

        boolean isVisible = GesElemMapFragment.isVisibleGrupo(nombreGrupo.getId());

        if (isVisible) {
            codigosGruposSeleccionados.add(nombreGrupo.getId());
            GruposActivity.getInstance().setCodigosGruposSeleccionados(codigosGruposSeleccionados);
        }
        item.setTextColor(isVisible ? Color.BLACK : Color.GRAY);

        item.setTypeface(null, isVisible ? Typeface.NORMAL : Typeface.ITALIC);

        return convertView;
    }

    @Override
    public Filter getFilter() {
        return new Filter() {
            private List<GrupoAreas> myData;
            private List<GrupoAreas> orig = null;

            @SuppressWarnings("unchecked")
            @Override
            protected void publishResults(CharSequence constraint,
                                          FilterResults results) {
                itemsFilter = (List<GrupoAreas>) results.values;
                notifyDataSetChanged();
            }

            @Override
            protected FilterResults performFiltering(CharSequence constraint) {
                FilterResults filteredResults = getFilteredResults(constraint);
                return filteredResults;
            }

            private FilterResults getFilteredResults(CharSequence constraint) {
                final FilterResults oReturn = new FilterResults();
                List<GrupoAreas> results = new ArrayList<GrupoAreas>();
                DBGrupoAreas db = new DBGrupoAreas();

                //Guardamos el texto del filtro en la configuración
                Config.getInstance().setValueUsuario("filtro_grupo", constraint.toString());

                if (constraint.length() < 3) {
                    results = db.getLast50MoreClicks(empresa);
                    if (results == null) results = new ArrayList<GrupoAreas>();
                    oReturn.values = results;
                    oReturn.count = results.size();
                    db.close();
                    return oReturn;
                }
                if (orig == null) {

                    orig = db.getAll(empresa, constraint.toString()); // items;
                    if (orig == null) orig = new ArrayList<GrupoAreas>();
                }
                if (constraint != null) {

                    oReturn.values = orig;
                    oReturn.count = results.size();
                }
                db.close();
                return oReturn;
            }
        };
    }

    public List<GrupoAreas> getAll() {
        return itemsFilter;
    }
}
