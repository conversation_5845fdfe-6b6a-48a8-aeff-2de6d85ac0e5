package com.movisat.adapter;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Typeface;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.text.style.StyleSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.movisat.database.Incidencia;
import com.movisat.database.IncidentFilterCriteria;
import com.movisat.ecosat.AppContext;
import com.movisat.ecosat.IncidenciasActivity;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;
import com.movisat.ecosat.UpdateEstadoIncidencia;
import com.movisat.fragment.GestionElementos;
import com.movisat.log.Logg;
import com.movisat.utilities.Config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.concurrent.CountDownLatch;

public class IncidenciasModelAdapter extends BaseAdapter implements Filterable {
   private Activity activity;
   private ArrayList<Incidencia> items;
   private int indexSeleccionado = -1;
   private IncidenciasFilter incidenciasFilter;
   private ArrayList<Incidencia> filteredList;
   private UpdatedIncidentReceiver updatedIncidentReceiver;
   private String searchText = "";
   private CountDownLatch latch;
   private IncidentFilterCriteria currentFilterCriteria;

   public IncidenciasModelAdapter(Activity activity, ArrayList<Incidencia> items) {
      this.activity = activity;
      this.items = items;
      this.filteredList = new ArrayList<>(items);
      this.latch = new CountDownLatch(1);
      this.currentFilterCriteria = new IncidentFilterCriteria();

      generateIncidenceStringsInBackground();

      updatedIncidentReceiver = new UpdatedIncidentReceiver();
      IntentFilter filter = new IntentFilter();
      filter.addAction(GestionElementos.MSG_UPDATE_INCIDENCIA_BR);
      LocalBroadcastManager.getInstance(AppContext.getContext()).registerReceiver(updatedIncidentReceiver, filter);

      // Calculo las dimensiones de los iconos en función de
      // la resolución de pantalla
      //	Display display = MainActivity.getInstance().getWindowManager()
      //			.getDefaultDisplay();

		/*Point size = new Point();
		display.getSize(size);

		if (size.x <= 320)
			iconWidth = iconHeight = 32;
		else if (size.x >= 1000)
			iconWidth = iconHeight = 72;
		else 
			iconWidth = iconHeight = 48;*/
   }

   private void generateIncidenceStringsInBackground(){
      // Para generar los toString y evitar lags en el listview
      new Thread(() -> {
         for (Incidencia incidencia : items) {
            incidencia.toString();
         }
         latch.countDown();
      }).start();
   }

   @Override
   public int getCount() {
      return filteredList.size();
   }

   @Override
   public Object getItem(int position) {
      return filteredList.get(position);
   }

   @Override
   public long getItemId(int position) {
      return filteredList.get(position).getId();
   }

   @Override
   public View getView(int position, View convertView, ViewGroup parent) {
      try {
         Incidencia incidencia = (Incidencia) filteredList.get(position);

         if (convertView == null) {
            LayoutInflater inflater = (LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater.inflate(R.layout.model_lista_incidencias_layout, null);
         }

         TextView item = (TextView) convertView.findViewById(R.id.textItem);
         Button button = convertView.findViewById(R.id.btCambioEstadoInci);

         button.setOnClickListener(v -> {
            // Abro la ventana para modificar el estado de la incidencia
            Intent updateEstadoIncidenciaActivity = new Intent(MainActivity.getInstance(),
                  UpdateEstadoIncidencia.class);
            updateEstadoIncidenciaActivity.putExtra("incidencia", incidencia.getId());
            updateEstadoIncidenciaActivity.putExtra("incidenciaExt", incidencia.getIdExterno());
            updateEstadoIncidenciaActivity.putExtra("usuario", MainActivity.getInstance().getUsuario());
            IncidenciasActivity.getInstance().startActivity(updateEstadoIncidenciaActivity);
         });

         if (indexSeleccionado == position) {
            convertView.setBackgroundColor(ContextCompat.getColor(activity, R.color.theme_principal_hover));
            item.setTextColor(ContextCompat.getColor(activity, R.color.white_color));
         } else {
            convertView.setBackgroundColor(ContextCompat.getColor(activity, R.color.default_color));
            item.setTextColor(ContextCompat.getColor(activity, android.R.color.tab_indicator_text));
         }

         String itemText = incidencia.toString();
         // Si hay texto de búsqueda, resaltar las partes coincidentes en el item
         if (!searchText.isEmpty()) {
            String lowerCaseItemText = itemText.toLowerCase();
            int startPos = lowerCaseItemText.indexOf(searchText.toLowerCase());
            if (startPos != -1) {
               Spannable spannable = new SpannableString(itemText);
               int endPos = startPos + searchText.length();
               int matchingTextColor = ContextCompat.getColor(activity, R.color.green_color);
               spannable.setSpan(new ForegroundColorSpan(matchingTextColor), startPos, endPos,
                     Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
               spannable.setSpan(new StyleSpan(Typeface.BOLD), startPos, endPos, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
               item.setText(spannable);
            }
         } else {
            item.setText(itemText);
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
      return convertView;
   }

   public void select(int position) {
      indexSeleccionado = position;
      notifyDataSetChanged();
   }

   public void setIndexSeleccionado(int indexSeleccionado) {
      this.indexSeleccionado = indexSeleccionado;
      notifyDataSetChanged();
   }

   public Filter getFilter() {
      if (incidenciasFilter == null) {
         incidenciasFilter = new IncidenciasFilter();
      }
      return incidenciasFilter;
   }

   public void applyAdvancedFilter(IncidentFilterCriteria criteria) {
      this.currentFilterCriteria = criteria;
      applyFilters();
   }

   public IncidentFilterCriteria getCurrentFilterCriteria() {
      return currentFilterCriteria;
   }

   private void applyFilters() {
      ArrayList<Incidencia> tempList = new ArrayList<>();
      
      for (Incidencia incidencia : items) {
         if (passesAllFilters(incidencia)) {
            tempList.add(incidencia);
         }
      }

      // Apply sorting
      if (currentFilterCriteria.getSortOrder() == IncidentFilterCriteria.SortOrder.NEWEST_FIRST) {
         Collections.sort(tempList, (a, b) -> Long.compare(b.getFechaUltimoEstado(), a.getFechaUltimoEstado()));
      } else {
         Collections.sort(tempList, (a, b) -> Long.compare(a.getFechaUltimoEstado(), b.getFechaUltimoEstado()));
      }

      filteredList = tempList;
      IncidenciasActivity.getInstance().setNumIncidencias(filteredList.size());
      setIndexSeleccionado(-1);
      notifyDataSetChanged();
   }

   private boolean passesAllFilters(Incidencia incidencia) {
      // Search text filter
      if (!searchText.isEmpty() && !incidencia.toString().toLowerCase().contains(searchText.toLowerCase())) {
         return false;
      }

      // Date filter
      Date[] dateRange = currentFilterCriteria.getEffectiveDateRange();
      if (dateRange[0] != null || dateRange[1] != null) {
         long incidentTime = incidencia.getFechaUltimoEstado() * 1000; // Convert to milliseconds
         Date incidentDate = new Date(incidentTime);
         
         if (dateRange[0] != null && incidentDate.before(dateRange[0])) {
            return false;
         }
         if (dateRange[1] != null && incidentDate.after(dateRange[1])) {
            return false;
         }
      }

      // Status filter
      if (currentFilterCriteria.getStatusFilter() != -1 && 
          incidencia.getUltimoEstado() != currentFilterCriteria.getStatusFilter()) {
         return false;
      }

      // Incident type filter
      if (currentFilterCriteria.getIncidentTypeFilter() != -1 && 
          incidencia.getTipo() != currentFilterCriteria.getIncidentTypeFilter()) {
         return false;
      }

      // Owner filter
      String ownerFilterName = currentFilterCriteria.getOwnerName();
      if (ownerFilterName != null && !ownerFilterName.isEmpty()) {
         String incidenciaOwnerName = incidencia.getNombrePropietario();
         if (incidenciaOwnerName == null || !incidenciaOwnerName.equals(ownerFilterName)) {
            return false;
         }
      }

      // Element filter
      if (currentFilterCriteria.isWithElementOnly() && incidencia.getElemento() <= 0) {
         return false;
      }

      return true;
   }

   public class UpdatedIncidentReceiver extends BroadcastReceiver {
      @Override
      public void onReceive(Context context, Intent intent) {
         try {
            latch.await();
         } catch (InterruptedException e) {
            Logg.error(e.toString());
         }

         Incidencia incidencia = (Incidencia) intent.getSerializableExtra("item");
         if (incidencia != null) {
            setIndexSeleccionado(-1);
            boolean delete = false;
            String estadosVisibles = Config.getInstance().getValueUsuario("inciVisibles", "");
            if (!estadosVisibles.isEmpty() && !estadosVisibles.equals("todos")) {
               String[] estados = estadosVisibles.split(",");
               // Si el estado de la incidencia no está entre los visibles, se elimina
               delete = !Arrays.asList(estados).contains(String.valueOf(incidencia.getUltimoEstado()));
            }
            for (int i = 0; i < items.size(); i++) {
               if (items.get(i).getId() == incidencia.getId()) {
                  items.set(i, incidencia);
                  if (delete)
                     items.remove(i);
                  break;
               }
            }
            for (int i = 0; i < filteredList.size(); i++) {
               if (filteredList.get(i).getId() == incidencia.getId()) {
                  filteredList.set(i, incidencia);
                  if (delete)
                     filteredList.remove(i);
                  break;
               }
            }
            if (delete) {
               IncidenciasActivity.getInstance().setNumIncidencias(filteredList.size());
            } else {
               Collections.sort(items);
               Collections.sort(filteredList);
            }
            notifyDataSetChanged();
         }
      }
   }

   class IncidenciasFilter extends Filter {
      @Override
      protected FilterResults performFiltering(CharSequence charSequence) {
         FilterResults results = new FilterResults();
         searchText = charSequence.toString().toLowerCase();
         
         // Re-apply all filters including the new search text
         ArrayList<Incidencia> tempList = new ArrayList<>();
         for (Incidencia incidencia : items) {
            if (passesAllFilters(incidencia)) {
               tempList.add(incidencia);
            }
         }

         // Apply sorting
         if (currentFilterCriteria.getSortOrder() == IncidentFilterCriteria.SortOrder.NEWEST_FIRST) {
            Collections.sort(tempList, (a, b) -> Long.compare(b.getFechaUltimoEstado(), a.getFechaUltimoEstado()));
         } else {
            Collections.sort(tempList, (a, b) -> Long.compare(a.getFechaUltimoEstado(), b.getFechaUltimoEstado()));
         }

         results.values = tempList;
         results.count = tempList.size();

         return results;
      }

      @Override
      protected void publishResults(CharSequence charSequence, FilterResults filterResults) {
         filteredList = (ArrayList<Incidencia>) filterResults.values;
         IncidenciasActivity.getInstance().setNumIncidencias(filteredList.size());
         setIndexSeleccionado(-1);
         notifyDataSetChanged();
      }
   }
}
