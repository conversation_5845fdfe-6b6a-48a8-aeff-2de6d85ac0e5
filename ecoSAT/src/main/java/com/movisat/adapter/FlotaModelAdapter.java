package com.movisat.adapter;

import java.util.ArrayList;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ImageSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.graphics.drawable.DrawableCompat;

import com.environment.Environment;
import com.movisat.database.DBIdentificaciones;
import com.movisat.database.FlotaPosiciones;
import com.movisat.database.Identificacion;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.R;
import com.movisat.utilities.Config;
import com.movisat.utilities.HelperDates;
import com.movisat.utilities.Utils;

public class FlotaModelAdapter extends BaseAdapter implements Filterable {
    private Activity activity;
    private ArrayList<FlotaPosiciones> items;
    private ArrayList<FlotaPosiciones> filteredList;
    private FlotaFilter flotaFilter;
    private int indexSeleccionado = -1;

    public ArrayList<FlotaPosiciones> getItems() {
        return filteredList;
    }

    public void setItems(ArrayList<FlotaPosiciones> items) {
        this.items = items;
        this.filteredList = items;
    }

    public void setFilteredItems(ArrayList<FlotaPosiciones> items) {
        this.filteredList = items;
    }

    public int getIndexSeleccionado() {
        return indexSeleccionado;
    }

    public void setIndexSeleccionado(int indexSeleccionado) {
        this.indexSeleccionado = indexSeleccionado;
    }

    public FlotaModelAdapter(Activity activity, ArrayList<FlotaPosiciones> items) {
        this.activity = activity;
        this.items = items;
        this.filteredList = items;

        getFilter();

        // Calculo las dimensiones de los iconos en función de
        // la resolución de pantalla
        //Display display = MainActivity.getInstance().getWindowManager()
        //		.getDefaultDisplay();

        /*
         * Point size = new Point(); display.getSize(size);
         *
         * if (size.x <= 320) iconWidth = iconHeight = 32; else if (size.x >=
         * 1000) iconWidth = iconHeight = 96; else iconWidth = iconHeight = 48;
         */
    }

    @Override
    public int getCount() {
        return filteredList.size();
    }

    @Override
    public Object getItem(int position) {
        return filteredList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return filteredList.get(position).getId();
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        Resources res = MainActivity.getInstance().getResources();
        FlotaPosiciones posicion = filteredList.get(position);

        if (convertView == null) {
            LayoutInflater inflater = (LayoutInflater) activity.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater.inflate(R.layout.flota_item_icon_text_layout, null);
        }

        int bgColor = indexSeleccionado == position ? R.color.theme_principal_hover : R.color.default_color;
        convertView.setBackgroundColor(res.getColor(bgColor));

        // Icono + Texto
        TextView item = convertView.findViewById(R.id.textItem);

        Bitmap image = BitmapFactory.decodeResource(res, posicion.getEstado() ? R.drawable.ico_green : R.drawable.ico_red);
        BitmapDrawable draw = Utils.resizeImage(image, image.getWidth(), image.getHeight(), 0);
        item.setCompoundDrawablesWithIntrinsicBounds(draw, null, null, null);

        long milliseconds = HelperDates.getInstance().getMillisecondsBy(posicion.getFecha());
        String fecha = HelperDates.getInstance().getDateStringBy(milliseconds, "dd/MM/yy HH:mm:ss");
        item.setText(posicion.getDescripcion() + "\r\n" + fecha);

        int textColor = indexSeleccionado == position ? Color.WHITE : posicion.getEstado() ? Color.BLACK : Color.GRAY;
        item.setTextColor(textColor);

        int style = posicion.getEstado() ? Typeface.NORMAL : Typeface.ITALIC;
        item.setTypeface(null, style);

        // Añado las identificaciones
        LinearLayout container = convertView.findViewById(Environment.isTablet ? R.id.containerRight : R.id.containerBottom);
        container.removeAllViews();
        if (posicion.getIdentificaciones().isEmpty()) {
            container.setVisibility(View.GONE);
        } else {
            container.setVisibility(View.VISIBLE);
            // Relleno el contenedor con las identificaciones
            for (Identificacion identificacion : posicion.getIdentificaciones()) {
                TextView tv = new TextView(activity);
                tv.setLayoutParams(new LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT
                ));
                tv.setTextSize(14);
                tv.setTextColor(textColor);
                tv.setEllipsize(TextUtils.TruncateAt.END);
                tv.setMaxLines(1);
                tv.setTypeface(null, style);

                String fechaInicioTurno = identificacion.getFechaInicio();
                long millis = HelperDates.getInstance().getMillisecondsBy(fechaInicioTurno);
                fechaInicioTurno = HelperDates.getInstance().getDateStringBy(millis, "dd/MM/yy HH:mm:ss");

                String identificacionTexto = fechaInicioTurno + " - * " + identificacion.getNombreEmpleado();

                // Sustituyo el asterisco por un icono de volante o trabajador
                SpannableString spannableString = new SpannableString(identificacionTexto);
                boolean isConductor = "Conductor".equalsIgnoreCase(identificacion.getNombreCategoria());
                Drawable icon = res.getDrawable(isConductor ? R.drawable.ic_volante : R.drawable.ic_trabajador);
                DrawableCompat.setTint(icon, textColor);
                int iconSize = (int) (item.getTextSize() * 0.75);
                icon.setBounds(0, 0, iconSize, iconSize);
                ImageSpan imageSpan = new ImageSpan(icon, ImageSpan.ALIGN_BASELINE);
                spannableString.setSpan(imageSpan, fechaInicioTurno.length() + 3, fechaInicioTurno.length() + 4, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
                tv.setText(spannableString);

                container.addView(tv);
            }
        }

        return convertView;
    }

    @Override
    public Filter getFilter() {
        if (flotaFilter == null) {
            flotaFilter = new FlotaFilter();
        }

        return flotaFilter;
    }

    class FlotaFilter extends Filter {

        @Override
        protected FilterResults performFiltering(CharSequence constraint) {
            FilterResults filterResults = new FilterResults();
            if (constraint != null && constraint.length() > 0) {
                ArrayList<FlotaPosiciones> tempList = new ArrayList<>();

                // search content in friend list
                for (FlotaPosiciones item : items) {
                    if (item.getDescripcion().toLowerCase().contains(constraint.toString().toLowerCase())) {
                        tempList.add(item);
                    }
                }

                filterResults.count = tempList.size();
                filterResults.values = tempList;
            } else {
                filterResults.count = items.size();
                filterResults.values = items;
            }

            return filterResults;
        }

        /**
         * Notify about filtered list to ui
         *
         * @param constraint text
         * @param results    filtered result
         */
        @SuppressWarnings("unchecked")
        @Override
        protected void publishResults(CharSequence constraint, FilterResults results) {
            filteredList = (ArrayList<FlotaPosiciones>) results.values;
            notifyDataSetChanged();
        }
    }
}
