package com.movisat.adapter;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.movisat.database.Incidencia;
import com.movisat.database.OperationsDone;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;
import com.movisat.utilities.Utils;

import java.util.ArrayList;

public class OperationsDoneAdapter extends BaseAdapter {
	private Activity activity;
	private ArrayList<OperationsDone> items;
	private int indexSeleccionado = -1;

	public OperationsDoneAdapter(Activity activity,
                                 ArrayList<OperationsDone> items) {
		this.activity = activity;
		this.items = items;
	}

	@Override
	public int getCount() {
		return items.size();
	}

	@Override
	public Object getItem(int position) {
		return items.get(position);
	}

	@Override
	public long getItemId(int i) {
		return 0;
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {

		try {

			OperationsDone operationsDone = (OperationsDone) items.get(position);

			if (convertView == null) {

				LayoutInflater inflater = (LayoutInflater) activity
						.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
				convertView = inflater.inflate(R.layout.item_icon_text_layout,
						null);
			}
			TextView textDistance = (TextView) convertView.findViewById(R.id.textDistance);
			textDistance.setVisibility(View.GONE);

			Resources res = MainActivity.getInstance().getResources();

			TextView item = (TextView) convertView.findViewById(R.id.textItem);
			item.setText(operationsDone.toString());
			item.setTextColor(convertView.getResources().getColor(R.color.black_color));

			if (indexSeleccionado == position) {
				convertView.setBackgroundColor(res.getColor(R.color.theme_principal_hover));
				item.setTextColor(res.getColor(R.color.white_color));
			} else {
				convertView.setBackgroundColor(res.getColor(R.color.default_color));
			}


		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return convertView;
	}

	public int getIndexSeleccionado() {
		return indexSeleccionado;
	}

	public void setIndexSeleccionado(int indexSeleccionado) {
		this.indexSeleccionado = indexSeleccionado;
	}
}
