package com.movisat.adapter;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

/**
 * Adapter para el spinner del Action Bar
 */
public class IconTextAdapter extends BaseAdapter {
	private ImageView imgIcon = new ImageView(MainActivity.getInstance());
	private ImageView imgIcon2;
	private TextView item;
	private ArrayList<IconTextItem> listItems;
	private Context context;

	public IconTextAdapter(Context context,
			ArrayList<IconTextItem> spinnerNavItem) {

		this.listItems = spinnerNavItem;
		this.context = context;
	}

	@Override
	public int getCount() {
		return listItems.size();
	}

	@Override
	public Object getItem(int index) {
		return listItems.get(index);
	}

	@Override
	public long getItemId(int position) {
		return listItems.get(position).getId();
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {

		try {

			if (convertView == null) {

				LayoutInflater mInflater = (LayoutInflater) context
						.getSystemService(Activity.LAYOUT_INFLATER_SERVICE);
				convertView = mInflater.inflate(R.layout.item_icon_text_layout,
						null);
			}

			item = (TextView) convertView.findViewById(R.id.textItem);

			imgIcon.setImageResource(MainActivity
					.getInstance()
					.getResources()
					.getIdentifier(listItems.get(position).getIcon(),
							"drawable",
							MainActivity.getInstance().getPackageName()));

			if (listItems.get(position).getType() > 0) {

				if (imgIcon2 == null)
					imgIcon2 = new ImageView(MainActivity.getInstance());

				String icon = listItems.get(position).getState() ? "icon_on"
						: "icon_off";

				imgIcon2.setImageResource(MainActivity
						.getInstance()
						.getResources()
						.getIdentifier(icon, "drawable",
								MainActivity.getInstance().getPackageName()));

			} else
				imgIcon2 = null;

			if (!listItems.get(position).getTitle().equals("--")) {

				item.setTextColor(convertView.getContext().getResources()
						.getColor(R.color.default_color));
				item.setText(listItems.get(position).getTitle());
				item.setCompoundDrawablesWithIntrinsicBounds(imgIcon
						.getDrawable(), null, imgIcon2 == null ? null
						: imgIcon2.getDrawable(), null);
				item.setCompoundDrawablePadding(10);

			} else {

				Drawable d = context.getResources().getDrawable(
						R.drawable.separador);

				item.setText("");
				item.setCompoundDrawablePadding(0);
				item.setCompoundDrawablesWithIntrinsicBounds(d, null, null,
						null);
				item.setHeight(1);
				item.setClickable(false);
				item.setEnabled(false);
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return convertView;
	}

	@Override
	public View getDropDownView(int position, View convertView, ViewGroup parent) {

		try {

			if (convertView == null) {

				LayoutInflater mInflater = (LayoutInflater) context
						.getSystemService(Activity.LAYOUT_INFLATER_SERVICE);
				convertView = mInflater.inflate(R.layout.item_icon_text_layout,
						null);
			}

			item = (TextView) convertView.findViewById(R.id.textItem);
			imgIcon.setImageResource(MainActivity
					.getInstance()
					.getResources()
					.getIdentifier(listItems.get(position).getIcon(),
							"drawable",
							MainActivity.getInstance().getPackageName()));

			if (listItems.get(position).getType() > 0) {

				if (imgIcon2 == null)
					imgIcon2 = new ImageView(MainActivity.getInstance());

				String icon = listItems.get(position).getState() ? "icon_on"
						: "icon_off";

				imgIcon2.setImageResource(MainActivity
						.getInstance()
						.getResources()
						.getIdentifier(icon, "drawable",
								MainActivity.getInstance().getPackageName()));

			} else
				imgIcon2 = null;

			if (!listItems.get(position).getTitle().equals("--")) {

				item.setTextColor(convertView.getContext().getResources()
						.getColor(R.color.default_color));
				item.setText(listItems.get(position).getTitle());

				item.setCompoundDrawablesWithIntrinsicBounds(imgIcon
						.getDrawable(), null, imgIcon2 == null ? null
						: imgIcon2.getDrawable(), null);
				item.setCompoundDrawablePadding(10);
				item.setHeight(imgIcon.getDrawable().getIntrinsicHeight() - 10);

			} else {

				Drawable d = context.getResources().getDrawable(
						R.drawable.separador);

				item.setText("");
				item.setCompoundDrawablePadding(0);
				item.setCompoundDrawablesWithIntrinsicBounds(d, null, null,
						null);
				item.setHeight(1);
				item.setClickable(false);
				item.setEnabled(false);
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return convertView;
	}

}
