package com.movisat.adapter;

import java.util.HashMap;
import java.util.List;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.PorterDuff.Mode;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.BaseExpandableListAdapter;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyBroadCastManager;
import com.movisat.ecosat.R;
import com.movisat.utilities.Config;
import com.movisat.utilities.Utils;

public class SliderDerechoExpandableListAdapter extends
        BaseExpandableListAdapter {

    private Context _context;
    private List<String> _listDataHeader; // header titles
    // child data in format of header title, child title
    private HashMap<String, List<ItemSliderDerecho>> _listDataChild;
    private List<Integer> IdMenus;

    public SliderDerechoExpandableListAdapter(Context context,
                                              List<String> listDataHeader,
                                              HashMap<String, List<ItemSliderDerecho>> listChildData) {
        this._context = context;
        this._listDataHeader = listDataHeader;
        this._listDataChild = listChildData;
    }

    @Override
    public Object getChild(int groupPosition, int childPosition) {

        ItemSliderDerecho child = null;

        // Se obtiene el elemento con índice childPosition de los children VISIBLES en el grupo
        int index = 0;
        for (ItemSliderDerecho itChild : this._listDataChild.get(this._listDataHeader.get(groupPosition))) {
            if (itChild.is_visible()) {
                if (index == childPosition) {
                    child = itChild;
                    break;
                }
                index++;
            }
        }

        if (child != null)
            return child;
        else
            return this._listDataChild.get(this._listDataHeader.get(groupPosition))
                    .get(childPosition);
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {

        ItemSliderDerecho child = (ItemSliderDerecho) getChild(groupPosition,
                childPosition);
        return child.get_idMenu();
    }

    @Override
    public View getChildView(int groupPosition, final int childPosition,
                             boolean isLastChild, View convertView, ViewGroup parent) {
        final ItemSliderDerecho childText = (ItemSliderDerecho) getChild(
                groupPosition, childPosition);

        // int IdMenu = Integer.parseInt(childText.split(";")[0]);
        // IdMenus.add(IdMenu);
        // String icono = childText.split(";")[1];
        // String texto = childText.split(";")[2];

        if (convertView == null) {
            LayoutInflater infalInflater = (LayoutInflater) this._context
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = infalInflater.inflate(R.layout.list_item, null);
        }


        LinearLayout contenedor = (LinearLayout) convertView
                .findViewById(R.id.contenedor_Controls);

        CheckBox checkSelect = (CheckBox) convertView
                .findViewById(R.id.chkSelect);

        ImageView ico_item = (ImageView) convertView
                .findViewById(R.id.ico_item);
        // checkSelect.setc
        // checkSelect.

        String sIdMenu = Config.getInstance().getValueUsuario("ultOpcSubmenu",
                "0");

        int IdMenu = Integer.parseInt(sIdMenu);

        if (!childText.is_checkBox()) {
            checkSelect.setVisibility(View.INVISIBLE);

        } else {
            checkSelect.setVisibility(View.VISIBLE);
            checkSelect.setOnCheckedChangeListener(null);
            checkSelect.setOnClickListener(null);
            // checkSelect.setEnabled(false);

            int id = Resources.getSystem().getIdentifier(
                    "btn_check_holo_light", "drawable", "android");
            Drawable drawable = convertView.getResources().getDrawable(id);

            drawable.setColorFilter(Color.GREEN, Mode.SRC_ATOP);
            checkSelect.setButtonDrawable(drawable);

            if (Integer.parseInt(Config.getInstance().getValueUsuario("segGps",
                    "0")) > 0) {
                checkSelect.setChecked(true);
            } else {
                checkSelect.setChecked(false);
            }
        }

        TextView txtListChild = (TextView) convertView
                .findViewById(R.id.lblListItem);
        txtListChild.setText(childText.get_nameMenu());

        txtListChild.setTag(childText.get_idMenu());
        if (IdMenu == childText.get_idMenu() && childText.is_dejarMarcado()) {


            /*if (IdMenu == MainActivity.getInstance().MENU_PARQUES_JARDINES_MEDIDA) {
                boolean isMedidaIniciada = (Config.getInstance().getValueUsuario(
                        "medidaIniciada", "0").equals("1")) ? true : false;
                if (isMedidaIniciada) {
                    contenedor
                            .setBackgroundResource(R.color.common_signin_btn_light_text_focused);
                    MainActivity.getInstance().getSupportActionBar()
                            .setSubtitle(childText.get_nameMenu());
                }
            } else {*/
            contenedor
                    .setBackgroundResource(R.color.gris);
            MainActivity.getInstance().getSupportActionBar()
                    .setSubtitle(childText.get_nameMenu());
            //}


        } else
            contenedor.setBackground(null);

        Resources r = convertView.getResources();

        final int resourceId = r.getIdentifier(childText.get_icoName(),
                "drawable", MainActivity.getInstance().getPackageName());

        Drawable ico = r.getDrawable(resourceId);
        ico_item.setImageDrawable(ico);

        return convertView;
    }

    @Override
    public int getChildrenCount(int groupPosition) {
        try {
            // Se devuelve el número de children visibles
            int size = 0;
            for (ItemSliderDerecho child : this._listDataChild.get(this._listDataHeader.get(groupPosition))) {
                if (child.is_visible()) size++;
            }

            return size;

//            return this._listDataChild.get(
//                    this._listDataHeader.get(groupPosition)).size();
        } catch (Exception ex) {
            return -1;
        }
    }

    @Override
    public Object getGroup(int groupPosition) {
        return this._listDataHeader.get(groupPosition);
    }

    @Override
    public int getGroupCount() {
        return this._listDataHeader.size();
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public View getGroupView(int groupPosition, boolean isExpanded,
                             View convertView, ViewGroup parent) {
        String headerTitle = (String) getGroup(groupPosition);
        if (convertView == null) {
            LayoutInflater infalInflater = (LayoutInflater) this._context
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = infalInflater.inflate(R.layout.list_group, null);
        }

        TextView lblListHeader = (TextView) convertView
                .findViewById(R.id.lblListHeader);
        lblListHeader.setTypeface(null, Typeface.BOLD);
        lblListHeader.setText(headerTitle);

        return convertView;
    }

    @Override
    public boolean hasStableIds() {
        return false;
    }

    @Override
    public boolean isChildSelectable(int groupPosition, int childPosition) {
        return true;
    }

    public static class ItemSliderDerecho {
        private Integer _idMenu;
        private String _nameMenu;
        private String _icoName;
        private boolean _checkBox;
        private boolean _isActivoAMaxNivelZoom;
        private boolean _closeMenu;
        private boolean _dejarMarcado;
        private boolean _cargar;
        private boolean _dejarMarcadoAnterior;
        private boolean _visible;

        public ItemSliderDerecho(Integer idMenu, String nameMenu,
                                 String icoName, boolean checkBox, boolean closeMenu,
                                 boolean isActivoAMaxNivelZoom, boolean dejarMarcado,
                                 boolean cargar, boolean dejarMarcadoAnterior) {
            this.set_idMenu(idMenu);
            this.set_nameMenu(nameMenu);
            this.set_icoName(icoName);
            this.set_checkBox(checkBox);
            this.set_isActivoAMaxNivelZoom(isActivoAMaxNivelZoom);
            this.set_closeMenu(closeMenu);
            this.set_dejarMarcado(dejarMarcado);
            this.set_cargar(cargar);
            this.set_dejarMarcadoAnterior(dejarMarcadoAnterior);
            _visible = true;
        }

        public Integer get_idMenu() {
            return _idMenu;
        }

        public void set_idMenu(Integer _idMenu) {
            this._idMenu = _idMenu;
        }

        public String get_nameMenu() {
            return _nameMenu;
        }

        public void set_nameMenu(String _nameMenu) {
            this._nameMenu = _nameMenu;
        }

        public String get_icoName() {
            return _icoName;
        }

        public void set_icoName(String _icoName) {
            this._icoName = _icoName;
        }

        public boolean is_checkBox() {
            return _checkBox;
        }

        public void set_checkBox(boolean _checkBox) {
            this._checkBox = _checkBox;
        }

        public boolean is_isActivoAMaxNivelZoom() {
            return _isActivoAMaxNivelZoom;
        }

        public void set_isActivoAMaxNivelZoom(boolean _isActivoAMaxNivelZoom) {
            this._isActivoAMaxNivelZoom = _isActivoAMaxNivelZoom;
        }

        public boolean is_closeMenu() {
            return _closeMenu;
        }

        public void set_closeMenu(boolean _closeMenu) {
            this._closeMenu = _closeMenu;
        }

        public boolean is_dejarMarcado() {
            return _dejarMarcado;
        }

        public void set_dejarMarcado(boolean _dejarMarcado) {
            this._dejarMarcado = _dejarMarcado;
        }

        public boolean is_dejarMarcadoAnterior() {
            return _dejarMarcadoAnterior;
        }

        public void set_dejarMarcadoAnterior(boolean _dejarMarcadoAnterior) {
            this._dejarMarcadoAnterior = _dejarMarcadoAnterior;
        }

        public boolean is_cargar() {
            return _cargar;
        }

        public void set_cargar(boolean _cargar) {
            this._cargar = _cargar;
        }

        public boolean is_visible() {
            return _visible;
        }

        public void set_visible(boolean _visible) {
            this._visible = _visible;
        }
    }

}