package com.movisat.adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.movisat.database.Estado;
import com.movisat.ecosat.R;
import com.movisat.fragment.GestionElementos;

import java.util.ArrayList;

public class InciEstadosAdapter extends BaseAdapter {
	private Activity activity;
	private ArrayList<Estado> items;

	public InciEstadosAdapter(Activity activity, ArrayList<Estado> items) {
		this.activity = activity;
		this.items = items;
	}

	@Override
	public int getCount() {
		return items != null ? items.size() : 0;
	}

	@Override
	public Object getItem(int position) {
		return items != null ? items.get(position) : null;
	}

	@Override
	public long getItemId(int position) {
		return items != null ? items.get(position).getIdExterno() : 0;
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {

		if (convertView == null) {

			LayoutInflater inflater = (LayoutInflater) activity
					.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
			convertView = inflater.inflate(R.layout.item_text_large_layout, null);
		}

		TextView item = convertView.findViewById(R.id.textItemLarge);
		Estado estado = items.get(position);
		boolean isVisible = GestionElementos.isVisibleEstadoIncidencia(estado.getIdExterno());

		item.setText(estado.getNombre());
		item.setTextColor(isVisible ? Color.BLACK : Color.GRAY);
		item.setTypeface(null, isVisible ? Typeface.NORMAL : Typeface.ITALIC);

		return convertView;
	}
}
