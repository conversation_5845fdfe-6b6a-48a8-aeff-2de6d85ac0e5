package com.movisat.adapter;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.movisat.database.NivelCritico;
import com.movisat.database.OperationsDone;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;

import java.util.ArrayList;

public class NivelCriticoAdapter extends BaseAdapter {
	private Activity activity;
	private ArrayList<String> items;

	public NivelCriticoAdapter(Activity activity) {
		this.activity = activity;

		ArrayList<String> list = new ArrayList<String>();
		list.add("");
		list.add("A");
		list.add("B");
		list.add("C");
		list.add("D");

		this.items = list;
	}

	@Override
	public int getCount() {
		return items.size();
	}

	@Override
	public Object getItem(int position) {
		return items.get(position);
	}

	@Override
	public long getItemId(int i) {
		return 0;
	}

	@Override
	public View getView(int position, View convertView, ViewGroup parent) {

		try {

			String nivel = (String) items.get(position);

			if (convertView == null) {

				LayoutInflater inflater = (LayoutInflater) activity
						.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
				convertView = inflater.inflate(R.layout.item_text_layout,
						null);
			}

			//Resources res = MainActivity.getInstance().getResources();
			TextView item = (TextView) convertView.findViewById(R.id.textItem);
			item.setText(nivel.toString());

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return convertView;
	}
}
