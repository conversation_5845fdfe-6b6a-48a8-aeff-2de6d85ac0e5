package com.movisat.helpers;

import android.app.AlertDialog;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.util.Log;

import com.movisat.ecosat.R;

import org.json.JSONObject;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;


/**
 * Created by faroca on 24/11/2015.
 */
public class Updater {


    private final Context context;

    private Updater(Context context) {
        this.context = context;
    }

    private static Updater _instance = null;

    public static Updater getInstance(Context context) {
        if (_instance == null)
            _instance = new Updater(context);

        return _instance;
    }


    String currentVersion, latestVersion;
    Dialog dialog;

    public void getCurrentVersion() {
        PackageManager pm = context.getPackageManager();
        PackageInfo pInfo = null;

        try {
            pInfo = pm.getPackageInfo(context.getPackageName(), 0);

        } catch (PackageManager.NameNotFoundException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        currentVersion = pInfo.versionName;

        new GetLatestVersion().execute();

    }

    private class GetLatestVersion extends AsyncTask<String, String, JSONObject> {

        private ProgressDialog progressDialog;
        private String urlOfAppFromPlayStore = "https://play.google.com/store/apps/details?id=com.movisat.ecosat";

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        @Override
        protected JSONObject doInBackground(String... params) {
            try {
                //It retrieves the latest version by scraping the content of current version from play store at runtime

                Document doc = Jsoup.connect(urlOfAppFromPlayStore).get();
                latestVersion = doc.getElementsByAttributeValue
                        ("itemprop", "softwareVersion").first().text();

            } catch (Exception e) {
                e.printStackTrace();
                Log.e("Updater", "Error al recuperar lastestVersion: "+e.getMessage());
            }

            return new JSONObject();
        }

        @Override
        protected void onPostExecute(JSONObject jsonObject) {

            if (latestVersion != null) {
                int playStoreVersion =  Integer.parseInt(latestVersion.replace(".", ""));
                int deviceVersion = Integer.parseInt(currentVersion.replace(".", ""));
                //if (!currentVersion.equalsIgnoreCase(latestVersion))
                if (playStoreVersion > deviceVersion)
                    showUpdateDialog();
            }
            super.onPostExecute(jsonObject);
        }
    }

    private void showUpdateDialog() {

        try {
            final AlertDialog.Builder builder = new AlertDialog.Builder(context);
            builder.setTitle(context.getString(R.string.nueva_version));
            builder.setPositiveButton(context.getText(R.string.update), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    context.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse
                            ("market://details?id=com.movisat.ecosat")));
                    dialog.dismiss();
                }
            });

            builder.setNegativeButton(context.getString(R.string.cancelar), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });

            builder.setCancelable(false);
            dialog = builder.show();
        } catch (Exception ex) {

        }
    }
}
