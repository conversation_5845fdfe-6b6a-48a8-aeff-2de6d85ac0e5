package com.movisat.helpers;

import android.app.Activity;

import androidx.annotation.Nullable;

import com.environment.Environment;
import com.google.android.gms.maps.model.LatLng;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.google.maps.android.PolyUtil;
import com.movisat.database.DBUsuario;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.R;
import com.movisat.log.Logg;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class ElementAreaBoundsValidator {

    private static List<List<LatLng>> multiPolygonCoords = null;

    // Los geojson se obtienen de http://polygons.openstreetmap.fr/get_geojson.py?id={$id}&params=0,
    // el id en https://www.openstreetmap.org/#map=6/40.007/-2.488

    public static boolean isInside(Activity activity, LatLng pointToCheck) {
        // Si son 0-0, no se ha obtenido la posición del GPS
        boolean areZero = pointToCheck.latitude == 0 && pointToCheck.longitude == 0;
        if (!Environment.isCompanyMadridContenur && !areZero) return true;

        if (Config.getInstance().getValue("nombreDispositivo").equals("MOVISAT")) return true;

        if (!areZero){
            List<List<LatLng>> multiPolygonCoords = parseGeoJSON();
            if (multiPolygonCoords == null) return false;
            
            for (List<LatLng> polygon : multiPolygonCoords) {
                if (PolyUtil.containsLocation(pointToCheck, polygon, true)) {
                    return true;               
                }
            }
        }

        new InfoDialog(activity, activity.getString(R.string.atencion),
                activity.getString(R.string.posicion_fuera_de_area),
                InfoDialog.ICON_ALERT, new OnInfoDialogSelect() {
            @Override
            public void onSelectOption(int option) {
                Logg.error("ElementAreaBoundsValidator",
                      "Coordenadas no válidas: " + pointToCheck.latitude + ", " + pointToCheck.longitude);
            }
        }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER)
                .show();

        return false;
    }

    @Nullable
    private static List<List<LatLng>> parseGeoJSON() {
        if(multiPolygonCoords != null) return multiPolygonCoords;

        InputStream inputStream = null;
        try {
            inputStream = MainActivity.getInstance().getAssets().open("geojson/province_madrid.geojson");
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        JsonElement element = JsonParser.parseReader(new InputStreamReader(inputStream));
        JsonArray geoJson = element.getAsJsonObject().getAsJsonArray("geometries");
        JsonArray coordinates = geoJson.get(0).getAsJsonObject().getAsJsonArray("coordinates");
        multiPolygonCoords = new ArrayList<>();

        for (JsonElement e : coordinates) {
            List<LatLng> polygon = new ArrayList<>();
            for (JsonElement coordList : e.getAsJsonArray()) {
                for (JsonElement coordinate : coordList.getAsJsonArray()) {
                    double lat = coordinate.getAsJsonArray().get(1).getAsDouble();
                    double lng = coordinate.getAsJsonArray().get(0).getAsDouble();
                    polygon.add(new LatLng(lat, lng));
                }
            }
            multiPolygonCoords.add(polygon);
        }
        return multiPolygonCoords;
    }
}
