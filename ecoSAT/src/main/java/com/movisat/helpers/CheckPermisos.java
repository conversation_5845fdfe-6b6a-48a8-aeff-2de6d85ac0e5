package com.movisat.helpers;

import com.movisat.utilities.Config;

import java.util.Arrays;

/**
 * Helper para comprobar permisos en funci?n de visibilidad de
 * los men?s.
 */
public class CheckPermisos {

    private static CheckPermisos _instance = null;

    private CheckPermisos() {

    }

    public static CheckPermisos getInstance() {
        if (_instance == null)
            _instance = new CheckPermisos();

        return _instance;
    }


    /**
     * Comprobar si tiene permisos sobre alguna accion del men?.
     * @param idMenu
     * @return
     */
    public boolean isPermisoValid(int idMenu) {


        String menu = Config.getInstance().getValue("menu", "");

        String ids[] = menu.split(";");

        if (Arrays.asList(ids).contains(String.valueOf(idMenu))) {
            return true;
        }

        return false;
    }
}
