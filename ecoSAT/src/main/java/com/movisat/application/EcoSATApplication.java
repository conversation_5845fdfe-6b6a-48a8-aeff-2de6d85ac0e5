package com.movisat.application;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.nfc.NfcAdapter;
import android.preference.PreferenceManager;

import androidx.multidex.MultiDex;

import com.environment.Environment;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.events.OnReadedTag;
import com.movisat.log.Logg;
import com.movisat.managers.LFChainwayManager;
import com.movisat.managers.SoundManager;
import com.movisat.managers.UHFManager;
import com.movisat.rfid_uhf_u9000.U9000UHFManager;
import com.movisat.tags.Tag134;
import com.movisat.tags.TagUHF;
import com.movisat.utilities.UHFReadingToast;

import org.greenrobot.eventbus.EventBus;

import java.util.Date;

/**
 * Created by faroca on 16/09/2015.
 */
public class EcoSATApplication extends Application {


    private static EcoSATApplication instance;

    public EcoSATApplication() {
        instance = this;
    }

    public static SharedPreferences sharedPref;

    public static synchronized EcoSATApplication getInstance() {
        return instance;
    }

    @Override
    public void onCreate() {
        //MultiDex.install(this);
        super.onCreate();
        Logg.addProvider(new LoggProviderMyLogger());

        sharedPref = PreferenceManager.getDefaultSharedPreferences(this);
    }

    @Override
    protected void attachBaseContext(Context base) {

        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    public void initReaders() {
        if (!Environment.hasReaderNFC)
            Environment.hasReaderNFC = checkNfc();
        if (!Environment.hasReaderUHFC71)
            Environment.hasReaderUHFC71 = initReaderUHF();
        if (!Environment.hasReaderLFChainway)
            Environment.hasReaderLFChainway = initReaderLF();
        if (!Environment.hasReaderUHFU9000)
            Environment.hasReaderUHFU9000 = initReaderUHFU9000();
    }

    private boolean checkNfc() {
        NfcAdapter nfcAdapter = NfcAdapter.getDefaultAdapter(this);
        return nfcAdapter != null;
    }

    /**
     * Inicializa el lector UHF si el dispositivo es un smartphone CHAINWAY.
     *
     * @return true si el lector UHF se ha iniciado correctamente, false en otro caso.
     */
    private boolean initReaderUHF() {
        try {
            UHFManager uhfManager = UHFManager.get();
            if (!uhfManager.init())
                throw new Exception("El dispositivo no tiene un lector UHF válido.");

            uhfManager.getReader().setFrequencyMode(UHFManager.FREQ_MODE_ETSI_STANDARD_865_868_MHZ);

            // Listener para obtener las lecturas UHF
            UHFManager.get().setOnTagReadListener((tid, epc, readDate) -> {
                if (!MainActivity.getInstance().hasSincro()) return;
                TagUHF uhf = new TagUHF("", tid, readDate);
                EventBus.getDefault().post(new OnReadedTag(uhf));
            });

            // Listener para controlar los inicios y paradas de lectura
            UHFManager.get().setOnStatusChangedListener(new UHFManager.OnStatusChangeListener() {
                @Override
                public void onReadStarted() {
                    UHFReadingToast.get().showToast(30000);
                }

                @Override
                public void onReadStopped() {
                    UHFReadingToast.get().cancel();
                }
            });

            return true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().info(e.getMessage());
            return false;
        }
    }

    /**
     * Inicializa el lector RFID LF (125-134 MHz) si el dispositivo es un smartphone CHAINWAY.
     *
     * @return true si el lector RFID LF se ha iniciado correctamente, false en otro caso.
     */
    private boolean initReaderLF() {
        try {
            LFChainwayManager lfChainwayManager = LFChainwayManager.get();
            if (!lfChainwayManager.init())
                throw new Exception("El dispositivo no tiene un lector RFID LF válido.");

            // Listener para obtener las lecturas RFID LF
            LFChainwayManager.get().setOnTagReadListener((tag, readDate) -> {
                if (!MainActivity.getInstance().hasSincro()) return;
                Tag134 tag134 = new Tag134("", tag, readDate);
                EventBus.getDefault().post(new OnReadedTag(tag134));
            });

            // Listener para controlar los inicios y paradas de lectura
            LFChainwayManager.get().setOnStatusChangedListener(new LFChainwayManager.OnStatusChangeListener() {
                @Override
                public void onReadStarted() {
                    UHFReadingToast.get().showToast(30000);
                }

                @Override
                public void onReadStopped() {
                    UHFReadingToast.get().cancel();
                }
            });

            return true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().info(e.getMessage());
            return false;
        }
    }

    private boolean initReaderUHFU9000() {
        // SOLO TIENEN LA VERSION 9 DE ANDROID
        if (android.os.Build.VERSION.SDK_INT != 28) {
            Logg.info("No se puede inicializar el lector UHF U9000 porque no es la versión 9 de Android, es la versión " + android.os.Build.VERSION.SDK_INT);
            return false;
        }
        try {
            return U9000UHFManager.get().initRFID(tag -> {
                      if (!MainActivity.getInstance().hasSincro()) return;
                      TagUHF uhf = new TagUHF("", tag, new Date());
                      EventBus.getDefault().post(new OnReadedTag(uhf));
                  },
                  () -> SoundManager.getInstance(getBaseContext()).play(),
                  () -> getBaseContext()
            );
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().info(e.getMessage());
            return false;
        }
    }
}
