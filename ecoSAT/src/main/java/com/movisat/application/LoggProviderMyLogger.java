package com.movisat.application;

import android.util.Log;

import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.log.ILoggProvider;
import com.movisat.log.LoggLine;

public class LoggProviderMyLogger implements ILoggProvider {
    @Override
    public boolean isStarted() {
        return true;
    }

    @Override
    public void start() {
    }

    @Override
    public void write(LoggLine line) {
        switch (line.logType) {
            case DATABASE:
            case SYNCHRONIZATION:
            case DEBUG:
                return;
            case INFO:
            case WARNING:
            case CATASTROPHE:
            case ERROR:
                break;
        }

        String message = "[" + line.datetime + "] [" + line.logType.toString() + "] " + line.message;
        MyLoggerHandler.getInstance().info(message);
    }
}
