package com.movisat.services;

import android.location.Location;

import com.movisat.events.onChangeLocation;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

/**
 * Servicio que cachea la última localización obtenida.
 * <p>
 * Permite acceder a la última localización sin tener que esperar a recibir un nuevo evento.
 */
public class PositionService {

    private Location lastLocation;

    private static PositionService instance;

    private PositionService() {
    }

    public static PositionService getInstance() {
        if (instance == null) {
            instance = new PositionService();
            EventBus.getDefault().register(instance);
        }
        return instance;
    }


    @Subscribe
    public void onEvent(onChangeLocation event) {
        if (event != null && event.location != null)
            lastLocation = event.location;
    }

    public boolean hasLocationValid() {
        if (lastLocation == null) return false;
        if (lastLocation.getLatitude() == 0 && lastLocation.getLongitude() == 0) return false;
        return true;
    }

    public boolean isLocationRecent() {
        if (!hasLocationValid()) return false;
        if (System.currentTimeMillis() >= (lastLocation.getTime() + 2 * 60000)) // 2 minutos
            return false;
        return true;
    }


    public Location getLastLocation() {
        return lastLocation;
    }
}
