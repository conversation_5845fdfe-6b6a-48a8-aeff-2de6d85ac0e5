package com.movisat.services;

import java.util.List;

import com.movisat.ecosat.MainActivity;

import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;

public class SensorAcelerometro implements SensorEventListener {

	// Acelerometro
	private boolean isAndando = false;

	private float mLimit = 10;
	private float mLastValues[] = new float[3 * 2];
	private float mScale[] = new float[2];
	private float mYOffset;
	private float mLastDirections[] = new float[3 * 2];
	private float mLastExtremes[][] = { new float[3 * 2], new float[3 * 2] };
	private float mLastDiff[] = new float[3 * 2];
	private int mLastMatch = -1;

	public SensorAcelerometro(String modoRuta) {
		if (modoRuta.equals("peaton")) {
			if (MainActivity.getInstance()!=null ) {
				SensorManager sm = (SensorManager) MainActivity.getInstance()
						.getSystemService(Context.SENSOR_SERVICE);

				List<Sensor> sensors = sm.getSensorList(Sensor.TYPE_ACCELEROMETER);
				if (sensors.size() > 0) {
					sm.registerListener(this, sensors.get(0),
							SensorManager.SENSOR_DELAY_NORMAL);
				}

				int h = 480;
				mYOffset = h * 0.5f;
				mScale[0] = -(h * 0.5f * (1.0f / (SensorManager.STANDARD_GRAVITY * 2)));
				mScale[1] = -(h * 0.5f * (1.0f / (SensorManager.MAGNETIC_FIELD_EARTH_MAX)));
			}
		}
	}

	@Override
	protected void finalize() throws Throwable {
		// Quitamos servicio de acelerometro.
		SensorManager sm = (SensorManager) MainActivity.getInstance()
				.getSystemService(Context.SENSOR_SERVICE);
		sm.unregisterListener(this);

	}

	@Override
	public void onSensorChanged(SensorEvent event) {
		Sensor sensor = event.sensor;
		synchronized (this) {
			if (sensor.getType() == Sensor.TYPE_ORIENTATION) {
			} else {
				int j = (sensor.getType() == Sensor.TYPE_ACCELEROMETER) ? 1 : 0;
				if (j == 1) {
					float vSum = 0;
					for (int i = 0; i < 3; i++) {
						final float v = mYOffset + event.values[i] * mScale[j];
						vSum += v;

					}
					int k = 0;
					float v = vSum / 3;
					// Log.e("data", "data"+v);

					float direction = (v > mLastValues[k] ? 1
							: (v < mLastValues[k] ? -1 : 0));
					if (direction == -mLastDirections[k]) {
						// Direction changed
						int extType = (direction > 0 ? 0 : 1); // minumum or
																// maximum?
						mLastExtremes[extType][k] = mLastValues[k];
						float diff = Math.abs(mLastExtremes[extType][k]
								- mLastExtremes[1 - extType][k]);

						if (diff > mLimit) {

							boolean isAlmostAsLargeAsPrevious = diff > (mLastDiff[k] * 2 / 3);
							boolean isPreviousLargeEnough = mLastDiff[k] > (diff / 3);
							boolean isNotContra = (mLastMatch != 1 - extType);

							if (isAlmostAsLargeAsPrevious
									&& isPreviousLargeEnough && isNotContra) {

								setAndando(true);
								mLastMatch = extType;
							} else {
								setAndando(false);
								mLastMatch = -1;
							}
						}
						mLastDiff[k] = diff;
					}
					mLastDirections[k] = direction;
					mLastValues[k] = v;
				}
			}
		}
	}

	@Override
	public void onAccuracyChanged(Sensor sensor, int accuracy) {
		// TODO Auto-generated method stub

	}

	public boolean isAndando() {
		return isAndando;
	}

	private void setAndando(boolean isAndando) {
		this.isAndando = isAndando;
	}

}
