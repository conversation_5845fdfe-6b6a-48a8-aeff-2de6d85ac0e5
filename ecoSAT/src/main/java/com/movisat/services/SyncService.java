package com.movisat.services;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.R;
import com.movisat.utilities.Config;

public class SyncService extends Service {
    private static final String TAG = "SyncService";
    private static final String CHANNEL_ID = "SyncServiceChannel";
    private static final String COMPLETION_CHANNEL_ID = "CompletionChannel";
    private static Intent serviceIntent;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.e(TAG, "SyncService onCreate");
        createNotificationChannel(CHANNEL_ID,
              "Canal de Servicio de Sincronización",
              NotificationManager.IMPORTANCE_LOW);
        createNotificationChannel(COMPLETION_CHANNEL_ID,
              "Canal de Notificación de Finalización",
              NotificationManager.IMPORTANCE_HIGH);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.e(TAG, "SyncService onStartCommand");
        mostrarNotificacionDeInicio();
        new Thread(this::realizarSincronizacion).start();
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "SyncService onDestroy");
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void createNotificationChannel(String channelId, String channelName, int importance) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O)
            return;
        NotificationChannel serviceChannel = new NotificationChannel(channelId, channelName, importance);
        NotificationManager manager = getSystemService(NotificationManager.class);
        if (manager != null) {
            manager.createNotificationChannel(serviceChannel);
        }
    }

    private void realizarSincronizacion() {
        try {
            while (Config.getInstance().getValueEmpresa("ultSincro", null) == null) {
                Thread.sleep(5000);
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "Error: ", e);
            Thread.currentThread().interrupt();
        }
        Log.e(TAG, "Sincronización completada");
        mostrarNotificacionDeFinalizacion();
        stopSelf();
    }

    private void mostrarNotificacionDeInicio() {
        Intent notificationIntent = new Intent(this, MainActivity.class);

        PendingIntent pendingIntent = PendingIntent.getActivity(
              this,
              0,
              notificationIntent,
              PendingIntent.FLAG_IMMUTABLE
        );

        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
              .setContentTitle("Sincronización en curso")
              .setContentText("Descargando datos...")
              .setSmallIcon(R.drawable.ic_launcher)
              .setContentIntent(pendingIntent)
              .build();

        startForeground(1, notification);
    }

    private void mostrarNotificacionDeFinalizacion() {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        notificationIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        PendingIntent pendingIntent = PendingIntent.getActivity(
              this,
              0,
              notificationIntent,
              PendingIntent.FLAG_IMMUTABLE
        );

        Notification notification = new NotificationCompat.Builder(this, COMPLETION_CHANNEL_ID)
              .setContentTitle("Sincronización completada")
              .setContentText("La sincronización de datos se ha completado.")
              .setSmallIcon(R.drawable.ic_launcher)
              .setContentIntent(pendingIntent)
              .setAutoCancel(true)
              .build();

        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        if (notificationManager != null) {
            notificationManager.notify(2, notification);
        }
    }

    public static void startService(Context context) {
        serviceIntent = new Intent(context, SyncService.class);
        context.startService(serviceIntent);
    }

    public static void stopService(Context context) {
        if (serviceIntent != null) {
            context.stopService(serviceIntent);
        }
    }
}
