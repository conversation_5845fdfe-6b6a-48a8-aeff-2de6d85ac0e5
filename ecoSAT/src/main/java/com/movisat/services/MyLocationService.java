package com.movisat.services;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

import com.movisat.database.Posicion;
import com.movisat.ecosat.InfoActivity;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;
import com.movisat.helpers.CheckPermisos;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.services.KalmanLocationManager.UseProvider;
import com.movisat.utilities.Config;
import com.movisat.utilities.GPS;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Utils;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import androidx.core.app.NotificationCompat;
import android.util.Log;
import android.widget.Toast;

public class MyLocationService extends Service implements Runnable {

    private static final String LOCATION_CHANNEL_ID = "location_updates";

    private static MyLocationService instance = null;
    public static int numeroPosiciones = 100;
    public static int tiempoMaximo = 180; // 3 minutos
    public static KalmanLocationManager mKalmanLocationManager;
    private long timerLastSave = 0;
    private volatile boolean finalize = false;

    public static int NumSatelites = 0;
    public static int minSat = 4;
    private static final int MAX_ACCURACY = 25;

    private String modoRuta = "";
    public MyLocationListener listener;
    public Location previousBestLocation = null;
    public static List<Posicion> Posiciones = null;
    private Posicion posicion = null;

    private Posicion posicionAccuracy = null;

    //SensorAcelerometro listenAcelerometro;

    // Constant

    /**
     * Request location updates with the highest possible frequency on gps.
     * Typically, this means one update per second for gps.
     */
    private static long GPS_TIME = 2000;

    /**
     * For the network provider, which gives locations with less accuracy (less
     * reliable), request updates every 5 seconds.
     */
    private static final long NET_TIME = 5000;

    /**
     * For the filter-time argument we use a "real" value: the predictions are
     * triggered by a timer. Lets say we want 10 updates (estimates) per second
     * = update each 100 millis.
     */
    private static final long FILTER_TIME = 1000;

    // Intent intent;
    int counter = 0;

    public MyLocationService() {}

    @Override
    public void onCreate() {
        super.onCreate();

        instance = this;

        Posiciones = new ArrayList<Posicion>();

        // modoRuta = Config.getInstance().getValue("modoRuta", "vehiculo");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        startNotification();
        try {

            int auxInt = 0;
            String auxStr;
            try {
                auxStr = Config.getInstance().getValue("dracoGprs.regRuta", "" + numeroPosiciones);
                auxInt = Integer.parseInt(auxStr);
            } catch (Throwable e) {
            }
            if (auxInt >= 10)
                numeroPosiciones = auxInt;

            try {
                auxStr = Config.getInstance().getValue("dracoGprs.intRuta", "" + tiempoMaximo);
                auxInt = Integer.parseInt(auxStr);
            } catch (Throwable e) {
            }
            if (auxInt >= 15)
                tiempoMaximo = auxInt;

            try {
                auxStr = Config.getInstance().getValue("dracoGps.minSat", "" + minSat);
                auxInt = Integer.parseInt(auxStr);
            } catch (Throwable e) {
            }
            if (auxInt >= 1)
                minSat = auxInt;

            mKalmanLocationManager = new KalmanLocationManager(this);

            listener = new MyLocationListener();

            mKalmanLocationManager.requestLocationUpdates(UseProvider.GPS,
                    FILTER_TIME, GPS_TIME, NET_TIME, listener, true);

            Bundle extras = null;
            if (intent != null)
                extras = intent.getExtras();
            if (extras != null && extras.get("modo_ruta") != null)
                modoRuta = (String) intent.getExtras().get("modo_ruta");
            else
                modoRuta = "vehiculo";

            // Creo el hilo que se encarga de enviar posiciones cada X tiempo
            Thread t = new Thread(new MyLocationService(), "ThreadGPS");
            t.start();

        } catch (Throwable ex) {
            Log.e("onStartCommand", ex.getMessage());
            MyLoggerHandler.getInstance().error(ex);
        }
        return Service.START_STICKY;
    }

    public static MyLocationService getInstance() {
        if (instance == null)
            instance = new MyLocationService();

        return instance;
    }

    private void checkModoRuta() {
        Boolean gps_enabled = false;
        gps_enabled = GPS.isActiveGPS(MainActivity.getInstance());

        if (!gps_enabled) {

            new InfoDialog(
                    MainActivity.getInstance(),
                    "ATENCIÓN",
                    MainActivity.getInstance().getString(
                            R.string.grabar_gps),
                    InfoDialog.ICON_QUESTION,
                    new OnInfoDialogSelect() {

                        @Override
                        public void onSelectOption(int option) {
                            if (option == InfoDialog.BUTTON_YES) {
                                Intent intent = new Intent(
                                        android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                MainActivity.getInstance()
                                        .startActivityForResult(intent,
                                                MainActivity.ACT_MODO_RUTA);
                            }
                        }

                    }, InfoDialog.BUTTON_YES,
                    InfoDialog.POSITION_CENTER).show();
        }
    }

    private void startNotification() {

        try {

            ensureChannel();

            NotificationCompat.Builder mBuilder =
                    new NotificationCompat.Builder(this, LOCATION_CHANNEL_ID)
                    .setSmallIcon(R.drawable.ic_launcher_v0)
                    .setLargeIcon(
                            ((BitmapDrawable) getResources().getDrawable(
                                    R.drawable.ic_launcher)).getBitmap())
                    .setContentTitle("ECOSAT")
                    .setContentText("Ejecutando aplicación ECOSAT Móvil");



            Intent notIntent = new Intent(this, MainActivity.class);
            notIntent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
            PendingIntent contIntent = PendingIntent.getActivity(
                    this, 0, notIntent, PendingIntent.FLAG_IMMUTABLE);

            mBuilder.setContentIntent(contIntent);

            // añado funcionalidad para compatibilidad versiones superiores a 8.0
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startMyOwnForeground();
            } else
                startForeground(1, mBuilder.build());

        } catch (Throwable e) {

            e.printStackTrace();
        }
    }

    private void ensureChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager nm =
                    (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (nm.getNotificationChannel(LOCATION_CHANNEL_ID) == null) {
                NotificationChannel ch = new NotificationChannel(
                        LOCATION_CHANNEL_ID,
                        "Actualización de ubicación",
                        NotificationManager.IMPORTANCE_LOW);
                nm.createNotificationChannel(ch);
            }
        }
    }


    /*private int getNotificationIcon(NotificationCompat.Builder notificationBuilder) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return R.drawable.ic_launcher;
        } else
            return R.drawable.ic_launcher_v0;

    }*/



    private void startMyOwnForeground(){
        try {
            String NOTIFICATION_CHANNEL_ID = "com.movisat.ecosat";
            String channelName = "MOVISAT - TecnoMovilidad Service";
            NotificationChannel chan = new NotificationChannel(NOTIFICATION_CHANNEL_ID, channelName, NotificationManager.IMPORTANCE_NONE);
            chan.setLightColor(Color.BLUE);
            chan.setLockscreenVisibility(Notification.VISIBILITY_PRIVATE);
            NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            assert manager != null;
            manager.createNotificationChannel(chan);

            NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID);
            Notification notification = notificationBuilder.setOngoing(true)
                    .setSmallIcon(R.drawable.ic_launcher)
                    .setContentTitle("")
                    .setPriority(NotificationManager.IMPORTANCE_MIN)
                    .setCategory(Notification.CATEGORY_SERVICE)
                    .build();
            startForeground(2, notification);
        } catch (Exception e){
            Log.e("Error", e.getMessage());
        }
    }


    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
    }

    @Override
    public void onDestroy() {
        finalize = true;
        // handler.removeCallbacks(sendUpdatesToUI);
        super.onDestroy();
        CreatePacketPosiciones();
        Log.v("STOP_SERVICE", "DONE");
        // locationManager.removeUpdates(listener);
        // Remove location updates
        if (mKalmanLocationManager != null)
            mKalmanLocationManager.removeUpdates(listener);

    }

    // / Crear paquete de posiciones.
    public static void CreatePacketPosiciones() {

        if (Posiciones != null && Posiciones.size() > 0) {

            // Creamos paquete con las cien posiciones
            DBPacket dbp = new DBPacket();
            dbp.insert(new Packet(Packet.ENVIO_POSICIONES, Packet.PRIORIDAD_NORMAL, Posiciones));
            dbp.close();

            // Limpiamos posiciones.
            Posiciones = new ArrayList<Posicion>();
        }
    }

    public Posicion getLastPosicion() {
        return posicion;
    }

    public Posicion getLastPosicionAccuracy() {
        return posicionAccuracy;
    }

    private void SavePosicion(Location loc) {
        if (Posiciones == null)
            Posiciones = new ArrayList<Posicion>();

        float velocidad = (loc.getSpeed() / 1000) * 3600;

        // guardo
        posicion = new Posicion(loc.getLatitude(), loc.getLongitude(),
                velocidad, loc.getAltitude(), loc.getBearing(), loc.getTime());


        if (loc.hasAccuracy()) {
            if (loc.getAccuracy() <= MAX_ACCURACY) {
                posicionAccuracy = posicion;
            }
        }

        Posiciones.add(posicion);

        //TODO: Fran quitar en producción el Toast.
//        if (BuildConfig.DEBUG) {
//            Toast.makeText(getApplicationContext(), "Guardando posición actual",
//                    Toast.LENGTH_SHORT).show();
//        }

//        if (Posiciones.size() >= numeroPosiciones || Utils.getTime() - timerLastSave > tiempoMaximo) {
//            timerLastSave = Utils.getTime();
//            CreatePacketPosiciones();
//        }

        createPositionPacket();
    }

    // Esta es la función sincronizada a la que se llama cuando se reciben posiciones de
    // GPS y desde el hilo que controla el tiempo máximo para el envío de posiciones
    private void createPositionPacket() {
        synchronized (this) {
            if (Posiciones.size() >= numeroPosiciones ||
                    (Posiciones.size() > 0 && Utils.getTime() - timerLastSave > tiempoMaximo)) {
                timerLastSave = Utils.getTime();
                CreatePacketPosiciones();
            }
        }
    }

    public void setModoRuta(String modoRuta) {
        this.modoRuta = modoRuta;
    }

    private void SaveRutaBy(Location loc) {
        if (loc.getExtras() == null) {
            NumSatelites = 0;
        } else {
            int NumAnt = NumSatelites;

            NumSatelites = loc.getExtras().getInt("satellites", 0);
            if (NumAnt != NumSatelites) {
                // Evento
                if (InfoActivity.getInstance() != null) {
                    InfoActivity.getInstance();
                    InfoActivity.setNumSatelites2(NumSatelites);
                }
            }
            // Intent i = new Intent();
            // i.setAction("com.movisat.satelitesActualizados");
            // sendBroadcast(i);
        }

        float velocidadAnterior = 0;
        float velocidad = (loc.getSpeed() / 1000) * 3600;

        posicion = new Posicion(loc.getLatitude(), loc.getLongitude(),
                velocidad, loc.getAltitude(), loc.getBearing(), loc.getTime());

        if (NumSatelites < minSat) {

            // A efectos de centrado en el mapa cojemos cualquier posición
            // No es una posición válida para ruta
            /*MyLoggerHandler.getInstance().info(
                    String.format("Posición GPS de ruta descartada %s. (Sat:%s, Req:%s)",
                            Utils.datetimeToString(new Date(loc.getTime())
                                    , "dd/MM/yyyy HH:mm:ss"), "" + NumSatelites, "" + minSat));*/
            return;
        }

        if (previousBestLocation != null)
            velocidadAnterior = (previousBestLocation.getSpeed() / 1000) * 3600;

        // Tenemos buena posición ahora comprobamos que no es la misma
        if (previousBestLocation == null) {
            // Guardamos la posición
            SavePosicion(loc);
        } else {
            if (previousBestLocation.getLatitude() == loc.getLatitude()
                    && previousBestLocation.getLongitude() == loc
                    .getLongitude()) {
                return;
            }

            long segundos = (loc.getTime() - previousBestLocation.getTime()) / 1000;

            if (modoRuta.equals("vehiculo")) {
                if (velocidad < 5 && velocidadAnterior < 5) {
                    return;
                }

                if (velocidad < 5 && velocidad != 0) {
                    return;
                }

                // Rango en funciin de la velocidad.
                if (velocidad < 60) {
                    // Tengo que ver si la posición anterior y la nueva han
                    // pasado 2 segundos.
                    if (segundos < 2) {
                        return;
                    }
                } else if (velocidad > 60 && velocidad <= 100) {
                    if (segundos < 5) {
                        return;
                    }
                } else if (velocidad > 100) {
                    if (segundos < 10) {
                        return;
                    }
                }
            } else if (modoRuta.equals("peaton")) {
                if (segundos < 30) { // || !listenAcelerometro.isAndando()) {
                    return;
                }
            }
            SavePosicion(loc);
            //WriteInfoGPS_Logger(loc);
        }

        previousBestLocation = loc;
    }

    private void WriteInfoGPS_Logger(Location loc) {
        //Guardamos información de posición actualizada.
        MyLoggerHandler.getInstance().info(
                String.format("Posición GPS de ruta %s. Coord. (Lat: %s, Lng: %s)",
                        Utils.datetimeToString(new Date(loc.getTime())
                                , "dd/MM/yyyy HH:mm:ss"), "" + loc.getLatitude(), "" + loc.getLongitude()));
    }

    @Override
    public void run() {
        while (!finalize) {
            try {
                // Eviamos posiciones cada X tiempo
                createPositionPacket();
                Thread.sleep(1000);
            } catch (Throwable e) {
            }
        }
    }

	/*
     * private void AlgoritmoGuardarRuta(Location loc) {
	 * 
	 * try {
	 * 
	 * if (Posiciones == null) Posiciones = new ArrayList<Posicion>();
	 * 
	 * long tiempo = System.currentTimeMillis(); float velocidad =
	 * (loc.getSpeed() / 1000) * 3600;
	 * 
	 * if (tiempo_inicio > 0 && (tiempo - tiempo_inicio) > limiteTiempo) {
	 * CreatePacketPosiciones(); }
	 * 
	 * if (loc.hasAltitude() && loc.hasAccuracy()) { // Precisiin menor de 25
	 * mts if (loc.getAccuracy() <= Criteria.ACCURACY_FINE) { // posición buena
	 * if (tiempo_inicio == 0) { // Primera posicion posicion = new
	 * Posicion(loc.getLatitude(), loc.getLongitude(), velocidad,
	 * loc.getAltitude(), loc.getBearing(), loc.getTime());
	 * Posiciones.add(posicion); tiempo_inicio = tiempo; } else { // demas
	 * posiciones if (tiempo - tiempo_inicio > intervalo) { // guardo posicion =
	 * new Posicion(loc.getLatitude(), loc.getLongitude(), velocidad,
	 * loc.getAltitude(), loc.getBearing(), loc.getTime());
	 * 
	 * Posiciones.add(posicion);
	 * 
	 * tiempo_inicio = tiempo; }
	 * 
	 * } } } if (Posiciones.size() >= numeroPosiciones) {
	 * CreatePacketPosiciones(); } } catch (Throwable e) {
	 * MyLoggerHandler.getInstance().error(e); } }
	 */

    public class MyLocationListener implements LocationListener {
        private LocationManager locationManager = null;

        public MyLocationListener() {
            locationManager = (LocationManager) getSystemService(LOCATION_SERVICE);
        }

        public void onLocationChanged(final Location loc) {


            // Vamos a grabar de otra forma.
            if (loc.getProvider().equals(KalmanLocationManager.KALMAN_PROVIDER)) {
                if (!Double.isNaN(loc.getLatitude()) && !Double.isNaN(loc.getLongitude()))
                    SaveRutaBy(loc);
            }
        }

        public void onProviderDisabled(String provider) {

            synchronized (provider) {
                checkModoRuta();
                Toast.makeText(getApplicationContext(), "Gps desactivado",
                        Toast.LENGTH_SHORT).show();
            }

        }

        public void onProviderEnabled(String provider) {
            Toast.makeText(getApplicationContext(), "Gps activo",
                    Toast.LENGTH_SHORT).show();
        }

        public void onStatusChanged(String provider, int status, Bundle extras) {

        }

    }

}
