package com.movisat.bll;

import android.graphics.Color;

import com.movisat.database.MedidaZonas;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.fragment.GesElemMapFragment;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.utilities.Config;

public class ZonasVerdesManager {
	
	private static ZonasVerdesManager _instance = null;

	private ZonasVerdesManager() {

	}

	public static ZonasVerdesManager getInstance() {

		if (_instance == null) {
			_instance = new ZonasVerdesManager();
		}

		return _instance;
	}

	
	public  void sendPacketMedida() {
		// Terminamos la medida, tengo que
		// recuperar
		// y
		// dejarlo en cola para enviar.
		try {
			int codigoGrupo = Integer.parseInt(Config.getInstance()
					.getValueUsuario("grupoMedir", "0"));
			long fechaInicio = Long.parseLong(Config.getInstance()
					.getValueUsuario("inicioMedida", "0"));

			String papelerasMedidas = Config.getInstance().getValueUsuario(
					"papelerasMedidas", "");

			if (codigoGrupo > 0)
				GesElemMapFragment.getInstance().showArea(codigoGrupo, false,
						Color.MAGENTA);
		
			
			//  Crear paquete para enviar medidas.
			/* Insertamos sensor en db */

			MedidaZonas medida = new MedidaZonas(codigoGrupo, MainActivity.getInstance().getEmpresa(),
					papelerasMedidas, fechaInicio, System.currentTimeMillis(), MainActivity.getInstance().getUsuario());
			
			DBPacket dbp = new DBPacket();
			dbp.insert(new Packet(Packet.MEDIDA_ZONAS_VERDES,
					Packet.PRIORIDAD_NORMAL, medida));
			dbp.close();
			MyLoggerHandler.getInstance().info("Paquete creado para enviar medida de parque.");


		} catch (Exception ex) {
			MyLoggerHandler.getInstance().error(ex);
		}

		
		
		
		

		Config.getInstance().setValueUsuario("papelerasMedidas", "");

		Config.getInstance().setValueUsuario("areaMedir", "0");

		Config.getInstance().setValueUsuario("inicioMedida", "0");

		Config.getInstance().setValueUsuario("medidaIniciada", "0");

	}
	
}
