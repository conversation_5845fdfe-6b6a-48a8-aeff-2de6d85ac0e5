package com.movisat.bll;

import java.util.ArrayList;
import java.util.Hashtable;

import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.drawable.BitmapDrawable;
import android.view.Display;

import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.GoogleMap.OnCameraChangeListener;
import com.google.android.gms.maps.GoogleMap.OnInfoWindowClickListener;
import com.google.android.gms.maps.GoogleMap.OnMapClickListener;
import com.google.android.gms.maps.GoogleMap.OnMarkerClickListener;
import com.google.android.gms.maps.GoogleMap.OnMarkerDragListener;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.movisat.database.DBElemento;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBEstados;
import com.movisat.database.ElementoModelo;
import com.movisat.database.Estado;
import com.movisat.database.Incidencia;
import com.movisat.database.ItemMapa;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Config;
import com.movisat.utilities.Utils;

public class MyMapManager implements OnMapClickListener, OnMarkerClickListener,
		OnCameraChangeListener, OnInfoWindowClickListener, OnMarkerDragListener {

	private GoogleMap map = null;
	// Constantes
	private static final int MINZOOM_CLICKABLE = 18;

	// Mensajes
	private static final int MSG_REFRESH_ELEMENTOS = 1;
	private static final int MSG_REFRESH_INCIDENCIAS = 2;

	// Elementos
	private ArrayList<ItemMapa> elementos = null;

	private String modelosActivos;

	// Tamaño de iconos.
	private static int iconWidth, iconHeight;

	// Tablas hash para almacenar los iconos de los modelos y
	// la visibilidad de cada uno
	private Hashtable<Integer, BitmapDescriptor> modelosIconos = null;
	private Hashtable<Integer, Boolean> modelosVisibles = null;

	// Lista de incidencias en memoria que corresponde con las
	// que hay en en mapa
	private static ArrayList<Incidencia> incidencias = null;
	private Hashtable<Integer, Boolean> incidenciasEstadosVisibles = null;

	// Mapa
	private float actualZoom = 6;

	public MyMapManager(GoogleMap map) {
		this.map = map;

		// Redirecciono algunos eventos del mapa al cluster
		map.setOnCameraChangeListener(this);
		map.setOnInfoWindowClickListener(this);
		map.setOnMarkerDragListener(this);
		map.setOnMarkerClickListener(this);
		map.setOnMapClickListener(this);

		loadElementos();
		setSizeIconos();
		loadIconosModelos(modelosActivos);
		loadEstadosIncidencias();
		setLatLng();
		setTipoMapa();
	}

	// region METODOS PARA USAR EL MAPA.
	/**
	 * Centra el mapa y establece nivel de zoom
	 */
	public void centerMap(LatLng pos, float zoom) {
		centerMap(pos, zoom, false, "");
	}

	/**
	 * Centra el mapa y establece nivel de zoom
	 */
	public void centerMap(double lat, double lng, float zoom) {
		centerMap(new LatLng(lat, lng), zoom, false, "");
	}

	public void centerMap(LatLng pos, float zoom, boolean addMarker,
			String title) {

		try {

			map.animateCamera(CameraUpdateFactory.newLatLngZoom(pos, zoom));

			if (addMarker) {

				map.clear();

				MarkerOptions options = new MarkerOptions();

				options.draggable(false);
				options.position(pos);
				options.title(title);

				map.addMarker(options);
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}

	}

	// endregion

	private void setTipoMapa() {

		String mapType = Config.getInstance().getValueUsuario("vistaMapa",
				"normal");

		// Se establece el tipo de mapa
		if (mapType.equals("satelite"))
			map.setMapType(GoogleMap.MAP_TYPE_SATELLITE);
	}

	private void setLatLng() {
		double lat = 0, lon = 0;
		actualZoom = 6;

		// Recupero la última información del usuario en cuanto a posición
		// del mapa y zoom
		lat = Double.parseDouble(Config.getInstance().getValueUsuario("miLat", "0"));
		lon = Double.parseDouble(Config.getInstance().getValueUsuario("miLon", "0"));
		actualZoom = Float.parseFloat(Config.getInstance().getValueUsuario("miZoom", "18"));

		// Si es la primera vez que entra el usuario centro en
		// la Puerta del Sol de Madrid
		if (lat == 0 && lon == 0) {

			lat = 40.416876;
			lon = -3.703304;

			Config.getInstance().setValueUsuario("miLat", "" + lat);
			Config.getInstance().setValueUsuario("miLon", "" + lon);
			Config.getInstance().setValueUsuario("miZoom", "" + actualZoom);
		}

		// Centro el mapa sobre la última posición del usuario
		centerMap(lat, lon, actualZoom);
	}

	/**
	 * Carga en memoria las tablas hash con la visibilidad de las incidencias
	 * según estados
	 */
	private void loadEstadosIncidencias() {

		try {

			// Obtengo todos los estados y los meto en una tabla hash
			DBEstados dbEstados = new DBEstados();
			ArrayList<Estado> estados = dbEstados.getAll(MainActivity
					.getInstance().getEmpresa());

			if (incidenciasEstadosVisibles != null)
				incidenciasEstadosVisibles.clear();

			incidenciasEstadosVisibles = new Hashtable<Integer, Boolean>(
					dbEstados.getCount(MainActivity.getInstance().getEmpresa()));

			dbEstados.close();

			// Recupero la configuraciin del usuario en cuanto a modelos
			// visibles
			String visModels = Config.getInstance().getValueUsuario(
					"inciVisibles", "ninguno");
			if (estados == null)
				return;
			for (Estado estadoInci : estados) {

				try {

					// Asigno la visibilidad del dependiendo de la
					// configuraciin del ususario
					if (visModels.equals("todos"))
						incidenciasEstadosVisibles.put(
								estadoInci.getIdExterno(), true);
					else if (visModels.equals("ninguno"))
						incidenciasEstadosVisibles.put(
								estadoInci.getIdExterno(), false);
					else {

						incidenciasEstadosVisibles.put(
								estadoInci.getIdExterno(), false);

						String arrayModels[] = visModels.split(",");

						// Si esti en la lista lo pongo como visible
						for (String id : arrayModels) {

							if (Integer.parseInt(id) == estadoInci
									.getIdExterno()) {

								incidenciasEstadosVisibles.put(
										estadoInci.getIdExterno(), true);

								break;
							}

						}

					}

				} catch (Throwable e) {
					MyLoggerHandler.getInstance().error(e);
					e.printStackTrace();
				}

			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}

	}

	private void loadIconosModelos(String modelosActivos) {
		try {

			// Obtengo todos los modelos y meto en una tabla hash
			// todos los iconos
			DBElementoModelo dbModelo = new DBElementoModelo();
			ArrayList<ElementoModelo> modelos = dbModelo.getAll(MainActivity
					.getInstance().getEmpresa());


			if (modelosIconos != null)
				modelosIconos.clear();

			if (modelosVisibles != null)
				modelosVisibles.clear();

			modelosIconos = new Hashtable<Integer, BitmapDescriptor>(
					dbModelo.getCount(MainActivity.getInstance().getEmpresa()));
			modelosVisibles = new Hashtable<Integer, Boolean>(
					dbModelo.getCount(MainActivity.getInstance().getEmpresa()));
			dbModelo.close();
			if (modelos == null)
				return;

			if (modelosActivos.length() <= 0) {
				modelosActivos = Config.getInstance().getValueUsuario(
						"modelVisibles", "todos");
			}

			for (ElementoModelo modelo : modelos) {

				try {

					// Redimensiono el icono
					Bitmap iconoModelo = Utils.ResizeImage(
							Utils.GetBitmapToArrayBytes(modelo.getIcono()),
							iconWidth, iconHeight, 0);

					BitmapDrawable bmp = new BitmapDrawable(MainActivity
							.getInstance().getResources(), iconoModelo);

					// Meto el icono en la tabla hash
					modelosIconos
							.put(modelo.getIdExterno(), BitmapDescriptorFactory
									.fromBitmap(bmp.getBitmap()));

					// Asigno la visibilidad del icono dependiendo de la
					// configuraciin del ususario
					if (modelosActivos.equals("todos"))
						modelosVisibles.put(modelo.getIdExterno(), true);
					else if (modelosActivos.equals("ninguno"))
						modelosVisibles.put(modelo.getIdExterno(), false);
					else {

						modelosVisibles.put(modelo.getIdExterno(), false);

						String arrayModels[] = modelosActivos.split(",");

						// Si esti en la lista lo pongo como visible
						for (String id : arrayModels) {

							if (Integer.parseInt(id) == modelo.getIdExterno()) {

								modelosVisibles
										.put(modelo.getIdExterno(), true);

								break;
							}

						}

					}

				} catch (Throwable e) {
					MyLoggerHandler.getInstance().error(e);
					e.printStackTrace();
				}

			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}
	}

	private void setSizeIconos() {
		// Calculo las dimensiones de los iconos en funciin de
		// la resoluciin de pantalla
		Display display = MainActivity.getInstance().getWindowManager()
				.getDefaultDisplay();

		Point size = new Point();
		display.getSize(size);

		if (size.x <= 320)
			iconWidth = iconHeight = 32;
		else if (size.x >= 1000)
			iconWidth = iconHeight = 96;
		else
			iconWidth = iconHeight = 48;
	}

	private void loadElementos() {

		modelosActivos = Config.getInstance().getValueUsuario("modelVisibles",
				"todos");

		DBElemento dbElem = new DBElemento();
		if (modelosActivos.equals("todos")) {
			elementos = dbElem.getAll(MainActivity.getInstance().getEmpresa());
		} else if (!modelosActivos.equals("ninguno"))
			elementos = dbElem.getAllByModel(MainActivity.getInstance()
					.getEmpresa(), modelosActivos);

		dbElem.close();

	}

	// region EVENTOS MAPA
	@Override
	public boolean onMarkerClick(Marker arg0) {
		return false;
	}

	@Override
	public void onMapClick(LatLng arg0) {

	}

	@Override
	public void onMarkerDrag(Marker arg0) {

	}

	@Override
	public void onMarkerDragEnd(Marker arg0) {

	}

	@Override
	public void onMarkerDragStart(Marker arg0) {

	}

	@Override
	public void onInfoWindowClick(Marker arg0) {

	}

	@Override
	public void onCameraChange(CameraPosition arg0) {

	}

	// endregion

}
