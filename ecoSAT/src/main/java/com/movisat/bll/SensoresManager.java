package com.movisat.bll;

import static java.lang.Integer.parseInt;

import com.environment.Environment;
import com.movisat.database.DBElemento;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBOperationsDone;
import com.movisat.database.DBSensoresLavado;
import com.movisat.database.DBSensoresNivelesLlenado;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModelo;
import com.movisat.database.OperationsEnum;
import com.movisat.database.SensorLavado;
import com.movisat.database.SensorNivelLLenado;
import com.movisat.database.SensorPesoVertedero;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.fragment.GestionElementos;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.utilities.Config;
import com.movisat.utilities.Utils;

public class SensoresManager {

    private static SensoresManager _instance = null;

    private SensoresManager() {
    }

    public static SensoresManager getInstance() {
        if (_instance == null) {
            _instance = new SensoresManager();
        }
        return _instance;
    }

    /*
     * Enviar sensor de lavado a servidor.
     */
    public boolean sendSensorLavado(Elemento elemento) {
        int codigoMovil = parseInt(Config.getInstance().getValue("codigoDispositivo", "0"));
        SensorLavado s = new SensorLavado(elemento.getId(), codigoMovil,
              elemento.getEmpresa(), System.currentTimeMillis());
        try {

            /* Insertamos sensor en db */
            DBSensoresLavado dbSensor = new DBSensoresLavado();
            dbSensor.insert(s);
            dbSensor.close();

            DBPacket dbp = new DBPacket();
            dbp.insert(new Packet(Packet.SENSOR_LAVADO,
                  Packet.PRIORIDAD_NORMAL, s));
            dbp.close();

            //Insertamos operación realizada
            DBOperationsDone dbOperationsDone = new DBOperationsDone();

            String message;
            if (Environment.isSoftIndra) {

                DBElementoModelo dbElementoModelo = new DBElementoModelo();
                ElementoModelo elementoModelo = dbElementoModelo.getByID(elemento.getModelo(),
                      elemento.getEmpresa());
                dbElementoModelo.close();

                message = elemento.getNombre() + " "
                      + "\nMod: " + elementoModelo.getNombre()
                      + "\nCod físico: " + elemento.getCodFisico();
            } else
                message = elemento.toString();

            dbOperationsDone.insert(
                  OperationsEnum.CLEAN,
                  s.getCodigoMovil(),
                  elemento.getId(),
                  s.getFechaRegistro(),
                  elemento.getPosition().latitude,
                  elemento.getPosition().longitude,
                  "\n" + message);
            dbOperationsDone.close();

            GestionElementos.getInstance().marcarLavado(elemento.getId());
        } catch (Exception ex) {
            MyLoggerHandler.getInstance().error(ex);
            return false;
        }
        return true;
    }

    /*
     * Enviar sensor de nivel de llenado a servidor.
     */
    public boolean sendSensorNivelLlenado(Elemento elemento, int codigoMovil, int fraccion,
                                          long fecha, int vaciado_parcial) {
        if (fecha == 0)
            fecha = System.currentTimeMillis();
        //TODO: Si viene el id de elemento a cero false.

        SensorNivelLLenado s = new SensorNivelLLenado(elemento.getId(), codigoMovil,
              elemento.getEmpresa(), fraccion, fecha, vaciado_parcial);
        try {

            /* Insertamos sensor en db */
            DBSensoresNivelesLlenado dbSensor = new DBSensoresNivelesLlenado();
            dbSensor.insert(s);
            dbSensor.close();

            DBPacket dbp = new DBPacket();
            dbp.insert(new Packet(Packet.SENSOR_NIVEL_LLENADO, Packet.PRIORIDAD_NORMAL, s));
            dbp.close();

            //Insertamos operación realizada
            DBOperationsDone dbOperationsDone = new DBOperationsDone();
            dbOperationsDone.insert(
                  OperationsEnum.LEVEL,
                  s.getCodigoMovil(),
                  elemento.getId(),
                  s.getFechaRegistro(),
                  elemento.getPosition().latitude,
                  elemento.getPosition().longitude,
                  ": Fracción " + fraccion + "\n" + elemento);
            dbOperationsDone.close();

            DBElemento dbElemento = new DBElemento();
            elemento.setFechaUltRecogida(Utils.datetimeToString(new java.util.Date(fecha), "yyyy-MM-dd HH:mm:ss"));
            dbElemento.update(elemento);
            dbElemento.close();

            GestionElementos.getInstance().marcarNivelLlenado(elemento.getId());
        } catch (Exception ex) {
            MyLoggerHandler.getInstance().error(ex);
            return false;
        }
        return true;
    }

    /*
     * Enviar sensor de peso en vertedero a servidor.
     */

    public boolean sendSensorPesoEnVertedero(int empresa, long fechaInicio,
                                             long fechaFin, int pesoVertedero, int planchada) {
        SensorPesoVertedero s = new SensorPesoVertedero(0, empresa,
              fechaInicio, fechaFin, pesoVertedero, planchada);
        try {

            DBPacket dbp = new DBPacket();
            dbp.insert(new Packet(Packet.SENSOR_PESO_VERTEDERO,
                  Packet.PRIORIDAD_NORMAL, s));
            dbp.close();

            MyLoggerHandler.getInstance().info(String.format("Paquete creado de ticket de vertedero con peso %d",
                  pesoVertedero));

            // Añadir todos los registros que tenemos como procesados.
            DBSensoresNivelesLlenado dbSensor = new DBSensoresNivelesLlenado();
            dbSensor.setProcesados(empresa);
            dbSensor.close();
        } catch (Exception ex) {
            MyLoggerHandler.getInstance().error(ex);
            return false;
        }
        return true;
    }

    public boolean sendSensorProcesado(Elemento elemento) {
        try {
            DBPacket dbp = new DBPacket();
            dbp.insert(new Packet(Packet.SENSOR_ELEMENTO_RECOGIDO,
                  Packet.PRIORIDAD_NORMAL, elemento));
            dbp.close();

            MyLoggerHandler.getInstance().info(String.format("%s paquete creado con recogida de elemento %d",
                  elemento.toString(), elemento.getIdExterno()));

            //Insertamos operación realizada
            DBOperationsDone dbOperationsDone = new DBOperationsDone();

            String message;
            if (Environment.isSoftIndra) {

                DBElementoModelo dbElementoModelo = new DBElementoModelo();
                ElementoModelo elementoModelo = dbElementoModelo.getByID(elemento.getModelo(),
                      elemento.getEmpresa());
                dbElementoModelo.close();

                message = elemento.getNombre() + " "
                      + "\nMod: " + elementoModelo.getNombre()
                      + "\nCod físico: " + elemento.getCodFisico();
            } else
                message = elemento.toString();

            dbOperationsDone.insert(
                  OperationsEnum.COLLECT,
                  MainActivity.getInstance().getCodigoDispositivo(),
                  elemento.getId(),
                  new java.util.Date().getTime(),
                  elemento.getPosition().latitude,
                  elemento.getPosition().longitude,
                  "\n" + message);
            dbOperationsDone.close();

            GestionElementos.getInstance().marcarRecogida(elemento.getId());
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            return false;
        }
        return true;
    }

}
