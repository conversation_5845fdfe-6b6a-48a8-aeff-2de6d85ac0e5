package com.movisat.utils;

import android.util.Log;

import java.io.File;
import java.io.FileWriter;

public class LFPowerUtils {

    private static String TAG = "PowerUtilLFU9000";
    //private final static String set_sam = "/proc/gpiocontrol/set_sam";
    private final static String set_id = "/proc/gpiocontrol/set_id";
    private final static String set_uhf = "/proc/gpiocontrol/set_uhf";
    static boolean isOpened = false;

    public static void power(String id) {
        power1(id);
        power2(id);

    }


    public static void power1(String id) {
        try {
            File file = new File(set_id);
            Log.d(TAG, "power: " + file.getPath());
            FileWriter localFileWriterOn = new FileWriter(new File(set_id));
            localFileWriterOn.write(id);
            localFileWriterOn.close();

            if(id.equals("1")) isOpened = true;
            else isOpened = false;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void power2(String id) {
        try {

            File file = new File(set_uhf);
            Log.d(TAG, "power: " + file.getPath());
            FileWriter localFileWriterOn = new FileWriter(new File(set_uhf));
            localFileWriterOn.write(id);
            localFileWriterOn.close();

            if(id.equals("1")) isOpened = true;
            else isOpened = false;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static boolean isOpened() {
        return isOpened;
    }
}
