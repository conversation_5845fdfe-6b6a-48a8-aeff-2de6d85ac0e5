package com.movisat.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;

import java.util.ArrayList;

public class GlobalUtils {

    public static boolean isNullOrEmptyString(String str) {
        if (str != null && !str.isEmpty())
            return false;
        return true;
    }

    public static boolean isNullOrEmptyArrayList(ArrayList arrayList) {
        if (arrayList == null) return true;
        if (arrayList.isEmpty()) return true;
        return false;
    }

    public static boolean isAppInstalled(String packageName, Context context) {
        try {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                context.getPackageManager().getPackageInfo(packageName, 0);
            } else {
                context.getPackageManager().getPackageInfo(packageName, PackageManager.PackageInfoFlags.of(0));
            }
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    public static void openURL(String url, Context context) {
        try {
            Uri uri = Uri.parse(url); // missing 'http://' will cause crashed
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            context.startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void openApp(String packageName, Context context) {
        try {
            Intent launchIntent = context.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                context.startActivity(launchIntent);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
