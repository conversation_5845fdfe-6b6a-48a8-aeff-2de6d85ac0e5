package com.movisat.utils;

import android.util.Log;

import java.math.BigInteger;

/**
 * Created by moxiaomo
 * on 2020/7/13
 */
public class LFByteUtils {

    //將16進制字符串轉換為10進制數字
    public static int decodeHEX(String hexs) {
        BigInteger bigint=new BigInteger (hexs, 16);
        int numb=bigint.intValue ();
        return numb;
    }

    /**
     * 取反
     */
    public static String parseHex2Opposite(String str) {
        String hex;
        //十六进制转成二进制
        byte[] er=parseHexStr2Byte (str);

        //取反
        byte erBefore[]=new byte[er.length];
        for (int i=0; i < er.length; i++) {
            erBefore[i]=(byte) ~er[i];
        }

        //二进制转成十六进制
        hex=parseByte2HexStr (erBefore);

        // 如果不够校验位的长度，补0,这里用的是两位校验
        hex=(hex.length () < 2 ? "0" + hex : hex);

        return hex;
    }


    public static String checkcode_0007(String para) {
        int length=para.length () / 2;
        String[] dateArr=new String[length];

        for (int i=0; i < length; i++) {
            dateArr[i]=para.substring (i * 2, i * 2 + 2);
        }
        String code="00";
        for (int i=0; i < dateArr.length; i++) {
            code=xor (code, dateArr[i]);
        }
        if (code.length () == 1) {
            code="0" + code;
            return code;
        } else {
            return code;
        }
    }


    private static String xor(String strHex_X, String strHex_Y) {
        //将x、y转成二进制形式
        String anotherBinary=Integer.toBinaryString (Integer.valueOf (strHex_X, 16));
        String thisBinary=Integer.toBinaryString (Integer.valueOf (strHex_Y, 16));
        String result="";
        //判断是否为8位二进制，否则左补零
        if (anotherBinary.length () != 8) {
            for (int i=anotherBinary.length (); i < 8; i++) {
                anotherBinary="0" + anotherBinary;
            }
        }
        if (thisBinary.length () != 8) {
            for (int i=thisBinary.length (); i < 8; i++) {
                thisBinary="0" + thisBinary;
            }
        }
        //异或运算
        for (int i=0; i < anotherBinary.length (); i++) {
            //如果相同位置数相同，则补0，否则补1
            if (thisBinary.charAt (i) == anotherBinary.charAt (i))
                result+="0";
            else {
                result+="1";
            }
        }
        Log.e ("code", result);
        return Integer.toHexString (Integer.parseInt (result, 2));
    }

    public static String convertHexToString(String hex) {

        StringBuilder sb=new StringBuilder ();

        //49204c6f7665204a617661 split into two characters 49, 20, 4c...
        for (int i=0; i < hex.length () - 1; i+=2) {

            //grab the hex in pairs
            String output=hex.substring (i, (i + 2));
            //convert hex to decimal
            int decimal=Integer.parseInt (output, 16);
            //convert the decimal to character
            sb.append ((char) decimal);
        }

        return sb.toString ();
    }

    public static String convertStringToHex(String str) {

        char[] chars=str.toCharArray ();

        StringBuffer hex=new StringBuffer ();
        for (int i=0; i < chars.length; i++) {
            hex.append (Integer.toHexString ((int) chars[i]));
        }

        return hex.toString ();
    }

    public static byte getXor(byte[] buff, int length) {

        byte temp=buff[0];

        for (int i=1; i < length; i++) {
            temp^=buff[i];
        }

        return temp;
    }

    public static String bytesToHexString(byte[] bArr) {
        StringBuffer sb=new StringBuffer (bArr.length);
        String sTmp;

        for (int i=0; i < bArr.length; i++) {
            sTmp=Integer.toHexString (0xFF & bArr[i]);
            if (sTmp.length () < 2)
                sb.append (0);
            sb.append (sTmp.toUpperCase ());
        }

        return sb.toString ();
    }

    public static String bytesToHexString2(byte[] bArr) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bArr) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }

    /**
     * 将二进制转换成十六进制
     */
    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb=new StringBuffer ();
        for (int i=0; i < buf.length; i++) {
            String hex=Integer.toHexString (buf[i] & 0xFF);
            if (hex.length () == 1) {
                hex='0' + hex;
            }
            sb.append (hex.toUpperCase ());
        }
        return sb.toString ();
    }

    /**
     * 将十六进制转换为二进制
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length () < 1) {
            return null;
        }
        byte[] result=new byte[hexStr.length () / 2];
        for (int i=0; i < hexStr.length () / 2; i++) {
            int high=Integer.parseInt (hexStr.substring (i * 2, i * 2 + 1), 16);
            int low=Integer.parseInt (hexStr.substring (i * 2 + 1, i * 2 + 2), 16);
            result[i]=(byte) (high * 16 + low);
        }
        return result;
    }


    public static String rev(String ox) {
        byte b[]=ox.getBytes ();
        byte result[]=new byte[b.length];
        for (int i=b.length - 1, j=0; i >= 0; i--, j++)
            result[j]=b[i];
        return new String (result);
    }

    public static String showResultHEX(byte[] buff) {

        String temStr= bytesToHexString (buff);
        String hexStr = convertHexToString(temStr);
        //String finalText=ByteUtils.rev (hexStr.substring (1, 27)); //26 es la longitud de su trama
        String finalText = temStr.substring(temStr.length()-6);

        return finalText;
    }

    public static String swapString(String str){
        String res = "";
        for (int i = 0; i < str.length() - 1; i = i + 2) {
            char aux1 = str.charAt(i);
            char aux2 = str.charAt(i + 1);
            res = str.charAt(i) + "" + str.charAt(i + 1) + "" + res;
        }

        return res;
    }

    public static String showResultASCII(byte[] buff) {

        String hex=LFByteUtils.bytesToHexString (buff);
        String ASCIItoHex=LFByteUtils.convertHexToString (hex);
        String finalText=LFByteUtils.rev (ASCIItoHex.substring (1, 7));

        return finalText;
    }

    public static String showALLResultASCII(byte[] buff) {

        String hex = LFByteUtils.bytesToHexString(buff);
        String hexToASCII = LFByteUtils.convertHexToString(hex);
        String finalText = LFByteUtils.rev(hexToASCII.substring(1, 17));

        return finalText;
    }

    public static String showLIPASAMResultASCII(byte[] buff) {

        String hex=LFByteUtils.bytesToHexString (buff);
        String ASCIItoHex=LFByteUtils.convertHexToString (hex);
        String finalText=LFByteUtils.rev (ASCIItoHex.substring (1, 9));

        return finalText;
    }

    public static String showFullResultASCII(byte[] buff) {

        String hex = LFByteUtils.bytesToHexString(buff);
        String ASCIItoHex = LFByteUtils.convertHexToString(hex);
        String finalText = LFByteUtils.rev(ASCIItoHex.substring(1, ASCIItoHex.length() - 2));

        return finalText;
    }

    public static boolean hasValue(byte[] id) {
        if (id.length < 30) return false;

        for(int i=0; i<29; i++){ //el tag tiene longitud 30 en LF
            if(id[i] != 0) return true;
        }
        return false;
    }
}
