package com.movisat.utilities;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.synchronize.DBSynchro;

import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class Config {
    public static final String TAG = "Config";
    public static final String TAG_LARGO_Y_CORTO = "tagLargoYcorto";
    public static final String TAG_UHF_EXTENDIDO = "tagUHFExtendido";
    public static final String INCIDENCIA_IMPROCEDENTE = "incidenciaImprocedente";
    public static final String FREC_PROCESADO = "frecProcesado";
    public static final String TIPO_SOFT = "tipoSoft";
    public static final String COMPANY_ID = "loginEmpresa";
    private static final String sync = "Config_sync";
    private static final Queue<ContentValues> confQueue = new LinkedList<>();
    private static final Map<String, String> cache = new ConcurrentHashMap<>();
    private static final ExecutorService dbExecutor = Executors.newSingleThreadExecutor();
    private static Config instance = null;
    private static int empresa;
    private static int usuario;
    private final String TABLE_NAME = "conf";
    private SQLiteDatabase db = null;

    private Config() {
        open();
        loadInitialConfig(true);
    }

    public static Config getInstance() {
        synchronized (sync) {
            if (instance == null)
                instance = new Config();
            if (empresa == 0 || usuario == 0) {
                empresa = MainActivity.getInstance().getEmpresa();
                usuario = MainActivity.getInstance().getUsuario();
            }
        }
        return instance;
    }

    public void open() {
        try {
            if (db == null) {
                MainActivity mainActivity = MainActivity.getInstance();
                if (mainActivity != null) {
                    String database = mainActivity.getDatabasePath("ecosat.sqlite").getPath();
                    db = Database.getConnection(database, SQLiteDatabase.OPEN_READWRITE);
                }
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().info("Error al abrir la bd Config: " + e.getMessage());
        }
    }

    /**
     * Carga la configuración inicial de la base de datos
     */
    private void loadInitialConfig(boolean retry) {
        Cursor cur = null;
        boolean isLoaded = false;
        try {
            if (db != null) {
                cur = db.query(TABLE_NAME, new String[]{"clave", "valor", "empresa", "usuario"}, null, null, null, null, null);
                while (cur != null && cur.moveToNext()) {
                    String key = buildCacheKey(cur.getString(0), cur.getInt(2), cur.getInt(3));
                    cache.put(key, cur.getString(1));
                }
                isLoaded = true;
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        } finally {
            if (cur != null) {
                cur.close();
            }
            if (!isLoaded) {
                try {
                    if (retry) {
                        Log.e(TAG, "Error al cargar la configuración inicial. Reintentando en 1 segundo.");
                        TimeUnit.SECONDS.sleep(1);
                        loadInitialConfig(false);
                    } else {
                        Log.e(TAG, "Error al cargar la configuración inicial.");
                    }
                } catch (InterruptedException e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            } else {
                Log.e(TAG, "Configuración inicial cargada.");
            }
        }
    }

    /**
     * Construye la clave para el mapa de la caché
     */
    private String buildCacheKey(String clave, int empresa, int usuario) {
        return clave + "_" + empresa + "_" + usuario;
    }

    public synchronized void setValue(String key, String value) {
        String cacheKey = buildCacheKey(key, 0, 0);
        cache.put(cacheKey, value);
        ContentValues values = new ContentValues();
        values.put("clave", key);
        values.put("valor", value);
        dbExecutor.submit(() -> addValue(values, cacheKey));
    }

    public synchronized void setValueEmpresa(String key, String value) {
        if (empresa == 0) {
            Log.e(TAG, "Intentando guardar el valor de '" + key + "' sin empresa asignada");
            // new Exception().printStackTrace();
            return;
        }
        String cacheKey = buildCacheKey(key, empresa, 0);
        cache.put(cacheKey, value);
        ContentValues values = new ContentValues();
        values.put("clave", key);
        values.put("valor", value);
        values.put("empresa", empresa);
        dbExecutor.submit(() -> addValue(values, cacheKey));
    }

    public synchronized void setValueUsuario(String key, String value) {
        if (empresa == 0 || usuario == 0) {
            Log.e(TAG, "Intentando guardar el valor de '" + key + "' sin empresa o usuario asignado.");
            // new Exception().printStackTrace();
            return;
        }
        String cacheKey = buildCacheKey(key, empresa, usuario);
        cache.put(cacheKey, value);
        ContentValues values = new ContentValues();
        values.put("clave", key);
        values.put("valor", value);
        values.put("empresa", empresa);
        values.put("usuario", usuario);
        dbExecutor.submit(() -> addValue(values, cacheKey));
    }

    private synchronized void addValue(ContentValues values, String cacheKey) {
        try {
            int reintento = 0;
            confQueue.add(values);

            while (!confQueue.isEmpty() && reintento < 3) {
                ContentValues conf = confQueue.poll();
                if (conf != null) {
                    while (!saveToDatabase(conf, cacheKey) && reintento < 3) {
                        TimeUnit.SECONDS.sleep(1);
                        reintento++;
                        Log.e(TAG, "Error al guardar la configuración en la base de datos: (" + reintento + ") " + cacheKey + " - " + conf.getAsString("valor"));
                    }
                }
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    private boolean saveToDatabase(ContentValues values, String cacheKey) {
        long result = 0;
        try {
            if (db == null) {
                open();
            }

            if (db != null && db.isOpen()) {
                String whereClause = "clave='" + values.getAsString("clave") + "'";
                if (values.containsKey("empresa")) {
                    whereClause += " AND empresa=" + values.getAsInteger("empresa");
                }
                if (values.containsKey("usuario")) {
                    whereClause += " AND usuario=" + values.getAsInteger("usuario");
                }
                result = db.update(TABLE_NAME, values, whereClause, null);
                if (result < 1) {
                    result = db.insert(TABLE_NAME, null, values);
                }
            }

            //            if (result > 0) {
            //                Log.e(TABLE_NAME, "Configuración guardada en la base de datos: " + cacheKey + " - " + values.getAsString("valor"));
            //            } else {
            //                Log.e(TABLE_NAME,"Error al guardar la configuración en la base de datos: " + cacheKey);
            //            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return result > 0;
    }

    public String getValue(String key) {
        return getValue(key, "0");
    }

    public String getValue(String key, String defaultValue) {
        String cacheKey = buildCacheKey(key, 0, 0);
        // Para Android 6 y para abajo(SDK 23 para abajo) no existe getOrDefault, así que se hace a mano
        if (android.os.Build.VERSION.SDK_INT < 24) {
            String cachetemp = cache.get(cacheKey);
            return cachetemp != null ? cachetemp : defaultValue;
        } else {
            return cache.getOrDefault(cacheKey, defaultValue);
        }
    }

    public String getValueEmpresa(String key, String defaultValue) {
        if (empresa == 0) {
            Log.e(TAG, "Intentando obtener el valor de '" + key + "' sin empresa asignada");
            // new Exception().printStackTrace();
        }
        String cacheKey = buildCacheKey(key, empresa, 0);
        if (android.os.Build.VERSION.SDK_INT < 24) {
            String cachetemp = cache.get(cacheKey);
            return cachetemp != null ? cachetemp : defaultValue;
        } else {
            return cache.getOrDefault(cacheKey, defaultValue);
        }
    }

    public String getValueUsuario(String key, String defaultValue) {
        if (empresa == 0 || usuario == 0) {
            Log.e(TAG, "Intentando obtener el valor de '" + key + "' sin empresa o usuario asignado.");
            // new Exception().printStackTrace();
        }
        String cacheKey = buildCacheKey(key, empresa, usuario);
        if (android.os.Build.VERSION.SDK_INT < 24) {
            String cachetemp = cache.get(cacheKey);
            return cachetemp != null ? cachetemp : defaultValue;
        } else {
            return cache.getOrDefault(cacheKey, defaultValue);
        }
    }

    public synchronized void removeKey(String key) {
        String generalKey = buildCacheKey(key, 0, 0);
        cache.remove(generalKey);
        String empresaKey = buildCacheKey(key, empresa, 0);
        cache.remove(empresaKey);
        String usuarioKey = buildCacheKey(key, empresa, usuario);
        cache.remove(usuarioKey);

        dbExecutor.submit(() -> removeFromDatabase(key));
    }

    private void removeFromDatabase(String key) {
        try {
            db.execSQL("DELETE FROM " + TABLE_NAME + " WHERE clave='" + key + "'");
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void processQueue() {
        Log.e(TAG, "Procesando cola de configuración: " + confQueue.size() + " registros.");
        while (!confQueue.isEmpty()) {
            ContentValues conf = confQueue.poll();
            if (conf != null) {
                addValue(conf, buildCacheKey(conf.getAsString("clave"), conf.getAsInteger("empresa"), conf.getAsInteger("usuario")));
            }
        }
        Log.e(TAG, "Cola de configuración procesada. Registros restantes: " + confQueue.size());
    }

    private void close() {
        dbExecutor.shutdown();
        try {
            dbExecutor.awaitTermination(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        synchronized (sync) {
            if (db != null)
                db.close();
        }
    }
}
