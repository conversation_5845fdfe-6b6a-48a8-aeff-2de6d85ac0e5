package com.movisat.utilities;

import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public final class DirectionEnum {
    public static final DirectionEnum RIGHT = new DirectionEnum("Right");
    public static final DirectionEnum LEFT = new DirectionEnum("Left");

    private final String name;

    private DirectionEnum(String name) {
        this.name = name;
    }

    public String toString() {
        return this.name;
    }


    public static com.movisat.database.OperationsEnum valueOf(String name) {
        Iterator iter = VALUES.iterator();
        while (iter.hasNext()) {
            com.movisat.database.OperationsEnum operation = (com.movisat.database.OperationsEnum) iter.next();
            if (name.equals(operation.toString())) {
                return operation;
            }
        }
        //this method is unusual in that IllegalArgumentException is
        //possibly thrown not at its beginning, but at its end.
        throw new IllegalArgumentException(
                "Cannot parse into an element of Suit : '" + name + "'"
        );
    }

    /**
     * These two lines are all that's necessary to export a List of VALUES.
     */
    private static final DirectionEnum[] fValues = {LEFT, RIGHT};
    //VALUES needs to be located here, otherwise illegal forward reference
    public static final List VALUES = Collections.unmodifiableList(Arrays.asList(fValues));

}

