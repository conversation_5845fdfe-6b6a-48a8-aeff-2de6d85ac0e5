package com.movisat.utilities;

import android.database.Cursor;
import android.database.sqlite.SQLiteCantOpenDatabaseException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.movisat.ecosat.MainActivity;

import java.io.InputStream;


public class Database {
    private static String sync = "DataBase_sync";
    private static boolean isInitialized = false;

    public static SQLiteDatabase getConnection(String path, int flags) {
        if (!isInitialized) {
         return null;
        }

        SQLiteDatabase db = null;
        int retry = 0;
        long backoffMillis = 1000;

        synchronized (sync) {
            // Intento abrir la base de datos durante 30 segundos porque a
            // veces da un fallo por bloqueo que desaparece pasado un tiempo
            while (db == null && retry < 5) {
                try {
//                    db = SQLiteDatabase.openDatabase(path, null, flags);
                    db = MainActivity.getInstance().openOrCreateDatabase(path, MainActivity.MODE_ENABLE_WRITE_AHEAD_LOGGING, null);
                    db.setMaxSqlCacheSize(SQLiteDatabase.MAX_SQL_CACHE_SIZE);
                    db.execSQL("PRAGMA temp_store = MEMORY;");
                } catch (SQLiteCantOpenDatabaseException e) {
                    //El fallo es otro. la base de datos no esta creada. salimos
                    Log.e("DatabaseError", "No existe o no hay permisos para abrir la base de datos: " + e.getMessage());
                    return null;
                } catch (SQLiteException e) {
                    Log.e("DatabaseError", "Excepción SQLite: " + e.getMessage());
                    return null;
                } catch (Throwable e) {
                    try {
                        Thread.sleep(backoffMillis);
                        retry++;
                        backoffMillis *= 2;
                    } catch (Throwable e2) {
                    }
                }
            }
        }
        return db;
    }

    // método que abre o crea la base de datos
    public static boolean init() {
        SQLiteDatabase db = null;
        boolean res=false;

        try {

            try {

                // Creo la base de datos si es necesario
                db = MainActivity.getInstance().openOrCreateDatabase(
                        "ecosat.sqlite", MainActivity.MODE_ENABLE_WRITE_AHEAD_LOGGING, null);


                boolean exist;
                try {
                    db.rawQuery("SELECT * FROM conf", null);
                    exist = true;
                    isInitialized = true;
                    // Se comenta por problemas de rendimiento
                    // Además parece que no es necesario
                    // "Unless SQLite is running in "auto_vacuum=FULL" mode"
                    // https://www.sqlite.org/lang_vacuum.html
                    // Se revisa el PRAGMA auto_vacuum, está configurado a FULL
                    // db.execSQL("VACUUM");

                } catch (Exception e) {
                    exist = false;
                }
                InputStream in;
                byte[] buf;
                String[] script;

                if (!exist) {
                    db.setPageSize(16384);
                    in = MainActivity.getInstance().getAssets().open("ecosat_create.txt");
                    buf = new byte[in.available()];

                    in.read(buf);

                    // Ejecuto el script de creaciin
                    script = new String(buf).split(";");

                    for (String query : script) {

                        try {

                            query = query.replace('\r', ' ');
                            query = query.replace('\n', ' ');
                            query = query.trim();

                            // Paso por alto los comentarios
                            if (!query.startsWith("#") && !query.equals("")){
                                System.out.println("Query: " + query);
                                db.execSQL(query);
                            }

                        } catch (Throwable e) {

                            e.printStackTrace();
                        }

                    }

                    in.close();
                    isInitialized = true;
                }

                in = MainActivity.getInstance().getAssets().open("ecosat_update.txt");
                buf = new byte[in.available()];

                in.read(buf);

                // Ejecuto el script de actualización
                script = new String(buf).split(";");
                boolean execute = false;
                String versionScript = Config.getInstance().getValue("versionScript", "0");
                String versionExecute = "";
                int iVersionScript = 0;
                int iVersionExecute = 0;
                for (String query : script) {

                    try {

                        query = query.replace('\r', ' ');
                        query = query.replace('\n', ' ');
                        query = query.trim();

                        try {
                            if (!query.startsWith("#") && !query.equals("") && execute) {
                                System.out.println("Query: " + query);
                                db.execSQL(query);
                            }

                        } catch (Throwable e){

                            e.printStackTrace();
                        }

                        if (query.startsWith("#")) {
                            // Compruebo si la versión que voy a ejecutar
                            // corresponde con la versión que tengo guardada.
                            versionExecute = query.replace("#", "");
                            if (Integer.parseInt(versionScript)>Integer.parseInt(versionExecute)) {
                                execute = false;
                            } else {
                                // Paso a numero las versiones, si la que tengo
                                // en base
                                // de datos es mayor que la que voy a ejecutar
                                // no la ejecuto.
                                iVersionScript = Integer.parseInt(versionScript);
                                iVersionExecute = Integer.parseInt(versionExecute);
                                execute = iVersionScript < iVersionExecute;
                            }

                            // Guardo la versión que estoy ejecutando.
                            if (execute)
                                Config.getInstance().setValue("versionScript", versionExecute);
                        }

                    } catch (Throwable e) {

                        e.printStackTrace();
                    }

                }

                in.close();

            } catch (Throwable e) {
                Log.e("Inicializar database", e.toString());
            }

            if (db != null && db.isOpen()) {
                db.close();
                res = true;
            }

        } catch (Exception e) {
            Log.e("Inicializar database", e.toString());
        }

        return res;

    }

}
