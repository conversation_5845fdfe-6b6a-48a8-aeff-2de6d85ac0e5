package com.movisat.utilities;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.TextView;

import com.movisat.ecosat.R;

public class ZoomPhotoDialog extends Dialog {

    private TouchImageView imageView;
    private TextView btnClose;

    public ZoomPhotoDialog(Context context) {
        super(context, android.R.style.Theme_Black_NoTitleBar_Fullscreen);
        init();
    }

    private void init() {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        
        LayoutInflater inflater = LayoutInflater.from(getContext());
        View dialogView = inflater.inflate(R.layout.dialog_zoom_photo, null);
        setContentView(dialogView);

        imageView = dialogView.findViewById(R.id.zoom_image_view);
        btnClose = dialogView.findViewById(R.id.btn_close);

        btnClose.setOnClickListener(v -> dismiss());

        getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
    }

    public void setPhoto(Bitmap bitmap) {
        if (imageView != null && bitmap != null) {
            imageView.setImageBitmap(bitmap);
        }
    }

    public static void showPhoto(Context context, Bitmap bitmap) {
        if (context == null || bitmap == null) {
            return;
        }

        ZoomPhotoDialog dialog = new ZoomPhotoDialog(context);
        dialog.setPhoto(bitmap);
        dialog.show();
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        dismiss();
    }
}