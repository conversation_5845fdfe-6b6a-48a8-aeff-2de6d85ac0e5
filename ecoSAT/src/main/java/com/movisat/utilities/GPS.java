package com.movisat.utilities;

import android.app.Activity;
import android.content.Context;
import android.location.LocationManager;
import android.location.Location;
import androidx.annotation.IntegerRes;

import com.google.android.gms.maps.model.LatLng;

public class GPS {

    public static boolean isActiveGPS(Activity activity) {

        final LocationManager manager = (LocationManager) activity
                .getSystemService(Context.LOCATION_SERVICE);

        return manager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    public static double calcularDistancia(LatLng p1, LatLng p2) {
        Location loc1, loc2;
        try {

            loc1 = new Location(LocationManager.GPS_PROVIDER);
            loc1.setLatitude(p1.latitude);
            loc1.setLongitude(p1.longitude);
            loc2 = new Location(LocationManager.GPS_PROVIDER);
            loc2.setLatitude(p2.latitude);
            loc2.setLongitude(p2.longitude);

        } catch (Throwable e) {
            return Double.MAX_VALUE;
        }

        return loc1.distanceTo(loc2);
    }

    public static LatLng getOffset(LatLng latLng, int meters, DirectionEnum direction) {
        //Position, decimal degrees
        double lat = latLng.latitude;
        double lon = latLng.longitude;

        //Earth’s radius, sphere
        int R = 6378137;

        //offsets in meters
        int dn = 0;
        int de = direction == DirectionEnum.RIGHT ? meters : meters * -1;

        //Coordinate offsets in radians
        double dLat = dn / R;
        double dLon = de / (R * Math.cos(Math.PI * lat / 180));

        //OffsetPosition, decimal degrees
        double latO = lat + dLat * 180 / Math.PI;
        double lonO = lon + dLon * 180 / Math.PI;

        return new LatLng(latO, lonO);
    }
}



