package com.movisat.utilities;

import com.environment.EnvironmentDebug;
import com.movisat.application.EcoSATApplication;
import com.movisat.ecosat.AppContext;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoadingActivity;
import com.movisat.ecosat.MyLoggerHandler;

import android.app.Application;
import android.content.Context;
import android.os.Looper;
import android.provider.Settings;
import android.telephony.PhoneStateListener;
import android.telephony.ServiceState;
import android.telephony.SignalStrength;
import android.telephony.TelephonyManager;
import android.util.Log;
import android.widget.Toast;

public class Phone extends PhoneStateListener {
	private static final String TAG = "Phone";
	private static String sync = "";
	private static Phone instance = null;
	private static PhoneData data = null;
	private TelephonyManager manager = null;
	private PhoneStateListener listener = null;
	boolean wifiConnected = false;

	private class PhoneData {
		boolean registered = false;
		boolean roaming = false;
		String imei = "";
		int csq;
	}

	public static Phone getInstance() {
		try {
			synchronized (sync) {
				if (instance == null)
					instance = new Phone();
			}
		} catch (Throwable e) {
			Log.e(TAG, e.getMessage());
			e.printStackTrace();
		}

		return instance;
	}

	private Phone() {}

	public void initialize(Context context) {
		try {
			data = new PhoneData();

			//manager = (TelephonyManager) MyLoadingActivity.getInstance()
			//		.getSystemService(Context.TELEPHONY_SERVICE);
			manager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);

			manager.listen((listener = new PhoneStateListener() {
				public void onServiceStateChanged(ServiceState serviceState) {
					int state;

					try {

						state = serviceState.getState();

						if (state == ServiceState.STATE_IN_SERVICE) {

							data.registered = true;
							data.roaming = serviceState.getRoaming();
						} else {

							data.registered = false;
							data.roaming = false;
						}
					} catch (Throwable e) {
						//Log.e("Phone:", "Error al crear el listener");
					}
				}

				public void onSignalStrengthsChanged(SignalStrength signal) {

					int csq;

					try {

						csq = signal.getGsmSignalStrength();

						if (((csq > 0 && csq < 32) || csq == 99) && data.registered)
							//csq = (int) (Math.random() % 15) + 15;
							if (csq > 31)
								csq = 31;
						else
							csq = 0;

						data.csq = csq;
					} catch (Exception e) {
					}
				}
			}), PhoneStateListener.LISTEN_SERVICE_STATE
					| PhoneStateListener.LISTEN_SIGNAL_STRENGTHS);
		} catch (Throwable e) {
			Log.e(TAG, e.toString());
			e.printStackTrace();
			//MyLoggerHandler.getInstance().error(e);
		}
	}

	public void close() {

		try {

			synchronized (sync) {

				if (manager != null)
					manager.listen(listener, LISTEN_NONE);
				manager = null;

				instance = null;
			}

		} catch (Throwable e) {
		}

	}

	public boolean isRegistered() {

		return data.registered;
	}

	public boolean isInRoaming() {

		return data.roaming;
	}

	public int getCsq() {

		return data.csq;
	}

	public String getSimState() {
		String res = "";

		try {

			switch (manager.getSimState()) {
			case TelephonyManager.SIM_STATE_READY:
				res = "Tarjeta SIM preparada";

				break;

			case TelephonyManager.SIM_STATE_ABSENT:
				res = "No hay Tarjeta SIM";

				break;

			case TelephonyManager.SIM_STATE_PIN_REQUIRED:
				res = "Tarjeta SIM requiere PIN";

				break;

			case TelephonyManager.SIM_STATE_PUK_REQUIRED:
				res = "Tarjeta SIM requiere PUK";

				break;

			case TelephonyManager.SIM_STATE_NETWORK_LOCKED:
				res = "Tarjeta SIM bloqueada";

				break;

			case TelephonyManager.SIM_STATE_UNKNOWN:
				res = "Tarjeta SIM no preparada";
			}

		} catch (Throwable e) {
		}

		return res;
	}

	public String getCurrentOperator() {
		String res = "";

		try {

			res = manager.getNetworkOperatorName();

		} catch (Throwable e) {
		}

		return res;
	}

	public String getNetworkType() {
		String res = "";

		try {

			switch (manager.getNetworkType()) {
			case TelephonyManager.NETWORK_TYPE_GPRS:
				res = "GPRS";

				break;

			case TelephonyManager.NETWORK_TYPE_EDGE:
				res = "EDGE";

				break;

			case TelephonyManager.NETWORK_TYPE_HSPA:
				res = "HSPA";

				break;

			case TelephonyManager.NETWORK_TYPE_HSDPA:
				res = "HSDPA";

				break;

			case TelephonyManager.NETWORK_TYPE_HSUPA:
				res = "HSUPA";

				break;

			case TelephonyManager.NETWORK_TYPE_UMTS:
				res = "UMTS";
			}

		} catch (Throwable e) {
		}

		return res;
	}

	public boolean isNetworkConnected() {
		boolean res = false;

		try {

			if (manager.getDataState() == TelephonyManager.DATA_CONNECTED)
				res = true;

		} catch (Throwable e) {
		}

		return res;
	}

	public String getIMEI() {

		try {
			if (EnvironmentDebug.getData().getDeviceId() != null)
				return EnvironmentDebug.getData().getDeviceId();

			// Falseo de IMEI si está establecida la opción en la base de datos
            boolean fakeDevice = Config.getInstance().getValue("fakeDevice", "0").equals("1");
			if (fakeDevice) {
				String savedImei = Config.getInstance().getValue("imei", "");
				if (savedImei.length() == 17)
					return savedImei;
			}

			if (data.imei.equals("")) {
				if (android.os.Build.VERSION.SDK_INT >= 29){
					//Toast.makeText(MainActivity.getInstance().getApplicationContext(),"AndroidID",Toast.LENGTH_LONG).show();
					data.imei = Settings.Secure.getString(EcoSATApplication.getInstance().getApplicationContext().getContentResolver(), Settings.Secure.ANDROID_ID);
					while (data.imei.length() < 17)
						data.imei += "0";


				} else{
					if ((data.imei = manager.getDeviceId()) != null) {
						//Toast.makeText(MainActivity.getInstance().getApplicationContext(),"IMEI",Toast.LENGTH_LONG).show();
						while (data.imei.length() < 17)
							data.imei += "0";

					} else
						data.imei = "";
				}
			}
			//Toast.makeText(MainActivity.getInstance().getApplicationContext(),data.imei,Toast.LENGTH_LONG).show();

		} catch (Throwable e) {
			e.printStackTrace();
		}

		return data.imei;
	}
}
