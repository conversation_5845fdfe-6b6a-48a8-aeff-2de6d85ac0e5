package com.movisat.utilities;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.DialogInterface.OnCancelListener;
import android.text.SpannableString;
import android.text.method.LinkMovementMethod;
import android.util.TypedValue;
import android.view.ContextThemeWrapper;
import android.widget.TextView;

import com.movisat.ecosat.R;

public class InfoDialog extends Activity {
    private AlertDialog.Builder dialogBuilder;
    private AlertDialog dialog;

    // Iconos
    public static final int ICON_NONE = 0x00;
    public static final int ICON_OK = 0x01;
    public static final int ICON_ALERT = 0x02;
    public static final int ICON_STOP = 0x03;
    public static final int ICON_INFO = 0x04;
    public static final int ICON_QUESTION = 0x05;

    // Botones
    public static final int BUTTON_NONE = 0;
    public static final int BUTTON_YES = 1;
    public static final int BUTTON_NO = 2;
    public static final int BUTTON_CANCEL = 4;
    public static final int BUTTON_RETRY = 8;
    public static final int BUTTON_ACCEPT = 16;
    public static final int BUTTON_MODIFY = 32;
    public static final int BUTTON_DELETE = 64;

    // Posici?n del dialogo
    public static final int POSITION_CENTER = 0x00;
    public static final int POSITION_TOP = 0x01;
    public static final int POSITION_BOTTOM = 0x02;

    // en este constructor "extra", se pasa el par?metro cancelable, para hacer que no se cierre el
    // dialogo al pulsar la tecla return, y adem?s un enlace
    public InfoDialog(Activity activity, String title, SpannableString message,
                      int icon, final OnInfoDialogSelect onSelectFn, int button,
                      int position, final boolean cancelable) {
        try {

            // Creo la plantilla para el dialogo
            dialogBuilder = new AlertDialog.Builder(new ContextThemeWrapper(
                    activity, R.style.InfoDialogStyle));

            dialogBuilder.setTitle(title);

            // dialogBuilder.setInverseBackgroundForced(true);
            //dialogBuilder.setMessage(message);
            TextView textView = new TextView(activity);
            textView.setMovementMethod(LinkMovementMethod.getInstance());
            textView.setText(message);
            textView.setTextAppearance(activity, R.style.InfoDialogStyle);
            textView.setPadding(15, 10, 10, 20);
            textView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 22f);
            dialogBuilder.setView(textView);


            dialogBuilder.setCancelable(cancelable);

            // Pongo el icono
            switch (icon) {
                case ICON_NONE:
                    break;

                case ICON_OK:
                    dialogBuilder.setIcon(R.drawable.ico_ok);

                    break;

                case ICON_ALERT:
                    dialogBuilder.setIcon(R.drawable.ic_stat_alert_warning);

                    break;

                case ICON_STOP:
                    dialogBuilder.setIcon(R.drawable.icon_stop);

                    break;

                case ICON_INFO:
                    dialogBuilder.setIcon(R.drawable.ico_advertencia);

                    break;

                case ICON_QUESTION:
                    dialogBuilder.setIcon(R.drawable.ic_stat_action_pregunta);

                    break;
            }

            // Pongo los botones
            if ((button & BUTTON_YES) > 0)
                dialogBuilder.setPositiveButton("Si",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_YES);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_NO) > 0)
                dialogBuilder.setNeutralButton("No",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_NO);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_CANCEL) > 0)
                dialogBuilder.setNegativeButton("Cancelar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_CANCEL);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_RETRY) > 0)
                dialogBuilder.setNeutralButton("Reintentar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_RETRY);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_ACCEPT) > 0)
                dialogBuilder.setPositiveButton("Aceptar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_ACCEPT);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_MODIFY) > 0)
                dialogBuilder.setPositiveButton("Modificar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_MODIFY);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_DELETE) > 0)
                dialogBuilder.setNegativeButton("Borrar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_DELETE);
                                dialog.dismiss();
                            }
                        });

            // Capturo la flecha atras
            dialogBuilder.setOnCancelListener(new OnCancelListener() {

                @Override
                public void onCancel(DialogInterface dialog) {
                    if (onSelectFn != null)
                        onSelectFn.onSelectOption(BUTTON_CANCEL);
                    dialog.dismiss();
                }
            });

            // Creo el dialogo
            dialog = dialogBuilder.create();
            dialog.setCanceledOnTouchOutside(cancelable);
            dialog.setOwnerActivity(activity);

            // Posiciono el dialogo
            switch (position) {
                case POSITION_TOP:
                    dialog.getWindow().getAttributes().verticalMargin = -0.3f;

                    break;

                case POSITION_BOTTOM:
                    dialog.getWindow().getAttributes().verticalMargin = 0.3f;

                    break;

                default:
                    dialog.getWindow().getAttributes().verticalMargin = 0.0f;
            }

        } catch (Throwable e) {

            e.printStackTrace();
        }
    }


    public InfoDialog(Activity activity, String title, String message,
                      int icon, final OnInfoDialogSelect onSelectFn, int button,
                      int position) {

        try {

            // Creo la plantilla para el dialogo
            dialogBuilder = new AlertDialog.Builder(new ContextThemeWrapper(
                    activity, R.style.InfoDialogStyle));

            dialogBuilder.setTitle(title);

            // dialogBuilder.setInverseBackgroundForced(true);
            dialogBuilder.setMessage(message);
            dialogBuilder.setCancelable(true);

            // Pongo el icono
            switch (icon) {
                case ICON_NONE:
                    break;

                case ICON_OK:
                    dialogBuilder.setIcon(R.drawable.ico_ok);

                    break;

                case ICON_ALERT:
                    dialogBuilder.setIcon(R.drawable.ic_stat_alert_warning);

                    break;

                case ICON_STOP:
                    dialogBuilder.setIcon(R.drawable.icon_stop);

                    break;

                case ICON_INFO:
                    dialogBuilder.setIcon(R.drawable.ico_advertencia);

                    break;

                case ICON_QUESTION:
                    dialogBuilder.setIcon(R.drawable.ic_stat_action_pregunta);

                    break;
            }

            // Pongo los botones
            if ((button & BUTTON_YES) > 0)
                dialogBuilder.setPositiveButton("Si",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_YES);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_NO) > 0)
                dialogBuilder.setNeutralButton("No",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_NO);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_CANCEL) > 0)
                dialogBuilder.setNegativeButton("Cancelar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_CANCEL);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_RETRY) > 0)
                dialogBuilder.setNeutralButton("Reintentar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_RETRY);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_ACCEPT) > 0)
                dialogBuilder.setPositiveButton("Aceptar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_ACCEPT);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_MODIFY) > 0)
                dialogBuilder.setPositiveButton("Modificar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_MODIFY);
                                dialog.dismiss();
                            }
                        });

            if ((button & BUTTON_DELETE) > 0)
                dialogBuilder.setNegativeButton("Borrar",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                if (onSelectFn != null)
                                    onSelectFn.onSelectOption(BUTTON_DELETE);
                                dialog.dismiss();
                            }
                        });

            // Capturo la flecha atras
            dialogBuilder.setOnCancelListener(new OnCancelListener() {

                @Override
                public void onCancel(DialogInterface dialog) {

                    if (onSelectFn != null)
                        onSelectFn.onSelectOption(BUTTON_CANCEL);
                    dialog.dismiss();
                }
            });

            // Creo el dialogo
            dialog = dialogBuilder.create();
            dialog.setCanceledOnTouchOutside(false);
            dialog.setOwnerActivity(activity);

            // Posiciono el diilogo
            switch (position) {
                case POSITION_TOP:
                    dialog.getWindow().getAttributes().verticalMargin = -0.3f;

                    break;

                case POSITION_BOTTOM:
                    dialog.getWindow().getAttributes().verticalMargin = 0.3f;

                    break;

                default:
                    dialog.getWindow().getAttributes().verticalMargin = 0.0f;
            }

        } catch (Throwable e) {

            e.printStackTrace();
        }
    }

    public void show() {

        dialog.show();
    }

}
