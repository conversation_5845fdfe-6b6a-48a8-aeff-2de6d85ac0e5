package com.movisat.utilities;

import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by faroca on 13/10/2015.
 */
public class HelperDates {

    private static HelperDates _instance;

    private HelperDates() {

    }

    public static HelperDates getInstance() {
        if (_instance == null)
            _instance = new HelperDates();

        return _instance;
    }

    /**
     * Obtener milisegundos de una dateString
     *
     * @param dateString Fecha en formato string
     * @param formatDate Formato de la fecha
     * @return
     */
    public long getMillisecondsBy(String dateString, String formatDate) {
        SimpleDateFormat sdf = new SimpleDateFormat(formatDate);
        Date date = null;
        try {
            date = sdf.parse(dateString);
        } catch (Exception ex) {
            Log.e("getMillisecondsBy", ex.getMessage());
        }
        if (date.equals(null)) return 0;
        return date.getTime();
    }

    public long getMillisecondsBy(String dateString) {
        return getMillisecondsBy(dateString, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * Recupera la fecha en formato que le pasamos a partir de los milisegundos
     *
     * @param milliSeconds Milisegundos
     * @param formatDate   Formato de la fecha
     * @return
     */
    public String getDateStringBy(long milliSeconds, String formatDate) {
        SimpleDateFormat sdf = new SimpleDateFormat(formatDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(milliSeconds);
        return sdf.format(calendar.getTime());
    }

    public String getDateStringBy(long milliSeconds) {
        return getDateStringBy(milliSeconds, "yyyy-MM-dd HH:mm:ss");
    }

    public  Date addDays(Date date, int days)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days); //minus number would decrement the days
        return cal.getTime();
    }

    public  Date addMonths(Date date, int months)
    {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, months); //minus number would decrement the months
        return cal.getTime();
    }

    public Date addHours (Date date, int hours) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR, hours);
        Date result = cal.getTime();
        return result;
    }
}
