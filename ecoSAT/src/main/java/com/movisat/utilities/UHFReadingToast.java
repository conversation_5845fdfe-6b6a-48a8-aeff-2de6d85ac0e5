package com.movisat.utilities;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.CountDownTimer;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.Toast;

import com.movisat.application.EcoSATApplication;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;


/**
 * Clase para mostrar un Toast informativo de que el proceso de lectura UHF está iniciado.
 * Este Toast se mostrará con {@link #showToast(int)} durante el tiempo especificado.
 */
public class UHFReadingToast {

    private static final int BLINK_DURATION_MS = 300;

    private static UHFReadingToast mInstance;

    private Toast mToastToShow;
    private CountDownTimer mToastCountDown;
    private ValueAnimator mBackgroundAnimation;


    /**
     * Devuelve la instancia de la clase con la que mostrar el mensaje de lectura UHF.
     * @return Instancia de UHFReadingToast.
     */
    public static UHFReadingToast get() {
        if (mInstance == null)
            mInstance = new UHFReadingToast();
        return mInstance;
    }


    private UHFReadingToast() {}


    /**
     * Muestra un Toast para informar del estado de lectura UHF.
     * @param millis Milisegundos de duración del mensaje.
     */
    public void showToast(int millis) {
        try {
            cancel();

            Context context = EcoSATApplication.getInstance().getBaseContext();

            View layout = LayoutInflater.from(context).inflate(R.layout.toast_uhf_reading, null);

            final ConstraintLayout layoutToast = layout.findViewById(R.id.layout_reading_tag_toast);
            ImageView imageView = layout.findViewById(R.id.img_reading_tag_toast);
            imageView.setImageResource(R.drawable.reading_tag);

            // Animación de fondo del Toast
            int colorFrom = context.getResources().getColor(R.color.toast_uhf_reading_background);
            int colorTo = context.getResources().getColor(R.color.toast_uhf_reading_background2);
            mBackgroundAnimation = ValueAnimator.ofObject(new ArgbEvaluator(), colorFrom, colorTo);
            mBackgroundAnimation.setStartDelay(0);
            mBackgroundAnimation.setDuration(BLINK_DURATION_MS);
            mBackgroundAnimation.setRepeatCount(millis / BLINK_DURATION_MS + 1);
            mBackgroundAnimation.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator valueAnimator) {
                    layoutToast.setBackgroundColor((int) valueAnimator.getAnimatedValue());
                }
            });
            mBackgroundAnimation.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    // Para que el Toast desaparezca inmediatamente
                    layoutToast.setVisibility(View.GONE);
                }
            });

            // Configuración del Toast
            mToastToShow = new Toast(context.getApplicationContext());
            mToastToShow.setView(layout);
            mToastToShow.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
            mToastToShow.setDuration(Toast.LENGTH_SHORT);

            // CountDownTimer para mostrar y refrescar el Toast durante el tiempo especificado
            mToastCountDown = new CountDownTimer(millis, 1500) {
                public void onTick(long millisUntilFinished) {
                    mToastToShow.show();
                }
                public void onFinish() {
                    mToastToShow.cancel();
                }
            };

            // Muestra el Toast e inicia el countdown y la animación
            mToastToShow.show();
            mToastCountDown.start();
            mBackgroundAnimation.start();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }


    /**
     * Cancela el Toast, ocultándolo de la pantalla.
     */
    public void cancel() {
        if (mToastCountDown != null)
            mToastCountDown.cancel();

        if (mToastCountDown != null)
            mToastCountDown.cancel();

        if (mBackgroundAnimation != null)
            mBackgroundAnimation.end();
    }
}
