package com.movisat.utilities;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.InetAddress;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

public class Ftp {
	private Socket sock1 = null;
	private Socket sock2 = null;
	private InputStream in1 = null;
	private OutputStream out1 = null;
	private InputStream in2 = null;

	public Ftp(boolean debug) {

		try {


		} catch (Throwable e) {
		}

	}

	public boolean connect(String host, String user, String passw) {
		boolean res = false;

		try {

			disconnect();

			//debug("Conectando a servidor FTP " + host + "...");

			sock1 = new Socket();
			sock1.connect(new InetSocketAddress(host, 21), 15000);
			in1 = sock1.getInputStream();
			out1 = sock1.getOutputStream();

			//debug("Esperando mensaje de bienvenida...");

			// Espero el mensaje de bienvenida
			if (getResult(220, 15000)) {

				//debug("Validando usuario y contraseia...");

				// Me identifico
				clearBuffer();
				out1.write(("user " + user + "\r\n").getBytes());
				if (getResult(331, 15000)) {

					clearBuffer();
					out1.write(("pass " + passw + "\r\n").getBytes());
					if (getResult(230, 15000)) {

						//debug("Conectado a servidor FTP");

						res = true;
					}

				}

			}

		} catch (Throwable e) {

			//debug(e.getMessage());
		}

		return res;
	}

	public void disconnect() {

		try {

			if (in1 != null)
				in1.close();
			in1 = null;

			if (out1 != null)
				out1.close();
			out1 = null;

			if (in2 != null)
				in2.close();
			in2 = null;

			if (sock1 != null)
				sock1.close();
			sock1 = null;

			if (sock2 != null)
				sock2.close();
			sock2 = null;

		} catch (Throwable e) {

			//debug(e.getMessage());
		}

	}

	private boolean getResult(int result, int timeout) {
		String auxBuf = "";
		boolean res = false;

		try {

			while (!(auxBuf = readLn(timeout)).equals(""))
				if (Integer.parseInt(auxBuf.substring(0, 3)) == result) {

					res = true;
					timeout = 1000;
				}

		} catch (Throwable e) {

			//debug(e.getMessage());
		}

		return res;
	}

	private void clearBuffer() {
		byte cc[] = new byte[1];

		try {

			sock1.setSoTimeout(1000);
			while (in1.read(cc) > 0)
				;

		} catch (Throwable e) {

			//debug(e.getMessage());
		}

		return;
	}

	private String readLn(int timeout) {
		byte[] cc = new byte[1];
		String res = "";

		try {

			sock1.setSoTimeout(timeout);

			while (in1.read(cc) > 0 && cc[0] != 0x0A)
				res += (char) cc[0];

		} catch (Throwable e) {

			//debug(e.getMessage());
		}

		return res;
	}

	private boolean setPassiveMode() {
		boolean res = false;
		String auxBuf, ip = "";
		int p1, p2, ii, port = 0;

		try {

			//debug("Entrando en modo pasivo...");

			if (in2 != null)
				in2.close();
			in2 = null;

			if (sock2 != null)
				sock2.close();
			sock2 = null;

			// Paso a modo de transferencia binario
			clearBuffer();
			out1.write("TYPE I\r\n".getBytes());
			if (getResult(200, 15000)) {

				// Paso a modo pasivo
				clearBuffer();
				out1.write("PASV\r\n".getBytes());

				if ((auxBuf = readLn(5000)).startsWith("227")) {

					if ((p1 = auxBuf.indexOf("(", 0)) > -1) {

						for (p2 = p1 + 1, ii = 0; ii < 4; p2++) {

							if (auxBuf.charAt(p2) == ',') {

								if (ii < 3)
									ip += ".";

								ii++;

							} else
								ip += auxBuf.charAt(p2);
						}

						if (ii == 4) {

							if ((p1 = auxBuf.indexOf(",", p2)) > -1) {

								port = Integer.parseInt(auxBuf
										.substring(p2, p1)) * 256;

								if ((p2 = auxBuf.indexOf(")", p1 + 1)) > -1) {

									port += Integer.parseInt(auxBuf.substring(
											p1 + 1, p2));

									//debug("Conectando a " + ip + ":" + port
									//		+ "...");

									sock2 = new Socket();
									sock2.connect(new InetSocketAddress(ip,
											port), 15000);
									in2 = sock2.getInputStream();

									//debug("Modo pasivo activado");

									res = true;
								}

							}

						}

					}

				}

			}

		} catch (Throwable e) {

			//debug(e.getMessage());
		}

		return res;
	}

	private String getFileInfo(String file) {
		byte[] auxBuf = new byte[256];
		String res = "";
		int size = -1;

		try {

			// Paso a modo pasivo
			if (setPassiveMode()) {

				//debug("Obteniendo informaciin de " + file + "...");

				// Obtengo la informaciin del archivo
				clearBuffer();
				out1.write(("LIST " + file + "\r\n").getBytes());

				try {

					sock2.setSoTimeout(15000);
					size = in2.read(auxBuf);

				} catch (Throwable e) {
				}

				in2.close();
				in2 = null;

				sock2.close();
				sock2 = null;

				if (size > 0) {

					res = new String(auxBuf, 0, size);

					if (!readLn(5000).startsWith("150")
							|| !readLn(5000).startsWith("226"))
						res = "";

					//debug(res);
				}

			}

		} catch (Throwable e) {

			//debug(e.getMessage());
		}

		return res;
	}

	public static boolean sendFile(String host, String user, String passw,
			String src, String dest) {
		boolean res = false;

		try {

			// Creo el cliente FTP e intento conectar al host
			FTPClient ftpUpload = new FTPClient();
			ftpUpload.connect(InetAddress.getByName(host));

			// Compruebo si he podido conectar
			if (FTPReply.isPositiveCompletion(ftpUpload.getReplyCode())) {

				// Valido el usuario y contaseia
				if (ftpUpload.login(user, passw)) {

					// Configuro la conexiin en modo binario y configuro el
					// modelo de conexiin como pasivo
					ftpUpload.setFileType(FTP.BINARY_FILE_TYPE);
					ftpUpload.enterLocalPassiveMode();

					// Abro el archivo de origen para enviarlo
					FileInputStream srcFileStream = new FileInputStream(src);

					try {

						// Envio el archivo
						res = ftpUpload.storeFile(dest, srcFileStream);

					} catch (Throwable e) {
					}

					srcFileStream.close();
				}

			}

			ftpUpload.disconnect();

		} catch (Throwable e) {
			Log.d("FTP UPLOAD", e.getMessage());
		}

		return res;
	}

	

}