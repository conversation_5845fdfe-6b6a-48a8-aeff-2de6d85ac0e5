package com.movisat.utilities;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;

import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.R;

public class Photo {

    private static String sync = "";
    private static Photo instance = null;
    private String PhotoPath;
    public static final int REQUEST_TAKE_PHOTO = 1;
    public static final int REQUEST_IMAGE_CAPTURE = 1;


    public static Photo getInstance() {

        synchronized (sync) {

            if (instance == null)
                instance = new Photo();
        }

        return instance;
    }


    public BitmapDescriptor textAsBitmapDescriptor(String text, float textSize, int textColor) {
        Bitmap bitmap = textAsBitmap(text, textSize, textColor);
        BitmapDescriptor bitmapDescriptor = BitmapDescriptorFactory.fromBitmap(bitmap);
        return bitmapDescriptor;
    }

    public Bitmap textAsBitmap(String text, float textSize, int textColor) {
        Paint paint = new Paint();
        paint.setTextSize(textSize);
        paint.setColor(textColor);
        paint.setTextAlign(Paint.Align.LEFT);
        float baseline = -paint.ascent(); // ascent() is negative
        int width = (int) (paint.measureText(text) + 0.5f); // round
        int height = (int) (baseline + paint.descent() + 0.5f);
        Bitmap image = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(image);
        canvas.drawText(text, 0, baseline, paint);
        return image;
    }

    public Bitmap scaleBitmap(Bitmap bm, float scalingFactor) {
        int scaleHeight = (int) (bm.getHeight() * scalingFactor);
        int scaleWidth = (int) (bm.getWidth() * scalingFactor);

        return Bitmap.createScaledBitmap(bm, scaleWidth, scaleHeight, true);
    }

    public Bitmap scaleWithAspectRatio(Activity context, Bitmap image) {
        int imaheVerticalAspectRatio, imageHorizontalAspectRatio;
        float bestFitScalingFactor = 0;
        float percesionValue = (float) 0.2;

        //getAspect Ratio of Image
        int imageHeight = (int) (Math.ceil((double) image.getHeight() / 100) * 100);
        int imageWidth = (int) (Math.ceil((double) image.getWidth() / 100) * 100);
        int GCD = BigInteger.valueOf(imageHeight).gcd(BigInteger.valueOf(imageWidth)).intValue();
        imaheVerticalAspectRatio = imageHeight / GCD;
        imageHorizontalAspectRatio = imageWidth / GCD;

        //getContainer Dimensions
        int displayWidth = context.getWindowManager().getDefaultDisplay().getWidth();
        int displayHeight = context.getWindowManager().getDefaultDisplay().getHeight();
        //I wanted to show the image to fit the entire device, as a best case. So my ccontainer dimensions were displayWidth & displayHeight. For your case, you will need to fetch container dimensions at run time or you can pass static values to these two parameters

        int leftMargin = 0;
        int rightMargin = 0;
        int topMargin = 0;
        int bottomMargin = 0;
        int containerWidth = displayWidth - (leftMargin + rightMargin);
        int containerHeight = displayHeight - (topMargin + bottomMargin);

        //iterate to get bestFitScaleFactor per constraints
        while ((imageHorizontalAspectRatio * bestFitScalingFactor <= containerWidth) &&
                (imaheVerticalAspectRatio * bestFitScalingFactor <= containerHeight)) {
            bestFitScalingFactor += percesionValue;
        }

        //return bestFit bitmap
        int bestFitHeight = (int) (imaheVerticalAspectRatio * bestFitScalingFactor);
        int bestFitWidth = (int) (imageHorizontalAspectRatio * bestFitScalingFactor);
        image = Bitmap.createScaledBitmap(image, bestFitWidth, bestFitHeight, true);

        //Position the bitmap centre of the container
        int leftPadding = (containerWidth - image.getWidth()) / 2;
        int topPadding = (containerHeight - image.getHeight()) / 2;
        Bitmap backDrop = Bitmap.createBitmap(containerWidth, containerHeight, Bitmap.Config.RGB_565);
        Canvas can = new Canvas(backDrop);
        can.drawBitmap(image, leftPadding, topPadding, null);

        return backDrop;
    }


    /*private File createImageFile() {

        Map<String, File> externalLocations = ExternalStorage.getAllStorageLocations();
        File sdCard = externalLocations.get(ExternalStorage.SD_CARD);
        final File path = new File(sdCard+File.separator+"EcoLimpiezaViaria"+File.separator+"images"+File.separator);

        if (!path.exists()) {
            path.mkdir();
        }

        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss")
                .format(new Date());
        String fileName  = "JPEG_" + timeStamp + "_";

        File image=new File(path, fileName);
        PhotoPath = image.getAbsolutePath();

        return image;
    }*/


    public File createImageFile() throws IOException {

        File image=null;

        try {
            // Create an image file name
            String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss")
                    .format(new Date());
            String imageFileName = "JPEG_" + timeStamp + "_";
            File storageDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES);

            try {
                image = File.createTempFile(imageFileName, ".jpg", storageDir);
            }catch (IOException e){
                storageDir = MainActivity.getInstance().getExternalFilesDir(Environment.DIRECTORY_PICTURES);
                image = File.createTempFile(imageFileName, ".jpg", storageDir);
            }

            // Save a file: path for use with ACTION_VIEW intents
            PhotoPath = image.getAbsolutePath();

        } catch (Throwable e){

            MyLoggerHandler.getInstance().error(e);
        }

        return image;
    }

    public void galleryAddPic(Activity actividad) {
        Intent mediaScan = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
        File f = new File(PhotoPath);
        Uri contentUri = Uri.fromFile(f);
        mediaScan.setData(contentUri);
        actividad.sendBroadcast(mediaScan);
    }

    public void dispatchTakePictureIntent(Activity actividad) {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        // Ensure that there's a camera activity to handle the intent
        if (takePictureIntent.resolveActivity(actividad.getPackageManager()) != null) {
            // Create the File where the photo should go
            File photoFile = null;
            try {
                photoFile = createImageFile();
            } catch (Exception ex) {
                // Error occurred while creating the File
                MyLoggerHandler.getInstance().error(ex);
                ex.printStackTrace();
            }
            // Continue only if the File was successfully created
            if (photoFile != null) {
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT,
                        Uri.fromFile(photoFile));
                actividad.startActivityForResult(takePictureIntent, REQUEST_TAKE_PHOTO);
            }
        }
    }

    public Bitmap setPic() {
        // Get the dimensions of the View

        int targetW = 1024;
        int targetH = 768;

        // Get the dimensions of the bitmap BitmapFactory.Options bmOptions =
        BitmapFactory.Options bmOptions = new BitmapFactory.Options();

        bmOptions.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(PhotoPath, bmOptions);
        int photoW = bmOptions.outWidth;
        int photoH = bmOptions.outHeight;

        // Determine how much to scale down the image int scaleFactor =
        Math.min(photoW / targetW, photoH / targetH);

        int scaleFactor = Math.min(photoW / targetW, photoH / targetH);

        // Decode the image file into a Bitmap sized to fill the View
        bmOptions.inJustDecodeBounds = false;
        bmOptions.inSampleSize = scaleFactor;
        bmOptions.inPurgeable = true;

        Bitmap bitmap = BitmapFactory.decodeFile(PhotoPath, bmOptions);
        //Bitmap imageResize = Utils.ResizeImage(bitmap, 800, 600);
        // jcaballero cambio la llamada al metodo ResizeImage por este, que decodifica la imagen
        // dandole las opciones que calcula segun el tamanio, tal cual indica en la documentacion de android
        Bitmap imageResize = Utils.decodeBitmapFromFile(PhotoPath,targetW,targetH);
        //

        // mImageView.setImageBitmap(bitmap);
        return imageResize;
        //fotos.add(imageResize);
        //lblFoto.setText("Aiadida(s) " + fotos.size() + " foto(s)");
    }


    public String getPhotoPath() {
        return PhotoPath;
    }


    public void setPhotoPath(String photoPath) {
        PhotoPath = photoPath;
    }

}
