package com.movisat.utilities;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;

public class EcoSATLog {
    private static EcoSATLog instance = null;
    private static String sync = "";
    private File file = null;
    private FileOutputStream out = null;

    public static EcoSATLog getInstance(){

        try{
            synchronized (sync){
                if (instance == null) instance = new EcoSATLog();
            }
        } catch (Throwable e) {
        }

        return instance;
    }

    private EcoSATLog(){
        try{


            file = new File("data/data/com.movisat.ecosat/files/EcoSAT_log.txt");
            out = new FileOutputStream(file);
            write(this, "-----------------------------------------------------");
        } catch (Throwable e) {
        }
    }

    public void write(Object obj, String text) {

        try {

            synchronized (sync) {

                if (file.length() > 10000000L) {

                    out.close();

                    new File("data/data/com.movisat.ecosat/files/EcoSAT_logh.txt")
                            .delete();

                    file.renameTo(new File(
                            "data/data/com.movisat.ecosat/files/EcoSAT_logh.txt"));

                    file = new File(
                            "data/data/com.movisat.ecosat/files/EcoSAT_log.txt");
                    out = new FileOutputStream(file);

                }

                String className = null;

                if (obj != null)
                    className = obj.getClass().getSimpleName();

                if (className == null)
                    className = "";

                if (out != null)
                    out.write((Utils.longToDatetimeString("yy-MM-dd HH:mm:ss",
                            Utils.getTime()) + " [" + className + "] " + text + "\r\n")
                            .getBytes());
            }

        } catch (Throwable e) {

            Log.e(getClass().getSimpleName(), e.getMessage());
        }

    }

}
