package com.movisat.utilities;

import android.content.Context;

/**
 * Created by faroca on 20/10/2015.
 */
public class HelperUpload {


    private static HelperUpload _instance = null;

    private HelperUpload() {

    }

    public static HelperUpload getInstance() {
        if (_instance == null)
            _instance = new HelperUpload();
        return _instance;
    }

    private final String HOST = "sempre01.movisat.com";
    private final String USER = "android";
    private final String PASS = "movisatAndroid2015";
    private final String DB = "ecosat.sqlite";

    /**
     * Sube la base de datos a la Intranet.
     */
    public void uploadDataBaseToIntranet(Context context, String imei) {
        int empresa = 0;
        String dispositivo = "";
        try {
            empresa = Integer.parseInt(Config.getInstance().getValue("loginEmpresa"));
            dispositivo = Config.getInstance().getValue("nombreDispositivo");
        } catch (Exception e) {
            e.printStackTrace();
        }

        Ftp.sendFile(HOST, USER, PASS,
              context.getDatabasePath(DB) + "",
              System.currentTimeMillis() + "_" + empresa + "_" + dispositivo + "_" + imei
                    + "_database.sqlite");
    }

}



