package com.movisat.utilities;

import android.content.*;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteQueryBuilder;
import android.net.Uri;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.movisat.ecosat.MainActivity;

public class MovisatShared extends ContentProvider {
    private static final String TAG = "MovisatShared";
    private static final String CONTENT_NAME = "com.movisat.ecosat.shareDB";
    public static final Uri CONTENT_URI = Uri.parse("content://" + CONTENT_NAME);

    // Tablas
    private static final int CONF = 4;

    private static final UriMatcher uriMatcher;
    private SQLiteDatabase db = null;
    private static MovisatShared instance = null;

    static {

        // Creo el objeto con las URI's permitidas
        uriMatcher = new UriMatcher(UriMatcher.NO_MATCH);

        // Para operaciones con la base de datos
        uriMatcher.addURI(CONTENT_NAME, "conf", CONF);
    }

    public boolean onCreate() {

        try {

            // En éste punto no intento conectar con la BD porque éste método es
            // llamado por la JVM automáticamente y es posible que todavía no
            // exista la base de datos

            instance = this;

        } catch (Throwable e) {
        }

        return false;
    }

    private synchronized boolean openDatabase() {

        try {
            // Conecto con la base de datos la primera vez
            if (db == null) {

                Log.d(TAG, "Conectando con base de datos...");

                db = Database.getConnection(MainActivity.getInstance()
                                .getDatabasePath("ecosat.sqlite").getPath(),
                        SQLiteDatabase.OPEN_READONLY);

                Log.d(TAG, "Conexión con base de datos establecida");
            }

        } catch (Throwable e) {
            Log.d(TAG, e.getMessage());
        }

        return db != null ? true : false;
    }

    public String getType(Uri uri) {
        return null;
    }

    @Nullable
    @Override
    public Uri insert(@NonNull Uri uri, @Nullable ContentValues contentValues) {
        return null;
    }

    @Override
    public int delete(@NonNull Uri uri, @Nullable String s, @Nullable String[] strings) {
        return 0;
    }

    @Override
    public int update(@NonNull Uri uri, @Nullable ContentValues contentValues, @Nullable String s, @Nullable String[] strings) {
        return 0;
    }

    public synchronized Cursor query(Uri uri, String[] projection, String selection,
                                     String[] selectionArgs, String sortOrder) {
        Cursor cur = null;

        try {

            // Creo el objeto para hacer la consulta
            SQLiteQueryBuilder qry = new SQLiteQueryBuilder();

            // Compruebo la URI para ver a que tabla se refiere la consulta
            switch (uriMatcher.match(uri)) {
                case CONF:
                    qry.setTables("conf");
                    break;

                default:
                    return null;
            }

            if (openDatabase()) {

                // Ejecuto la consulta
                cur = qry.query(db, projection, selection, selectionArgs,
                        null, null, sortOrder);

                // Devuelvo el resultado de la consulta
                cur.setNotificationUri(getContext().getContentResolver(), uri);
            }

        } catch (Throwable e) {
            Log.e(TAG, e.getMessage());
        }

        return cur;
    }


}
