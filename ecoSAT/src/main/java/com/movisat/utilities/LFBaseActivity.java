package com.movisat.utilities;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;

import com.movisat.ecosat.R;
import com.movisat.utils.LFPowerUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.InvalidParameterException;

import android_serialport_api.SerialPort;
import android_serialport_api.SerialPortTool;

public abstract class LFBaseActivity extends Activity {

    public static final String PATH = "/dev/ttyS3";
    public static final int BAUTRATE = 9600;

    protected SerialPortTool serialPortTool;
    protected SerialPort mSerialPort;
    protected OutputStream mOutputStream;
    private InputStream mInputStream;
    private ReadThread mReadThread;

    private class ReadThread extends Thread {

        @Override
        public void run() {

            super.run();
            while (!isInterrupted()) {
                int size;
                try {
                    byte[] buffer = new byte[64];
                    if (mInputStream == null) {
                        LFPowerUtils.power("1");
                        open();
                    }
                    size = mInputStream.read(buffer);
                    if (size > 0) {
                        onDataReceived(buffer, size);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    return;
                }
            }
        }
    }

    private void DisplayError(int resourceId) {
        AlertDialog.Builder b = new AlertDialog.Builder(this);
        b.setTitle("ERROR");
        b.setMessage(resourceId);
        b.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int which) {
                finish();
            }
        });
        b.show();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    protected abstract void onDataReceived(final byte[] buffer, final int size);


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Log.i("onKeyDown", "keyCode"+keyCode);
        if(keyCode == 301 || keyCode == 302 || keyCode == 303){ //KEYCODE_KEY_HSCAN = 301;302;303
            open();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        Log.i("onKeyUp", "keyCode"+keyCode);
        if(keyCode == 301 || keyCode == 302 || keyCode == 303){ //KEYCODE_KEY_HSCAN = 301;302;303
            close();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        close();
        super.onDestroy();
    }

    public boolean open() {
        boolean bret = false;
        if (serialPortTool == null) {
            serialPortTool = new SerialPortTool();
        }

        try {
            mSerialPort = serialPortTool.getSerialPort(PATH, BAUTRATE);
            mOutputStream = mSerialPort.getOutputStream();
            mInputStream = mSerialPort.getInputStream();

            /* Create a receiving thread */
            if (mReadThread == null) {
                mReadThread = new ReadThread();
                mReadThread.start();
            }
            bret = true;
        } catch (SecurityException e) {
            DisplayError(R.string.error_security);
        } catch (IOException e) {
            DisplayError(R.string.error_unknown);
        } catch (InvalidParameterException e) {
            DisplayError(R.string.error_configuration);
        } catch (Exception e) {
            DisplayError(R.string.error_unknown);
        }

        return bret;
    }

    public void close() {
        System.out.println("closeLFU9000Port" + LFPowerUtils.isOpened() );
        if(LFPowerUtils.isOpened()){
            LFPowerUtils.power("0");
        }
        if (mReadThread != null) {
            mReadThread.interrupt();
            mReadThread = null;
        }
        if (serialPortTool != null) {
            serialPortTool.closeSerialPort();
            serialPortTool = null;
        }
        mSerialPort = null;
    }
}
