package com.movisat.utilities;

import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import android.content.Context;
import android.content.CursorLoader;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.media.MediaScannerConnection;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;

import androidx.annotation.DrawableRes;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utils.Utilss;

import org.json.JSONException;
import org.json.JSONObject;


public class Utils {

    private static SimpleDateFormat auxSdf = null;
    private static SimpleDateFormat auxSdf2 = null;
    private static Calendar auxCal = null;
    private static Calendar auxCal2 = null;
    static private String sync = "Utils_sync";
    final static char HEXC[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8',
            '9', 'A', 'B', 'C', 'D', 'E', 'F'};


    /**
     * Redimensiona una imagen
     *
     * @param srcBmp La imagen original
     * @param width  Anchura de la nueva imagen
     * @param height Altura de la nueva imagen
     * @return La imagen redimensionada
     */

    @SuppressWarnings("deprecation")
    public static BitmapDrawable resizeImage(Bitmap srcBmp, float width,
                                             float height, int rotate) {
        Bitmap res = null;

        try {

            synchronized (sync) {

                // Copio la imagen original
                Bitmap tempBmp = srcBmp.copy(Bitmap.Config.ARGB_8888, true);

                // Calculo la nueva escala
                float scaleWidth = width / tempBmp.getWidth();
                float scaleHeight = height / tempBmp.getHeight();

                // Creo una matriz para manipularla
                Matrix matrix = new Matrix();

                // Cambio el tamaio
                matrix.postScale(scaleWidth, scaleHeight);

                // Giro la imagen
                if (rotate != 0)
                    matrix.postRotate(rotate);

                // Creo la nueva imagen redimensionada
                res = Bitmap.createBitmap(tempBmp, 0, 0, tempBmp.getWidth(),
                        tempBmp.getHeight(), matrix, true);
            }

        } catch (Exception e) {
        }

        return new BitmapDrawable(res);
    }

    public static int convertToPixels(Context context, int nDP) {
        final float conversionScale = context.getResources().getDisplayMetrics().density;
        return (int) ((nDP * conversionScale) + 0.5f);
    }

    public static Bitmap writeTextOnDrawable(Context context, Bitmap bm, String text, int textSize, int color, Paint.Align align) {
        Bitmap res = bm.copy(Bitmap.Config.ARGB_8888, true);
        try {

            Typeface tf = Typeface.create("Helvetica", Typeface.BOLD);

            Paint paint = new Paint();
            paint.setStyle(Paint.Style.FILL);
            paint.setColor(color);
            paint.setTypeface(tf);
            paint.setTextAlign(align);
            paint.setTextSize(convertToPixels(context, textSize));

            Rect textRect = new Rect();
            paint.getTextBounds(text, 0, text.length(), textRect);

            Canvas canvas = new Canvas(res);

            //Calculamos el centro
            int xPos = canvas.getWidth() / 2;
            int yPos = (int) ((canvas.getHeight() / 2) - ((paint.descent() + paint.ascent()) / 2));
            //Pintamos el texto
            canvas.drawText(text, xPos, yPos, paint);

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    /**
     * @param oldDate
     * @param newDate
     * @param timeUnit
     * @return
     */
    public static long getDateDiff(long oldDate, long newDate, TimeUnit timeUnit) {
        long diffInMillies = newDate - oldDate;
        return timeUnit.convert(diffInMillies, TimeUnit.MILLISECONDS);
    }

    public static Bitmap GetBitmapToArrayBytes(byte[] arrayBytes) {
        Bitmap res = null;

        try {
            res = BitmapFactory.decodeByteArray(arrayBytes, 0,
                    arrayBytes.length);

        } catch (Exception e) {
        }

        return res;
    }

    public static Bitmap ResizeImage(Bitmap srcBmp, float width, float height,
                                     int rotate) {
        Bitmap res = null;

        try {

            synchronized (sync) {

                // Copio la imagen original
                Bitmap tempBmp = srcBmp.copy(Bitmap.Config.ARGB_8888, true);

                // Calculo la nueva escala
                float scaleWidth = width / tempBmp.getWidth();
                float scaleHeight = height / tempBmp.getHeight();

                // Creo una matriz para manipularla
                Matrix matrix = new Matrix();

                // Cambio el tamaio
                matrix.postScale(scaleWidth, scaleHeight);

                // Giro la imagen
                if (rotate != 0)
                    matrix.postRotate(rotate);

                // Creo la nueva imagen redimensionada
                res = Bitmap.createBitmap(tempBmp, 0, 0, tempBmp.getWidth(),
                        tempBmp.getHeight(), matrix, true);
            }

        } catch (Exception e) {
        }

        return res;
    }


    // jcaballero añadido método para decodificar la imagen según el tamaño indicado por parámetro,
    public static Bitmap decodeBitmapFromFile(String path, int reqWidth, int reqHeight) {

        // First decode with inJustDecodeBounds=true to check dimensions
        final BitmapFactory.Options options = new BitmapFactory.Options();
        //options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(path, options);

        // Calculate inSampleSize, Raw height and width of image
        final int height = options.outHeight;
        final int width = options.outWidth;
        options.inPreferredConfig = Bitmap.Config.RGB_565;
        int inSampleSize = 1;

        if (height > reqHeight) {
            inSampleSize = Math.round((float) height / (float) reqHeight);
        }
        int expectedWidth = width / inSampleSize;

        if (expectedWidth > reqWidth) {
            // if(Math.round((float)width / (float)reqWidth) > inSampleSize) //
            // If bigger SampSize..
            inSampleSize = Math.round((float) width / (float) reqWidth);
        }

        options.inSampleSize = inSampleSize;

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false;

        return BitmapFactory.decodeFile(path, options);
    }


    /**
     * Formatea una fecha
     *
     * @param date   Fecha
     * @param format Formato de salida (p.ej. yyyy-MM-dd HH:mm:ss)
     * @return La fecha formateada
     */

    public static String datetimeToString(Date date, String format) {
        String res = "";

        try {

            SimpleDateFormat sdf = (SimpleDateFormat) SimpleDateFormat
                    .getDateTimeInstance();
            sdf.applyLocalizedPattern(format);

            res = sdf.format(date);

        } catch (Throwable e) {
        }

        return res;
    }

    /**
     * Resuelve el problema con el método getString del JSONObject. El método devuelve los campos
     * null como un string con el valor 'null'. Con este método los valores null se devuelven como
     * null reales.
     */
    public static String jsonStringNormalize(JSONObject jsonObject, String key) throws JSONException {
        return Utilss.normalize(jsonObject.getString(key));
    }

    /**
     * @param dateString
     * @return
     */
    public static java.util.Date StringToDateTime(String dateString) {
        if (dateString == null || dateString.length() == 0)
            return null;
        java.util.Date fecha = null;
        try {
            String formato = "yyyy-MM-dd HH:mm:ss";
            SimpleDateFormat format = (SimpleDateFormat) SimpleDateFormat
                    .getDateTimeInstance();
            format.applyLocalizedPattern(formato);

            long milisegundos = (format.parse(dateString.replace('T', ' '))).getTime();
            fecha = new java.util.Date(milisegundos);

        } catch (Throwable e) {
        }

        return fecha;
    }

    /**
     * Convierte una fecha a segundos
     *
     * @param strDate Fecha con formato yyyy-MM-dd hh:MM:ss
     * @return Segundos desde 01/01/1970 00:00:00
     */
    public static long strDatetimeToSeconds(String strDate) {
        long res = 0;

        if (strDate.length() == 0) return 0;

        try {

            synchronized (sync) {

                Calendar.getInstance().setTimeZone(
                        TimeZone.getTimeZone("GMT+00:00"));

                SimpleDateFormat sdf = (SimpleDateFormat) SimpleDateFormat
                        .getInstance();

                Calendar.getInstance().setTime(
                        sdf.parse(strDate.substring(0, 19)));

                res = Calendar.getInstance().getTimeInMillis() / 1000;
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;

    }

    /**
     * Convierte segundos a fecha con formato
     *
     * @param seconds Segundos desde 01/01/1970 00:00:00
     * @param format  Formato de salida (p.ej. yyyy-MM-dd HH:mm:ss)
     * @return La fecha formateada
     */
    public static String secondsToDatetimeString(long seconds, String format) {
        String res = "";

        try {
            Utils.strDatetimeToSeconds("");

            synchronized (sync) {

                Calendar calendar = Calendar.getInstance();
                calendar.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));

                calendar.setTimeInMillis(seconds * 1000);

                res = datetimeToString((Date) calendar.getTime(), format);
            }

        } catch (Throwable e) {
        }

        return res;
    }

    public static String getM5(String input) {
        String md5 = null;

        try {

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            BigInteger number = new BigInteger(1, messageDigest);

            md5 = number.toString(16);

            while (md5.length() < 32)
                md5 = "0" + md5;

        } catch (Throwable e) {

            md5 = null;
        }

        return md5;
    }

    /**
     * Convierte un array de bytes en Drawable
     */
    @SuppressWarnings("deprecation")
    public static Drawable convertArrayByteToDrawable(byte[] src) {

        ByteArrayInputStream bis = new ByteArrayInputStream(src);
        Bitmap tempBitmap = BitmapFactory.decodeStream(bis);

        return new BitmapDrawable(tempBitmap);
    }

    /**
     * Envia informaciin para depuraciin
     */
    public static void debug(Object obj, String tag, String message) {

        try {

            Log.d(obj.getClass().getSimpleName(), tag + " " + message);

        } catch (Throwable e) {
        }

    }

    /**
     * Convierte una cadena con caracteres hexadecimales un array de bytes.
     *
     * @param cadena  Cadeana a convertir
     * @param destino array de destino
     * @param offset  the offset
     * @param length  Cuantos bytes
     * @return String con el volcado en hexadecimal
     * @throws NumberFormatException the number format exception
     */
    public static byte[] hexToArray(final String cadena, byte[] destino,
                                    int offset, int length) throws NumberFormatException {

        if (cadena == null
                || (length >= cadena.length() && cadena.length() % 2 != 0))
            throw new NumberFormatException(
                    "La longitud de la cadena debe ser par");

        if (destino == null)
            destino = new byte[Math.min(length, cadena.length() / 2)];

        for (int i = 0; i < Math.min(length, cadena.length() / 2); i++) {
            destino[offset + i] = (byte) ((0xFF) & Integer.parseInt(
                    cadena.substring(i * 2, i * 2 + 2), 16));
        }

        return destino;
    }

    /**
     * Convierte un array de bytes en una cadena hexadecimal.
     *
     * @param array  Array a convertir
     * @param offset Desde donde se empieza
     * @param length Cuantos bytes
     * @return String con el volcado en hexadecimal
     */
    public static String arrayToHex(byte[] array, int offset, int length) {
        StringBuffer ultima = new StringBuffer();

        for (int i = 0; i < length; i++) {
            ultima.append(HEXC[(array[offset + i] >>> 4) & 0x0f]);
            ultima.append(HEXC[array[offset + i] & 0x0f]);
        }

        return ultima.toString();
    }

    static public Drawable getAndroidDrawable(String pDrawableName) {
        int resourceId = Resources.getSystem().getIdentifier(pDrawableName, "drawable", "android");
        if (resourceId == 0) {
            return null;
        } else {
            return Resources.getSystem().getDrawable(resourceId);
        }
    }

    static public long getTime() {
        return System.currentTimeMillis() / 1000;
    }


    public static int parseStringToInt(String value, int default_value) {
        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            return default_value;
        }
    }

    public static int parseStringToInt(String value) {
        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            return 0;
        }
    }

    public static String getRealPathFromURI(Uri contentUri, Context context) {
        String[] proj = {MediaStore.Images.Media.DATA};
        CursorLoader loader = new CursorLoader(context, contentUri, proj, null, null, null);
        Cursor cursor = loader.loadInBackground();
        int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
        cursor.moveToFirst();
        String result = cursor.getString(column_index);
        cursor.close();
        return result;
    }


    /**
     * Obtiene un BitmapDescriptor a partir de un recurso de imagen vectorial. Esta función se
     * utiliza para mostrar marcadores en el mapa desde imágenes vectoriales, porque una conversión
     * directa con BitmapDescriptorFactory.fromResource() puede causar una excepción en algunos
     * dispositivos. https://stackoverflow.com/questions/33548447
     *
     * @param context Contexto para acceder a los recursos.
     * @param id      Id del recurso vectorial.
     * @return BitmapDescriptor a partir del recurso vectorial.
     */
    public static BitmapDescriptor getBitmapDescriptorFromVectorDrawable(Context context, @DrawableRes int id) {

        BitmapDescriptor bitmapDescriptor = null;

        try {
            Drawable vectorDrawable = ResourcesCompat.getDrawable(context.getResources(), id, null);
            Bitmap bitmap = Bitmap.createBitmap(vectorDrawable.getIntrinsicWidth(),
                    vectorDrawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(bitmap);
            vectorDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            vectorDrawable.draw(canvas);
            bitmapDescriptor = BitmapDescriptorFactory.fromBitmap(bitmap);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return bitmapDescriptor;
    }


    /**
     * Convierte una cadena de texto a una lista de número enteros.
     *
     * @param string     Cadena de texto que se desea convertir a una lista.
     * @param splitRegex Expresión regular para separar la cadena de texto en elementos.
     * @return Lista de números enteros.
     */
    public static List<Integer> stringToIntegerList(String string, String splitRegex) {

        List<Integer> integers = new ArrayList<>();

        try {
            String[] values = string.split(splitRegex);

            for (String value : values)
                integers.add(Integer.valueOf(value));

        } catch (Throwable e) {
        }

        return integers;
    }


    /**
     * Convierte los elementos de una lista a una cadena de texto, usando el separador indicado
     * para delimitar los elementos.
     */
    public static String listToSingleString(List list, String delimiter) {

        try {
            return TextUtils.join(delimiter, list);

        } catch (Throwable e) {
        }

        return null;
    }


    /**
     * Convierte un String en un objeto Date siguiendo el formato de fecha especificado.
     *
     * @param dateStr Cadena de texto representando una fecha en el formato indicado.
     * @param format  Formato de fecha que sigue dateStr.
     * @return Objeto Date que representa la fecha de dateStr.
     */
    public static Date stringToDate(String dateStr, String format) {

        try {
            DateFormat dateFormat = new SimpleDateFormat(format);
            return dateFormat.parse(dateStr);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return null;
    }

    public static boolean isPaqueteInstalado(Context contexto, String packageName) {
        try {
            PackageManager pm = contexto.getPackageManager();
            pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (Throwable e) {
        }
        return false;
    }

    // Dada una imagen, se convierte a base 64 para su envío al servidor.
    public static String imagePathToBase64(String path) {
        String s = "";

        File file = new java.io.File(path);
        byte[] bytesArray = new byte[(int) file.length()];
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(file);
            fis.read(bytesArray); //read file into bytes[]
            fis.close();
            s = Base64
                    .encodeToString(bytesArray, Base64.DEFAULT);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return s;
    }

    public static void saveImage(Bitmap foto, int id) {

        // compruebo si existe el fichero vacío .nomedia, en ese caso lo creo, este nos sirve para que
        // las imágenes que guardemos de la app, no sean visibles desde la galería


        String file_path_no_media = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getPath() + "/EcoSAT/";
        File dir_nomedia = new File(file_path_no_media);
        if (!dir_nomedia.exists()) {
            dir_nomedia.mkdirs();
        }

        File file_nomedia = new File(dir_nomedia, ".nomedia");

        try {
            if (!file_nomedia.exists())
                file_nomedia.createNewFile();

        } catch (IOException e) {
            e.printStackTrace();
        }


        String NameOfFolder = "/EcoSAT/incidencias";
        String file_path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getPath() + NameOfFolder;

        File dir = new File(file_path);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(dir, id + ".jpg");

        try {
            FileOutputStream fOut = new FileOutputStream(file,true);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            foto.compress(Bitmap.CompressFormat.JPEG, 80, baos);
            byte[] byteFormat = baos.toByteArray();
            fOut.flush();
            fOut.close();
            MakeSureFileWasCreatedThenMakeAvabile(file);
            //AbleToSave();
        } catch (FileNotFoundException e) {
            MyLoggerHandler.getInstance().error(e);
        } catch (IOException e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public static void MakeSureFileWasCreatedThenMakeAvabile(File file) {
        MediaScannerConnection.scanFile(MainActivity.getInstance(),
                new String[]{file.toString()}, null,
                new MediaScannerConnection.OnScanCompletedListener() {
                    public void onScanCompleted(String path, Uri uri) {
                    }
                });
    }

    public static String longToDatetimeString(String format, long longDate) {
        String res = "";

        try {

            synchronized (sync) {

                if (auxSdf2 == null) {

                    auxSdf2 = new SimpleDateFormat(format);
                    auxCal2 = Calendar.getInstance();
                    auxCal2.setTimeZone(TimeZone.getTimeZone("GMT+00:00"));
                }

                auxCal2.setTimeInMillis(longDate * 1000);
                res = auxSdf2.format(auxCal2.getTime());
            }

        } catch (Throwable e) {
        }

        return res;
    }

    /**
     * Mantis 4842
     * Para tratar el problema de subida de imágenes en zonas de poca cobertura, se ajusta el nivel
     * de compresión de las imágenes en función del nivel de cobertura y del tipo de red a la que se está conectado.
     * El rango va desde 0 (calidad baja, imagen muy comprimida) hasta 100 (calidad alta, imagen poco comprimida)
     * <p>
     * Cobertura    Calidad
     * 0-30         20
     * 31-50        40
     * 51-70        50
     * 71-100       70
     */
    public static int getCalidadImagenSegunCobertura(Context context) {
        int csq = Phone.getInstance().getCsq();
        double coberturaPorc = csq * 100 / 31; // porcentaje de cobertura
        String network = getNetworkClass(context);

        Log.e("COBERTURA", "Nivel de cobertura " + csq + " porcentaje de cobertura " + coberturaPorc + " y red=" + network);

        // Nos aseguramos de que NO devuelve datos negativos, solo evaluamos >= 0.
        // Además, solo conexiones 3G o 4G. Si es 2G default calidad 20;
        if (coberturaPorc >= 0 && (network.equals("3G") || network.equals("4G"))) {
            if (coberturaPorc <= 30) return 20;
            else if (coberturaPorc <= 50) return 40;
            else if (coberturaPorc <= 70) return 50;
            else return 70;
        } else Log.e("Error", "csq=" + csq + " y red=" + network);

        return 20; //default
    }

    public static String getNetworkClass(Context context) {
        TelephonyManager mTelephonyManager = (TelephonyManager)
                context.getSystemService(Context.TELEPHONY_SERVICE);
        int networkType = mTelephonyManager.getNetworkType();
        switch (networkType) {
            case TelephonyManager.NETWORK_TYPE_GPRS:
            case TelephonyManager.NETWORK_TYPE_EDGE:
            case TelephonyManager.NETWORK_TYPE_CDMA:
            case TelephonyManager.NETWORK_TYPE_1xRTT:
            case TelephonyManager.NETWORK_TYPE_IDEN:
                return "2G";
            case TelephonyManager.NETWORK_TYPE_UMTS:
            case TelephonyManager.NETWORK_TYPE_EVDO_0:
            case TelephonyManager.NETWORK_TYPE_EVDO_A:
            case TelephonyManager.NETWORK_TYPE_HSDPA:
            case TelephonyManager.NETWORK_TYPE_HSUPA:
            case TelephonyManager.NETWORK_TYPE_HSPA:
            case TelephonyManager.NETWORK_TYPE_EVDO_B:
            case TelephonyManager.NETWORK_TYPE_EHRPD:
            case TelephonyManager.NETWORK_TYPE_HSPAP:
                return "3G";
            case TelephonyManager.NETWORK_TYPE_LTE:
                return "4G";
            default:
                return "Unknown";
        }
    }

}
