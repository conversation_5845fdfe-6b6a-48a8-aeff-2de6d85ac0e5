package com.movisat.utilities;

import static com.movisat.utilities.Utils.MakeSureFileWasCreatedThenMakeAvabile;

import android.content.Context;
import android.graphics.Bitmap;
import android.os.Environment;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

public class StoreImage {
    private String resourcePath;
    private static StoreImage instance;
    private  boolean isInternalPath = false;
    private Context context;
    private static final String RUTA_IMAGENES_INCIDENCIAS = "/EcoSAT/incidencias";

    public static StoreImage getInstance(){
        if(instance == null){
            instance = new StoreImage();
            instance.context = MainActivity.getInstance();
        }
        return instance;
    }

    public String imageSave(Bitmap foto, int idFoto) {
        loadPaths();
        resourcePath = pathFinal();
        if(!isInternalPath) {
            String file_path_no_media = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getPath() + "/EcoSAT/";
            File dir_nomedia = new File(file_path_no_media);
            if (!dir_nomedia.exists()) {
                dir_nomedia.mkdirs();
            }

            File file_nomedia = new File(dir_nomedia, ".nomedia");

            try {
                if (!file_nomedia.exists())
                    file_nomedia.createNewFile();

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        File dir = new File(resourcePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        File file = new File(dir, idFoto + ".jpg");
        if(file.exists())
            idFoto = idFoto + 1;

        try {
            FileOutputStream fOut = new FileOutputStream(file,true);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            foto.compress(Bitmap.CompressFormat.JPEG,  Utils.getCalidadImagenSegunCobertura(context),baos);
            byte[] byteFormat = baos.toByteArray();
            fOut.write(byteFormat);
            fOut.flush();
            fOut.close();
            MakeSureFileWasCreatedThenMakeAvabile(file);
            //AbleToSave();
        } catch (FileNotFoundException e) {
            MyLoggerHandler.getInstance().error(e);
        } catch (IOException e) {
            MyLoggerHandler.getInstance().error(e);
            if(!isInternalPath){
                isInternalPath = true;
                resourcePath = null;
                imageSave(foto,idFoto);
            }else{
                return null;
            }
        }
        return file.getAbsolutePath();
    }

    private  String pathFinal(){
        String path = resourcePath + RUTA_IMAGENES_INCIDENCIAS;
        return path;
    }

    private  void loadPaths(){
        resourcePath = (isInternalPath && resourcePath == null) ? Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM).getPath() : context.getFilesDir().getPath();
    }

}
