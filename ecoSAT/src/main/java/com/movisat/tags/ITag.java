package com.movisat.tags;

import com.environment.Environment;

import java.io.Serializable;
import java.util.Date;

public abstract class ITag implements Serializable {

    public String Matricula;
    public String Raw;
    public Date Fecha;
    public boolean isShortInDatabase = false;

    public ITag(String matricula, String raw, Date fecha) {
        Matricula = matricula;
        // Evitamos que se pueda asignar un valor incorrecto.
        if (raw == null) raw = "";
        Raw = raw.trim();
        Fecha = fecha;
    }

    public boolean isNFC() {
        return this instanceof TagNFC;
    }

    public boolean isUHF() {
        return this instanceof TagUHF;
    }

    public boolean is134() {
        return this instanceof Tag134;
    }

    public abstract boolean isValid();

    public abstract String get();

    public abstract String getShort();

    public abstract String getLong();

    public abstract String toString();
}
