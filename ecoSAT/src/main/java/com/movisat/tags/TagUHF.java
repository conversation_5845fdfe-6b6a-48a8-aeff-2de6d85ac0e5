package com.movisat.tags;

import com.environment.Environment;

import java.util.Date;

public class TagUHF extends ITag {
    public TagUHF(String matricula, String raw, Date fecha) {
        super(matricula, raw, fecha);
    }

    public String get() {
        if (isShortInDatabase) return getShort();
        if (Environment.isTagUHFExtended) return getLong();
        // If isTagExtended is false but isTagLongAndShort is true, the returned tag must be long.
        if (Environment.isTagUHFLongAndShort) return getLong();
        return getShort();
    }

    @Override
    public String getShort() {
        if (Raw.length() <= 8) return Raw;
        return Raw.substring(Raw.length() - 8);
    }

    @Override
    public String getLong() {
        return Raw;
    }

    @Override
    public boolean isValid() {
        try {
            String tag = get();

            if (tag.length() < 8) return false;

            // Se comprueba que el tag sea hexadecimal
            return tag.length() % 2 == 0 && tag.matches("[A-Fa-f0-9]+");
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return false;
    }


    @Override
    public String toString() {
        return "TagUHF [Matricula=" + Matricula + ", Raw=" + Raw + ", Fecha=" + Fecha + ", get() =" + get() + "]";
    }
}
