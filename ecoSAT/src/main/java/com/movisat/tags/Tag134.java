package com.movisat.tags;

import com.environment.Environment;

import java.util.Date;

public class Tag134 extends ITag {
    public Tag134(String matricula, String raw, Date fecha) {
        super(matricula, raw, fecha);
    }

    public String get() {
        return getShort();
    }

    @Override
    public String getShort() {
        if (Raw.length() <= 6) return Raw;
        return Raw.substring(Raw.length() - 6);
    }

    @Override
    public String getLong() {
        return Raw;
    }

    @Override
    public String toString() {
        return "Tag134 [Matricula=" + Matricula + ", Raw=" + Raw + ", Fecha=" + <PERSON>cha + ", get() =" + get() + "]";
    }

    @Override
    public boolean isValid() {
        try {
            String tag = get();

            if (tag.length() < 6) return false;

            // Se comprueba que el tag sea hexadecimal
            return tag.length() % 2 == 0 && tag.matches("[A-Fa-f0-9]+");
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return false;
    }
}
