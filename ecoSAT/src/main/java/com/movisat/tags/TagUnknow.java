package com.movisat.tags;

import com.environment.Environment;
import com.movisat.utils.Utilss;

import java.util.Date;

/// This class is for tags read from the database, where we don’t know the type.
public class TagUnknow extends ITag {
    public TagUnknow(String matricula, String raw, Date fecha) {
        super(matricula, raw, fecha);
    }

    public static TagUnknow empty() {
        return new TagUnknow("", "", new Date());
    }

    public String get() {
        return Raw;
    }

    @Override
    public String getShort() {
        return Raw;
    }

    @Override
    public String getLong() {
        return Raw;
    }

    @Override
    public boolean isValid() {
        if (Utilss.isNullOrEmpty(Raw)) return false;
        return true;
    }


    @Override
    public String toString() {
        return "TagUnknow [Matricula=" + Matricula + ", Raw=" + Raw + ", Fecha=" + Fecha + ", get() =" + get() + "]";
    }
}
