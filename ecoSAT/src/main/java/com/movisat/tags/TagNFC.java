package com.movisat.tags;

import com.movisat.utils.Utilss;

import java.util.Date;

public class TagNFC extends ITag {
    public TagNFC(String matricula, String raw, Date fecha) {
        super(matricula, raw, fecha);
    }

    @Override
    public String get() {
        return Raw;
    }

    @Override
    public boolean isValid(){
        if (Utilss.isNullOrEmpty(Raw)) return false;
        return true;
    }

    @Override
    public String getShort() {
        return Raw;
    }

    @Override
    public String getLong() {
        return Raw;
    }

    @Override
    public String toString() {
        return "TagNFC [Matricula=" + Matricula + ", Raw=" + Raw + ", Fecha=" + <PERSON>cha + "]";
    }

}
