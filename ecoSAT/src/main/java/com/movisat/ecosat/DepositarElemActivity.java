package com.movisat.ecosat;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Vibrator;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.environment.Environment;
import com.movisat.database.DBElemento;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModelo;
import com.movisat.database.Tags;
import com.movisat.events.OnReadedTag;
import com.movisat.log.Logg;
import com.movisat.managers.DepositManager;
import com.movisat.managers.LFChainwayManager;
import com.movisat.managers.TagReaderManager;
import com.movisat.managers.UHFManager;
import com.movisat.tags.ITag;
import com.movisat.use_case.TagSendSensor;
import com.movisat.utils.LFByteUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created by dsanchez on 08/02/2018.
 */

public class DepositarElemActivity extends BaseActivity {

    private static final String TAG = "DEPOSITAR";
    private static DepositarElemActivity instance = null;

    public static int DEPOSITAR_ELEM_REQUEST = 1;

    private static final int ESTADO_SIN_INICIAR = 0;
    private static final int ESTADO_ESPERANDO_DEPOSITADO = 2;
    private static final int ESTADO_ESPERANDO_CREACION_ELEM = 3;
    private static final int ESTADO_ESPERANDO_CONFIRMACION = 5;

    private static final int TAG_VIBRATION_MS = 100;
    private static final boolean DEBUG = false;

    private int estado_activity = ESTADO_SIN_INICIAR;
    private Elemento elementoDepositado = null;
    private Tags tagDepositado = null;
    private boolean crearElemento = false;
    private boolean procesandoTag = false;
    private static ArrayList<ElementoModelo> modelos;

    private double latitude = 0;
    private double longitude = 0;

    private LinearLayout layoutEstadoDepositar;
    private ImageView imgEstadoDepositar;
    private TextView textEstadoDepositar;
    private TextView textAddMatricula;
    private EditText editMatriculaDepositado;
    private LinearLayout layoutBtnComprobarMatriculaCrearElemento;
    private Button btnComprobarMatriculaCrearElemento;
    private LinearLayout layoutTagDepositado;
    private TextView textTagDepositado;
    private Button btnVolver;
    private Button btnAceptar;
    private Button leerTag = null;
    private Tags tag = null;

    public static DepositarElemActivity getInstance() {
        return instance;
    }

    public static String binary(byte[] bytes, int radix) {
        return new BigInteger(1, bytes).toString(radix);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            instance = this;

            setContentView(R.layout.retirar_depositar_elem_layout);

            CheckBox checkBaja = findViewById(R.id.baja);
            checkBaja.setVisibility(View.INVISIBLE);

            // Se obtienen las coordenadas donde se debe depositar el elemento
            Intent intent = getIntent();
            latitude = intent.getDoubleExtra("latitud", Double.MIN_VALUE);
            longitude = intent.getDoubleExtra("longitud", Double.MIN_VALUE);

            if (latitude == Double.MIN_VALUE || longitude == Double.MIN_VALUE) {
                if (DEBUG)
                    Toast.makeText(this, "(Debug) Las coordenadas no son correctas.", Toast.LENGTH_SHORT).show();
                finish();
                return;
            }

            // Componentes de la UI para indicar la fase del proceso
            layoutEstadoDepositar = (LinearLayout) findViewById(R.id.layoutEstado);
            imgEstadoDepositar = (ImageView) findViewById(R.id.imgEstado);
            textEstadoDepositar = (TextView) findViewById(R.id.textEstado);
            textAddMatricula = (TextView) findViewById(R.id.textAddMatricula);

            // Componentes de la UI para mostrar la información del elemento leído
            editMatriculaDepositado = (EditText) findViewById(R.id.editMatricula);
            textTagDepositado = (TextView) findViewById(R.id.textTag);
            leerTag = (Button) findViewById(R.id.leerTag);

            leerTag.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    updateTag();
                }
            });

            // Layout de botón de comprobar e información de tag (para mostrarlo y ocultarlos)
            layoutBtnComprobarMatriculaCrearElemento = (LinearLayout) findViewById(R.id.layoutBotonComprobarMatriculaCrearElemento);
            layoutTagDepositado = (LinearLayout) findViewById(R.id.layoutTag);

            // Botones de la UI
            btnComprobarMatriculaCrearElemento = (Button) findViewById(R.id.btnComprobarMatriculaCrearElemento);
            btnVolver = (Button) findViewById(R.id.btnVolver);
            btnAceptar = (Button) findViewById(R.id.btnAceptar);

            addListeners();

            // Se establecen los estados iniciales de la Activity
            setEstadoActivity(ESTADO_SIN_INICIAR);
            setEstadoActivity(ESTADO_ESPERANDO_DEPOSITADO);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }


    /**
     * Añade los listeners para las distintas funciones de la UI.
     */
    private void addListeners() {

        editMatriculaDepositado.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                // Se habilita/deshabilita el botón de comprobar matrícula según haya o no texto
                btnComprobarMatriculaCrearElemento.setEnabled(charSequence.length() > 0);
                btnComprobarMatriculaCrearElemento.setBackgroundResource(charSequence.length() > 0 ? R.drawable.bg_key : R.color.gris);
            }

            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void afterTextChanged(Editable editable) {
            }
        });

        btnComprobarMatriculaCrearElemento.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (estado_activity == ESTADO_ESPERANDO_DEPOSITADO)
                    comprobarMatricula();
                else if (estado_activity == ESTADO_ESPERANDO_CREACION_ELEM)
                    showCrearElemento();
            }
        });

        btnVolver.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        btnAceptar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (DepositManager.depositElement(elementoDepositado, tagDepositado, latitude, longitude, crearElemento, false)) {
                    MainActivity.getInstance().showMessage(getString(R.string.depositado_ok));
                    finish();
                } else {
                    MainActivity.getInstance().showMessage(R.string.depositado_error, Toast.LENGTH_LONG);
                }
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Logg.info(TAG + "-" + getActivityName(), "[onKeyDown] keyCode: " + keyCode);

        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            //fotos.clear();
            //UHFManager.get().release();
            finish();
        }

        return super.onKeyDown(keyCode, event);
    }


    /**
     * Toma la referencia del elemento que va a ser depositado y su tag y muestra sus datos en la UI.
     *
     * @param tag      Tag del elemento depositado.
     * @param elemento Elemento depositado.
     */
    private void setElementoDepositado(Tags tag, Elemento elemento) {
        if (tag != null) {
            editMatriculaDepositado.setText(tag.getMatricula());
            textTagDepositado.setText(tag.getTag());
            elementoDepositado = elemento;
            tagDepositado = tag;
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }


    @Override
    protected void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }


    private void comprobarMatricula() {

        try {
            Elemento elemento = null;
            Tags tag = null;
            String matricula = editMatriculaDepositado.getText().toString().trim();

            if (matricula.equals("")) {
                MainActivity.getInstance().showMessage("Introduzca una matrícula");
                return;
            }

            // Se obtiene el elemento a partir de la matrícula
            DBElemento dbElemento = new DBElemento();
            elemento = dbElemento.getElementoByMatricula(matricula, MainActivity.getInstance().getEmpresa());
            dbElemento.close();

            if (elemento != null) {
                // Se obtiene el tag a partir de la matrícula
                DBTags dbTags = new DBTags();
                tag = dbTags.getByMatricula(matricula, MainActivity.getInstance().getEmpresa());
                dbTags.close();
            }

            if (elemento == null || tag == null) {
                MainActivity.getInstance().showMessage("No existe ningún elemento con esta matrícula");
                return;
            }

            processElementoDepositado(tag, elemento);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Lanza el activity de creación de elemento.
     */
    private void showCrearElemento() {

        Intent pickContactIntent = new Intent(this, AddElemActivity2.class);
        startActivityForResult(pickContactIntent, DEPOSITAR_ELEM_REQUEST);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {

        if (requestCode == DEPOSITAR_ELEM_REQUEST && resultCode == RESULT_OK) {

            int idModelo = data.getIntExtra("idModelo", 0);
            String description = data.getStringExtra("description");

            elementoDepositado = new Elemento(0, 0,
                    MainActivity.getInstance().getEmpresa(), "", idModelo,
                    tagDepositado.getMatricula(), 0, latitude, longitude, description, "", 0, "");

            setEstadoActivity(ESTADO_ESPERANDO_CONFIRMACION);
        }
    }


    /**
     * Esta función está suscrita al EventBus y recibe de tags leídos por el lector Bluetooth.
     *
     * @param event Contiene el tag leído por el lector.
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRfidTagRead(OnReadedTag event) {
        try {
            // Se obtiene el tag leído de la base de datos
            DBTags dbTags = new DBTags();
            Tags tag = dbTags.getByTag(event.tag, MainActivity.getInstance().getEmpresa());
            dbTags.close();

            // Se procesa el tag leído
            tagRead(tag, event.tag);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Procesa el tag indicado en función del estado actual de la Activity (esperando o no tags)
     * y del elemento esperado (retirado o depositado).
     *
     * @param tag Tag que va a ser procesado.
     */
    private void tagRead(Tags tag, ITag iTag) {

        try {
            procesandoTag = false;
            // Se hace vibrar el dispositivo ante la lectura de un tag
            try {
                Vibrator v = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
                v.vibrate(TAG_VIBRATION_MS);
            } catch (Exception e) {
                MyLoggerHandler.getInstance().error(e);
                e.printStackTrace();
            }

            // Si ya se han leído el elemento depositado, no se aceptan más lecturas
            if (estado_activity > ESTADO_ESPERANDO_DEPOSITADO) {
                if (DEBUG)
                    Toast.makeText(this, "(Debug) Ya no se aceptan más lecturas de tags.", Toast.LENGTH_SHORT).show();
                return;
            }

            // Si la lectura ocurre antes de completar la anterior (por ejemplo, si se ha mostrado
            // un cuadro de diálogo), se indica y se ignora la lectura actual.
            if (procesandoTag) {
                MainActivity.getInstance().showMessage(R.string.no_se_puede_procesar);
                return;
            }

            TagSendSensor.execute(iTag, tag);

            // No se puede trabajar con un tag que no está en la base de datos
            if (tag == null)
                return;


            procesandoTag = true;

            // Se obtiene el elemento asociado al tag
            DBElemento dbElemento = new DBElemento();
            Elemento elemento = dbElemento.getElementoByTag(tag);
            dbElemento.close();

            if (elemento == null) {
                MainActivity.getInstance().showMessage(
                        getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()),
                        Toast.LENGTH_LONG);
                return;
            }

            editMatriculaDepositado.setText(elemento.getMatricula());

            switch (estado_activity) {

                case ESTADO_SIN_INICIAR:
                case ESTADO_ESPERANDO_CONFIRMACION:
                    procesandoTag = false;
                    break;

                case ESTADO_ESPERANDO_DEPOSITADO:
                    processElementoDepositado(tag, elemento);
                    break;
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }


    /**
     * Comprueba si el elemento es válido para ser retirado y realiza las acciones adecuadas.
     * El elemento a depositar debe existir y estar inactivo . Si está activo, se ofrece la
     * opción de cambiar ubicación a la del elemento retirado. Si no existe el elemento, se
     * ofrece la opción de crearlo.
     *
     * @param tag      Tag del elemento.
     * @param elemento Elemento asociado al tag.
     */
    private void processElementoDepositado(Tags tag, Elemento elemento) {

        if (elemento == null) {

            // No se ha encontrado elemento -> Se ofrece la opción de crearlo
            alertElemNoExisteCrear(tag);

        } else if (elemento.getEstado() == Elemento.ESTADO_ACTIVO) {

            // Elemento activo -> Se ofrece la opción de forzar cambio de ubicación
            alertElemActivoCambiarPU(tag, elemento);

        } else {

            // Elemento inactivo -> Se añade el elemento y se avanza a la siguiente fase de la Activity
            setElementoDepositado(tag, elemento);
            setEstadoActivity(ESTADO_ESPERANDO_CONFIRMACION);
        }
    }


    /**
     * Cambia el estado de la Activity y modifica los objetos y la UI en función del nuevo estado.
     *
     * @param estado Estado que tomará la Activity.
     */
    private void setEstadoActivity(int estado) {

        estado_activity = estado;

        switch (estado_activity) {

            case ESTADO_SIN_INICIAR:
                // Se establece la Activity a su estado inicial
                crearElemento = false;
                editMatriculaDepositado.setFocusable(true);
                editMatriculaDepositado.setText("");
                layoutBtnComprobarMatriculaCrearElemento.setVisibility(View.VISIBLE);
                layoutTagDepositado.setVisibility(View.GONE);
                btnComprobarMatriculaCrearElemento.setText(R.string.comprobar_matricula);
                btnComprobarMatriculaCrearElemento.setBackgroundResource(R.color.gris);
                btnComprobarMatriculaCrearElemento.setEnabled(false);
                btnAceptar.setBackgroundResource(R.color.gris);
                btnAceptar.setEnabled(false);
                break;

            case ESTADO_ESPERANDO_DEPOSITADO:
                // Se establece la Activity al estado de espera de elemento a depositar
                layoutEstadoDepositar.setBackgroundResource(R.color.gris);
                textEstadoDepositar.setText(R.string.escanear_elem_depositado_o_matricula);
                imgEstadoDepositar.setImageResource(R.drawable.ic_depositar_black_60dp);
                break;

            case ESTADO_ESPERANDO_CREACION_ELEM:
                // Se establece la Activity al estado de espera de creación de elemento
                layoutEstadoDepositar.setBackgroundResource(R.color.gris);
                textEstadoDepositar.setText(R.string.estado_crear_elemento);
                imgEstadoDepositar.setImageResource(R.drawable.ic_crear_black_60dp);
                layoutBtnComprobarMatriculaCrearElemento.setVisibility(View.VISIBLE);
                editMatriculaDepositado.setFocusable(false);
                layoutTagDepositado.setVisibility(View.VISIBLE);
                btnComprobarMatriculaCrearElemento.setText(R.string.crear_elemento);
                btnComprobarMatriculaCrearElemento.setBackgroundResource(R.drawable.bg_key);
                btnComprobarMatriculaCrearElemento.setEnabled(true);
                break;

            case ESTADO_ESPERANDO_CONFIRMACION:
                // Se establece la Activity al estado de espera de confirmación
                layoutEstadoDepositar.setBackgroundResource(R.color.theme_principal);
                textEstadoDepositar.setText(R.string.confirmar_depositar);
                imgEstadoDepositar.setImageResource(R.drawable.ic_check_black_60dp);
                editMatriculaDepositado.setFocusable(false);
                layoutBtnComprobarMatriculaCrearElemento.setVisibility(View.GONE);
                layoutTagDepositado.setVisibility(View.VISIBLE);
                btnAceptar.setBackgroundResource(R.drawable.bg_key);
                btnAceptar.setEnabled(true);
                break;
        }

        procesandoTag = false;
    }


    /**
     * Muestra un diálogo para indicar que el elemento que se desea depositar no existe,
     * ofreciendo la posibilidad de crearlo.
     *
     * @param tag Tag leído.
     */
    private void alertElemNoExisteCrear(final Tags tag) {

        AlertDialog.Builder builder = new AlertDialog.Builder(this);

        builder.setMessage(R.string.elem_no_existe_crear)
                .setTitle(R.string.elem_no_existe_crear_title)
                .setPositiveButton("Sí", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        // TODO Crear elemento

                        Elemento elemento = createElemento(tag);
                        if (elemento != null) {
                            crearElemento = true;
                            setElementoDepositado(tag, elemento);
                            if (DEBUG)
                                Toast.makeText(getBaseContext(), "(Debug) Elemento creado", Toast.LENGTH_SHORT).show();
                            setEstadoActivity(ESTADO_ESPERANDO_CREACION_ELEM);
                        } else {
                            if (DEBUG)
                                Toast.makeText(getBaseContext(), "(Debug) ERROR creando elemento", Toast.LENGTH_SHORT).show();
                            procesandoTag = false;
                        }

                    }
                })
                .setNegativeButton("No", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        dialog.cancel();
                    }
                })
                .setOnCancelListener(new DialogInterface.OnCancelListener() {
                    @Override
                    public void onCancel(DialogInterface dialogInterface) {
                        // La activity se queda a la espera de un nuevo tag (no se cambia el estado)
                        if (DEBUG)
                            Toast.makeText(getBaseContext(), "(Debug) Creación cancelada.", Toast.LENGTH_SHORT).show();
                        procesandoTag = false;
                    }
                })
                .create()
                .show();
    }


    /**
     * Muestra un diálogo para indicar que el elemento que se desea depositar ya está activo en
     * otro p.u., ofreciendo la posibilidad de forzar el cambio de p.u.
     *
     * @param tag      Tag leído.
     * @param elemento Elemento asociado al tag leído.
     */
    private void alertElemActivoCambiarPU(final Tags tag, final Elemento elemento) {

        AlertDialog.Builder builder = new AlertDialog.Builder(this);

        builder.setMessage(R.string.elem_activo_cambiar_pu)
                .setTitle(R.string.elem_activo_cambiar_pu_title)
                .setPositiveButton("Sí", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        setElementoDepositado(tag, elemento);
                        if (DEBUG)
                            Toast.makeText(getBaseContext(), "(Debug) El elemento cambiará su p.u.", Toast.LENGTH_SHORT).show();
                        setEstadoActivity(ESTADO_ESPERANDO_CONFIRMACION);
                    }
                })
                .setNegativeButton("No", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        dialog.cancel();
                    }
                })
                .setOnCancelListener(new DialogInterface.OnCancelListener() {
                    @Override
                    public void onCancel(DialogInterface dialogInterface) {
                        // La activity se queda a la espera de un nuevo tag (no se cambia el estado)
                        if (DEBUG)
                            Toast.makeText(getBaseContext(), "(Debug) Cambio de ubicación cancelado.", Toast.LENGTH_SHORT).show();
                        procesandoTag = false;
                    }
                })
                .create()
                .show();
    }


    /**
     * Crea un nuevo elemento con los valores del tag y la ubicación escogida, pero
     * no lo introduce en la base de datos.
     *
     * @param tag Tag del elemento que va a ser creado.
     * @return Elemento creado
     */
    private Elemento createElemento(Tags tag) {

        Elemento elemento = null;

        try {
            elemento = new Elemento(0, 0, MainActivity.getInstance().getEmpresa(),
                    "", 0, tag.getMatricula(), Elemento.ESTADO_ACTIVO,
                    latitude, longitude, "", "", 0, "");

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return elemento;
    }


    private void updateTag() {
        Logg.info(TAG, "[updateTag] Tag == null: " + (tag == null));
        if (tag == null) {

            // Comprobar si estoy utilizando el smartphone U9000
            if (Build.MODEL.equals("PDA")) {
                Logg.info(TAG, "[updateTag] U9000 - Leyendo 134 o UHF");
                TagReaderManager.read134orUHF((buffer, size) -> onReaded134TagData(buffer, size));
            }

            //leer tag
            else if (UHFManager.get().isReading()) {
                UHFManager.get().stopReadLoop();
            } else {
                // Se inicia la lectura UHF si el usuario está identificado en la aplicación
                MainActivity mainActivity = MainActivity.getInstance();
                if (mainActivity != null && mainActivity.getUsuario() > 0) {
                    UHFManager.get().readSingleTag(3000);
                }
            }
        }
    }

}
