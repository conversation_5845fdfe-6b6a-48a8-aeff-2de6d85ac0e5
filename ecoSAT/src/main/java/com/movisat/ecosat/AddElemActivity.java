package com.movisat.ecosat;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.content.res.Configuration;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.text.InputType;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.SimpleCursorAdapter;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.environment.Environment;
import com.google.android.gms.maps.model.LatLng;
import com.movisat.adapter.ElemModelAdapter;
import com.movisat.database.DBElemento;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBMunicipios;
import com.movisat.database.DBProvincias;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModelo;
import com.movisat.database.Tags;
import com.movisat.events.OnReadedTag;
import com.movisat.fragment.GestionElementos;
import com.movisat.log.Logg;
import com.movisat.managers.TagReaderManager;
import com.movisat.managers.UHFManager;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.services.MyLocationService;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.synchronize.DBSynchro;
import com.movisat.use_case.TagSendSensor;
import com.movisat.utilities.Config;
import com.movisat.utilities.GPS;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Photo;
import com.movisat.utilities.Utils;
import com.movisat.utilities.ZoomPhotoDialog;
import com.movisat.utils.GlobalUtils;
import com.movisat.utils_android.PhotoService;
import com.movisat.utils_android.UtilssAndroid;
import com.movisat.repository.ElementosRepository;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.zip.GZIPInputStream;

public class AddElemActivity extends BaseActivity {
    private static final String TAG = "AddElemActivity";
    private static AddElementParam param = null;
    private boolean modificarElementoAunqueNoExistaTag = false;
    private static AddElemActivity instance = null;
    private static Spinner spinnerModelos;
    private static ArrayList<ElementoModelo> modelos;
    private static final int RESULT_GALLERY = 0;
    private ClickImageView eventoClick = new ClickImageView();
    private LongClickImageView eventoLongClick = new LongClickImageView();
    private boolean no_indica_nombre = false;
    private String municipio = "";
    private boolean update_elemento = false;
    private boolean borrarImagenesAnterioresServidor = false;
    private final static int RADIO_METROS = 30;
    private boolean altaTaller = false;
    private int empresa;
    //--------------------------------------------

    public static AddElemActivity getInstance() {
        return instance;
    }

    public static void openWithParam(AddElementParam param, Activity activity) {
        AddElemActivity.param = param;
        Logg.info(TAG, "[openWithParam] " + param.toString());
        activity.startActivity(new Intent(activity, AddElemActivity.class));
    }

    public static void openFrom(Activity activity) {
        AddElemActivity.param = null;
        activity.startActivity(new Intent(activity, AddElemActivity.class));
    }

    // Campo NOMBRE
    TextView labelNombre = null;
    Spinner spinnerProvincias = null;
    EditText textPrefijo = null;
    Spinner spinnerPrefijo = null;
    EditText textNombre = null;

    // Campo MATRÍCULA
    TextView labelMatricula = null;
    EditText textMatricula = null;
    TextView labelTag = null;
    EditText textTag = null;
    Button leerTag = null;

    TextView textLatitude = null;
    TextView textLongitude = null;

    //Campo COD FISICO
    TextView labelCodFisico = null;
    EditText textCodFisico = null;

    // Campo DESCRIPCIÓN
    TextView labelDescripcion = null;
    EditText textDescripcion = null;
    LinearLayout llImagenes;

    LinearLayout infoPosLayout;
    ImageView btExisteImagen = null;
    private static ArrayList<Bitmap> fotos = new ArrayList<Bitmap>();
    // Historial imágenes (servidor)
    private TextView txtHistImgNumeroElem;
    private LinearLayout layoutScrollHistElem;
    private View histContainerElem;
    private ProgressBar progressHistImgElem;

    private Tags tag = null;
    private int tipoSoft;

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            if (param == null) {
                finish();
            }
            empresa = MainActivity.getInstance().getEmpresa();
            instance = this;
            borrarImagenesAnterioresServidor = false;
            altaTaller = false;

            setContentView(R.layout.add_elem_layout);
            String tipoSoftStr = Config.getInstance().getValue("tipoSoft", "0");
            tipoSoft = Utils.parseStringToInt(tipoSoftStr, 0);

            // Alta en taller.
            setRepairShopRegister();

            spinnerModelos = (Spinner) findViewById(R.id.cbModelos);

            // Campo NOMBRE
            labelNombre = (TextView) findViewById(R.id.lblNombre);
            spinnerProvincias = (Spinner) findViewById(R.id.eProvincias);
            spinnerProvincias.setVisibility(View.INVISIBLE);

            textPrefijo = (EditText) findViewById(R.id.ePrefijoNombre);
            spinnerPrefijo = (Spinner) findViewById(R.id.eMunicipio);
            textNombre = (EditText) findViewById(R.id.ebNombre);

            // Campos lat y long de elemento para Contenur
            infoPosLayout = findViewById(R.id.infoPosLayout);
            textLatitude = findViewById(R.id.textLatitude);
            textLongitude = findViewById(R.id.textLongitude);

            if (!Environment.isCompanyMadridContenur) {
                infoPosLayout.setVisibility(View.GONE);
            } else {
                textLatitude.setText(String.valueOf(param.latitude));
                textLongitude.setText(String.valueOf(param.longitude));
            }

            // Campo MATRÍCULA
            labelMatricula = (TextView) findViewById(R.id.textAddMatricula);
            textMatricula = (EditText) findViewById(R.id.ebMatricula);
            labelTag = (TextView) findViewById(R.id.textAddTag);
            textTag = (EditText) findViewById(R.id.ebTag);
            textTag.setEnabled(false);
            leerTag = (Button) findViewById(R.id.leerTag);

            // Campo COD FISICO
            labelCodFisico = (TextView) findViewById(R.id.textCodFisico);
            textCodFisico = (EditText) findViewById(R.id.ebCodFisico);

            leerTag.setOnClickListener(v -> updateTag());

            // Campo DESCRIPCIÓN
            labelDescripcion = (TextView) findViewById(R.id.textAddDescripcion);
            textDescripcion = (EditText) findViewById(R.id.ebDescripcion);

            btExisteImagen = (ImageView) findViewById(R.id.btExisteImagen);
            // Historial imágenes (servidor)
            txtHistImgNumeroElem = (TextView) findViewById(R.id.txt_hist_img_numero_elem);
            layoutScrollHistElem = (LinearLayout) findViewById(R.id.layout_scroll_hist_elem);
            histContainerElem = findViewById(R.id.relativelayout_hist_elem);
            progressHistImgElem = (ProgressBar) findViewById(R.id.progress_hist_img_elem);
            if (txtHistImgNumeroElem != null) txtHistImgNumeroElem.setText("0");

            //  Boton abrir galeria
            findViewById(R.id.btGalleryElem).setOnClickListener(v -> openGallery());

            // Evento boton add foto
            findViewById(R.id.btImagenElem).setOnClickListener(v -> {
                        // 05/08/2020 - DINIGO - Mantis #0004755
                        // Ahora se admiten 2 imágenes como máximo.
                        if (!sePuedenAniadirMasFotos()) {
                            Toast.makeText(MainActivity.getInstance(),
                                    "No puede añadir más de 2 fotografía. Por favor, elimina una imagen actual para poder asociar otra.",
                                    Toast.LENGTH_LONG).show();
                            return;
                        }

                        if (UtilssAndroid.isAndroidGreater11())
                            PhotoService.get().takePhoto(instance);
                        else
                            Photo.getInstance().dispatchTakePictureIntent(instance);
                    });

            findViewById(R.id.btPapeleraElem).setOnClickListener(v -> {
                // Si hay fotos seleccionadas preguntamos si queremos eliminar
                if (hayFotosSeleccionadas()) {
                    AlertDialog.Builder builder = new AlertDialog.Builder(instance);
                    builder.setMessage(getString(R.string.questionEliminarImagenesSeleccionadas));
                    builder.setPositiveButton(getString(R.string.yes), (dialog, which) -> eliminarFotos());
                    builder.setNegativeButton(getString(R.string.no), (dialog, which) -> dialog.dismiss());
                    builder.create().show();
                } else {
                    // Mostramos advertencia de que no hay imagenes seleccionadas
                    Toast.makeText(instance, R.string.noSelectedImages, Toast.LENGTH_SHORT).show();
                }
            });

            double lat = 0, lon = 0;

            labelCodFisico.setVisibility(View.GONE);
            textCodFisico.setVisibility(View.GONE);
            labelMatricula.setVisibility(View.VISIBLE);
            textMatricula.setVisibility(View.VISIBLE);

            // Recupero la última información del usuario en cuanto a posición del mapa y zoom
            lat = Double.parseDouble(Config.getInstance().getValueUsuario("miLat", "0"));
            lon = Double.parseDouble(Config.getInstance().getValueUsuario("miLon", "0"));

            tag = param.tags;

            if (tag != null) {
                textMatricula.setText(tag.getMatricula());
                // 15/02/23 - Según jpalao se debe poder modificar en cualquier caso
                // textMatricula.setEnabled(false);
                textTag.setText(tag.getTag());
            }

            // si viene de abrir la app, y había dejado seleccionado la opción del menú modificar, comprobasé
            // si se está mostrando en el menú alguna opción de crear elementos con imagen.
            if (!param.insertImage)
                if (MainActivity.getInstance().getMenuCrearElementosNombreImagen() ||
                      MainActivity.getInstance().getMenuCrearElementosImagen()) {
                    param.insertImage = true;
                }

            if (!param.insertImage) {
                llImagenes = (LinearLayout) findViewById(R.id.llImagenes);
                llImagenes.setVisibility(View.GONE);
            }

            setFieldNombreVisibility(false);

            // Si hay que mostrar el campo "Nombre", se muestra un solo EditText
            if (param.showNameField) {
                labelNombre.setVisibility(View.VISIBLE);
                textNombre.setVisibility(View.VISIBLE);
                textNombre.setInputType(InputType.TYPE_CLASS_TEXT);
            }

            int visualizar_matricula = Utils.parseStringToInt(Config.getInstance().getValue("elemVerMatricula"));
            modificarElementoAunqueNoExistaTag = visualizar_matricula == 2;

            setFieldMatriculaVisibility(visualizar_matricula != 0, visualizar_matricula != 0);

            // Historial imágenes solo en modificación y con código externo (>0)
            if (param.update && param.externalId > 0) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US);
                sdf.setTimeZone(TimeZone.getTimeZone("Europe/Madrid"));
                String hasta = sdf.format(new Date());

                setHistoricImagesLoadingElem(true);
                fetchHistoricoImagenesElemento("1970-01-01T00:00:00", hasta);
                if (histContainerElem != null) histContainerElem.setVisibility(View.VISIBLE);
            } else {
                if (histContainerElem != null) histContainerElem.setVisibility(View.GONE);
            }

            // PEDIR EN BASE A TIPO SOFTWARE
            switch (tipoSoft) {
                case 222:
                case 223:
                case 225:
                case 226:
                case 231:
                case 255:
                    if (param.update) {
                        update_elemento = true;
                        setFieldNombreVisibility(false);
                    } else {
                        setFieldNombreVisibility(true);
                        setMunicipio(lat, lon);
                    }
                    break;

                case 203: // ASCAN ECOVIDRIO
                case 208:
                case 215:
                case 219:
                case 220:
                case 218:
                    setFieldMatriculaVisibility(false, false);
                    break;
                case 247: // INDRA
                    setFieldMatriculaVisibility(false, true);
                    break;
            }

            if (param.update) {
                update_elemento = true;

                DBElemento db = new DBElemento();
                Elemento elemento = db.getByIdInterno(param.internalId, empresa);
                db.close();

                if (elemento == null) {
                    new InfoDialog(
                          AddElemActivity.this,
                          getString(R.string.atencion),
                          "No se ha podido obtener el elemento.",
                          InfoDialog.ICON_STOP, option -> finish(), InfoDialog.BUTTON_ACCEPT,
                          InfoDialog.POSITION_CENTER).show();
                    findViewById(R.id.btnAceptarAdd).setEnabled(true);
                    return;
                }

                if (elemento.getDescripcion() != null && elemento.getDescripcion().length() > 0)
                    textDescripcion.setText(elemento.getDescripcion());

                if (elemento.getTieneImagen())
                    btExisteImagen.setImageResource(R.drawable.ic_menu_report_image_si);

                if (!elemento.getMatricula().equals("")) {
                    DBTags dbTags = new DBTags();
                    Tags tag = dbTags.getByMatricula(elemento.getMatricula(), elemento.getEmpresa());
                    dbTags.close();

                    if (tag != null) {
                        if (textMatricula.getVisibility() == View.VISIBLE)
                            textMatricula.setText(tag.getMatricula());

                        if (textTag.getVisibility() == View.VISIBLE)
                            textTag.setText(tag.getTag());
                    }
                }

                if (elemento.getEstado() == Elemento.ESTADO_TALLER || elemento.getEstado() == Elemento.ESTADO_LAVADO) {
                    findViewById(R.id.llImagenes).setVisibility(View.GONE);
                    //findViewById(R.id.rlImagenElemento).setVisibility(View.GONE);
                }

                if (Environment.isSoftIndra && !elemento.getCodFisico().equals(""))
                    textCodFisico.setText(elemento.getCodFisico());

                setTitle("Edición " + elemento.getNombre());
            }

            findViewById(R.id.btnVolver).setOnClickListener(v -> {
                fotos.clear();
                finish();
            });

            // Evento Botón Crear
            findViewById(R.id.btnAceptarAdd).setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            findViewById(R.id.btnAceptarAdd).setEnabled(false);
                            String tag = textTag.getText().toString();

                            if (modificarElementoAunqueNoExistaTag && !tag.equals("") && !checkTag(tag)) {
                                new InfoDialog(
                                        AddElemActivity.this,
                                        getString(R.string.atencion),
                                        "El código de tag no es válido. No se puede crear el elemento",
                                        InfoDialog.ICON_STOP, option -> finish(), InfoDialog.BUTTON_ACCEPT,
                                        InfoDialog.POSITION_CENTER).show();
                                findViewById(R.id.btnAceptarAdd).setEnabled(true);
                                return;
                            }

                            // por si no hay modelos cargados, hago que no falle.
                            int idModelo = 0;
                            // Recuperamos datos a insertar en bbdd
                            try {
                                idModelo = ((ElementoModelo) spinnerModelos.getSelectedItem()).getIdExterno();
                            } catch (Exception e) {

                            }

                            if (idModelo == 0) {
                                new InfoDialog(
                                        AddElemActivity.this,
                                        getString(R.string.atencion),
                                        getString(R.string.noModelDisponible),
                                        InfoDialog.ICON_STOP, option -> finish(), InfoDialog.BUTTON_ACCEPT,
                                        InfoDialog.POSITION_CENTER).show();
                                findViewById(R.id.btnAceptarAdd).setEnabled(true);
                                return;
                            }

                            String matricula = textMatricula.getText().toString();

                            TextView textInfo = (TextView) spinnerPrefijo.getSelectedView();

                            if (spinnerPrefijo.getVisibility() != View.GONE) {
                                Config.getInstance().setValueUsuario(
                                        "SelectedMunicipio",
                                        String.valueOf(spinnerPrefijo.getSelectedItemPosition()));
                            }

                            String municipio = (textInfo != null) ? textInfo
                                    .getText().toString().trim() : "";

                            if (municipio == "" && spinnerProvincias.getVisibility() != View.GONE) {

                                municipio = ((TextView) spinnerProvincias.getSelectedView()).getText().toString();
                            }

                            String prefijoNombre = (textPrefijo.length() > 0) ? textPrefijo
                                    .getText().toString().trim() : municipio;

                            String sufijo = textNombre.getText().toString().trim();
                            if (sufijo == null || sufijo.length() == 0)
                                no_indica_nombre = true;

                            String nombre = (prefijoNombre + " " + sufijo).trim();
                            String descripcion = textDescripcion.getText().toString();

                            // Si nos viene update hay que actualizar el elemento
                            if (param.update) {

                                DBElemento db = new DBElemento();
                                Elemento elemento = db.getByIdInterno(param.internalId, empresa);
                                db.close();

                                if (elemento == null) {
                                    new InfoDialog(
                                            AddElemActivity.this,
                                            getString(R.string.atencion),
                                            "No se ha encontrado el elemento",
                                            InfoDialog.ICON_STOP, option -> finish(), InfoDialog.BUTTON_ACCEPT,
                                            InfoDialog.POSITION_CENTER).show();
                                    findViewById(R.id.btnAceptarAdd).setEnabled(true);
                                    return;
                                }

                                elemento.setModelo(idModelo);
                                elemento.setMatricula(matricula);

                                if (Environment.isSoftIndra)
                                    elemento.setCodFisico(textCodFisico.getText().toString());

                                if (modificarElementoAunqueNoExistaTag) {
                                    elemento.setTag(tag);
                                }

                                // Si el nombre no está vacío y es diferente, se actualiza
                                if (!nombre.equals("") && !elemento.getNombre().equals(nombre))
                                    elemento.setNombre(nombre);

                                elemento.setDescripcion(descripcion);

                                if (!GlobalUtils.isNullOrEmptyArrayList(fotos) && param.insertImage)
                                    elemento.setTieneImagen(true);

                                //PMARCO 11/08/2021 MANTIS 5322
                                //Comprobar si el check de borrar datos esta activado aunque no se hayan añadido fotos
                                if (borrarImagenesAnterioresServidor && GlobalUtils.isNullOrEmptyArrayList(fotos)) {
                                    DBPacket dbp = new DBPacket();
                                    dbp.insert(new Packet(Packet.ELEMENTO_BORRAR_IMAGEN, Packet.PRIORIDAD_NORMAL, elemento));
                                    dbp.close();
                                }

                                // si se ha conseguido actualizar el elemento, seguimos.
                                if (updateElemento(elemento)) {

                                    MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

                                    // Guardo la informaciin para enviar en la
                                    // bandeja de salida
                                    DBPacket dbp = new DBPacket();
                                    dbp.insert(new Packet(
                                            Packet.ELEMENTO_MODIFICAR,
                                            Packet.PRIORIDAD_NORMAL, elemento));
                                    dbp.close();

                                    //sincro
                                    if (MainActivity.getInstance().isNetworkAvailable()) {
                                        if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                                            DBSynchro.getInstance().forceSync();
                                        }
                                    }

                                    // si hay imagen a enviar
                                    if (!GlobalUtils.isNullOrEmptyArrayList(fotos) && param.insertImage) {
                                        guardarFotos(elemento, 80);
                                    }

                                    MyLoggerHandler.getInstance().info(String.format("%s elemento modificado.",
                                            elemento.toString()));

                                    finish();
                                }
                            } else {

                                // El id del elemento es temporal porque será el servidor quien
                                // finalmente lo asignará
                                DBElementoModelo dbMod = new DBElementoModelo();
                                ElementoModelo mod = dbMod.getByID(idModelo, empresa);
                                dbMod.close();

                                if (nombre.equals("")) {
                                    nombre = mod.getNombre() + " (nuevo)";
                                }

                                Elemento elementoTMP = null;
                                DBElemento db = new DBElemento();

                                // Si no se permiten nombres repetidos... (si está repetido se
                                // preguntará si se desea cambiar la ubicación).
                                // Solo en las versiones de Camacho (Ecovidrio con contenedores
                                // identificados) debe verificarse el nombre.
                                if (MainActivity.isEcovidrioContIdentificados()) {
                                    // Antes de crear compruebo si existe un elemento con ese nombre
                                    elementoTMP = db.getElementoBy(nombre, empresa);
                                }

                                // Busco el TAG asociado a la matrícula
                                DBTags dbTags = new DBTags();
                                Tags tagTmp = dbTags.getByMatricula(matricula, empresa);
                                dbTags.close();

                                Elemento elementoTMP2 = null;
                                if (tagTmp != null) {
                                    if (tagTmp.getIdExternoElemento() > 0) {
                                        // Busco un elemento con la misma matrícula
                                        elementoTMP2 = db.getByIdExterno(tagTmp.getIdExternoElemento(), empresa);
                                    }
                                }

                                db.close();

                                if ((elementoTMP != null || elementoTMP2 != null) && !no_indica_nombre) {
                                    // Tenemos que avisar si quiere modificar las coordenadas
                                    // o si quiere cambiar el nombre

                                    final Elemento elemento = elementoTMP != null ? elementoTMP : elementoTMP2;

                                    String tmpNombre = elemento.getNombre();
                                    int idModel = elemento.getModelo();

                                    DBElementoModelo dbModel = new DBElementoModelo();
                                    ElementoModelo eleModel = dbModel.getByID(idModel, empresa);

                                    tmpNombre = "\r\n" + eleModel.getNombre() + "\r\n" + tmpNombre + "\r\n";

                                    /*String ubicacion = GestionElementos.getInfoGIS(
                                            elemento.getPosition().latitude,
                                            elemento.getPosition().longitude);
                                    ubicacion = ubicacion.replace("(0-0)", "");
                                    ubicacion = ubicacion.replace("(null)", "");*/
                                    if (municipio != null && !municipio.isEmpty())
                                        municipio = "En " + municipio;
                                    municipio = tmpNombre + municipio + "\r\n";
                                    String question = getString(R.string.questionCambiarUbicacionElemento, municipio);

                                    // Si no hay matrícula, se busca en el tag asociado al elemento
                                    if (matricula.equals("")) {
                                        Tags tagElemento;
                                        dbTags = new DBTags();

                                        if (elemento.getIdExterno() > 0)
                                            tagElemento = dbTags.getByElemento(elemento.getIdExterno(), empresa);
                                        else
                                            tagElemento = dbTags.getByIdInternoElemento(elemento.getId(), empresa);

                                        dbTags.close();

                                        if (tagElemento != null)
                                            matricula = tagElemento.getMatricula();
                                    }

                                    final int idModeloElemento = idModelo;
                                    final String nombreElemento = nombre;
                                    final String descElemento = descripcion;
                                    final String matriculaElemento = matricula;

                                    new InfoDialog(AddElemActivity.getInstance(), getString(R.string.atencion), question,
                                            InfoDialog.ICON_STOP, new OnInfoDialogSelect() {

                                        @Override
                                        public void onSelectOption(
                                                int option) {

                                            if (option == InfoDialog.BUTTON_YES) {

                                                // Modifico los datos en base de datos
                                                elemento.setModelo(idModeloElemento);
                                                elemento.setMatricula(matriculaElemento);
                                                elemento.setDescripcion(descElemento);
                                                elemento.setPosition(param.latitude, param.longitude);
                                                elemento.setFechaCreacion(new Date());
                                                if (modificarElementoAunqueNoExistaTag)
                                                    elemento.setTag(tag);

                                                // Si el nombre no está vacío y es diferente, se actualiza
                                                if (!nombreElemento.equals("") && !elemento.getNombre().equals(nombreElemento))
                                                    elemento.setNombre(nombreElemento);

                                                if (!GlobalUtils.isNullOrEmptyArrayList(fotos) && param.insertImage)
                                                    elemento.setTieneImagen(true);

                                                if (Environment.isSoftIndra)
                                                    elemento.setCodFisico(textCodFisico.getText().toString());

                                                DBElemento db = new DBElemento();
                                                db.update(elemento);
                                                db.close();

                                                // Guardo la información para enviar en la
                                                // bandeja de salida
                                                DBPacket dbp = new DBPacket();
                                                dbp.insert(new Packet(
                                                        Packet.ELEMENTO_MODIFICAR,
                                                        Packet.PRIORIDAD_NORMAL,
                                                        elemento));
                                                dbp.close();
                                                MyLoggerHandler.getInstance().info(String.format("%s elemento modificado",
                                                        elemento.toString()));

                                                //sincro
                                                if (MainActivity.getInstance().isNetworkAvailable()) {
                                                    if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                                                        DBSynchro.getInstance().forceSync();
                                                    }
                                                }

                                                // si hay imagen a enviar
                                                if (!GlobalUtils.isNullOrEmptyArrayList(fotos) && param.insertImage) {
                                                    guardarFotos(elemento, 70);
                                                }


                                                // actualizo el cluster.
                                                MyBroadCastManager
                                                        .getInstance()
                                                        .sendBroadCastUpdateItemCluster(
                                                                elemento);

                                                Toast.makeText(MainActivity.getInstance(),
                                                        R.string.sendingChangeCoordenadas,
                                                        Toast.LENGTH_LONG).show();

                                                finish();
                                            }

                                        }

                                    }, InfoDialog.BUTTON_YES
                                            | InfoDialog.BUTTON_NO,
                                            InfoDialog.POSITION_CENTER).show();
                                    findViewById(R.id.btnAceptarAdd).setEnabled(true);
                                    return;
                                }

                                // Creo el elemento con código 0 de momento para que se genere un
                                // código temporal mientras no se obtenga el código definitivo
                                // asignado por el servidor
                                if (!param.showNameField && sufijo.equals("")) nombre = "";

                                Elemento elemento = null;
                                if (!altaTaller) {
                                    elemento = new Elemento(
                                            0,
                                            0,
                                            empresa,
                                            nombre, idModelo, matricula, Elemento.ESTADO_ACTIVO,
                                            param.latitude, param.longitude, descripcion, "", 0, "");
                                } else {
                                    elemento = new Elemento(
                                            0,
                                            0,
                                            empresa,
                                            nombre, idModelo, matricula, Elemento.ESTADO_TALLER,
                                            0, 0, descripcion, "", 0, "");
                                }

                                elemento.setFechaCreacion(new Date());

                                if (modificarElementoAunqueNoExistaTag) {
                                    elemento.setTag(tag);
                                }

                                // Inserto en la tabla de configuracion el último modelo insertado
                                // para que si volvemos a la actividad se mantenga el mismo
                                Config.getInstance().setValueUsuario("tipoModelo", "" + idModelo);

                                if (!GlobalUtils.isNullOrEmptyArrayList(fotos) && param.insertImage)
                                    elemento.setTieneImagen(true);

                                // Llamo al mapa para que añada el elemento en bd y en el cluster
                                int idInterno = addElemento(elemento);
                                elemento.setId(idInterno);

                                if (Environment.isSoftIndra)
                                    elemento.setCodFisico(textCodFisico.getText().toString());

                                MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

                                // Si se ha especificado que se muestre el campo "Nombre" se
                                // añadirá un campo extra en el json de la llamada al servidor
                                int packet_type = Packet.ELEMENTO_CREAR;
                                if (param.showNameField && !no_indica_nombre)
                                    packet_type = Packet.ELEMENTO_CREAR_CON_NOMBRE;

                                // Guardo la información para enviar en la bandeja de salida
                                DBPacket dbp = new DBPacket();
                                dbp.insert(new Packet(packet_type, Packet.PRIORIDAD_NORMAL, elemento));
                                dbp.close();

                                // Se guarda el tag en local para impedir que se pueda volver a utilizar
                                if (modificarElementoAunqueNoExistaTag && tagTmp == null) {
                                    Tags tag2 = new Tags(0, 0, elemento.getEmpresa(), elemento.getMatricula(),
                                          elemento.getTag(), elemento.getIdExterno(), elemento.getId());
                                    DBTags dbTags2 = new DBTags();
                                    dbTags2.insert(tag2);
                                    dbTags2.close();
                                }

                                //sincro
                                if (MainActivity.getInstance().isNetworkAvailable()) {
                                    if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                                        DBSynchro.getInstance().forceSync();
                                    }
                                }

                                // si hay imagen a enviar
                                if (!GlobalUtils.isNullOrEmptyArrayList(fotos) && param.insertImage) {
                                    guardarFotos(elemento, 70);
                                }

                                // Se actualiza el tag del elemento
                                if (tagTmp != null) {
                                    tagTmp.setIdExternoElemento(0);
                                    tagTmp.setIdInternoElemento(elemento.getId());
                                    DBTags tagsManager = new DBTags();
                                    tagsManager.update(tagTmp);
                                }

                                MyLoggerHandler.getInstance().info(String.format("%s elemento creado.",
                                        "" + elemento.getId()));

                                fotos.clear();
                                finish();
                            }
                        }
                    });

            // Recupero todos los modelos
            DBElementoModelo dbElementoModelo = new DBElementoModelo();
            modelos = dbElementoModelo.getAll(empresa);
            dbElementoModelo.close();

            if (modelos != null && modelos.size() > 0) {
                // Dejo silo los modelos visibles
                for (int i = 0; i < modelos.size(); i++) {

                    if (!GestionElementos.isVisibleModeloElemento(modelos.get(i).getIdExterno())) {
                        modelos.remove(i);
                        i--;
                    }

                }

                if (modelos.size() < 1) {

                    new InfoDialog(
                            this,
                            getString(R.string.atencion),
                            getString(R.string.noModelDisponible),
                            InfoDialog.ICON_STOP, new OnInfoDialogSelect() {

                        @Override
                        public void onSelectOption(int option) {

                            finish();
                        }

                    }, InfoDialog.BUTTON_ACCEPT,
                            InfoDialog.POSITION_CENTER).show();
                }

                // Relleno el combo de modelos
                ElemModelAdapter modelAdapter;
                modelAdapter = new ElemModelAdapter(MainActivity.getInstance(),
                        modelos, 1);

                spinnerModelos.setAdapter(modelAdapter);
            }
            // Si estoy modificando relleno el campo matricula
            if (param.update) {

                DBElemento db = new DBElemento();
                Elemento elemento = null;
                if (param.externalId > 0)
                    elemento = db.getByIdExterno(param.externalId, empresa);
                else
                    elemento = db.getByIdInterno(param.internalId, empresa);


                // Recorro los modelos para seleccionar en el combo el iltimo
                for (ElementoModelo modelo : modelos) {

                    if (modelo.getIdExterno() == elemento.getModelo()) {

                        spinnerModelos.setSelection(modelos.indexOf(modelo));
                        break;
                    }
                }

                // Relleno los datos del elemento
                textNombre.setText(elemento.getNombre());
                textNombre.setSelection(elemento.getNombre().length());
                textMatricula.setText(elemento.getMatricula());
                textMatricula.setSelection(elemento.getMatricula().length());

                if (modificarElementoAunqueNoExistaTag && elemento != null) {
                    DBTags dbTags = new DBTags();
                    Tags tag = dbTags.getByElementIdInternalOrExternal(elemento.getIdExterno(),elemento.getId(), elemento.getEmpresa());
                    if (tag != null)
                        textTag.setText(tag.getTag());
                }
            } else {
                DBTags dbTags = new DBTags();
                Tags tag = dbTags.getByTag(param.getITag(), empresa);
                if (tag != null) {
                    textMatricula.setText(tag.getMatricula());
                } else if (visualizar_matricula == 2) {
                    textMatricula.setText(param.getITag().get());
                }
                textTag.setText(param.getITag().get());

                // Se escoge el ultimo modelo creado si lo hubiese
                // Recupero el ultimo modelo que se inserto en el mapa
                int lastModelo = Integer.parseInt(Config.getInstance()
                        .getValueUsuario("tipoModelo", "-1"));

                if (modelos != null && modelos.size() > 0)
                    // Recorro los modelos para seleccionar en el combo el iltimo
                    for (ElementoModelo modelo : modelos) {

                        if (modelo.getIdExterno() == lastModelo) {

                            spinnerModelos.setSelection(modelos.indexOf(modelo));
                            break;
                        }
                    }
            }

            // Lo ponemos al final para que sobreescriba la lógica anterior. (Para Indra)
            setPhysicalCode();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);

            new InfoDialog(this,
                  getString(R.string.atencion),
                  getString(R.string.surgidoProblema) + e.getMessage(),
                  InfoDialog.ICON_STOP, option -> finish(),
                  InfoDialog.BUTTON_ACCEPT,
                  InfoDialog.POSITION_CENTER).show();
        }


        // ATTENTION: This was auto-generated to implement the App Indexing API.
        // See https://g.co/AppIndexing/AndroidStudio for more information.
        //client = new GoogleApiClient.Builder(this).addApi(AppIndex.API).build();
    }

    private void fetchHistoricoImagenesElemento(String desde, String hasta) {
        try {
            int empresa = MainActivity.getInstance().getEmpresa();
            int codigo = param.externalId;
            new ElementosRepository().getImagenesPuntoUbicacion(
                    empresa,
                    codigo,
                    desde,
                    hasta,
                    bitmaps -> {
                        try {
                            Logg.info("AddElemActivity", "Historico imagenes elemento: " + (bitmaps != null ? bitmaps.size() : 0));
                            renderHistoricoImagenesElemento(bitmaps);
                        } catch (Throwable t) {
                            Logg.error("AddElemActivity", "Error procesando historico imagenes elemento: " + t.getMessage());
                        }
                    }
            );
        } catch (Throwable e) {
            Logg.error("AddElemActivity", "Error solicitando historico imagenes elemento: " + e.getMessage());
        }
    }

    private void renderHistoricoImagenesElemento(List<Bitmap> bitmaps) {
        try {
            setHistoricImagesLoadingElem(false);
            if (txtHistImgNumeroElem != null) {
                int count = (bitmaps != null) ? bitmaps.size() : 0;
                txtHistImgNumeroElem.setText(Integer.toString(count));
            }
            if (layoutScrollHistElem == null) return;
            layoutScrollHistElem.removeAllViews();
            if (bitmaps == null || bitmaps.size() == 0) return;

            for (Bitmap bmp : bitmaps) {
                try {
                    ImageView imageView = new ImageView(instance);
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(200, 200);
                    params.setMargins(5, 5, 5, 5);
                    imageView.setLayoutParams(params);
                    imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                    imageView.setImageBitmap(bmp);
                    imageView.setOnClickListener(v -> {
                        try {
                            Drawable drawable = imageView.getDrawable();
                            Bitmap bitmap;
                            if (drawable instanceof BitmapDrawable) {
                                bitmap = ((BitmapDrawable) drawable).getBitmap();
                            } else {
                                imageView.setDrawingCacheEnabled(true);
                                bitmap = Bitmap.createBitmap(imageView.getDrawingCache());
                                imageView.setDrawingCacheEnabled(false);
                            }
                            ZoomPhotoDialog.showPhoto(instance, bitmap);
                        } catch (Throwable t) {
                            MyLoggerHandler.getInstance().error(t);
                        }
                    });
                    layoutScrollHistElem.addView(imageView);
                } catch (Throwable t) {
                    MyLoggerHandler.getInstance().error(t);
                }
            }

            try {
                if (btExisteImagen != null && bitmaps.size() > 0) {
                    btExisteImagen.setImageResource(R.drawable.ic_menu_report_image_si);
                }
            } catch (Throwable ignore) {}
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    private void setHistoricImagesLoadingElem(boolean loading) {
        try {
            if (progressHistImgElem != null)
                progressHistImgElem.setVisibility(loading ? View.VISIBLE : View.GONE);
            if (txtHistImgNumeroElem != null)
                txtHistImgNumeroElem.setVisibility(loading ? View.GONE : View.VISIBLE);
        } catch (Throwable ignore) {}
    }

    private void setRepairShopRegister() {
        CheckBox checkAltaTaller = findViewById(R.id.alta_taller);

        // Mantis 0005847: Fallos vistos en la versión de Indra.
        // No se debe mostrar este campo en Indra.
        if (Environment.isSoftIndra) {
            checkAltaTaller.setVisibility(View.GONE);
            return;
        }

        if (param.update) {
            checkAltaTaller.setVisibility(View.GONE);
        }
    }

    private void updateTag() {
        Logg.info(TAG, "[updateTag] Tag == null: " + (tag == null));
//        Mantis 6053: Aunque tenga un tag asociado se debe poder modificar
//        if (tag == null) {
            // Comprobar si estoy utilizando el smartphone U9000
            if (Build.MODEL.equals("PDA")) {
                Logg.info(TAG, "[updateTag] U9000 - Leyendo 134 o UHF");
                TagReaderManager.read134orUHF((buffer, size) -> onReaded134TagData(buffer, size));
            } else {
                //leer tag
                if (UHFManager.get().isReading()) {
                    UHFManager.get().stopReadLoop();
                } else {
                    // Se inicia la lectura UHF si el usuario está identificado en la aplicación
                    MainActivity mainActivity = MainActivity.getInstance();
                    if (mainActivity != null && mainActivity.getUsuario() > 0) {
                        UHFManager.get().readSingleTag(3000);
                    }
                }
            }
//        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(OnReadedTag event) {
        if (textMatricula.getVisibility() == View.VISIBLE) {
            DBTags dbTags = new DBTags();
            Tags tagR = dbTags.getByTag(event.tag, empresa);
            dbTags.close();
            TagSendSensor.execute(event.tag, tagR);

            if (tagR == null) {
                if (modificarElementoAunqueNoExistaTag) {
                    if (textMatricula.getText().toString() == null || textMatricula.getText().toString().isEmpty())
                        textMatricula.setText(event.tag.get());
                    textTag.setText(event.tag.get());
                }
            } else {
                DBElemento dbElemento = new DBElemento();
                Elemento elemento = dbElemento.getElementoByTag(tagR);
                dbElemento.close();

                if (elemento != null) {
                    // Mantis 5983: Si está en taller le paso el evento a MainActivity
                    // + Mantis 6053: Solo si es crear elemento
                    if (elemento.getEstado() == Elemento.ESTADO_TALLER && !param.update) {
                        MainActivity.getInstance().onEventMainThread(new OnReadedTag(event.tag));
                        finish();
                        return;
                    }

                    // Estamos modificando y el tag esta asociado a otro elemento que está en taller
                    boolean tagEnTaller = param.update && elemento.getEstado() == Elemento.ESTADO_TALLER;
                    // Estamos modificando y el tag esta asociado a OTRO elemento que está activo
                    boolean tagActivo = param.update && elemento.getEstado() == Elemento.ESTADO_ACTIVO
                          && (param.tags == null || !param.tags.getTag().equals(tagR.getTag()));

                    if (tagEnTaller || tagActivo) {
                        String mensaje = tagEnTaller
                              ? getString(R.string.modificar_elemento_tag_existe_taller)
                              : getString(R.string.modificar_elemento_tag_existe, elemento.getNombre());
                        new InfoDialog(this, getString(R.string.atencion), mensaje, InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                            @Override
                            public void onSelectOption(int option) {
                                if (option == InfoDialog.BUTTON_YES) {
                                    textTag.setText(tagR.getTag());
                                    textMatricula.setText(tagR.getMatricula());
                                }
                            }
                        }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();
                    } else {
                        final LatLng posElem = elemento.getPosition();
                        MyLocationService locationService = MyLocationService.getInstance();

                        GestionElementos.GPSInfo posGps = GestionElementos.ultGpsPos;

                                /*if (locationService != null && locationService.NumSatelites <= 4) {
                                    posGps = null;
                                }*/

                        // Guardo la posición marcada sobre el mapa.
                        //Modificar aquí
                        if (GestionElementos.isNotNullCrearElementos()) {
                            LatLng posMarker = new LatLng(GestionElementos.mCrearElemento.getPosition().latitude,
                                  GestionElementos.mCrearElemento.getPosition().longitude);
                            double dist = Double.MAX_VALUE;
                            if (posGps != null && posElem != null) {
                                LatLng posActual = new LatLng(posGps.getPosition().latitude, posGps.getPosition().longitude);
                                dist = GPS.calcularDistancia(posElem, posActual);
                            } else {
                                dist = GPS.calcularDistancia(posElem, posMarker);
                            }

                            if (dist > RADIO_METROS && (posGps != null || posMarker != null)) {
                                System.out.println("DISTANCIA entre el elemento y posición GPS: " + dist);
                                //final GestionElementos.GPSInfo finalPosGps = posGps;
                                new InfoDialog(AddElemActivity.getInstance(), getString(R.string.atencion),
                                      getString(R.string.reubicar_elemento) + "\n" +
                                            "Esta acción no modificará las características del elemento que se hayan modificado",
                                      InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                                    @Override
                                    public void onSelectOption(int option) {
                                        if (option == InfoDialog.BUTTON_YES) {
                                            // Cambio las coordenadas al elemento por las actuales del GPS
                                            //elemSelc.setPosition(finalPosGps.getPosition().latitude, finalPosGps.getPosition().longitude);

                                            // Cambio las coordenadas al elemento por las del marcador
                                            elemento.setPosition(GestionElementos.mCrearElemento.getPosition().latitude, GestionElementos.mCrearElemento.getPosition().longitude);

                                            if (Environment.isSoftIndra)
                                                elemento.setCodFisico(textCodFisico.getText().toString());

                                            // Actualizo la BD y el cluster
                                            GestionElementos.getInstance().updateElemento(elemento);

                                            // Guardo la información para enviar en
                                            // la bandeja de salida
                                            DBPacket dbp = new DBPacket();
                                            dbp.insert(new Packet(
                                                  Packet.ELEMENTO_MODIFICAR,
                                                  Packet.PRIORIDAD_NORMAL, elemento));
                                            dbp.close();

                                            GestionElementos.getInstance().centerMap(elemento.getPosition());

                                            //sincro
                                            if (MainActivity.getInstance().isNetworkAvailable()) {
                                                if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                                                    DBSynchro.getInstance().forceSync();
                                                }
                                            }

                                            finish();
                                        } else {
                                            if (option == InfoDialog.BUTTON_NO) {
                                                finish();
                                                //BACK VISTA MAINActivity
                                            }
                                        }
                                    }
                                }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();

                            } else {
                                MainActivity.getInstance().showMessage(getString(R.string.tag_ya_existe, Toast.LENGTH_LONG));
                            }
                        } else {
                            textMatricula.setText(tagR.getMatricula());
                            labelMatricula.setText("Matrícula");
                            if (textTag.getVisibility() == View.VISIBLE) {
                                textTag.setText(tagR.getTag());
                            }
                        }
                    }
                } else { //el elemento es nulo
                    textMatricula.setText(tagR.getMatricula());
                    labelMatricula.setText("Matrícula");
                    if (textTag.getVisibility() == View.VISIBLE) {
                        textTag.setText(tagR.getTag());
                    }
                    // Se obtiene el elemento asociado al tag
                    //textNombre.setText(elemento.getNombre());
                    //textDescripcion.setText(elemento.getDescripcion());
                }

            }


        }

    }

    private void setMunicipio(double latitud, double longitud) {

        // Recupero la última información del usuario en cuanto a posición del mapa y zoom
        if (latitud == 0 && longitud == 0) {
            latitud = Double.parseDouble(Config.getInstance().getValueUsuario("miLat",
                    "0"));
            longitud = Double.parseDouble(Config.getInstance().getValueUsuario("miLon",
                    "0"));
        }


        new asyncGIS(latitud, longitud).execute();

    }

    private void setProvincias(final Spinner textProvincias) {
        DBProvincias managerProvincias = new DBProvincias();
        Cursor cursor = managerProvincias.getAllCursor();
        String[] from = new String[]{"provincia"};
        int[] to = new int[]{android.R.id.text1};

        SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(this,
                android.R.layout.simple_spinner_item, cursor, from, to, 0);

        // set layout for activated adapter
        dataAdapter
                .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        textPrefijo.setVisibility(View.GONE);
        textProvincias.setAdapter(dataAdapter);
        OnItemSelectedListener selectedProvincia = new SelectedProvincia();
        textProvincias.setOnItemSelectedListener(selectedProvincia);
        managerProvincias.close();

        int indexPos = Integer.valueOf(Config.getInstance().getValueUsuario(
                "SelectedProvincia", "-1"));
        if (indexPos > -1)
            textProvincias.setSelection(indexPos);
    }

    /**
     * Añade el elemento en BD
     */
    public int addElemento(Elemento elemento) {
        int id = elemento.getId();
        try {

            if (id == 0) {
                //Quito matricula del contenedor que la tenga
                removeMatricula(elemento.getMatricula(), elemento.getEmpresa());

                // Inserto el elemento en la BD
                DBElemento dbElemento = new DBElemento();
                id = (int) dbElemento.insert(elemento);
                dbElemento.close();

                // Se establece el id interno del elemento creado en su tag
                if (tag != null) {
                    tag.setIdInternoElemento(elemento.getId());
                    DBTags dbTags = new DBTags();
                    dbTags.update(tag);
                    dbTags.close();
                }
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return id;

    }

    /**
     * Actualiza el elemento en BD
     */
    private boolean updateElemento(Elemento elemento) {

        boolean res = true;

        try {

            //Quito matricula del contenedor que la tenga
            removeMatricula(elemento.getMatricula(), elemento.getEmpresa());
            DBElemento dbElemento = new DBElemento();
            if (dbElemento.update(elemento)) {
                DBTags dbTags = new DBTags();
                Tags tag = dbTags.getByMatricula(elemento.getMatricula(), elemento.getEmpresa());
                if (tag != null) {
                    tag.setIdExternoElemento(elemento.getIdExterno());
                    tag.setIdInternoElemento(elemento.getId());
                    dbTags.update(tag);
                }
                dbTags.close();
            }
            dbElemento.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    private boolean checkTag(String tag) {
        if (tag.length() < 6) return false;
        try {
            return tag.matches("[A-Fa-f0-9]+");
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return false;
    }

    private void removeMatricula(String matricula, int empresa) {

        try {
            if (matricula.equals("")) return;

            // Actualizo la BD
            DBElemento dbElemento = new DBElemento();
            Elemento elemento = dbElemento.getElementoByMatricula(matricula, empresa);

            DBTags dbTags = new DBTags();
            Tags tag = dbTags.getByMatricula(matricula, empresa);

            if (elemento != null) {
                elemento.setMatricula("");
                elemento.setTag("");
                dbElemento.update(elemento);

                if (tag != null) {
                    tag.setIdExternoElemento(0);
                    tag.setIdInternoElemento(0);
                    dbTags.update(tag);
                }
            }

            dbTags.close();
            dbElemento.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    /**
     * Este metodo se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    public void borrarImagenesAnterioresServidorSeleccionado(View view) {
        borrarImagenesAnterioresServidor = !borrarImagenesAnterioresServidor;
    }

    public void altaTallerSeleccionado(View view) {
        altaTaller = !altaTaller;
    }

    public class SelectedProvincia implements OnItemSelectedListener {

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int pos,
                                   long id) {
            Cursor c = ((SimpleCursorAdapter) parent.getAdapter()).getCursor();
            c.moveToPosition(pos);

            DBMunicipios managerMunicipios = new DBMunicipios();
            int idProvincia = c.getInt(0);
            Cursor cursor = managerMunicipios.getAllCursorBy(idProvincia);
            String[] from = new String[]{"municipio"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(instance,
                    android.R.layout.simple_spinner_item, cursor, from, to, 0);

            // set layout for activated adapter
            dataAdapter
                    .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            textPrefijo.setVisibility(View.GONE);
            spinnerPrefijo.setAdapter(dataAdapter);
            managerMunicipios.close();

            int indexOf = Integer.valueOf(Config.getInstance().getValueUsuario(
                    "SelectedMunicipio", "-1"));

            if (indexOf > -1) {
                try {
                    spinnerPrefijo.setSelection(indexOf);
                } catch (Exception ex) {

                }
            }

            Config.getInstance().setValueUsuario("SelectedProvincia",
                    String.valueOf(pos));
        }

        @Override
        public void onNothingSelected(AdapterView<?> arg0) {
            // TODO Auto-generated method stub

        }
    }

    /**
     * Establece la visibilidad del campo Nombre.
     */
    private void setFieldNombreVisibility(boolean visible) {
        int visibility = visible ? View.VISIBLE : View.GONE;
        labelNombre.setVisibility(visibility);
        spinnerProvincias.setVisibility(visibility);
        textPrefijo.setVisibility(visibility);
        spinnerPrefijo.setVisibility(visibility);
        textNombre.setVisibility(visibility);
    }

    /**
     * Establece la visibilidad del campo Matrícula.
     */
    private void setFieldMatriculaVisibility(boolean visible, boolean isVisibleTag) {
        int visibility = visible ? View.VISIBLE : View.GONE;
        int visibilityTag = isVisibleTag ? View.VISIBLE : View.GONE;
        textMatricula.setVisibility(visibility);
        labelMatricula.setVisibility(visibility);
        textTag.setVisibility(visibilityTag);
        labelTag.setVisibility(visibilityTag);
        if (Environment.hasReaderUHFC71 || Environment.hasReaderLFChainway || Build.MODEL.equals("PDA")) {
            leerTag.setVisibility(visibilityTag);
        } else {
            leerTag.setVisibility(View.GONE);
        }
    }

    private void setPhysicalCode() {
        if (Environment.isSoftIndra) {
            labelMatricula.setVisibility(View.GONE);
            textMatricula.setVisibility(View.GONE);
            labelCodFisico.setVisibility(View.VISIBLE);
            textCodFisico.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Establece la visibilidad del campo Descripción.
     */
    private void setFieldDescripcionVisibility(boolean visible) {
        int visibility = visible ? View.VISIBLE : View.GONE;
        labelDescripcion.setVisibility(visibility);
        textDescripcion.setVisibility(visibility);
    }

    private boolean sePuedenAniadirMasFotos() {
        if (GlobalUtils.isNullOrEmptyArrayList(fotos)) return true;
        if (fotos.size() < 2) return true;
        return false;
    }

    private void openGallery() {
        if (!sePuedenAniadirMasFotos()) {

            Toast.makeText(MainActivity.getInstance(),
                    "No puede añadir más de 2 fotografía. Por favor, elimina una imagen actual para poder asociar otra.",
                    Toast.LENGTH_LONG).show();

            return;
        }

        Intent galleryIntent = new Intent(Intent.ACTION_PICK,
                android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(galleryIntent, RESULT_GALLERY);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {

        boolean isFromCamera = requestCode == Photo.REQUEST_IMAGE_CAPTURE || requestCode == PhotoService.REQUEST_IMAGE_CAPTURE;
        // Insertamos foto desde la camara
        if (isFromCamera && resultCode == RESULT_OK) {
            Bitmap foto = null;

            if (UtilssAndroid.isAndroidGreater11())
                foto = PhotoService.get().setPic();
            else
                foto = Photo.getInstance().setPic();
            if (foto != null) {
                insertFotoGallery(foto);
                fotos.add(foto);
            }
        }
        // Insertamos foto desde la galeria
        else if (requestCode == AddElemActivity.RESULT_GALLERY) {
            if (null != data) {
                Uri imageUri = data.getData();
                try {
                    BitmapFactory.Options options = new BitmapFactory.Options();
                    options.inSampleSize = 4;
                    AssetFileDescriptor fileDescriptor = null;
                    Bitmap foto = Utils.decodeBitmapFromFile(Utils.getRealPathFromURI(imageUri, this), 800, 600);
                    insertFotoGallery(foto);
                    fotos.add(foto);
                } catch (Throwable e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            }
        }
    }


    private void insertFotoGallery(Bitmap foto) {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll_imagenes);

        ImageView imageView = new ImageView(instance);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(200, 200);
        params.setMargins(5, 5, 5, 5);
        imageView.setLayoutParams(params);
        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        imageView.setImageBitmap(foto);
        imageView.setTag(0);
        layout.addView(imageView);
        imageView.setOnClickListener(eventoClick);
        imageView.setOnLongClickListener(eventoLongClick);
    }


    private class ClickImageView implements OnClickListener {
        private ImageView view;

        @Override
        public void onClick(View v) {
            view = (ImageView) v;

            // Siempre mostrar vista flotante con zoom
            Drawable drawable = view.getDrawable();
            Bitmap bitmap;
            if (drawable instanceof BitmapDrawable) {
                bitmap = ((BitmapDrawable) drawable).getBitmap();
            } else {
                // Fallback por seguridad
                view.setDrawingCacheEnabled(true);
                bitmap = Bitmap.createBitmap(view.getDrawingCache());
                view.setDrawingCacheEnabled(false);
            }
            
            // Mostrar dialog con zoom
            ZoomPhotoDialog.showPhoto(instance, bitmap);
        }
    }

    private class LongClickImageView implements View.OnLongClickListener {
        @Override
        public boolean onLongClick(View v) {
            ImageView imageView = (ImageView) v;
            
            if (imageView.getTag() == (Integer) 1) {
                // Deseleccionar
                imageView.clearColorFilter();
                imageView.setTag(0);
            } else {
                // Seleccionar
                imageView.setColorFilter(0xAA26FFFF);
                imageView.setTag(1);
            }
            
            v.performHapticFeedback(android.view.HapticFeedbackConstants.LONG_PRESS);
            return true;
        }
    }

    private boolean hayFotosSeleccionadas() {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll_imagenes);
        int numImagenes = layout.getChildCount();
        ImageView img;

        for (int i = 0; i < numImagenes; i++) {

            img = (ImageView) layout.getChildAt(i);
            if (img.getTag() == (Integer) 1)
                return true;
        }

        return false;
    }

    private void eliminarFotos() {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll_imagenes);
        int numImagenes = layout.getChildCount();
        ImageView img;

        for (int i = numImagenes - 1; i >= 0; i--) {

            img = (ImageView) layout.getChildAt(i);

            if (img.getTag() == (Integer) 1) {

                layout.removeViewAt(i);
                fotos.clear();
            }

        }

        fotos.clear();
        /*TextView texto = (TextView) findViewById(R.id.textoImagenesNumero);
        texto.setText("(" + layout.getChildCount() + ")");*/
    }


    class asyncGIS extends AsyncTask<Void, String, JSONArray> {

        private ProgressDialog pDialog;
        private double lat, lon;
        JSONArray jsonData = null;

        public asyncGIS(double latitud, double longitud) {

            this.lat = latitud;
            this.lon = longitud;
        }

        protected void onPreExecute() {
            // para el progress dialog
            pDialog = new ProgressDialog(AddElemActivity.getInstance());
            pDialog.setMessage("Conectando al servidor, por favor espere");
            pDialog.setIndeterminate(true);
            pDialog.setCancelable(false);
            pDialog.show();
        }

        @Override
        protected JSONArray doInBackground(Void... strings) {

            // DINIGO - Mantis 0005119 - Si no tiene internet no realizamos la comprobación ya que genera que el sistema cierre la aplicación por el retraso.
            if (!MainActivity.getInstance().isNetworkAvailable()) return null;

            ClientWebSvc sweb = new ClientWebSvc();

            String url = Config.getInstance().getValue("webSvc", "");
            if (url.equals(""))
                return null;

            url = url + "/api/gis/calle/" + this.lat + "/" + this.lon;

            HttpPost post = new HttpPost(url);

            // Preparo la llamada para recibir datos
            post.setHeader("User-Agent", "java-Android");
            post.setHeader("Content-type", "application/json");
            post.setHeader("Accept-encoding", "gzip");
            post.setHeader("Authorization", "Bearer " + sweb.token);

            // Establezco timeout alto
            HttpParams parametros = new BasicHttpParams();

            int timeoutConnection = 10000;
            HttpConnectionParams.setConnectionTimeout(parametros, timeoutConnection);
            int timeoutSocket = 10000;
            HttpConnectionParams.setSoTimeout(parametros, timeoutSocket);
            DefaultHttpClient httpClient = new DefaultHttpClient(parametros);
            HttpResponse response;

            boolean salir = false;

            // lo intento 2 veces, en caso contrario sigo sin indicar el municipio, tendrá que ponerlo manualmente.
            for (int i = 0; i < 2 && !salir; i++) {
                try {
                    response = httpClient.execute(post);
                    int result = response.getStatusLine().getStatusCode();

                    switch (result) {
                        case 200: // Respuesta correcta

                            InputStream inStream = response.getEntity().getContent();

                            Header contentEncoding = response.getFirstHeader("Content-Encoding");
                            if (contentEncoding != null && contentEncoding.getValue().equalsIgnoreCase("gzip")) {
                                inStream = new GZIPInputStream(inStream);
                            }

                            String strResponse = sweb.convertStreamToString(inStream);

                            try {
                                // En principio intento recuperar los datos como un array JSON si falla lo intento como JSON
                                jsonData = new JSONArray(strResponse);
                                salir = true;

                            } catch (JSONException e) {

                                try {
                                    // Recupero la respuesta JSON
                                    jsonData = new JSONArray()
                                            .put(new JSONObject(strResponse));
                                    salir = true;
                                } catch (Exception ex) {
                                    MyLoggerHandler.getInstance().error(
                                            ex);
                                }
                            }
                            break;

                        case 401: // El token ya no es vilido
                            /*sweb.token = "";
                            sweb.refreshToken();*/
                            MyLoggerHandler.getInstance().info("[TOKEN]: En AddElemActivity el token ha expirado.");
                            break;

                        default:
                            try {

                                Thread.sleep(5000);

                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                    }

                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                } catch (ClientProtocolException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    if (e instanceof HttpHostConnectException) {
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException e1) {
                            e1.printStackTrace();
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return jsonData;
        }


        protected void onPostExecute(JSONArray result) {

            try {
                if (result != null)
                    municipio = result.getJSONObject(0).get("Municipio").toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }

            pDialog.dismiss();
            if (municipio == null)
                municipio = "";

            // SI ES UNA MODIFICACIÓN, NO MUESTRO EL NOMBRE, PUES NO SE PUEDE MODIFICAR
            if (update_elemento) {
                setFieldNombreVisibility(false);
            } else {

                if (municipio == "") {
                    changeVisibilityNombre(true);
                    setProvincias(spinnerProvincias);

                } else {
                    changeVisibilityNombre(false);
                    textPrefijo.setVisibility(View.VISIBLE);
                    textNombre.setVisibility(View.VISIBLE);

                    textPrefijo.setEnabled(false);
                    textPrefijo.setText(municipio.toUpperCase());
                }
            }

            if (!municipio.equals("")) {

                textPrefijo.setText(municipio.toUpperCase());
                textPrefijo.setEnabled(false);
                spinnerPrefijo.setVisibility(View.GONE);

            } else {

                Log.e("Aqui", "aqui hace algo");
            }
        }

        @Override
        protected void onCancelled() {
            Toast.makeText(AddElemActivity.getInstance(), "Tarea cancelada!",
                    Toast.LENGTH_SHORT).show();
        }
    }

    private void changeVisibilityNombre(boolean b) {

        labelNombre.setVisibility(!b ? View.GONE : View.VISIBLE);
        textNombre.setVisibility(!b ? View.GONE : View.VISIBLE);
        textPrefijo.setVisibility(!b ? View.GONE : View.VISIBLE);
        spinnerProvincias.setVisibility(!b ? View.GONE : View.VISIBLE);
        spinnerPrefijo.setVisibility(!b ? View.GONE : View.VISIBLE);

    }

    private void guardarFotos(Elemento elemento, int quality) {

        DBPacket dbp = new DBPacket();
        elemento.tieneQueBorrarImagenesAnterioresEnServidor = borrarImagenesAnterioresServidor;
        for (Bitmap foto : fotos) {

            // Guardo la información para enviar en la bandeja de salida
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            foto.compress(
                    Bitmap.CompressFormat.JPEG,
                    Utils.getCalidadImagenSegunCobertura(getApplicationContext()),
                    stream
            );
            byte[] byteArray = stream.toByteArray();
            elemento.setFoto(byteArray);

            if (Environment.isSoftIndra)
                elemento.setCodFisico(textCodFisico.getText().toString());

            dbp.insert(new Packet(Packet.ELEMENTO_CREAR_IMAGEN, Packet.PRIORIDAD_NORMAL, elemento));
        }

        dbp.close();
        //sincro
        if (MainActivity.getInstance().isNetworkAvailable()) {
            if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                DBSynchro.getInstance().forceSync();
            }
        }
        fotos.clear();
    }

    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Logg.info(TAG, "[onKeyDown] keyCode: " + keyCode);

        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            fotos.clear();
            //UHFManager.get().release();
            finish();
        }

        return super.onKeyDown(keyCode, event);
    }

}
