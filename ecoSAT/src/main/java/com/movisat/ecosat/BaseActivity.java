package com.movisat.ecosat;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.nfc.NfcAdapter;
import android.os.Build;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.environment.Environment;
import com.movisat.events.OnReadedTag;
import com.movisat.log.Logg;
import com.movisat.managers.LFChainwayManager;
import com.movisat.managers.SoundManager;
import com.movisat.managers.TagReaderManager;
import com.movisat.managers.UHFManager;
import com.movisat.rfid_uhf_u9000.TagNotFoundToast;
import com.movisat.tags.Tag134;
import com.movisat.utilities.ReadingTagToast;
import com.movisat.utils.LFByteUtils;
import com.movisat.utils.Utilss;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;

/**
 * Created by faroca on 26/04/2016.
 */
public abstract class BaseActivity extends AppCompatActivity {

    private static final String TAG = "BaseActivity";
    private long lastKeyPressTime = 0;

    public static final int MY_PERMISSIONS_ECOSAT = 1;

    public static String[] permissionsOld = {
          Manifest.permission.READ_PHONE_STATE,
          Manifest.permission.ACCESS_FINE_LOCATION,
          Manifest.permission.GET_ACCOUNTS,
          Manifest.permission.INTERNET,
          Manifest.permission.ACCESS_NETWORK_STATE,
          Manifest.permission.WRITE_EXTERNAL_STORAGE,
          Manifest.permission.READ_EXTERNAL_STORAGE,
          Manifest.permission.RECEIVE_BOOT_COMPLETED,
          Manifest.permission.VIBRATE,
          Manifest.permission.ACCESS_COARSE_LOCATION,
          Manifest.permission.BLUETOOTH,
          Manifest.permission.BLUETOOTH_ADMIN,
          Manifest.permission.CAMERA
    };

    public static String[] permissionsSdk33 = {
          Manifest.permission.READ_PHONE_STATE,
          Manifest.permission.ACCESS_FINE_LOCATION,// Se sustituye por Manifest.permission.NEARBY_WIFI_DEVICES,
          Manifest.permission.GET_ACCOUNTS,
          Manifest.permission.INTERNET,
          Manifest.permission.ACCESS_NETWORK_STATE,
//          Manifest.permission.WRITE_EXTERNAL_STORAGE,
          Manifest.permission.READ_MEDIA_IMAGES, // Sustituye a Manifest.permission.READ_EXTERNAL_STORAGE,
          Manifest.permission.RECEIVE_BOOT_COMPLETED,
          Manifest.permission.VIBRATE,
          Manifest.permission.ACCESS_COARSE_LOCATION,
          Manifest.permission.BLUETOOTH,
          Manifest.permission.BLUETOOTH_ADMIN,
          Manifest.permission.CAMERA,
          Manifest.permission.POST_NOTIFICATIONS
    };

    static {
        AppCompatDelegate.setCompatVectorFromResourcesEnabled(true);
    }

    private void DisplayError(int resourceId) {
        AlertDialog.Builder b = new AlertDialog.Builder(this);
        b.setTitle("ERROR");
        b.setMessage(resourceId);
        b.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int which) {
                finish();
            }
        });
        b.show();
    }

    protected String getActivityName() {
        return this.getClass().getSimpleName();
    }

    protected void onReaded134TagData(final byte[] buffer, final int size) {
        String activityName = getActivityName();
        Logg.info(TAG + "-" + activityName, "[onReaded134TagData] buffer: " + Arrays.toString(Arrays.copyOfRange(buffer, 0, size)) + " size: " + size);

        switch (activityName) {
            case "AddElemActivity":
            case "DepositarElemActivity":
            case "SustituirElemActivity":
            case "RetirarElemActivity":
            case "MainActivity":
                runOnUiThread(() -> {
                    ReadingTagToast.get().cancel();
                    byte[] id = new byte[size];
                    if (size <= 0) {
                        TagNotFoundToast.get().showToast(getBaseContext());
                    } else {
                        System.arraycopy(buffer, 0, id, 0, size);
                        if (!LFByteUtils.hasValue(id)) {
                            MainActivity.getInstance().showMessage("Aléjese del tag, pulse el botón y acerque el lector de nuevo.", Toast.LENGTH_LONG);
                        } else {
                            String tagRead;
                            SoundManager.getInstance(getBaseContext()).play();
                            if (MainActivity.getInstance().getEmpresa() == 661) { //Lipasam
                                tagRead = "00400000" + LFByteUtils.showLIPASAMResultASCII(id);
                                if (!activityName.equals("AddElemActivity"))
                                    tagRead = LFByteUtils.swapString(tagRead);
                            } else {
                                tagRead = LFByteUtils.showResultASCII(id);
                            }
                            Tag134 tag134 = new Tag134("", tagRead, Utilss.now());
                            EventBus.getDefault().post(new OnReadedTag(tag134));
                        }
                    }
                });
                break;
            default:
                break;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        displayKeepAlive();
        startReadingNfc();
        Logg.info(TAG + "-" + getActivityName(), "[onCreate]");
    }

    private void startReadingNfc() {
        // (Mantis 5848) En las versiones más recientes de Android tras bloquear y desbloquear
        // esto deja de funcionar, se lee a través del intent que se obtiene en NfcActivity
        NfcAdapter nfcAdapter = NfcAdapter.getDefaultAdapter(this);
        if (nfcAdapter != null && nfcAdapter.isEnabled()) {
            Bundle options = new Bundle();
            options.putInt(NfcAdapter.EXTRA_READER_PRESENCE_CHECK_DELAY, 1000);
            nfcAdapter.enableReaderMode(this, NfcActivity::sendTagNfc,
                  NfcAdapter.FLAG_READER_NFC_A | NfcAdapter.FLAG_READER_NFC_B
                        | NfcAdapter.FLAG_READER_NFC_F | NfcAdapter.FLAG_READER_NFC_V
                        | NfcAdapter.FLAG_READER_SKIP_NDEF_CHECK, options);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Se muestra el icono de la aplicación en el ActionBar
        getSupportActionBar().setDisplayOptions(ActionBar.DISPLAY_HOME_AS_UP | ActionBar.DISPLAY_SHOW_TITLE);
        getSupportActionBar().setHomeAsUpIndicator(R.drawable.ic_toolbar2);
        return super.onCreateOptionsMenu(menu);
    }

    /**
     * Comprueba que todos los permisos estén concedidos. Si es así, se inicializa la aplicación.
     * De lo contrario, se solicitan los permisos.
     */
    public void checkPermissions() {
        int permissionsGrantedCount = 0;
        String[] permissions = Build.VERSION.SDK_INT >= 33 ? permissionsSdk33 : permissionsOld;

        for (String permission : permissions) {
            if (isPermissionGranted(permission))
                permissionsGrantedCount++;
        }
        System.out.println("Permissions granted: " + permissionsGrantedCount + "/" + permissions.length);
        if (permissionsGrantedCount != permissions.length) {
            ActivityCompat.requestPermissions(this, permissions, MY_PERMISSIONS_ECOSAT);
        } else {
            MainActivity.getInstance().grantPermissions(true);
        }
    }

    /**
     * Indica si el permiso especificado está concedido o no.
     *
     * @param permission Identificador del permiso.
     * @return true si el permiso está concedido, false en otro caso.
     */
    public boolean isPermissionGranted(String permission) {
        int permissionCheck = ContextCompat.checkSelfPermission(this, permission);
        return permissionCheck == PackageManager.PERMISSION_GRANTED;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (event.getRepeatCount() == 0) {
            Logg.info(TAG + "-" + getActivityName(), "[onKeyDown] keyCode: " + keyCode);

            // Compruebo si el usuario está logueado
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity == null || mainActivity.getUsuario() == 0) return false;

            // Actividades que no deben leer tags
            String activityName = getActivityName();
            if (activityName.equals("InfoActivity")) return false;
            if (activityName.equals("FilterInciActivity")) return false;
            if (activityName.equals("FilterElemActivity")) return false;
            if (activityName.equals("OperationsActivity")) return false;
            if (activityName.equals("NivelLlenadoActivity")) return false;

            // U9000
            if (Build.MODEL.equals("PDA")) {
                if ((keyCode >= 301 && keyCode <= 303) || (keyCode >= 307 && keyCode <= 309)) {
                    long currentTime = System.currentTimeMillis();
                    // Verificar si han pasado menos de 200 milisegundos desde la última pulsación
                    if (currentTime - lastKeyPressTime < 200) return false;

                    lastKeyPressTime = currentTime;
                    Logg.info(TAG, "[onKeyDown] U9000 - Leyendo 134 o UHF");
                    TagReaderManager.read134orUHF((buffer, size) -> onReaded134TagData(buffer, size));
                    return true;
                }
            }

            // Botón de escaneo UHF (solo para los lectores UHF C71)
            else if (keyCode == UHFManager.SCAN_KEYCODE && Environment.hasReaderUHFC71) {
                if (UHFManager.get().isReading()) {
                    UHFManager.get().stopReadLoop();
                } else {
                    UHFManager.get().readSingleTag(3000);
                }
                return true;
            }

            // Botón de escaneo RFID LF (125-134 MHz) (solo para los lectores RFID LF CHAINWAY)
            else if (Environment.hasReaderLFChainway &&
                  Arrays.binarySearch(LFChainwayManager.SCAN_KEYCODES, keyCode) >= 0) {
                if (LFChainwayManager.get().isReading()) {
                    LFChainwayManager.get().stopReadLoop();
                } else {
                    LFChainwayManager.get().readSingleTag(30000);
                }
                return true;
            }

        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        Log.i("onKeyUp", "keyCode" + keyCode);
        return super.onKeyDown(keyCode, event);
    }

    @SuppressLint("InvalidWakeLockTag")
    public void displayKeepAlive() {
        try {
            // Compruebo si se ha configurado que no se apague la pantalla
            SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(this);
            int keepAlive = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_KEEP_ALIVE, "0"));

            /*if (wakelock != null)
                wakelock.release();
            wakelock = null;*/

            if (keepAlive > 0) {
                getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
                /*PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
                wakelock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK, "WL");
                wakelock.acquire();*/
            } else {
                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

}
