package com.movisat.ecosat;

import android.app.SearchManager;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.location.Address;
import android.location.Geocoder;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.SearchView;

import com.environment.Environment;
import com.google.android.gms.maps.model.LatLng;
import com.movisat.adapter.FlotaModelAdapter;
import com.movisat.database.DBFlotaPosiciones;
import com.movisat.database.DBIdentificaciones;
import com.movisat.database.FlotaPosiciones;
import com.movisat.database.Identificacion;
import com.movisat.events.onFinishSincroUltimasIdentificaciones;
import com.movisat.events.onFinishSincroUltimasPosiciones;
import com.movisat.fragment.GestionElementos;
import com.movisat.log.Logg;
import com.movisat.synchronize.DBSynchro;
import com.movisat.utilities.Config;
import com.movisat.utilities.HelperDates;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jsoup.helper.StringUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class FlotaActivity extends BaseActivity implements SearchView.OnQueryTextListener {
    private static final String TAG = FlotaActivity.class.getSimpleName();
    private static FlotaActivity instance = null;
    private static ListView listaMoviles;
    private static FlotaPosiciones posicionSeleccionada;
    public static FlotaModelAdapter modelAdapter;
    public static TextView titulo;
    public static MenuItem searchMenuItem;
    public static SearchView searchView;
    private Toast toast;

    public static FlotaActivity getInstance() {
        return instance;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.search_menu, menu);

        SearchManager searchManager = (SearchManager)
                getSystemService(Context.SEARCH_SERVICE);

        // El filtro no se va a implementar aquí todavía
        menu.findItem(R.id.filter).setVisible(false);
        menu.findItem(R.id.filter).setEnabled(false);

        searchMenuItem = menu.findItem(R.id.search);
        searchView = (SearchView) searchMenuItem.getActionView();

        searchView.setSearchableInfo(searchManager.
                getSearchableInfo(getComponentName()));
        searchView.setSubmitButtonEnabled(true);
        searchView.setOnQueryTextListener(this);

        return true;
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {

            instance = this;
            posicionSeleccionada = null;
            setContentView(R.layout.flota_layout);

            listaMoviles = (ListView) findViewById(R.id.lvFlota);
            titulo = (TextView) findViewById(R.id.titulo);

            titulo.setText("Mi flota");

            boolean verHistorico = false;
            try {
                verHistorico = this.getIntent().getExtras().getBoolean("verHistorico");
            } catch (Exception e) {
                // no hay parámetro de entrada
                verHistorico = false;
            }

            findViewById(R.id.btHistorico).setVisibility(verHistorico ? View.VISIBLE : View.INVISIBLE);

            // Evento botón histórico
            findViewById(R.id.btHistorico).setOnClickListener(v -> {

                if (posicionSeleccionada != null) {
                    MainActivity.getInstance().setMenuLimpiarFlotaVisibility(true);
                    Intent intent = new Intent(FlotaActivity.getInstance(), FlotaHistoricoActivity.class);
                    intent.putExtra("fecha", posicionSeleccionada.getFecha());
                    intent.putExtra("movil", posicionSeleccionada.getMovil());
                    startActivity(intent);
                    getInstance().finish();

                } else {

                    // Cancelar evento
                    Toast.makeText(FlotaActivity.getInstance(), R.string.debeSeleccionarMovil,
                            Toast.LENGTH_LONG).show();
                }
            });


            // Evento botón centrar
            findViewById(R.id.btCentrar).setOnClickListener(v -> {

                if (posicionSeleccionada != null) {

                    // Se inserta el elemento de flota seleccionado en la lista de
                    // GestionElementos para incluirlo en el cluster
                    posicionSeleccionada.setPosition(posicionSeleccionada.getLat(), posicionSeleccionada.getLng());
                    GestionElementos.getFlotaList().add(posicionSeleccionada);

                    // Como ahora hay elementos de flota en el mapa, se muestra la
                    // opción de menú de eliminar los marcadores
                    MainActivity.getInstance().setMenuLimpiarFlotaVisibility(true);

                    LatLng posicion = new LatLng(
                            posicionSeleccionada.getLat(),
                            posicionSeleccionada.getLng());
                    MyBroadCastManager
                            .getInstance()
                            .sendBroadCastCenterMapBy(
                                    posicion,
                                    18,
                                    posicionSeleccionada.toString(),
                                    false);

                    finish();
                }
            });

            // Evento botón salir
            findViewById(R.id.btSalir).setOnClickListener(v -> finish());

            listaMoviles.setOnItemClickListener((list, view, index, id) -> {

                posicionSeleccionada = (FlotaPosiciones) listaMoviles.getItemAtPosition(index);

                modelAdapter.setIndexSeleccionado(index);

                modelAdapter.notifyDataSetChanged();

                LayoutInflater inflater = getLayoutInflater();
                View layout = inflater.inflate(R.layout.toast_custom,
                        (ViewGroup) findViewById(R.id.toast_layout_root));

                TextView text = (TextView) layout.findViewById(R.id.text);

                // llamo al geocoder de google para recuperar la dirección, en vez de llamar al gis que es muy lento
                Geocoder geocoder = new Geocoder(getApplicationContext(), new Locale("es"));

                List<Address> addresses = null;
                String addressText = "";
                String numCalle = "";
                try {
                    addresses = geocoder.getFromLocation(posicionSeleccionada.getLat(), posicionSeleccionada.getLng(), 1);

                    if (addresses != null && addresses.size() > 0) {
                        Address address = addresses.get(0);
                        addressText = String.format("%s, %s",
                                (address.getAddressLine(0) == null ? "" : address.getAddressLine(0)),
                                address.getLocality() == null ? "" : address.getLocality());

                        if (StringUtil.isNumeric(address.getFeatureName())) {
                            addressText += " - Nº: " + address.getFeatureName();
                        }
                    }
                } catch (IOException e) {
                    addressText = "No puede recuperarse la información sobre su última posición";
                }

                /*text.setText(GestionElementos.getInfoGIS(
                        posicionSeleccionada.getLat(),
                        posicionSeleccionada.getLng()));*/
                text.setText(addressText);

                text.setTextColor(getResources().getColor(R.color.white_color));

                if (toast != null) {
                    toast.cancel();
                }
                toast = new Toast(getApplicationContext());
                toast.setView(layout);
                toast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
                toast.setDuration(Toast.LENGTH_LONG);
                toast.show();
            });

            String ultimasIdentificaciones = Config.getInstance().getValueEmpresa("ultSincroIdentificaciones", "");
            if (!ultimasIdentificaciones.isEmpty()) {
                setFechaUltimasIdentificaciones(ultimasIdentificaciones);
                findViewById(R.id.btActualizar).setOnClickListener(v -> {
                    try {
                        DBSynchro.getInstance().synchroData(DBSynchro.GET_IDENTIFICACIONES);
                    } catch (Throwable e) {
                        Logg.error(TAG, e.toString());
                    }
                });
            }

            ArrayList<FlotaPosiciones> items = new ArrayList<>();

            modelAdapter = new FlotaModelAdapter(MainActivity.getInstance(), items);

            listaMoviles.setAdapter(modelAdapter);
            listaMoviles.setTextFilterEnabled(true);

            updateItems();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            new InfoDialog(this, getString(R.string.atencion),
                  getString(R.string.problema) + e.getMessage(),
                  InfoDialog.ICON_STOP,
                  option -> finish(),
                  InfoDialog.BUTTON_ACCEPT,
                  InfoDialog.POSITION_CENTER)
                  .show();
        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(onFinishSincroUltimasPosiciones event) {
        updateItems();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(onFinishSincroUltimasIdentificaciones event) {
       updateItems();
    }

    private void updateItems() {
        DBFlotaPosiciones dbFlota = new DBFlotaPosiciones();
        ArrayList<FlotaPosiciones> items = dbFlota.getAll(MainActivity.getInstance().getEmpresa(),
                MainActivity.getInstance().getUsuario());
        dbFlota.close();

        String ultimasIdentificaciones = Config.getInstance().getValueEmpresa("ultSincroIdentificaciones", "");
        if (!ultimasIdentificaciones.isEmpty()) {
            setFechaUltimasIdentificaciones(ultimasIdentificaciones);
            DBIdentificaciones dbIdentificaciones = new DBIdentificaciones();
            for (FlotaPosiciones posicion : items) {
                posicion.setIdentificaciones(dbIdentificaciones.getByMovil(posicion.getMovil()));
            }
        }

        modelAdapter.setItems(items);
        modelAdapter.notifyDataSetChanged();
    }

    private void setFechaUltimasIdentificaciones(String fecha) {
        long milliseconds = HelperDates.getInstance().getMillisecondsBy(fecha);
        fecha = HelperDates.getInstance().getDateStringBy(milliseconds, "dd/MM/yy HH:mm:ss");
        fecha = (Environment.isTablet ? ": " : "\n") + fecha;
        TextView fechaTv = findViewById(R.id.txtUltimasIdentificaciones);
        fechaTv.setTextSize(Environment.isTablet ? 14 : 10);
        fechaTv.setText("Últimas identificaciones" + fecha);
    }

    @Override
    protected void onStop() {
        EventBus.getDefault().unregister(this);
        super.onStop();
    }

    /**
     * Este mitodo se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Override
    public boolean onQueryTextSubmit(String query) {
        return false;
    }

    @Override
    public boolean onQueryTextChange(String newText) {
        modelAdapter.getFilter().filter(newText);
        /*if (TextUtils.isEmpty(newText)) {
            listaMoviles.clearTextFilter();
        }
        else {
            listaMoviles.setFilterText(newText);
        }*/
        return true;
    }
}
