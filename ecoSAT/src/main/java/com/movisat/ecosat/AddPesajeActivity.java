package com.movisat.ecosat;

import static java.lang.Integer.parseInt;

import android.app.Dialog;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;
import android.widget.Toast;

import com.movisat.adapter.PlanchadasAdapter;
import com.movisat.bll.SensoresManager;
import com.movisat.database.DBPlanchadas;
import com.movisat.database.DBSensoresNivelesLlenado;
import com.movisat.database.Planchada;
import com.movisat.database.SensorNivelLLenado;
import com.movisat.utilities.Config;
import com.movisat.utilities.Utils;

import java.sql.Date;
import java.util.ArrayList;
import java.util.Calendar;

public class AddPesajeActivity extends BaseActivity {

    private EditText desde;
    private EditText hasta;
    private EditText pesaje;
    private TextView txtPlanchada;
    private Button btnPesajeInsert;
    private Spinner spinnerPlanchadas;
    private ArrayList<Planchada> planchadas;
    private static AddPesajeActivity instance = null;


    //private AddPesajeActivity(){}

    public static AddPesajeActivity getInstance() {
        /*if (instance == null)
            instance = new AddPesajeActivity();*/

        return instance;
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.layout_pesaje_vertedero);
        instance = this;

        desde = (EditText) findViewById(R.id.editDesde);
        hasta = (EditText) findViewById(R.id.editHasta);
        spinnerPlanchadas = (Spinner) findViewById(R.id.spinnerPlanchadas);
        txtPlanchada = (TextView) findViewById(R.id.textPlanchada);
        String tipoSoft = Config.getInstance().getValue("tipoSoft", "0");

        boolean mostrarPlanchado = getIntent().getBooleanExtra("planchado", false);

        if (!mostrarPlanchado) {
            txtPlanchada.setVisibility(View.GONE);
            spinnerPlanchadas.setVisibility(View.GONE);
        }

        // Desde y hasta para la versiones de operario bloqueado.
        desde.setEnabled(false);
        hasta.setEnabled(false);

        pesaje = (EditText) findViewById(R.id.editPesaje);
        btnPesajeInsert = (Button) findViewById(R.id.btnInsertPesaje);

        OnClickListener clickInsertPesaje = new ClickInsertPesaje();
        btnPesajeInsert.setOnClickListener(clickInsertPesaje);

        DBSensoresNivelesLlenado dbSensor = new DBSensoresNivelesLlenado();

        // TODO: ¿Y si lo guarda con el código del vehículo, como hace en NivelLlenadoActivity.java?
        //      ¿No debería comprobar si está por ese código también?
        int codigoMovil = parseInt(Config.getInstance().getValue("codigoDispositivo", "0"));
        SensorNivelLLenado sensor = dbSensor.getFirstSensorSinProcesarByMovil(MainActivity.getInstance()
                .getEmpresa(), codigoMovil);

        // TODO -> ¿Por qué se hace esto? Descubrirlo y arreglarlo para no ser selectivo por versiones.
        //

        if  ((!tipoSoft.equals("207") || !tipoSoft.equals("208") || !tipoSoft.equals("213") ||
                !tipoSoft.equals("215") || !tipoSoft.equals("218")) && sensor == null) {
            sensor = dbSensor.getFirstSensorSinProcesarByMovil(
                    MainActivity.getInstance().getEmpresa(), 0);
        }

        dbSensor.close();

        if (sensor == null) {
            Toast.makeText(
                    this,
                    R.string.noDataNivelesLlenado,
                    Toast.LENGTH_LONG).show();
            finish();
        } else {
            // Ahora lo que tenemos que hacer es poner en desde el primer
            // elemento y en hasta el iltimo
            desde.setText(Utils.datetimeToString(
                    new Date(sensor.getFechaRegistro()), "yyyy-MM-dd HH:mm:ss"));

            hasta.setText(Utils.datetimeToString(
                    new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss"));

            pesaje.setText("0");
        }

        // Recupero todas las planchadas
        DBPlanchadas dbPlanchadas = new DBPlanchadas();
        planchadas = dbPlanchadas.getAll(MainActivity.getInstance().getEmpresa());
        dbPlanchadas.close();

        if (planchadas != null) {
            // Relleno el combo de modelos
            PlanchadasAdapter planchadasAdapter;
            planchadasAdapter = new PlanchadasAdapter(AddPesajeActivity.getInstance(), planchadas);

            spinnerPlanchadas.setAdapter(planchadasAdapter);

            //Recuperamos posicion
            String stringPosition = Config.getInstance().getValueEmpresa("planchada_seleccionada", "");
            if (!stringPosition.equals("")) {
                int position = Integer.valueOf(Config.getInstance().getValueEmpresa("planchada_seleccionada", ""));
                spinnerPlanchadas.setSelection(position);
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    private class ClickInsertPesaje implements OnClickListener {
        @Override
        public void onClick(View v) {

            // TODO: AO, deshabilito el bot?n para que no env?en m?s de un registro a la vez
            btnPesajeInsert.setEnabled(false);

            int empresa = MainActivity.getInstance().getEmpresa();

            long fechaInicio = Utils.StringToDateTime(
                    desde.getText().toString()).getTime();
            long fechaFin = Utils.StringToDateTime(
                    hasta.getText().toString()).getTime();
            int pesoVertedero = parseInt(pesaje.getText().toString());

            Planchada planchadaObjSeleccionada = (Planchada) spinnerPlanchadas.getSelectedItem();
            int planchada = 0;
            if (planchadaObjSeleccionada != null)
                planchada = planchadaObjSeleccionada.getId();

            //Guardamos el valor para recuperarlo
            Config.getInstance().setValueEmpresa("planchada_seleccionada",
                    String.valueOf(spinnerPlanchadas.getSelectedItemPosition()));

            if (fechaFin > fechaInicio) {
                SensoresManager.getInstance().sendSensorPesoEnVertedero(empresa,
                        fechaInicio, fechaFin, pesoVertedero, planchada);

                Toast.makeText(AddPesajeActivity.this,
                        R.string.sendingTicketVertedero,
                        Toast.LENGTH_LONG).show();

                finish();
            } else {
                Toast.makeText(AddPesajeActivity.this,
                        R.string.fechasDesdeHastaIncorrectas,
                        Toast.LENGTH_SHORT).show();
            }

        }
    }

    Dialog dialog;
    View vistaDialog;

    public void showTimePickerDialog(View v) {

        vistaDialog = v;
        dialog = new Dialog(this);

        dialog.setContentView(R.layout.date_time_layout);

        dialog.setTitle(getString(R.string.choseDate));

        dialog.show();

        OnClickListener clickOkDialog = new clickTimePicker();
        OnClickListener clickCancelarDialog = new clickCancelarTimePicker();
        Button ok = (Button) dialog.findViewById(R.id.OkDateTime);
        ok.setOnClickListener(clickOkDialog);

        Button cancelar = (Button) dialog.findViewById(R.id.CancelarDateTime);
        cancelar.setOnClickListener(clickCancelarDialog);

    }

    public class clickCancelarTimePicker implements OnClickListener {

        @Override
        public void onClick(View v) {
            dialog.dismiss();
        }
    }

    public class clickTimePicker implements OnClickListener {

        @Override
        public void onClick(View v) {
            DatePicker dp = (DatePicker) dialog.findViewById(R.id.datePicker1);
            TimePicker tp = (TimePicker) dialog.findViewById(R.id.timePicker1);

            Calendar c = Calendar.getInstance();
            int iSeconds = c.get(Calendar.SECOND);

            String minutes = (tp.getCurrentMinute() > 9) ? ""
                    + tp.getCurrentMinute() : "0" + tp.getCurrentMinute();
            String months = (dp.getMonth() + 1) > 9 ? "" + (dp.getMonth() + 1)
                    : "0" + (dp.getMonth() + 1);
            String days = (dp.getDayOfMonth() > 9) ? "" + dp.getDayOfMonth()
                    : "0" + dp.getDayOfMonth();
            String hours = tp.getCurrentHour() > 9 ? "" + tp.getCurrentHour()
                    : "0" + tp.getCurrentHour();
            String seconds = iSeconds > 9 ? "" + iSeconds : "0" + iSeconds;

            String strDateTime = dp.getYear() + "-" + months + "-" + days + " "
                    + hours + ":" + minutes + ":" + seconds;

            EditText edit = (EditText) vistaDialog;
            edit.setText(strDateTime);

            dialog.dismiss();

        }

    }

}
