package com.movisat.ecosat;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.AsyncTask;
import android.os.Message;
import android.util.Log;

import com.environment.EnvironmentDebug;
import com.movisat.database.DBError;
import com.movisat.events.OnExitApplication;
import com.movisat.events.OnNotifyIntranet;
import com.movisat.utilities.Config;
import com.movisat.utilities.Ftp;
import com.movisat.utilities.HelperDates;
import com.movisat.utilities.HelperUpload;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;


public class IntranetNew extends AsyncTask<Void, Void, Void> {
    private static final String TAG = "IntranetNew";
    private boolean interrupted;
    private final static int INTERVAL = 1000 * 60 * 10; // 10 minutos
    public static boolean haveConnected = false;

//    long startTime = 0;
//    long estimatedTime = 0;

//    @Override
//    protected void onPreExecute() {
//        startTime = System.currentTimeMillis();
//    }

    @Override
    protected Void doInBackground(Void... params) {
        //  estimatedTime = System.currentTimeMillis() - startTime;
        
        //if (estimatedTime <= 5 || estimatedTime > INTERVAL) {
        //   startTime = System.currentTimeMillis();
        while (!interrupted && !isCancelled() && MainActivity.getInstance() != null) {
            checkLog();
            checkIntranet();
            // Después de obtener las variables, volvermos a actualizar Environment.
            MainActivity.getInstance().startEnvironment();
            //Se deshabilita por problemas con el IMEI
            checkActivacionMovil();
        }
        // }
        if (MainActivity.getInstance() != null) {
            sendMessageOK();
        }
        return null;
    }

    public void uploadEcoSAT_log(String imei)
            throws NumberFormatException, IOException {

        Ftp.sendFile("sempre01.movisat.com", "android", "movisatAndroid2015",
                MainActivity.getInstance().getFilesDir() + "/EcoSAT_log.txt",
                System.currentTimeMillis() + "_" + imei + ".txt");
        // upload("sempre01.movisat.com/android/logs/", "android",
        // "movisatAndroid2015", imei + ".txt", fichero);

    }

    public void uploadLog(String imei, String number_lines)
            throws NumberFormatException, IOException {
        writeTxt(Integer.parseInt(number_lines));

        Ftp.sendFile("sempre01.movisat.com", "android", "movisatAndroid2015",
                MainActivity.getInstance().getFilesDir() + "/log.txt",
                System.currentTimeMillis() + "_" + imei + ".txt");
        // upload("sempre01.movisat.com/android/logs/", "android",
        // "movisatAndroid2015", imei + ".txt", fichero);

    }


    public void uploadLog(String imei, String fecha_ini, String fecha_fin)
            throws NumberFormatException, IOException {

        //HelperUpload.getInstance().uploadDataBaseToIntranet(MainActivity.getInstance(), imei);
        writeTxt(HelperDates.getInstance().getMillisecondsBy(fecha_ini),
                HelperDates.getInstance().getMillisecondsBy(fecha_fin));

        Ftp.sendFile("sempre01.movisat.com", "android", "movisatAndroid2015",
                MainActivity.getInstance().getFilesDir() + "/log.txt",
                System.currentTimeMillis() + "_" + imei + ".txt");
        // upload("sempre01.movisat.com/android/logs/", "android",
        // "movisatAndroid2015", imei + ".txt", fichero);

    }

    public void writeTxt(int lineas) throws IOException {
        String myfile = "log.txt";
        DBError db = new DBError();

        String string = db.getLastTxt(lineas);
        db.close();

        FileOutputStream fos = MainActivity.getInstance().openFileOutput(
                myfile, Context.MODE_PRIVATE);
        fos.write(string.getBytes());
        fos.close();
    }


    public void writeTxt(long fecha_ini, long fecha_fin) throws IOException {
        String myfile = "log.txt";
        DBError db = new DBError();

        String string = db.getLastTxt(fecha_ini, fecha_fin);
        db.close();

        FileOutputStream fos = MainActivity.getInstance().openFileOutput(
                myfile, Context.MODE_PRIVATE);
        fos.write(string.getBytes());
        fos.close();
    }

    private void checkLog() {
        HttpClient httpClient = new DefaultHttpClient();
        HttpContext localContext = new BasicHttpContext();
        String imei = Config.getInstance().getValue("imei", "");

        HttpGet httpGet = new HttpGet("http://rest.movisat.com/api"
                + "/log/check/" + imei);

//        PackageInfo pInfo = null;
//        try {
//            pInfo = MainActivity
//                    .getInstance()
//                    .getPackageManager()
//                    .getPackageInfo(
//                            MainActivity.getInstance().getPackageName(), 0);
//        } catch (NameNotFoundException e1) {
//            e1.printStackTrace();
//            return;
//        } catch (NullPointerException e2) {
//            e2.printStackTrace();
//            return;
//        }
        String text = null;

        try {
            HttpResponse response = httpClient.execute(httpGet, localContext);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                text = EntityUtils.toString(entity);
                JSONObject json = null;

                try {
                    if (text.startsWith("[")) {
                        JSONArray jsonA = new JSONArray(text);
                        json = jsonA.getJSONObject(0);
                    } else {
                        json = new JSONObject(text);
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                }

                if (json.isNull("Estado")) {

                    String numero_lineas_log = json
                            .getString("numero_lineas_log");

                    if (!json.isNull("database") && json.getString("database").equals("1")) {
                        HelperUpload.getInstance().uploadDataBaseToIntranet(MainActivity.getInstance(), imei);
                    }

                    if (!numero_lineas_log.equals("0")) {
                        uploadLog(imei, numero_lineas_log);
                    } else if (!json.isNull("fecha_ini") &&
                            !json.getString("fecha_ini").contains("0000-00-00 00:00:00")) {
                        String fecha_ini = json
                                .getString("fecha_ini");
                        String fecha_fin = json
                                .getString("fecha_fin");

                        uploadLog(imei, fecha_ini, fecha_fin);

                    }/*else{
                        uploadEcoSAT_log(imei);
                    }*/

                }
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    @SuppressWarnings("deprecation")
    private void checkActivacionMovil() {
        HttpClient httpClient = new DefaultHttpClient();
        HttpContext localContext = new BasicHttpContext();
        String imei = Config.getInstance().getValue("imei", "");

        HttpGet httpGet = new HttpGet(
                "http://rest.movisat.com/api/licencias/status/" + imei);

        String text = null;

        try {
            HttpResponse response = httpClient.execute(httpGet, localContext);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                text = EntityUtils.toString(entity);
                // parse json

                JSONObject json = null;

                json = new JSONObject(text);

                if (!json.isNull("Estado")) {

                    if (json.getString("Estado").equals("KO")) {
                        // Borrar datos de la aplicación
                        // siempre que tengamos guardada la url para asegurarnos que
                        // no es una activación nueva.
                        //String urlWeb = Config.getInstance().getValue("webSvc", "");
                        MainActivity.getInstance().deleteLicense();
                        //String urlWeb = Config.getInstance().getValue("webSvc", "");
                        /*if (!urlWeb.equals("")) {
                            deleteData();

                        }*/

                    }
                }

            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
        }

        // Dormimos la tarea durante x minutos.
        try {
            //  wait(INTERVAL);
            //    Thread.currentThread()
            Thread.sleep(INTERVAL);
        } catch (InterruptedException e) {


        }
    }

    private void deleteData() {
        File cache = MainActivity.getInstance().getCacheDir();
        File appDir = new File(cache.getParent());
        if (appDir.exists()) {
            String[] children = appDir.list();
            for (String s : children) {
                if (!s.equals("lib")) {
                    deleteDir(new File(appDir, s));
                    /*Log.i("TAG",
                            "**************** File /data/data/APP_PACKAGE/" + s
                                    + " DELETED *******************");*/
                }
            }
        }

        //que salga con un evento enviado con event bus
        EventBus.getDefault().post(new OnExitApplication("Reiniciando sistema para aplicar cambios"));
    }

    public static boolean deleteDir(File dir) {
        if (dir != null && dir.isDirectory()) {
            String[] children = dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success = deleteDir(new File(dir, children[i]));
                if (!success) {
                    return false;
                }
            }
        }

        return dir.delete();
    }

    private void checkIntranet() {
        HttpClient httpClient = new DefaultHttpClient();
        HttpContext localContext = new BasicHttpContext();
        String tipoSoft = Config.getInstance().getValue("tipoSoft", "0");
        String tipoSoftDebug = EnvironmentDebug.getData().getTipoSoft();
        if (tipoSoftDebug != null)
            tipoSoft = tipoSoftDebug;
        String imei = Config.getInstance().getValue("imei", "");
        String url = Config.getInstance().getValue("webSvc", "");
        String save_ruta = Config.getInstance().getValue("save_ruta", "0");
        // Boolean GuardarRuta = false;
        String menu = Config.getInstance().getValue("menu", ""); // "10;20;30;100;110;120;130;140;1000;1010;1020;1030";
        String modoRuta = Config.getInstance().getValue("modo_ruta", "");

        HttpGet httpGet = new HttpGet("http://rest.movisat.com/api"
                + "/software?tipoSoft=" + tipoSoft + "&imei=" + imei + "&url="
                + url + "&menu=" + menu + "&save_ruta=" + save_ruta
                + "&modo_ruta=" + modoRuta);

        PackageInfo pInfo = null;
        try {
            pInfo = MainActivity.getInstance().getPackageManager()
                    .getPackageInfo(MainActivity.getInstance().getPackageName(), 0);
        } catch (NameNotFoundException e1) {

        }

        String version = (pInfo != null) ? String.valueOf(pInfo.versionCode) : "";
        String versionCode = (pInfo != null) ? String.valueOf(pInfo.versionCode) : "";
        Config.getInstance().setValue("versionSoftware", version);

        String text = null;

        try {
            HttpResponse response = httpClient.execute(httpGet, localContext);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                text = EntityUtils.toString(entity);
                // parse json

                JSONObject json = null;

                try {
                    if (text.startsWith("[")) {
                        JSONArray jsonA = new JSONArray(text);
                        json = jsonA.getJSONObject(0);
                    } else {
                        json = new JSONObject(text);
                    }
                } catch (Exception ex) {
                    MyLoggerHandler.getInstance().error(ex);
                }

                if (json.isNull("Estado")) {

                    String urlIntranet = json.getString("urlAPI");
                    String tipoSoftIntranet = json.getString("verSoftware");

                    String menuIntranet = (json.isNull("menu")) ? menu : json
                            .getString("menu");

                    String save_rutaItranet = (json.isNull("save_ruta")) ? save_ruta
                            : json.getString("save_ruta");

                    String modoRutaIntranet = (json.isNull("modo_ruta")) ? modoRuta
                          : json.getString("modo_ruta");

                    // TODO jcaballero esto se comenta si quiero poner yo la url de conexión y no la de la intranet
                    if (urlIntranet != url)
                        Config.getInstance().setValue("webSvc", urlIntranet);

                    if (tipoSoftDebug != null) {
                        tipoSoftIntranet = tipoSoftDebug;
                        Config.getInstance().setValue("tipoSoft", tipoSoftIntranet);
                    }


                    if (tipoSoftIntranet != tipoSoft)
                        Config.getInstance().setValue("tipoSoft", tipoSoftIntranet);
                        
                    haveConnected = true;

                    Config.getInstance().setValue("menu", menuIntranet);
                    Config.getInstance().setValue("save_ruta", save_rutaItranet);
                    Config.getInstance().setValue("modo_ruta", modoRutaIntranet);

                    //Para forzar sincronización de toda la flota de nuevo.
                    Config.getInstance().removeKey("ultSincroFlota");

                    Message msg = new Message();

                    msg.what = MainActivity.MESSAGE_RESTART;

                    MainActivity.handler.sendMessage(msg);
                    insertCambioToIntranet(imei, tipoSoftIntranet, versionCode);

                } else {

                    // Esta llamada la quitamos porque se duplican datos en la Intranet ya que
                    // hace cosas que se hacen en IntranetManager.java
                    //sendEstadisticaToIntranet(imei, tipoSoft, versionCode);
                    haveConnected = true;
                }
                // Informar a la intranet que todo esta ok.
                sendMessageOK();
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            sendMessageOK();
        }
    }

    private synchronized void sendMessageOK() {

        if (MainActivity.getInstance() != null)
            MainActivity.getInstance().setIntranet(true);

        EventBus.getDefault().post(new OnNotifyIntranet(MyLoadingActivity.MESSAGE_INTRANET_OK));

    }

    private void insertCambioToIntranet(String imei, String tipoSoft,
                                        String version) throws IOException, JSONException {
        HttpClient httpClient = new DefaultHttpClient();
        HttpContext localContext = new BasicHttpContext();

        String text = null;
        String urlAPI = "";
        HttpGet httpGet = null;
        try {
            urlAPI = "http://rest.movisat.com/api"
                    + "/software/insert/" + imei + "/" + tipoSoft + "/"
                    + version;

            String ultFechaGPS = Config.getInstance().getValue("ultFechaGPS", "");

            if (!ultFechaGPS.equals("")) {
                urlAPI = "http://rest.movisat.com/api"
                        + "/log/insert/" + imei + "/" + tipoSoft + "/"
                        + version + "/" + ultFechaGPS;
            }

            httpGet = new HttpGet(urlAPI);
        } catch (Exception e) {
            return;
        }

        HttpResponse response = httpClient.execute(httpGet, localContext);
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            text = EntityUtils.toString(entity);
            // parse json

            JSONObject json = null;
            json = new JSONObject(text);
            try {
                json = new JSONObject(text);
                if (!json.isNull("Estado")) {
                    return;
                }
            } catch (Exception ex) {
            }

        }
    }

    /*private void sendEstadisticaToIntranet(String imei, String tipoSoft,
                                           String version) throws IOException,
            JSONException {
        HttpClient httpClient = new DefaultHttpClient();
        HttpContext localContext = new BasicHttpContext();
//        String url = Config.getInstance().getValue("webSvc", "");
        String text = null;
        String urlAPI = "";
        HttpGet httpGet = null;
        try {

            urlAPI = "http://rest.movisat.com/api"
                    + "/software/update/" + imei + "/"
                    + tipoSoft.replace(".", "") + "/"
                    + version.replace(".", "");
            String ultFechaGPS = Config.getInstance().getValue("ultFechaGPS", "");

            if (!ultFechaGPS.equals("")) {
                urlAPI = "http://rest.movisat.com/api"
                        + "/log/update/" + imei + "/"
                        + tipoSoft.replace(".", "") + "/"
                        + version.replace(".", "") + "/" + ultFechaGPS;
            }
            httpGet = new HttpGet(urlAPI);

        } catch (Exception e) {
            e.printStackTrace();
        }

        HttpResponse response = httpClient.execute(httpGet, localContext);
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            text = EntityUtils.toString(entity);
            // parse json

            JSONObject json = null;
            try {
                json = new JSONObject(text);
                if (!json.isNull("Estado")) {
                    return;
                }
            } catch (Exception ex) {

            }

        }
    }*/

    public boolean isInterrupted() {
        return interrupted;
    }

    public void setInterrupted(boolean interrupted) {
        this.interrupted = interrupted;
    }

}
