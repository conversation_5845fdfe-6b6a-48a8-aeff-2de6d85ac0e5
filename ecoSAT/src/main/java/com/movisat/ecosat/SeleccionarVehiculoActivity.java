package com.movisat.ecosat;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ListView;
import android.widget.Toast;

import com.movisat.adapter.VehiculoAdapter;
import com.movisat.database.DBVehiculo;
import com.movisat.database.Vehiculo;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Phone;

import java.util.ArrayList;
import java.util.Iterator;

/**
 * Created by dsanchez on 23/05/2018.
 */

public class SeleccionarVehiculoActivity extends BaseActivity {

    private ListView listVehiculos;
    private Button btnSalir;

    private VehiculoAdapter mAdapterVehiculos;
    private Vehiculo mVehiculoAsociado = null;
    private Vehiculo mDispositivo = null;       // Dispositivo Android actual

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            setContentView(R.layout.seleccionar_vehiculo);

            // Elementos de la UI
            listVehiculos = (ListView) findViewById(R.id.listVehiculos);
            btnSalir = (Button) findViewById(R.id.btnSalir);

            loadVehiculos();

            setListVehiculosOnItemClickListener();

            btnSalir.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    finish();
                }
            });

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }


    /**
     * Obtiene los vehículos de la base de datos y crea el adaptador para mostrarlos en la lista.
     */
    private void loadVehiculos() {
        try {
            DBVehiculo dbVehiculo = new DBVehiculo();
            ArrayList<Vehiculo> vehiculos = dbVehiculo.getAllBy(MainActivity.getInstance().getEmpresa());
            dbVehiculo.close();

            // Si no hay vehículos, se muestra un mensaje y se sale del Activity
            if (vehiculos == null || vehiculos.size() == 0) {
                MainActivity.getInstance().showMessage(R.string.no_hay_vehiculos);
                finish();
            }

            // Se obtiene el dispositivo Android actual y se coloca al inicio de la lista
            String imei = Phone.getInstance().getIMEI();

            Iterator<Vehiculo> iter =  vehiculos.iterator();

            while (iter.hasNext()) {
                Vehiculo vehiculo = iter.next();

                if (vehiculo.getTipo() == Vehiculo.TIPO_MOVIL) {
                    // Los vehículos de tipo MOVIL no se muestran en la lista (TEMPORAL)
                    if (vehiculo.getImei() != null && vehiculo.getImei().equals(imei)) {
                        // El dispositivo Android actual se colocará al inicio de la lista
                        mDispositivo = vehiculo;
                    }
                    iter.remove();
                }
            }

            // El dispositivo Android actual se coloca al inicio de la lista
            if (mDispositivo != null)
                vehiculos.add(0, mDispositivo);

//            for (Vehiculo vehiculo : vehiculos) {
//                if (vehiculo.getImei() != null && vehiculo.getImei().equals(imei)) {
//                    mDispositivo = vehiculo;
//                    vehiculos.remove(mDispositivo);
//                    vehiculos.add(0, mDispositivo);
//                    break;
//                }
//            }

            // Se crea el adaptador con lo vehículos y se aplica a la lista
            mAdapterVehiculos = new VehiculoAdapter(this, vehiculos);
            listVehiculos.setAdapter(mAdapterVehiculos);

            //Se obtiene el vehículo asociado al dispositivo y se selecciona en la lista
            mVehiculoAsociado = Vehiculo.getVehiculoAsociado();
            if (mVehiculoAsociado != null) {
                int index = mAdapterVehiculos.getIndexVehiculo(mVehiculoAsociado.getCodigo(),
                        mVehiculoAsociado.getEmpresa(), mVehiculoAsociado.getTipo());
                if (index >= 0)
                    mAdapterVehiculos.setIndexSeleccionado(index);
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Establece en la lista de vehículos el listener para recibir las pulsaciones de elementos.
     */
    private void setListVehiculosOnItemClickListener() {
        try {
            listVehiculos.setOnItemClickListener(new AdapterView.OnItemClickListener() {
                @Override
                public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {

                    try {
                        int indexAsociado = mAdapterVehiculos.getIndexSeleccionado();

                        if (i != indexAsociado)
                            asociarVehiculo(i);
                        else
                            MainActivity.getInstance().showMessage(R.string.vehiculo_ya_asociado);

                    } catch (Exception e) {
                        MyLoggerHandler.getInstance().error(e);
                        e.printStackTrace();
                    }
                }
            });

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Asocia el vehículo con el índice especificado al dispositivo tras mostrar un diálogo.
     */
    private void asociarVehiculo(final int index) {
        try {
            final Vehiculo vehiculo = mAdapterVehiculos.getItem(index);

            new InfoDialog(this,
                    getString(R.string.asociar_vehiculo_title),
                    getString(R.string.asociar_vehiculo_message, vehiculo.getNombre()),
                    InfoDialog.ICON_QUESTION,
                    new OnInfoDialogSelect() {
                        @Override
                        public void onSelectOption(int option) {
                            if (option == InfoDialog.BUTTON_YES) {

                                mAdapterVehiculos.setIndexSeleccionado(index);

                                // Se envía un mensaje al servidor
                                DBPacket dbPacket = new DBPacket();
                                dbPacket.insert(new Packet(
                                        Packet.VEHICULO_ASOCIAR, Packet.PRIORIDAD_NORMAL, vehiculo));
                                dbPacket.close();

                                Vehiculo.setVehiculoAsociado(vehiculo);
                                MainActivity.getInstance().showMessage(
                                        getString(R.string.vehiculo_asociado, vehiculo.getNombre()),
                                        Toast.LENGTH_LONG);
                                finish();
                            }
                        }
                    },
                    InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                    InfoDialog.POSITION_CENTER).show();

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }
}
