package com.movisat.ecosat;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.BitmapDrawable;
import android.media.RingtoneManager;

import androidx.core.app.NotificationCompat;
import android.util.Log;
import android.os.Build;

public class NotificacionesActivity extends BroadcastReceiver {

    private static final String CHANNEL_ID_INCIDENCIAS = "incidencias_channel";

    public static int ID_INCIDENCIA = 1000;
    String act_incidencias = "com.movisat.INCIDENCIAS";

    @Override
    public void onReceive(Context context, Intent intent) {



        if(intent.getAction().equals(act_incidencias)){
            //your code here

           boolean extra = intent.hasExtra("idIncidencia");

            if (!extra)
                return;

            int idInci = intent.getIntExtra("idIncidencia", 0);
            createNotificacionIncidencias(context, idInci);

        }

    }



    public synchronized void createNotificacionIncidencias(Context context, int inci) {

        try {

            ensureChannel(context);

            Intent resultIntent = new Intent(context, IncidenciasActivity.class);
            resultIntent.setAction(Intent.ACTION_MAIN);
            resultIntent.putExtra("idIncidencia", inci);

            /*TaskStackBuilder stackBuilder = TaskStackBuilder.create(context);
            stackBuilder.addParentStack(MainActivity.class);
            stackBuilder.addNextIntentWithParentStack(resultIntent);*/

            //PendingIntent resultPendingIntent = stackBuilder.getPendingIntent(0, PendingIntent.FLAG_UPDATE_CURRENT);
            PendingIntent resultPendingIntent = PendingIntent.getActivity(context, 0,
                    resultIntent, PendingIntent.FLAG_CANCEL_CURRENT | PendingIntent.FLAG_IMMUTABLE);

            NotificationCompat.Builder mBuilder =
                    new NotificationCompat.Builder(context, CHANNEL_ID_INCIDENCIAS)
                    .setContentTitle("Incidencias EcoSAT Móvil")
                    .setSmallIcon(R.drawable.ic_launcher_v0)
                    .setLargeIcon(
                            ((BitmapDrawable) context.getResources().getDrawable(
                                    R.drawable.ic_launcher)).getBitmap())
                    .setAutoCancel(true)
                    .setOngoing(false)
                    .setGroup("Incidencias")
                    .setContentText("Notificación de incidencias")
                    .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                    .setPriority(NotificationCompat.PRIORITY_MAX);

            mBuilder.setContentIntent(resultPendingIntent);
            NotificationManager mNotificationManager = (NotificationManager) context
                    .getSystemService(Context.NOTIFICATION_SERVICE);

            mBuilder.setDefaults(Notification.DEFAULT_SOUND | Notification.DEFAULT_VIBRATE
                    | Notification.DEFAULT_LIGHTS)
                    .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
            mNotificationManager.notify(inci, mBuilder.build());




            resultPendingIntent = null;
            //stackBuilder = null;
            mNotificationManager = null;
            mBuilder = null;
            resultIntent = null;

        } catch (Exception e) {
            Log.e("Error", "Se ha producido un error al gestionar notificacion: " + e.getMessage());
        }

        /*if (dbom != null) {
            dbom.cierraDB();
            dbom=null;
            System.gc();
        }*/
    }

    private static void ensureChannel(Context ctx) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager nm =
                    (NotificationManager) ctx.getSystemService(Context.NOTIFICATION_SERVICE);
            if (nm.getNotificationChannel(CHANNEL_ID_INCIDENCIAS) == null) {
                NotificationChannel ch = new NotificationChannel(
                        CHANNEL_ID_INCIDENCIAS,
                        "Incidencias asignadas",
                        NotificationManager.IMPORTANCE_HIGH);
                ch.enableVibration(true);
                ch.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION), null);
                ch.setShowBadge(true);
                nm.createNotificationChannel(ch);
            }
        }
    }


    /*private int getNotificationIcon(NotificationCompat.Builder notificationBuilder) {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            int color = 0x008000;
            notificationBuilder.setColor(color);
            return R.drawable.ic_launcher_v0;

        }
        return R.drawable.ic_launcher;
    }*/



}
