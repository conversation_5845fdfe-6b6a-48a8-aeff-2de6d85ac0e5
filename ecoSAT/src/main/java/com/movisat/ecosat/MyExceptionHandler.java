package com.movisat.ecosat;

import java.sql.Date;

import android.util.Log;

import com.movisat.database.DBError;
import com.movisat.database.MyError;
import com.movisat.events.OnExitApplication;
import com.movisat.utilities.Utils;

import org.greenrobot.eventbus.EventBus;


public class MyExceptionHandler {

    private static MyExceptionHandler _instance = null;

    private MyExceptionHandler() {

    }

    public static MyExceptionHandler getInstance() {
        if (_instance == null)
            _instance = new MyExceptionHandler();
        return _instance;
    }

    public void error(Exception e) {
        synchronized (this) {

            try {
                String message = "";
                String stack = "";
                if (e.getMessage() != null)
                    message = e.getMessage();
                if (e.getStackTrace() != null)

                    stack = Log.getStackTraceString(e);

                DBError db = new DBError();

                MyError err = new MyError();
                err.setFecha(Utils.datetimeToString(
                        new Date(System.currentTimeMillis()),
                        "yyyy-MM-dd HH:mm:ss"));
                err.setMessage(message);
                err.setStacktrace(stack);
                db.insert(err);
                db.close();
            } catch (Exception ex) {

            }
        }

    }

    public void error(Throwable e) {
        synchronized (this) {

            try {
                String message = "";
                String stack = "";
                if (e.getMessage() != null)
                    message = e.getMessage();
                if (e.getStackTrace() != null)
                    stack = Log.getStackTraceString(e);

                if (!message.equals("") || !stack.equals("")) {

                    DBError db = new DBError();
                    if (db == null) {
                        EventBus.getDefault().post(new OnExitApplication("Reiniciando la aplicación."));
                        return;

                    }
                    MyError err = new MyError();
                    err.setFecha(Utils.datetimeToString(
                            new Date(System.currentTimeMillis()),
                            "yyyy-MM-dd HH:mm:ss"));
                    err.setMessage(message);
                    err.setStacktrace(stack);
                    db.insert(err);
                    db.close();
                }
            } catch (Exception ex) {
            }
        }
    }

}
