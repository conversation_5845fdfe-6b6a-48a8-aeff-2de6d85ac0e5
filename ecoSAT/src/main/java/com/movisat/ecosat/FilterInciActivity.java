package com.movisat.ecosat;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.KeyEvent;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.ListView;
import android.widget.Toast;

import com.movisat.adapter.InciEstadosAdapter;
import com.movisat.database.DBEstados;
import com.movisat.database.Estado;
import com.movisat.fragment.GestionElementos;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import java.util.ArrayList;

public class FilterInciActivity extends BaseActivity {
	private InciEstadosAdapter estadosAdapter;
	private ListView listaEstados;
	private Button todos;
	private Button ninguno;
	private Button confirmar;

	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.filter_inci_layout);
		try {
			listaEstados = findViewById(R.id.lvEstados);
			todos = findViewById(R.id.btTodos);
			ninguno = findViewById(R.id.btNinguno);
			confirmar = findViewById(R.id.btConfirmarFiltro);

			listaEstados.setOnItemClickListener(getOnItemClickListenerListaModelos());
			todos.setOnClickListener((v) -> selectAll(true));
			ninguno.setOnClickListener(v -> selectAll(false));
			confirmar.setOnClickListener((v) -> onConfirm());

			// Recupero todos los estados
			DBEstados dbEstado = new DBEstados();
			ArrayList<Estado> estados = dbEstado.getAll(MainActivity.getInstance().getEmpresa());
			dbEstado.close();

			if (estados == null || estados.isEmpty()) {
				Toast.makeText(this, getResources().getString(R.string.avisoNoHayIncidenciasModelos), Toast.LENGTH_LONG).show();
				finish();
			}

			estadosAdapter = new InciEstadosAdapter(MainActivity.getInstance(), estados);
			listaEstados.setAdapter(estadosAdapter);
		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}
	}

	private OnItemClickListener getOnItemClickListenerListaModelos() {
		return (list, view, index, id) -> {
			Estado item = (Estado) list.getItemAtPosition(index);
			MyBroadCastManager.getInstance().sendBroadCastToggleVisibleEstadoIncidencia(item.getIdExterno());
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
			refresh();
		};
	}

	private void selectAll(boolean isTodos) {
		for (int i = 0; i < listaEstados.getCount(); i++) {
			Estado item = (Estado) listaEstados.getItemAtPosition(i);
			MyBroadCastManager.getInstance().sendBroadCastSetVisibleEstadoIncidencia(item.getIdExterno(), isTodos);
		}
		if (isTodos) {
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_all, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
		} else {
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_all, 0);
		}
		refresh();
	}

	private void refresh() {
		MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
		listaEstados.invalidateViews();
		estadosAdapter.notifyDataSetChanged();
	}

	private void onConfirm() {
		new InfoDialog(this, getString(R.string.atencion),
				getString(R.string.confirmar_seleccion), InfoDialog.ICON_QUESTION,
				new OnInfoDialogSelect() {
					@Override
					public void onSelectOption(int option) {
						if (option == InfoDialog.BUTTON_YES) {
							int vis = 0;
							int inv = 0;
							StringBuilder estados = new StringBuilder();
							// Compruebo los modelos seleecionados
							for (int i = 0; i < listaEstados.getCount(); i++) {
								Estado item = (Estado) listaEstados.getItemAtPosition(i);
								// Compruebo la visibilidad del modelo
								if (GestionElementos.isVisibleEstadoIncidencia(item.getIdExterno())) {
									if (estados.length() == 0)
										estados.append(item.getIdExterno());
									else
										estados.append(",").append(item.getIdExterno());
									vis++;
								} else
									inv++;
							}
							// Guardo la configuraciin del usuario
							if (vis == listaEstados.getCount())
								Config.getInstance().setValueUsuario("inciVisibles", "todos");
							else if (inv == listaEstados.getCount())
								Config.getInstance().setValueUsuario("inciVisibles", "ninguno");
							else
								Config.getInstance().setValueUsuario("inciVisibles", estados.toString());
							MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
							finish();
						}
					}
				}, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
				InfoDialog.POSITION_CENTER).show();
	}

	@Override
	public boolean onKeyDown(int keyCode, KeyEvent event) {
		if (keyCode == KeyEvent.KEYCODE_BACK) onConfirm();
		return super.onKeyDown(keyCode, event);
	}

	/**
	 * Este metodo se ejecuta cada vez que se gira la pantalla
	 */
	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
	}

}
