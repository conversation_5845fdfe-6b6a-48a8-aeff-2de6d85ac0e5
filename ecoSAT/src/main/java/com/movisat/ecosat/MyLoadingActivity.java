package com.movisat.ecosat;

import com.movisat.events.OnNotifyIntranet;
import com.movisat.utilities.Config;
import com.movisat.utilities.Database;
import com.movisat.utilities.Phone;

import android.app.Activity;
import android.database.sqlite.SQLiteDatabase;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.KeyEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;

public class MyLoadingActivity extends BaseActivity {
    private static MyLoadingActivity instance = null;
    private final static String TAG = "MyLoadingActivity";
    public final static int MESSAGE_INTRANET_OK = 0;

    public static MyLoadingActivity getInstance() {

        //synchronized (LOADING_ACTIVITY) {
        //	if (instance == null) {
        //		instance = new MyLoadingActivity();
        //	}
        //}

        return instance;
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onResume() {
        super.onResume();
        EventBus.getDefault().register(this);
        Phone.getInstance().initialize(this);
        String imei = Phone.getInstance().getIMEI();
        Config.getInstance().setValue("imei", imei);

        MainActivity.sincroIntranet = new IntranetNew();
        MainActivity.sincroIntranet.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);

        if (MainActivity.getInstance() != null && MainActivity.getInstance().getIntranet()) {
            EventBus.getDefault().post(new OnNotifyIntranet(MyLoadingActivity.MESSAGE_INTRANET_OK));
        }
    }

    @Override
    protected void onStop() {
        EventBus.getDefault().unregister(this);
        super.onStop();
    }

    @Override
    public void finish() {
        super.finish();
        instance = null;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        instance = this;

        try {
            setContentView(R.layout.loading);
            getSupportActionBar().hide();
        } catch (Exception ex) {
            MyLoggerHandler.getInstance().error(ex);
        }
    }


	/*public static final Handler handler = new Handler() {

		@Override
		public void handleMessage(final Message message) {

			try {

				synchronized (instance) {

					if (!MainActivity.getInstance().getIntranet())
						return;

					switch (message.what) {
						case MESSAGE_INTRANET_OK:
                            if (MyLoadingActivity.getInstance() != null) {
								MyLoadingActivity.getInstance().setResult(RESULT_OK);
								MyLoadingActivity.getInstance().finish();
							}
						break;

					default:
						break;
					}

				}

			} catch (Throwable e) {
				MyLoggerHandler.getInstance().error(e);
				e.printStackTrace();
			}

		}

	};*/

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(OnNotifyIntranet event) {
        try {
            if (!MainActivity.getInstance().getIntranet())
                return;

            switch (event.getCodeMessage()) {
                case MESSAGE_INTRANET_OK:
                    if (MyLoadingActivity.getInstance() != null) {
                        MyLoadingActivity.getInstance().setResult(RESULT_OK);
                        MyLoadingActivity.getInstance().finish();
                    }
                    break;

                default:
                    break;
            }
        } catch (Throwable e) {
            if (MyLoadingActivity.getInstance() != null)
                MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }
}
