package com.movisat.ecosat;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.KeyEvent;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.ListView;
import android.widget.Toast;

import com.environment.Environment;
import com.movisat.adapter.ElemModelAdapter;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.ElementoModelo;
import com.movisat.fragment.GestionElementos;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import java.util.ArrayList;
import java.util.Collections;

public class FilterElemActivity extends BaseActivity {
	private ElemModelAdapter modelAdapter;
	private ListView listaModelos;
	private Button todos;
	private Button ninguno;
	private Button confirmar;
	private int todosNinguno = -1;

	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.filter_elem_layout);
		try {
			listaModelos = findViewById(R.id.lvModelos);
			todos = findViewById(R.id.btTodos);
			ninguno = findViewById(R.id.btNinguno);
			confirmar = findViewById(R.id.btConfirmarFiltro);

			listaModelos.setOnItemClickListener(getOnItemClickListenerListaModelos());
			todos.setOnClickListener(v -> selectAll(true));
			ninguno.setOnClickListener(v -> selectAll(false));
			confirmar.setOnClickListener(v -> onConfirm());

			// Recupero todos los modelos
			DBElementoModelo dbElementoModelo = new DBElementoModelo();
			ArrayList<ElementoModelo> modelos = dbElementoModelo.getAll(MainActivity.getInstance().getEmpresa());
			dbElementoModelo.close();

			if (modelos == null || modelos.isEmpty()) {
				Toast.makeText(this, getResources().getString(R.string.avisoNoHayModelos), Toast.LENGTH_LONG).show();
				finish();
			}
			if (Environment.isSoftIndra) Collections.sort(modelos);

			modelAdapter = new ElemModelAdapter(MainActivity.getInstance(), modelos, 0);
			listaModelos.setAdapter(modelAdapter);
			updateTodosNingunoButtons();
		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}
	}

	private OnItemClickListener getOnItemClickListenerListaModelos() {
		return (list, view, index, id) -> {
			ElementoModelo item = (ElementoModelo) list.getItemAtPosition(index);
			MyBroadCastManager.getInstance().sendBroadCastToggleVisibleModeloElemento(item.getIdExterno());
			updateTodosNingunoButtons();
		};
	}

	private void updateTodosNingunoButtons() {
		int count = listaModelos.getCount();
		int selectedCount = 0;

		for (int i = 0; i < count; i++) {
			ElementoModelo item = (ElementoModelo) listaModelos.getItemAtPosition(i);
			if (GestionElementos.isVisibleModeloElemento(item.getIdExterno())) {
				selectedCount++;
			}
		}

		if (selectedCount == 0) {
			todosNinguno = 0;
		} else if (selectedCount == count) {
			todosNinguno = 1;
		} else {
			todosNinguno = -1;
		}

		renderButtons(todosNinguno);
		refresh();
	}

	private void selectAll(boolean isTodos) {
		todosNinguno = isTodos ? 1 : 0;
		for (int i = 0; i < listaModelos.getCount(); i++) {
			ElementoModelo item = (ElementoModelo) listaModelos.getItemAtPosition(i);
			MyBroadCastManager.getInstance().sendBroadCastSetVisibleModeloElemento(item.getIdExterno(), isTodos);
		}
		renderButtons(todosNinguno);
		refresh();
	}

	private void renderButtons(int todosNinguno) {
		if (todosNinguno == 1) {
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_all, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
		} else if (todosNinguno == 0) {
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_all, 0);
		} else {
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
		}
	}

	private void refresh() {
		MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
		MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
		listaModelos.invalidateViews();
		modelAdapter.notifyDataSetChanged();
	}

	private void onConfirm() {
		new InfoDialog(this, getString(R.string.atencion), getString(R.string.confirmar_seleccion), InfoDialog.ICON_QUESTION,
			option -> {
				if (option == InfoDialog.BUTTON_YES) {
					// Guardo la configuraciin del usuario
					if (todosNinguno == 1)
						Config.getInstance().setValueUsuario("modelVisibles", "todos");
					else if (todosNinguno == 0)
						Config.getInstance().setValueUsuario("modelVisibles", "ninguno");
					else {
						StringBuilder modelos = new StringBuilder();
						// Compruebo los modelos seleecionados
						for (int i = 0; i < listaModelos.getCount(); i++) {
							ElementoModelo item = (ElementoModelo) listaModelos.getItemAtPosition(i);
							// Compruebo la visibilidad del modelo
							if (GestionElementos.isVisibleModeloElemento(item.getIdExterno())) {
								if (modelos.length() == 0)
									modelos.append(item.getIdExterno());
								else
									modelos.append(",").append(item.getIdExterno());
							}
						}
						Config.getInstance().setValueUsuario("modelVisibles", modelos.toString());
					}
					MyBroadCastManager.getInstance().sendBroadCastLoadModels();
					MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
					finish();
				}
			}, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER)
			.show();
	}

	@Override
	public boolean onKeyDown(int keyCode, KeyEvent event) {
		if (keyCode == KeyEvent.KEYCODE_BACK) onConfirm();
		return super.onKeyDown(keyCode, event);
	}

	/**
	 * Este metodo se ejecuta cada vez que se gira la pantalla
	 */
	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
	}

}
