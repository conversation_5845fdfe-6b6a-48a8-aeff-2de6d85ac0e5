package com.movisat.ecosat;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Intent;
import android.content.res.AssetFileDescriptor;
import android.content.res.Configuration;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.InputType;
import android.view.KeyEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SimpleCursorAdapter;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.environment.Environment;
import com.environment.VisibilityElementPlate;
import com.movisat.adapter.ElemModelAdapter;
import com.movisat.adapter.NivelCriticoAdapter;
import com.movisat.adapter.TipoZonasAdapter;
import com.movisat.database.DBElemento;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBMunicipios;
import com.movisat.database.DBProvincias;
import com.movisat.database.DBTags;
import com.movisat.database.DBTipoZonas;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModelo;
import com.movisat.database.Tags;
import com.movisat.database.TipoZonas;
import com.movisat.fragment.GestionElementos;
import com.movisat.log.Logg;
import com.movisat.managers.SoundManager;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.rfid_uhf_u9000.TagNotFoundToast;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.synchronize.DBSynchro;
import com.movisat.tags.Tag134;
import com.movisat.use_case.TagSendSensor;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.Photo;
import com.movisat.utilities.ReadingTagToast;
import com.movisat.utilities.Utils;
import com.movisat.utilities.ZoomPhotoDialog;
import com.movisat.utils.LFByteUtils;
import com.movisat.utils.Utilss;
import com.movisat.utils_android.PhotoService;
import com.movisat.utils_android.UtilssAndroid;
import com.movisat.repository.ElementosRepository;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.zip.GZIPInputStream;

public class AddElemActivityCamacho extends BaseActivity {
    private static final String TAG = "AddElemActivityCamacho";
    private static AddElementParam param = null;

    private static final int VERSION_CAMACHO = 1;
    private static AddElemActivityCamacho instance = null;
    private Spinner spinnerModelos;
    private static ArrayList<ElementoModelo> modelos;
    private static final int RESULT_GALLERY = 0;
    private boolean no_indica_nombre = false;
    private String municipio = "";
    private Elemento elemento;
    //--------------------------------------------
    private Tags tag = null;

    public static AddElemActivityCamacho getInstance() {
        return instance;
    }

    public static void openWithParam(AddElementParam param, Activity activity) {
        AddElemActivityCamacho.param = param;
        Logg.info(TAG, "[openWithParam] " + param);
        activity.startActivity(new Intent(activity, AddElemActivityCamacho.class));
    }

    // Campo NOMBRE
    TextView labelNombre = null;
    EditText textNombre = null;

    // Campo MATRÍCULA
    TextView labelMatricula = null;
    EditText textMatricula = null;

    // Campo DESCRIPCIÓN
    TextView labelDescripcion = null;
    EditText textDescripcion = null;

    // Campo NIVEL CRITICO ELEMENTO
    TextView labelEstadoCritico = null;
    Spinner spinnerEstadoCritico = null;

    // Campo DÍAS DE BLOQUEO
    TextView textDiasBloqueo = null;
    Button btLunes, btLunesTarde, btLunesManiana = null;
    Button btMartes, btMartesTarde, btMartesManiana = null;
    Button btMiercoles, btMiercolesTarde, btMiercolesManiana = null;
    Button btJueves, btJuevesTarde, btJuevesManiana = null;
    Button btViernes, btViernesTarde, btViernesManiana = null;
    Button btSabado, btSabadoTarde, btSabadoManiana = null;
    Button btDomingo, btDomingoTarde, btDomingoManiana = null;
    ImageView btExisteImagen = null;
    private boolean bLunes = false, bMartes = false, bMiercoles = false, bJueves = false, bViernes = false, bSabado = false, bDomingo = false;

    private boolean bLunesManiana = false, bLunesTarde = false, bMartesManiana = false,
            bMartesTarde = false, bMiercolesManiana = false, bMiercolesTarde = false,
            bJuevesManiana = false, bJuevesTarde = false, bViernesManiana = false,
            bViernesTarde = false, bSabadoManiana = false, bSabadoTarde = false,
            bDomingoManiana = false, bDomingoTarde = false;

    private LinearLayout llImagenes;
    private static Bitmap foto = null;
    // Historial imágenes (servidor)
    private TextView txtHistImgNumeroElem;
    private LinearLayout layoutScrollHistElem;
    private View histContainerElem;

    // Campo Elemento Similar
    TextView labelElementoSimilar = null;
    EditText textEditElementoSimilar = null;
    EditText textPrefijoSimilar = null;
    Spinner spinnerProvinciasSimilar = null;
    Spinner spinnerMunicipioSimilar = null;

    Spinner spinnerProvincias = null;
    Spinner spinnerMunicipio = null;

    EditText textPrefijo = null;

    // Campo Tipo de Zonas
    TextView labelTipoZona = null;
    Spinner spinnerTipoZona = null;

    CheckBox cbRotativo = null;
    CheckBox cbVaciaBajoDemanda = null;

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            if (param == null) {
                finish();
            }

            instance = this;

            setContentView(R.layout.add_elem_layout_camacho);

            spinnerModelos = (Spinner) findViewById(R.id.cbModelos);

            // Campo NOMBRE
            labelNombre = (TextView) findViewById(R.id.lblNombre);
            spinnerProvincias = (Spinner) findViewById(R.id.eProvincias);
            spinnerProvinciasSimilar = (Spinner) findViewById(R.id.eProvinciasSimilar);

            // Campo DÍAS DE BLOQUEO
            textDiasBloqueo = (TextView) findViewById(R.id.textDiasBloqueo);
            // Campo Tipo de Zonas
            labelTipoZona = (TextView) findViewById(R.id.textZonaElemento);
            spinnerTipoZona = (Spinner) findViewById(R.id.cbTipoZona);
            // Indica si el elemento es rotativo o no
            cbRotativo = (CheckBox) findViewById(R.id.cbRotativo);
            // Indica si el elemento se vacía bajo demanda
            cbVaciaBajoDemanda = (CheckBox) findViewById(R.id.cbBajoDemanda);

            spinnerMunicipioSimilar = (Spinner) findViewById(R.id.eMunicipioSimilar);

            textPrefijo = (EditText) findViewById(R.id.ePrefijoNombre);
            spinnerMunicipio = (Spinner) findViewById(R.id.eMunicipio);
            textNombre = (EditText) findViewById(R.id.ebNombre);

            // Campo MATRÍCULA
            labelMatricula = (TextView) findViewById(R.id.textAddMatricula);
            textMatricula = (EditText) findViewById(R.id.ebMatricula);

            // Campo DESCRIPCIÓN
            labelDescripcion = (TextView) findViewById(R.id.textAddDescripcion);
            textDescripcion = (EditText) findViewById(R.id.ebDescripcion);

            // Campo NIVEL CRITICO ELEMENTO
            labelEstadoCritico = (TextView) findViewById(R.id.textCritico);
            spinnerEstadoCritico = (Spinner) findViewById(R.id.cbCritico);

            llImagenes = (LinearLayout) findViewById(R.id.llImagenes);
            // Historial imágenes (servidor)
            txtHistImgNumeroElem = (TextView) findViewById(R.id.txt_hist_img_numero_elem);
            layoutScrollHistElem = (LinearLayout) findViewById(R.id.layout_scroll_hist_elem);
            histContainerElem = findViewById(R.id.relativelayout_hist_elem);
            if (txtHistImgNumeroElem != null) txtHistImgNumeroElem.setText("0");

            NivelCriticoAdapter dataAdapter = new NivelCriticoAdapter(AddElemActivityCamacho.this);
            spinnerEstadoCritico.setAdapter(dataAdapter);

            // Campo Elemento Similar
            labelElementoSimilar = (TextView) findViewById(R.id.textElementoSimilar);
            textEditElementoSimilar = (EditText) findViewById(R.id.textEditElementoSimilar);
            textPrefijoSimilar = (EditText) findViewById(R.id.ePrefijoNombreSimilar);

            btExisteImagen = (ImageView) findViewById(R.id.btExisteImagen);

            setMunicipio();
            cargarElementosRecycling();
            setFieldExtrasVisibility(true);

            // Recupero todos los modelos
            try {
                DBElementoModelo dbElementoModelo = new DBElementoModelo();
                modelos = dbElementoModelo.getAll(MainActivity.getInstance().getEmpresa());
                dbElementoModelo.close();
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().info("Error al cargar los modelos: " + e.getMessage());
            }

            if (modelos != null) {
                // Dejo solo los modelos visibles
                for (int i = 0; i < modelos.size(); i++) {
                    if (!GestionElementos.isVisibleModeloElemento(modelos.get(i).getIdExterno())) {
                        modelos.remove(i);
                        i--;
                    }
                }

                if (modelos.size() < 1) {
                    new InfoDialog(this, getString(R.string.atencion),
                          getString(R.string.noModelDisponible),
                          InfoDialog.ICON_STOP, option -> finish(),
                          InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER).show();
                }

                // Relleno el combo de modelos
                ElemModelAdapter modelAdapter = new ElemModelAdapter(MainActivity.getInstance(), modelos, VERSION_CAMACHO);
                spinnerModelos.setAdapter(modelAdapter);

                // Selecciono el modelo por defecto
                if (!param.update) {
                    // Se escoge el ultimo modelo creado si lo hubiese
                    // Recupero el ultimo modelo que se inserto en el mapa
                    int lastModelo = Integer.parseInt(Config.getInstance().getValueUsuario("tipoModelo", "-1"));

                    // Recorro los modelos para seleccionar en el combo el último
                    for (ElementoModelo modelo : modelos) {
                        if (modelo.getIdExterno() == lastModelo) {
                            spinnerModelos.setSelection(modelos.indexOf(modelo));
                            break;
                        }
                    }
                }
            }

            // Recupero todos los tipos
            DBTipoZonas dbTipoZonas = new DBTipoZonas();
            ArrayList<TipoZonas> tipos = dbTipoZonas.getAll();
            dbTipoZonas.close();

            // Relleno el combo de tipos
            TipoZonasAdapter tiposAdapter = new TipoZonasAdapter(MainActivity.getInstance(), tipos);
            spinnerTipoZona.setAdapter(tiposAdapter);

            if (param.update) {
                DBElemento db = new DBElemento();
                elemento = null;

                if (param.externalId < 1) {
                    elemento = db.getByIdInterno(param.internalId, MainActivity.getInstance().getEmpresa());

                    Toast.makeText(MainActivity.getInstance(),
                          "No puede modificar este elemento mientras no se haya sincronizado con el servidor.",
                          Toast.LENGTH_LONG).show();
                } else
                    elemento = db.getByIdExterno(param.externalId, MainActivity.getInstance().getEmpresa());

                db.close();
                cargaDiasBloqueo(elemento);
                setTitle("Edición " + elemento.getNombre());

                // Historial imágenes solo en modificación con código externo (>0)
                if (param.externalId > 0) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US);
                    sdf.setTimeZone(TimeZone.getTimeZone("Europe/Madrid"));
                    String hasta = sdf.format(new Date());

                    fetchHistoricoImagenesElemento("1970-01-01T00:00:00", hasta);
                    if (histContainerElem != null) histContainerElem.setVisibility(View.VISIBLE);
                } else {
                    if (histContainerElem != null) histContainerElem.setVisibility(View.GONE);
                }
            }

            tag = param.tags;
            if (tag != null) {
                textMatricula.setText(tag.getMatricula());
//                    15/02/23 - Según jpalao se debe poder modificar en cualquier caso
//                    textMatricula.setEnabled(false);
            }
            setFieldMatriculaVisibility(Environment.visibilityElementPlate != VisibilityElementPlate.NO);

            // Si 'insertImage' es falso, verifica las condiciones del menú y actualiza el valor.
            if (!param.insertImage) {
                param.insertImage = MainActivity.getInstance().getMenuModificarElementosImagen() ||
                      MainActivity.getInstance().getMenuCrearElementosNombreImagen() ||
                      MainActivity.getInstance().getMenuCrearElementosImagen();
            }

            // Si 'insertImage' sigue siendo falso, actualiza la visibilidad de 'llImagenes'.
            if (!param.insertImage) {
                llImagenes = (LinearLayout) findViewById(R.id.llImagenes);
                llImagenes.setVisibility(View.GONE);
            }
            llImagenes.setVisibility(param.insertImage ? View.VISIBLE : View.GONE);

            // Si hay que mostrar el campo "Nombre", se muestra un solo EditText
            if (param.showNameField) {
                labelNombre.setVisibility(View.VISIBLE);
                textNombre.setVisibility(View.VISIBLE);
                textNombre.setInputType(InputType.TYPE_CLASS_TEXT);
            }

            // Evento Boton abrir galeria
            findViewById(R.id.btGalleryElem).setOnClickListener(v -> openGallery());

            // Evento Boton add foto
            findViewById(R.id.btImagenElem).setOnClickListener(v -> {
                if (foto != null) {
                    Toast.makeText(MainActivity.getInstance(),
                          "No puede añadir más de 1 fotografía. Por favor, elimina la imagen actual para poder asociar otra.",
                          Toast.LENGTH_LONG).show();
                    return;
                }
                if (UtilssAndroid.isAndroidGreater11())
                    PhotoService.get().takePhoto(instance);
                else
                    Photo.getInstance().dispatchTakePictureIntent(instance);
            });

            // Evento Boton eliminar foto
            findViewById(R.id.btPapeleraElem).setOnClickListener(v -> {
                // Si hay fotos seleccionadas preguntamos si queremos eliminar
                if (hayFotosSeleccionadas()) {
                    new AlertDialog.Builder(instance)
                          .setMessage(getString(R.string.questionEliminarImagenesSeleccionadas))
                          .setPositiveButton(getString(R.string.yes), (dialog, which) -> eliminarFotos())
                          .setNegativeButton(getString(R.string.no), (dialog, which) -> dialog.dismiss())
                          .create()
                          .show();
                } else {
                    // Mostramos advertencia de que no hay imagenes seleccionadas
                    Toast.makeText(instance, R.string.noSelectedImages, Toast.LENGTH_SHORT).show();
                }
            });

            // Evento Botón Volver
            findViewById(R.id.btnVolver).setOnClickListener(v -> {
                foto = null;
                finish();
            });

            // Evento Botón Crear
            findViewById(R.id.btnAceptarAdd).setOnClickListener(v -> onClickAceptar());

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            new InfoDialog(this, getString(R.string.atencion),
                  getString(R.string.surgidoProblema) + e.getMessage(),
                  InfoDialog.ICON_STOP, option -> finish(),
                  InfoDialog.BUTTON_ACCEPT,
                  InfoDialog.POSITION_CENTER)
                  .show();
        }
    }

    private void fetchHistoricoImagenesElemento(String desde, String hasta) {
        try {
            int empresa = MainActivity.getInstance().getEmpresa();
            int codigo = param.externalId;
            new ElementosRepository().getImagenesPuntoUbicacion(
                    empresa,
                    codigo,
                    desde,
                    hasta,
                    bitmaps -> {
                        try {
                            Logg.warning("AddElemActivityCamacho", "Historico imagenes elemento: " + (bitmaps != null ? bitmaps.size() : 0));
                            renderHistoricoImagenesElemento(bitmaps);
                        } catch (Throwable t) {
                            MyLoggerHandler.getInstance().error(t);
                        }
                    }
            );
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    private void renderHistoricoImagenesElemento(java.util.List<Bitmap> bitmaps) {
        try {
            if (txtHistImgNumeroElem != null) {
                int count = (bitmaps != null) ? bitmaps.size() : 0;
                txtHistImgNumeroElem.setText(Integer.toString(count));
            }
            if (layoutScrollHistElem == null) return;
            layoutScrollHistElem.removeAllViews();
            if (bitmaps == null || bitmaps.size() == 0) return;

            for (Bitmap bmp : bitmaps) {
                try {
                    ImageView imageView = new ImageView(instance);
                    LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(200, 200);
                    params.setMargins(5, 5, 5, 5);
                    imageView.setLayoutParams(params);
                    imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                    imageView.setImageBitmap(bmp);
                    imageView.setOnClickListener(v -> {
                        try {
                            Drawable drawable = imageView.getDrawable();
                            Bitmap bitmap;
                            if (drawable instanceof BitmapDrawable) {
                                bitmap = ((BitmapDrawable) drawable).getBitmap();
                            } else {
                                imageView.setDrawingCacheEnabled(true);
                                bitmap = Bitmap.createBitmap(imageView.getDrawingCache());
                                imageView.setDrawingCacheEnabled(false);
                            }
                            ZoomPhotoDialog.showPhoto(instance, bitmap);
                        } catch (Throwable t) {
                            MyLoggerHandler.getInstance().error(t);
                        }
                    });
                    layoutScrollHistElem.addView(imageView);
                } catch (Throwable t) {
                    MyLoggerHandler.getInstance().error(t);
                }
            }

            // Optional: icon indicate server images
            try {
                if (btExisteImagen != null && bitmaps.size() > 0) {
                    btExisteImagen.setImageResource(R.drawable.ic_menu_report_image_si);
                }
            } catch (Throwable ignore) {}
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    private void onClickAceptar() {
        // Recuperamos datos a insertar en bbdd por si no hay modelos cargados, hago que no falle.
        int idModelo = 0;
        try {
            idModelo = ((ElementoModelo) spinnerModelos.getSelectedItem()).getIdExterno();
        } catch (Exception e) {

        }

        if (idModelo == 0) {
            new InfoDialog(
                  AddElemActivityCamacho.this,
                  getString(R.string.atencion),
                  getString(R.string.noModelDisponible),
                  InfoDialog.ICON_STOP, option -> finish(),
                  InfoDialog.BUTTON_ACCEPT,
                  InfoDialog.POSITION_CENTER).show();
            return;
        }

        String matricula = textMatricula.getText().toString();

        TextView textInfo = (TextView) spinnerMunicipio.getSelectedView();

        if (spinnerMunicipio.getVisibility() != View.GONE) {
            Config.getInstance().setValueUsuario(
                  "SelectedMunicipio",
                  String.valueOf(spinnerMunicipio.getSelectedItemPosition()));
        }

        String municipio = (textInfo != null) ? textInfo.getText().toString().trim() : "";

        if (municipio.isEmpty() && spinnerProvincias.getVisibility() != View.GONE) {
            municipio = ((TextView) spinnerProvincias.getSelectedView()).getText().toString();
        }

        String prefijoNombre = (textPrefijo.length() > 0) ? textPrefijo.getText().toString().trim() : municipio;

        String sufijo = textNombre.getText().toString().trim();
        if (sufijo.length() == 0)
            no_indica_nombre = true;

        String nombre = (prefijoNombre + " " + sufijo).trim();
        String descripcion = textDescripcion.getText().toString();

        final double latitud = param.latitude;
        final double longitud = param.longitude;

        // Si nos viene update hay que actualizar el elemento
        if (param.update) {
            DBElemento db = new DBElemento();
            Elemento elemento = null;
            if (param.externalId > 0) {
                elemento = db.getByIdExterno(param.externalId, MainActivity
                      .getInstance().getEmpresa());
            } else {
                elemento = db.getByIdInterno(param.internalId, MainActivity
                      .getInstance().getEmpresa());
            }

            db.close();

            elemento.setModelo(idModelo);
            elemento.setMatricula(matricula);

            // Si el nombre no está vacío y es diferente, se actualiza
            if (!nombre.equals("") && !elemento.getNombre().equals(nombre))
                elemento.setNombre(nombre);

            elemento.setDescripcion(descripcion);

            if (foto != null && param.insertImage)
                elemento.setTieneImagen(true);

            // si se ha conseguido actualizar el elemento, seguimos.
            if (updateElemento(elemento)) {
                MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

                // Guardo la informaciin para enviar en la
                // bandeja de salida
                DBPacket dbp = new DBPacket();
                dbp.insert(new Packet(
                      Packet.ELEMENTO_MODIFICAR,
                      Packet.PRIORIDAD_NORMAL, elemento));
                dbp.close();

                sincro();

                // si hay imagen a enviar
                if (foto != null && param.insertImage) {
                    // Guardo la información para enviar en la bandeja de salida
                    ByteArrayOutputStream stream = new ByteArrayOutputStream();
                    foto.compress(
                          Bitmap.CompressFormat.JPEG, Utils.getCalidadImagenSegunCobertura(getApplicationContext()),
                          stream);
                    byte[] byteArray = stream.toByteArray();
                    elemento.setFoto(byteArray);
                    foto = null;

                    //dbp = new DBPacket();
                    //dbp.insert(new Packet(Packet.ELEMENTO_CREAR_IMAGEN, Packet.PRIORIDAD_NORMAL, elemento));
                    //dbp.close();
                }

                MyLoggerHandler.getInstance().info(String.format("%s elemento modificado.", elemento.toString()));
                finish();
            }
        } else {
            // El id del elemento es temporal porque será el servidor quien finalmente lo asignará
            DBElementoModelo dbMod = new DBElementoModelo();
            ElementoModelo mod = dbMod.getByID(idModelo, MainActivity.getInstance().getEmpresa());
            dbMod.close();

            if (nombre.equals("")) {
                nombre = mod.getNombre() + " (nuevo)";
            }

            Elemento elementoTMP = null;
            DBElemento db = new DBElemento();

            // Si no se permiten nombres repetidos... (si está repetido se
            // preguntará si se desea cambiar la ubicación).
            // Solo en las versiones de Camacho (Ecovidrio con contenedores
            // identificados) debe verificarse el nombre.
            if (MainActivity.isEcovidrioContIdentificados()) {
                // Antes de crear compruebo si existe un elemento con ese nombre
                elementoTMP = db.getElementoBy(nombre, MainActivity.getInstance().getEmpresa());
            }

            // Busco el TAG asociado a la matrícula
            DBTags dbTags = new DBTags();
            Tags tagTmp = dbTags.getByMatricula(matricula, MainActivity.getInstance().getEmpresa());
            dbTags.close();

            Elemento elementoTMP2 = null;
            if (tagTmp != null) {
                // Busco un elemento con la misma matrícula
                elementoTMP2 = db.getByIdExterno(tagTmp.getIdExternoElemento(), MainActivity.getInstance().getEmpresa());
            }

            db.close();

            if ((elementoTMP != null || elementoTMP2 != null) && !no_indica_nombre) {
                // Tenemos que avisar si quiere modificar las coordenadas o si quiere cambiar el nombre

                final Elemento elemento = elementoTMP != null ? elementoTMP : elementoTMP2;

                String tmpNombre = elemento.getNombre();
                int idModel = elemento.getModelo();

                DBElementoModelo dbModel = new DBElementoModelo();
                ElementoModelo eleModel = dbModel.getByID(idModel, MainActivity.getInstance().getEmpresa());

                tmpNombre = "\r\n" + eleModel.getNombre() + "\r\n" + tmpNombre + "\r\n";

                            /*String ubicacion = GestionElementos.getInfoGIS(
                                    elemento.getPosition().latitude,
                                    elemento.getPosition().longitude);
                            ubicacion = ubicacion.replace("(0-0)", "");
                            ubicacion = ubicacion.replace("(null)", "");*/
                if (municipio != null && !municipio.isEmpty())
                    municipio = "En " + municipio;
                municipio = tmpNombre + municipio + "\r\n";

                // Si no hay matrícula, se busca en el tag asociado al elemento
                if (matricula.equals("")) {
                    Tags tagElemento;
                    dbTags = new DBTags();

                    if (elemento.getIdExterno() > 0)
                        tagElemento = dbTags.getByElemento(elemento.getIdExterno(), MainActivity.getInstance().getEmpresa());
                    else
                        tagElemento = dbTags.getByIdInternoElemento(elemento.getId(), MainActivity.getInstance().getEmpresa());

                    dbTags.close();

                    if (tagElemento != null)
                        matricula = tagElemento.getMatricula();
                }

                final int idModeloElemento = idModelo;
                final String nombreElemento = nombre;
                final String descElemento = descripcion;
                final String matriculaElemento = matricula;

                new InfoDialog(AddElemActivityCamacho.getInstance(), getString(R.string.atencion),
                      getString(R.string.questionCambiarUbicacionElemento, municipio),
                      InfoDialog.ICON_STOP, option -> {
                    if (option == InfoDialog.BUTTON_YES) {

                        // Modifico los datos en base de datos
                        elemento.setModelo(idModeloElemento);
                        elemento.setMatricula(matriculaElemento);
                        elemento.setDescripcion(descElemento);
                        elemento.setPosition(latitud, longitud);

                        // Si el nombre no está vacío y es diferente, se actualiza
                        if (!elemento.getNombre().equals(nombreElemento))
                            elemento.setNombre(nombreElemento);

                        if (foto != null && param.insertImage)
                            elemento.setTieneImagen(true);

                        DBElemento dbElemento = new DBElemento();
                        dbElemento.update(elemento);
                        dbElemento.close();

                        // Guardo la información para enviar en la bandeja de salida
                        DBPacket dbp = new DBPacket();
                        dbp.insert(new Packet(Packet.ELEMENTO_MODIFICAR, Packet.PRIORIDAD_NORMAL, elemento));
                        dbp.close();
                        MyLoggerHandler.getInstance().info(String.format("%s elemento modificado", elemento.toString()));

                        sincro();

                        // si hay imagen a enviar
                        if (foto != null && param.insertImage) {
                            // Guardo la información para enviar en la bandeja de salida
                            ByteArrayOutputStream stream = new ByteArrayOutputStream();
                            foto.compress(
                                  Bitmap.CompressFormat.JPEG, Utils.getCalidadImagenSegunCobertura(getApplicationContext()),
                                  stream);
                            byte[] byteArray = stream.toByteArray();
                            elemento.setFoto(byteArray);
                            foto = null;
                            dbp = new DBPacket();
                            dbp.insert(new Packet(Packet.ELEMENTO_CREAR_IMAGEN, Packet.PRIORIDAD_NORMAL, elemento));
                            dbp.close();

                            sincro();
                        }

                        // actualizo el cluster.
                        MyBroadCastManager.getInstance().sendBroadCastUpdateItemCluster(elemento);

                        Toast.makeText(MainActivity.getInstance(),
                              R.string.sendingChangeCoordenadas,
                              Toast.LENGTH_LONG).show();

                        finish();
                    }

                }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();

                return;
            }

            // Creo el elemento con código 0 de momento para que se genere un
            // código temporal mientras no se obtenga el código definitivo
            // asignado por el servidor
            if (!param.showNameField && sufijo.equals("")) nombre = "";

            Elemento elemento;

            Elemento elemento_similar;

            int elemento_simil = 0;
            String estado_critico_seleccionado = "";
            int tipo_zona_selec;

            // Si se ha introducido un elemento similar, comprobamos que éste existe
            if (textEditElementoSimilar.getText().toString().length() > 0) {
                DBElemento dbElemento = new DBElemento();
                if (textPrefijoSimilar.getText().toString().length() == 0)
                    textPrefijoSimilar.setText(((TextView) spinnerMunicipioSimilar.getSelectedView()).getText().toString());

                String elem_similar_text = textPrefijoSimilar.getText().toString() + " " + textEditElementoSimilar.getText().toString();
                elemento_similar = dbElemento.getElementoBy(elem_similar_text,
                      MainActivity.getInstance().getEmpresa());
                dbElemento.close();

                if (elemento_similar == null) {
                    Toast.makeText(
                          instance,
                          R.string.noEncontradoElemento,
                          Toast.LENGTH_SHORT).show();
                    return;
                } else
                    elemento_simil = elemento_similar.getIdExterno();
            }

            try {
                estado_critico_seleccionado = spinnerEstadoCritico.getSelectedItem().toString();
            } catch (Throwable e) {
                estado_critico_seleccionado = "";
            }

            try {
                tipo_zona_selec = ((TipoZonas) spinnerTipoZona.getSelectedItem()).getId();
            } catch (Throwable e) {
                tipo_zona_selec = 0;
            }

            int es_rotativo = cbRotativo.isChecked() ? 1 : 0;
            int vacia_bajo_demanda = cbVaciaBajoDemanda.isChecked() ? 1 : 0;

            if (no_indica_nombre) {
                nombre = "";
            }
            elemento = new Elemento(
                  0,
                  0,
                  MainActivity.getInstance().getEmpresa(), nombre, idModelo, matricula,
                  Elemento.ESTADO_ACTIVO, latitud, longitud, descripcion, estado_critico_seleccionado,
                  elemento_simil, dameValorDiaBloqueo(1), dameValorDiaBloqueo(2), dameValorDiaBloqueo(3),
                  dameValorDiaBloqueo(4), dameValorDiaBloqueo(5), dameValorDiaBloqueo(6), dameValorDiaBloqueo(7),
                  tipo_zona_selec, es_rotativo, vacia_bajo_demanda, 0, new Date(), "", 0, "");

            // Inserto en la tabla de configuracion el último modelo insertado
            // para que si volvemos a la actividad se mantenga el mismo
            Config.getInstance().setValueUsuario("tipoModelo", "" + idModelo);

            if (foto != null && param.insertImage)
                elemento.setTieneImagen(true);

            // Llamo al mapa para que añada el elemento en bd y en el cluster
            int idInterno = addElemento(elemento);
            elemento.setId(idInterno);

            MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

            // Si se ha especificado que se muestre el campo "Nombre" se
            // añadirá un campo extra en el json de la llamada al servidor
            int packet_type = Packet.ELEMENTO_CREAR;
            if (param.showNameField && !no_indica_nombre)
                packet_type = Packet.ELEMENTO_CREAR_CON_NOMBRE;

            // Guardo la información para enviar en la bandeja de salida
            DBPacket dbp = new DBPacket();
            dbp.insert(new Packet(packet_type, Packet.PRIORIDAD_NORMAL, elemento));
            dbp.close();

            sincro();

            // si hay imagen a enviar
            if (foto != null && param.insertImage) {
                // Guardo la información para enviar en la bandeja de salida
                ByteArrayOutputStream stream = new ByteArrayOutputStream();
                foto.compress(
                      Bitmap.CompressFormat.JPEG, Utils.getCalidadImagenSegunCobertura(getApplicationContext()),
                      stream);
                byte[] byteArray = stream.toByteArray();
                elemento.setFoto(byteArray);
                foto = null;

                dbp = new DBPacket();
                dbp.insert(new Packet(Packet.ELEMENTO_CREAR_IMAGEN, Packet.PRIORIDAD_NORMAL, elemento));
                dbp.close();

                sincro();
            }

            // Se actualiza el tag del elemento
            if (tagTmp != null) {
                tagTmp.setIdExternoElemento(0);
                tagTmp.setIdInternoElemento(elemento.getId());
                DBTags tagsManager = new DBTags();
                tagsManager.update(tagTmp);
            }

            MyLoggerHandler.getInstance().info(elemento.getId() + " elemento creado.");
            foto = null;
            finish();
        }
    }

    private void sincro() {
        if (MainActivity.getInstance().isNetworkAvailable() && MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
            DBSynchro.getInstance().forceSync();
        }
    }

    private void setMunicipio() {
        // Recupero la última información del usuario en cuanto a posición del mapa y zoom
        double lat = Double.parseDouble(Config.getInstance().getValueUsuario("miLat", "0"));
        double lon = Double.parseDouble(Config.getInstance().getValueUsuario("miLon", "0"));

        if (lat == 0 && lon == 0) {
            return;
        }
        new AsyncGIS(lat, lon).execute();
    }

    private void cargarElementosRecycling() {
        // Lunes
        btLunes = (Button) findViewById(R.id.ctvLunes);
        btLunes.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bLunes = v.isActivated();
            changeButtonStateStyle(btLunes, bLunes);
            changeButtonStateStyle(btLunesManiana, bLunes);
            changeButtonStateStyle(btLunesTarde, bLunes);

            btLunesManiana.setActivated(bLunes);
            btLunesTarde.setActivated(bLunes);
            bLunesManiana = bLunes;
            bLunesTarde = bLunes;
        });

        btLunesManiana = (Button) findViewById(R.id.ctvLunesManiana);
        btLunesManiana.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bLunesManiana = v.isActivated();
            changeButtonStateStyle(btLunesManiana, bLunesManiana);
        });

        btLunesTarde = (Button) findViewById(R.id.ctvLunesTarde);
        btLunesTarde.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bLunesTarde = v.isActivated();
            changeButtonStateStyle(btLunesTarde, bLunesTarde);
        });

        // Martes
        btMartes = (Button) findViewById(R.id.ctvMartes);
        btMartes.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bMartes = v.isActivated();
            changeButtonStateStyle(btMartes, bMartes);
            changeButtonStateStyle(btMartesManiana, bMartes);
            changeButtonStateStyle(btMartesTarde, bMartes);

            btMartesManiana.setActivated(bMartes);
            btMartesTarde.setActivated(bMartes);
            bMartesManiana = bMartes;
            bMartesTarde = bMartes;
        });

        btMartesManiana = (Button) findViewById(R.id.ctvMartesManiana);
        btMartesManiana.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bMartesManiana = v.isActivated();
            changeButtonStateStyle(btMartesManiana, bMartesManiana);
        });

        btMartesTarde = (Button) findViewById(R.id.ctvMartesTarde);
        btMartesTarde.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bMartesTarde = v.isActivated();
            changeButtonStateStyle(btMartesTarde, bMartesTarde);
        });

        // Miercoles
        btMiercoles = (Button) findViewById(R.id.ctvMiercoles);
        btMiercoles.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bMiercoles = v.isActivated();
            changeButtonStateStyle(btMiercoles, bMiercoles);
            changeButtonStateStyle(btMiercolesManiana, bMiercoles);
            changeButtonStateStyle(btMiercolesTarde, bMiercoles);

            btMiercolesManiana.setActivated(bMiercoles);
            btMiercolesTarde.setActivated(bMiercoles);
            bMiercolesManiana = bMiercoles;
            bMiercolesTarde = bMiercoles;
        });

        btMiercolesManiana = (Button) findViewById(R.id.ctvMiercolesManiana);
        btMiercolesManiana.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bMiercolesManiana = v.isActivated();
            changeButtonStateStyle(btMiercolesManiana, bMiercolesManiana);
        });

        btMiercolesTarde = (Button) findViewById(R.id.ctvMiercolesTarde);
        btMiercolesTarde.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bMiercolesTarde = v.isActivated();
            changeButtonStateStyle(btMiercolesTarde, bMiercolesTarde);
        });

        // Jueves
        btJueves = (Button) findViewById(R.id.ctvJueves);
        btJueves.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bJueves = v.isActivated();
            changeButtonStateStyle(btJueves, bJueves);
            changeButtonStateStyle(btJuevesManiana, bJueves);
            changeButtonStateStyle(btJuevesTarde, bJueves);

            btJuevesManiana.setActivated(bJueves);
            btJuevesTarde.setActivated(bJueves);
            bJuevesManiana = bJueves;
            bJuevesTarde = bJueves;
        });

        btJuevesManiana = (Button) findViewById(R.id.ctvJuevesManiana);
        btJuevesManiana.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bJuevesManiana = v.isActivated();
            changeButtonStateStyle(btJuevesManiana, bJuevesManiana);
        });

        btJuevesTarde = (Button) findViewById(R.id.ctvJuevesTarde);
        btJuevesTarde.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bJuevesTarde = v.isActivated();
            changeButtonStateStyle(btJuevesTarde, bJuevesTarde);
        });

        // Viernes
        btViernes = (Button) findViewById(R.id.ctvViernes);
        btViernes.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bViernes = v.isActivated();
            changeButtonStateStyle(btViernes, bViernes);
            changeButtonStateStyle(btViernesManiana, bViernes);
            changeButtonStateStyle(btViernesTarde, bViernes);

            btViernesManiana.setActivated(bViernes);
            btViernesTarde.setActivated(bViernes);
            bViernesManiana = bViernes;
            bViernesTarde = bViernes;
        });

        btViernesManiana = (Button) findViewById(R.id.ctvViernesManiana);
        btViernesManiana.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bViernesManiana = v.isActivated();
            changeButtonStateStyle(btViernesManiana, bViernesManiana);
        });

        btViernesTarde = (Button) findViewById(R.id.ctvViernesTarde);
        btViernesTarde.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bViernesTarde = v.isActivated();
            changeButtonStateStyle(btViernesTarde, bViernesTarde);
        });

        //Sabado
        btSabado = (Button) findViewById(R.id.ctvSabado);
        btSabado.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bSabado = v.isActivated();
            changeButtonStateStyle(btSabado, bSabado);
            changeButtonStateStyle(btSabadoManiana, bSabado);
            changeButtonStateStyle(btSabadoTarde, bSabado);

            btSabadoManiana.setActivated(bSabado);
            btSabadoTarde.setActivated(bSabado);
            bSabadoManiana = bSabado;
            bSabadoTarde = bSabado;
        });

        btSabadoManiana = (Button) findViewById(R.id.ctvSabadoManiana);
        btSabadoManiana.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bSabadoManiana = v.isActivated();
            changeButtonStateStyle(btSabadoManiana, bSabadoManiana);
        });

        btSabadoTarde = (Button) findViewById(R.id.ctvSabadoTarde);
        btSabadoTarde.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bSabadoTarde = v.isActivated();
            changeButtonStateStyle(btSabadoTarde, bSabadoTarde);
        });

        // Domingo
        btDomingo = (Button) findViewById(R.id.ctvDomingo);
        btDomingo.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bDomingo = v.isActivated();
            changeButtonStateStyle(btDomingo, bDomingo);
            changeButtonStateStyle(btDomingoManiana, bDomingo);
            changeButtonStateStyle(btDomingoTarde, bDomingo);

            btDomingoManiana.setActivated(bDomingo);
            btDomingoTarde.setActivated(bDomingo);
            bDomingoManiana = bDomingo;
            bDomingoTarde = bDomingo;
        });

        btDomingoManiana = (Button) findViewById(R.id.ctvDomingoManiana);
        btDomingoManiana.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bDomingoManiana = v.isActivated();
            changeButtonStateStyle(btDomingoManiana, bDomingoManiana);
        });

        btDomingoTarde = (Button) findViewById(R.id.ctvDomingoTarde);
        btDomingoTarde.setOnClickListener(v -> {
            v.setActivated(!v.isActivated());
            bDomingoTarde = v.isActivated();
            changeButtonStateStyle(btDomingoTarde, bDomingoTarde);
        });
    }

    /**
     * Cambia el estilo del botón según su estado de activación.
     *
     * @param button    El botón cuyo estilo se actualizará.
     * @param activated El estado de activación del botón. Si es {@code true}, el botón se considera activado; si es {@code false}, desactivado.
     */
    private void changeButtonStateStyle(Button button, boolean activated) {
        if (activated) {
            button.setBackgroundResource(R.drawable.button_style_corner_p);
            button.setTextColor(Color.WHITE);
        } else {
            button.setBackgroundResource(R.drawable.button_style_corner);
            button.setTextColor(Color.parseColor("#545353"));
        }
    }

    private void setProvincias(final Spinner textProvincias, boolean similar) {
        DBProvincias managerProvincias = new DBProvincias();
        Cursor cursor = managerProvincias.getAllCursor();
        String[] from = new String[]{"provincia"};
        int[] to = new int[]{android.R.id.text1};

        SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(this,
              android.R.layout.simple_spinner_item, cursor, from, to, 0);

        // set layout for activated adapter
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        textProvincias.setAdapter(dataAdapter);
        OnItemSelectedListener selectedProvincia;

        if (similar)
            selectedProvincia = new SelectedProvinciaSimilar();
        else
            selectedProvincia = new SelectedProvincia();

        textProvincias.setOnItemSelectedListener(selectedProvincia);
        managerProvincias.close();

        int indexPos = Integer.valueOf(Config.getInstance().getValueUsuario("SelectedProvincia", "-1"));
        if (indexPos > -1)
            textProvincias.setSelection(indexPos);
    }

    /**
     * Añade el elemento en BD
     */
    public int addElemento(Elemento elemento) {
        int id = elemento.getId();
        try {
            if (id == 0) {
                //Quito matricula del contenedor que la tenga
                removeMatricula(elemento.getMatricula(), elemento.getEmpresa());

                // Inserto el elemento en la BD
                DBElemento dbElemento = new DBElemento();
                id = (int) dbElemento.insert(elemento);
                dbElemento.close();

                // Se establece el id interno del elemento creado en su tag
                if (tag != null) {
                    tag.setIdInternoElemento(elemento.getId());
                    DBTags dbTags = new DBTags();
                    dbTags.update(tag);
                    dbTags.close();
                }
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return id;
    }

    /**
     * Actualiza el elemento en BD
     */
    private boolean updateElemento(Elemento elemento) {
        boolean res = true;

        try {
            //Quito matricula del contenedor que la tenga
            removeMatricula(elemento.getMatricula(), elemento.getEmpresa());

            // asigno a cada uno de los 7 días el estado de bloqueo (0, 1, 2 o 3)
            for (int i = 1; i < 8; i++) {
                elemento.setDiaBloqueo(i, dameValorDiaBloqueo(i));
            }

            int tipo_zona;
            try {
                tipo_zona = ((TipoZonas) spinnerTipoZona.getSelectedItem()).getId();
            } catch (Throwable e) {
                tipo_zona = 0;
            }
            elemento.setTipoZona(tipo_zona);

            try {
                elemento.setEstadoCritico(spinnerEstadoCritico.getSelectedItem().toString());
            } catch (Throwable e) {
                elemento.setEstadoCritico("");
            }

            try {
                int esRotativo = cbRotativo.isChecked() ? 1 : 0;
                elemento.setRotativo(esRotativo);
            } catch (Throwable e) {
                Logg.error(TAG, e.getMessage());
            }

            try {
                int vacia_bajo_demanda = cbVaciaBajoDemanda.isChecked() ? 1 : 0;
                elemento.setVaciaBajoDemanda(vacia_bajo_demanda);
            } catch (Throwable e) {
                Logg.error(TAG, e.getMessage());
            }

            DBElemento dbElemento = new DBElemento();
            try {
                if (textEditElementoSimilar.getText().toString().length() > 0) {
                    if (textPrefijoSimilar.getText().toString().length() == 0)
                        textPrefijoSimilar.setText(((TextView) spinnerMunicipioSimilar.getSelectedView()).getText().toString());

                    String elem_similar_text = textPrefijoSimilar.getText().toString() + " " + textEditElementoSimilar.getText().toString();
                    Elemento elemento_similar = dbElemento.getElementoBy(elem_similar_text, MainActivity.getInstance().getEmpresa());

                    if (elemento_similar == null || elemento_similar.getId() == 0) {
                        Toast.makeText(
                                instance,
                                R.string.noEncontradoElemento,
                                Toast.LENGTH_SHORT).show();
                        dbElemento.close();
                        return false;
                    } else if (elemento_similar.getIdExterno() > 0)
                        elemento.setIdElementoSimilar(elemento_similar.getIdExterno());
                } else
                    elemento.setIdElementoSimilar(0);
            } catch (Throwable e) {
                elemento.setIdElementoSimilar(0);
            }

            // Actualizo la BD
            dbElemento.update(elemento);
            dbElemento.close();

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    private int dameValorDiaBloqueo(int dia) {
        switch (dia) {
            case 1:
                return calcularValor(bLunesManiana, bLunesTarde);
            case 2:
                return calcularValor(bMartesManiana, bMartesTarde);
            case 3:
                return calcularValor(bMiercolesManiana, bMiercolesTarde);
            case 4:
                return calcularValor(bJuevesManiana, bJuevesTarde);
            case 5:
                return calcularValor(bViernesManiana, bViernesTarde);
            case 6:
                return calcularValor(bSabadoManiana, bSabadoTarde);
            case 7:
                return calcularValor(bDomingoManiana, bDomingoTarde);
            default:
                return 0;
        }
    }

    private int calcularValor(boolean maniana, boolean tarde) {
        if (maniana && tarde)
            return 3;
        else if (maniana)
            return 1;
        else if (tarde)
            return 2;
        else
            return 0;
    }

    private void removeMatricula(String matricula, int empresa) {
        if (matricula.equals("")) return;
        try {
            // Actualizo la BD
            DBElemento dbElemento = new DBElemento();
            Elemento elementoToRemoveMatricula = dbElemento.getElementoByMatricula(matricula, empresa);
            if (elementoToRemoveMatricula != null) {
                elementoToRemoveMatricula.setMatricula("");
                dbElemento.update(elementoToRemoveMatricula);
            }
            dbElemento.close();
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * Este mitodo se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    /*@Override
    public void onStart() {
        super.onStart();

        // ATTENTION: This was auto-generated to implement the App Indexing API.
        // See https://g.co/AppIndexing/AndroidStudio for more information.
        client.connect();
        Action viewAction = Action.newAction(
                Action.TYPE_VIEW, // TODO: choose an action type.
                "AddElem Page", // TODO: Define a title for the content shown.
                // TODO: If you have web page content that matches this app activity's content,
                // make sure this auto-generated web page URL is correct.
                // Otherwise, set the URL to null.
                Uri.parse("http://host/path"),
                // TODO: Make sure this auto-generated app deep link URI is correct.
                Uri.parse("android-app://com.movisat.ecosat/http/host/path")
        );
        AppIndex.AppIndexApi.start(client, viewAction);
    }@Override
    public void onStop() {
        super.onStop();

        // ATTENTION: This was auto-generated to implement the App Indexing API.
        // See https://g.co/AppIndexing/AndroidStudio for more information.
        Action viewAction = Action.newAction(
                Action.TYPE_VIEW, // TODO: choose an action type.
                "AddElem Page", // TODO: Define a title for the content shown.
                // TODO: If you have web page content that matches this app activity's content,
                // make sure this auto-generated web page URL is correct.
                // Otherwise, set the URL to null.
                Uri.parse("http://host/path"),
                // TODO: Make sure this auto-generated app deep link URI is correct.
                Uri.parse("android-app://com.movisat.ecosat/http/host/path")
        );
        AppIndex.AppIndexApi.end(client, viewAction);
        client.disconnect();
    }*/

//    public class SelectedTipoZona implements OnItemSelectedListener {
//
//        @Override
//        public void onItemSelected(AdapterView<?> parent, View view, int pos,
//                                   long id) {
//            Cursor c = ((SimpleCursorAdapter) parent.getAdapter()).getCursor();
//            c.moveToPosition(pos);
//
//            DBTipoZonas manager = new DBTipoZonas();
//            int id_tipo = c.getInt(0);
//            Cursor cursor = manager.getAllCursorBy(id_tipo);
//            String[] from = new String[]{"nombre"};
//            int[] to = new int[]{android.R.id.text1};
//
//            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(instance,
//                    android.R.layout.simple_spinner_item, cursor, from, to, 0);
//            dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
//
//            spinnerTipoZona.setAdapter(dataAdapter);
//            manager.close();
//
//            int indexOf = Integer.parseInt(Config.getInstance().getValueUsuario("SelectedMunicipio", "-1"));
//
//            if (indexOf > -1) {
//                try {
//                    spinnerTipoZona.setSelection(indexOf);
//                } catch (Exception ex) {
//
//                }
//            }
//
//            Config.getInstance().setValueUsuario("SelectedTipoZona", String.valueOf(pos));
//        }
//
//        @Override
//        public void onNothingSelected(AdapterView<?> arg0) {
//            // TODO Auto-generated method stub
//        }
//
//    }

    private void insertFotoGallery(Bitmap foto) {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll_imagenes);

        ImageView imageView = new ImageView(instance);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(200, 200);
        params.setMargins(5, 5, 5, 5);
        imageView.setLayoutParams(params);
        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        imageView.setImageBitmap(foto);
        imageView.setTag(0);

        layout.addView(imageView);

        /*TextView texto = (TextView) findViewById(R.id.textoImagenesNumero);
        texto.setText("(" + layout.getChildCount() + ")");*/

        imageView.setOnClickListener(v -> {
            ImageView view = (ImageView) v;

            // Siempre mostrar vista flotante con zoom
            Drawable drawable = view.getDrawable();
            Bitmap bitmap;
            if (drawable instanceof BitmapDrawable) {
                bitmap = ((BitmapDrawable) drawable).getBitmap();
            } else {
                // Fallback por seguridad
                view.setDrawingCacheEnabled(true);
                bitmap = Bitmap.createBitmap(view.getDrawingCache());
                view.setDrawingCacheEnabled(false);
            }
            
            // Mostrar dialog con zoom
            ZoomPhotoDialog.showPhoto(instance, bitmap);
        });
        
        // Añadir long click listener para selección
        imageView.setOnLongClickListener(v -> {
            ImageView view = (ImageView) v;
            
            if (view.getTag() == (Integer) 1) {
                // Deseleccionar
                view.clearColorFilter();
                view.setTag(0);
            } else {
                // Seleccionar
                view.setColorFilter(0xAA26FFFF);
                view.setTag(1);
            }
            
            v.performHapticFeedback(android.view.HapticFeedbackConstants.LONG_PRESS);
            return true;
        });
    }

    private void cargaDiasBloqueo(Elemento elemento) {
        int bloqueo_lunes = elemento.getDiaBloqueo(1);
        int bloqueo_martes = elemento.getDiaBloqueo(2);
        int bloqueo_miercoles = elemento.getDiaBloqueo(3);
        int bloqueo_jueves = elemento.getDiaBloqueo(4);
        int bloqueo_viernes = elemento.getDiaBloqueo(5);
        int bloqueo_sabado = elemento.getDiaBloqueo(6);
        int bloqueo_domingo = elemento.getDiaBloqueo(7);

        if (bloqueo_lunes > 0) {
            if (bloqueo_lunes == 1) {
                activarBotonBloqueo(btLunesManiana);
                bLunesManiana = true;
            } else if (bloqueo_lunes == 2) {
                activarBotonBloqueo(btLunesTarde);
                bLunesTarde = true;
            } else if (bloqueo_lunes == 3) {
                activarBotonBloqueo(btLunes);
                bLunes = true;

                activarBotonBloqueo(btLunesManiana);
                bLunesManiana = true;

                activarBotonBloqueo(btLunesTarde);
                bLunesTarde = true;
            }
        }

        if (bloqueo_martes > 0) {
            if (bloqueo_martes == 1) {
                activarBotonBloqueo(btMartesManiana);
                bMartesManiana = true;
            } else if (bloqueo_martes == 2) {
                activarBotonBloqueo(btMartesTarde);
                bMartesTarde = true;
            } else if (bloqueo_martes == 3) {
                activarBotonBloqueo(btMartes);
                bMartes = true;

                activarBotonBloqueo(btMartesManiana);
                bMartesManiana = true;

                activarBotonBloqueo(btMartesTarde);
                bMartesTarde = true;
            }
        }

        if (bloqueo_miercoles > 0) {
            if (bloqueo_miercoles == 1) {
                activarBotonBloqueo(btMiercolesManiana);
                bMiercolesManiana = true;
            } else if (bloqueo_miercoles == 2) {
                activarBotonBloqueo(btMiercolesTarde);
                bMiercolesTarde = true;
            } else if (bloqueo_miercoles == 3) {
                activarBotonBloqueo(btMiercoles);
                bMiercoles = true;

                activarBotonBloqueo(btMiercolesManiana);
                bMiercolesManiana = true;

                activarBotonBloqueo(btMiercolesTarde);
                bMiercolesTarde = true;
            }
        }

        if (bloqueo_jueves > 0) {
            if (bloqueo_jueves == 1) {
                activarBotonBloqueo(btJuevesManiana);
                bJuevesManiana = true;
            } else if (bloqueo_jueves == 2) {
                activarBotonBloqueo(btJuevesTarde);
                bJuevesTarde = true;
            } else if (bloqueo_jueves == 3) {
                activarBotonBloqueo(btJueves);
                bJueves = true;

                activarBotonBloqueo(btJuevesManiana);
                bJuevesManiana = true;

                activarBotonBloqueo(btJuevesTarde);
                bJuevesTarde = true;
            }
        }

        if (bloqueo_viernes > 0) {
            if (bloqueo_viernes == 1) {
                activarBotonBloqueo(btViernesManiana);
                bViernesManiana = true;
            } else if (bloqueo_viernes == 2) {
                activarBotonBloqueo(btViernesTarde);
                bViernesTarde = true;
            } else if (bloqueo_viernes == 3) {
                activarBotonBloqueo(btViernes);
                bViernes = true;

                activarBotonBloqueo(btViernesManiana);
                bViernesManiana = true;

                activarBotonBloqueo(btViernesTarde);
                bViernesTarde = true;
            }
        }

        if (bloqueo_sabado > 0) {
            if (bloqueo_sabado == 1) {
                activarBotonBloqueo(btSabadoManiana);
                bSabadoManiana = true;
            } else if (bloqueo_sabado == 2) {
                activarBotonBloqueo(btSabadoTarde);
                bSabadoTarde = true;
            } else if (bloqueo_sabado == 3) {
                activarBotonBloqueo(btSabado);
                bSabado = true;

                activarBotonBloqueo(btSabadoManiana);
                bSabadoManiana = true;

                activarBotonBloqueo(btSabadoTarde);
                bSabadoTarde = true;
            }
        }

        if (bloqueo_domingo > 0) {
            if (bloqueo_domingo == 1) {
                activarBotonBloqueo(btDomingoManiana);
                bDomingoManiana = true;
            } else if (bloqueo_domingo == 2) {
                activarBotonBloqueo(btDomingoTarde);
                bDomingoTarde = true;
            } else if (bloqueo_domingo == 3) {
                activarBotonBloqueo(btDomingo);
                bDomingo = true;

                activarBotonBloqueo(btDomingoManiana);
                bDomingoManiana = true;

                activarBotonBloqueo(btDomingoTarde);
                bDomingoTarde = true;
            }
        }

        // marco el estado crítico
        String estadoCritico = elemento.getEstadoCritico();
        if (estadoCritico.isEmpty())
            spinnerEstadoCritico.setSelection(0);
        else if (estadoCritico.equals("A"))
            spinnerEstadoCritico.setSelection(1);
        else if (estadoCritico.equals("B"))
            spinnerEstadoCritico.setSelection(2);
        else if (estadoCritico.equals("C"))
            spinnerEstadoCritico.setSelection(3);
        else
            spinnerEstadoCritico.setSelection(4);
    }

    /**
     * Establece la visibilidad del campo Nombre.
     */
    /*private void setFieldNombreVisibility(boolean visible) {
        int visibility = visible ? View.VISIBLE : View.GONE;
        //labelNombre.setVisibility(visibility);
        //textNombre.setVisibility(visibility);
        spinnerProvincias.setVisibility(visibility);
        spinnerProvinciasSimilar.setVisibility(visibility);
        textPrefijo.setVisibility(visibility);
        spinnerPrefijo.setVisibility(visibility);

    }*/
    private void setFieldExtrasVisibility(boolean visible) {
        int visibility = visible ? View.VISIBLE : View.GONE;

        textDiasBloqueo.setVisibility(visibility);
        //textElementoSimilar.setVisibility(visibility);
        //textEditElementoSimilar.setVisibility(visibility);
        //textPrefijoSimilar.setVisibility(visibility);

        labelTipoZona.setVisibility(visibility);
        spinnerTipoZona.setVisibility(visibility);
        cbRotativo.setVisibility(visibility);
        cbVaciaBajoDemanda.setVisibility(visibility);
        btLunes.setVisibility(visibility);
        btMartes.setVisibility(visibility);
        btMiercoles.setVisibility(visibility);
        btJueves.setVisibility(visibility);
        btViernes.setVisibility(visibility);
        btSabado.setVisibility(visibility);
        btDomingo.setVisibility(visibility);

        btLunesManiana.setVisibility(visibility);
        btLunesTarde.setVisibility(visibility);
        btMartesManiana.setVisibility(visibility);
        btMartesTarde.setVisibility(visibility);
        btMiercolesManiana.setVisibility(visibility);
        btMiercolesTarde.setVisibility(visibility);
        btJuevesTarde.setVisibility(visibility);
        btJuevesManiana.setVisibility(visibility);
        btViernesManiana.setVisibility(visibility);
        btViernesTarde.setVisibility(visibility);
        btSabadoManiana.setVisibility(visibility);
        btSabadoTarde.setVisibility(visibility);
        btDomingoManiana.setVisibility(visibility);
        btDomingoTarde.setVisibility(visibility);
        labelEstadoCritico.setVisibility(visibility);
        spinnerEstadoCritico.setVisibility(visibility);
    }

    /**
     * Establece la visibilidad del campo Matrícula.
     */
    private void setFieldMatriculaVisibility(boolean visible) {
        int visibility = visible ? View.VISIBLE : View.GONE;
        textMatricula.setVisibility(visibility);
        labelMatricula.setVisibility(visibility);
    }

    private void openGallery() {
        if (foto != null) {
            Toast.makeText(MainActivity.getInstance(),
                    "No puede añadir más de 1 fotografía. Por favor, elimina la imagen actual para poder asociar otra.",
                    Toast.LENGTH_LONG).show();

            return;
        }

        Intent galleryIntent = new Intent(Intent.ACTION_PICK,
                android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(galleryIntent, RESULT_GALLERY);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        boolean isFromCamera = requestCode == Photo.REQUEST_IMAGE_CAPTURE || requestCode == PhotoService.REQUEST_IMAGE_CAPTURE;
        // Insertamos foto desde la camara
        if (isFromCamera && resultCode == RESULT_OK) {
            Bitmap foto;

            if (UtilssAndroid.isAndroidGreater11())
                foto = PhotoService.get().setPic();
            else
                foto = Photo.getInstance().setPic();
            if (foto != null) {
                insertFotoGallery(foto);
            }
        }
        // Insertamos foto desde la galeria
        else if (requestCode == AddElemActivityCamacho.RESULT_GALLERY && data != null) {
            Uri imageUri = data.getData();
            try {
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inSampleSize = 4;
                AssetFileDescriptor fileDescriptor = null;
                foto = Utils.decodeBitmapFromFile(Utils.getRealPathFromURI(imageUri, this), 800, 600);
                insertFotoGallery(foto);
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
                e.printStackTrace();
            }
        }
    }

    /**
     * Activa el botón de bloqueo y lo pinta de color verde oscuro.
     *
     * @param button
     */
    private void activarBotonBloqueo(Button button) {
        button.setActivated(true);
        changeButtonStateStyle(button, true);
    }

    private boolean hayFotosSeleccionadas() {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll_imagenes);
        int numImagenes = layout.getChildCount();
        ImageView img;

        for (int i = 0; i < numImagenes; i++) {
            img = (ImageView) layout.getChildAt(i);
            if (img.getTag() == (Integer) 1)
                return true;
        }

        return false;
    }

    private void eliminarFotos() {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll_imagenes);
        int numImagenes = layout.getChildCount();
        ImageView img;

        for (int i = numImagenes - 1; i >= 0; i--) {
            img = (ImageView) layout.getChildAt(i);

            if (img.getTag() == (Integer) 1) {
                layout.removeViewAt(i);
                foto = null;
            }
        }

        foto = null;
        /*TextView texto = (TextView) findViewById(R.id.textoImagenesNumero);
        texto.setText("(" + layout.getChildCount() + ")");*/
    }

    /**
     * Clase que se ejecuta cuando se selecciona una provincia
     */
    public class SelectedProvincia implements OnItemSelectedListener {

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int pos, long id) {
            Cursor c = ((SimpleCursorAdapter) parent.getAdapter()).getCursor();
            c.moveToPosition(pos);

            DBMunicipios managerMunicipios = new DBMunicipios();
            int idProvincia = c.getInt(0);
            Cursor cursor = managerMunicipios.getAllCursorBy(idProvincia);
            String[] from = new String[]{"municipio"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(instance,
                  android.R.layout.simple_spinner_item, cursor, from, to, 0);
            dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            textPrefijo.setVisibility(View.GONE);
            spinnerMunicipio.setAdapter(dataAdapter);
            managerMunicipios.close();

            int indexOf = Integer.valueOf(Config.getInstance().getValueUsuario("SelectedMunicipio", "-1"));

            if (indexOf > -1) {
                try {
                    spinnerMunicipio.setSelection(indexOf);
                } catch (Exception ex) {

                }
            }

            Config.getInstance().setValueUsuario("SelectedProvincia", String.valueOf(pos));
        }

        @Override
        public void onNothingSelected(AdapterView<?> arg0) {
            // TODO Auto-generated method stub
        }

    }

    public class SelectedProvinciaSimilar implements OnItemSelectedListener {

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int pos, long id) {
            Cursor c = ((SimpleCursorAdapter) parent.getAdapter()).getCursor();
            c.moveToPosition(pos);

            DBMunicipios managerMunicipios = new DBMunicipios();
            int idProvincia = c.getInt(0);
            Cursor cursor = managerMunicipios.getAllCursorBy(idProvincia);
            String[] from = new String[]{"municipio"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(instance,
                  android.R.layout.simple_spinner_item, cursor, from, to, 0);
            dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            textPrefijoSimilar.setVisibility(View.GONE);
            spinnerMunicipioSimilar.setAdapter(dataAdapter);
            spinnerMunicipioSimilar.setVisibility(View.VISIBLE);
            managerMunicipios.close();

            int indexOf = Integer.valueOf(Config.getInstance().getValueUsuario("SelectedMunicipio", "-1"));

            if (indexOf > -1) {
                try {
                    spinnerMunicipioSimilar.setSelection(indexOf);
                } catch (Exception ex) {

                }
            }

            Config.getInstance().setValueUsuario("SelectedProvincia", String.valueOf(pos));
        }

        @Override
        public void onNothingSelected(AdapterView<?> arg0) {
            // TODO Auto-generated method stub
        }
    }

    class AsyncGIS extends AsyncTask<Void, String, JSONArray> {

        private ProgressDialog pDialog;
        private double lat, lon;
        JSONArray jsonData = null;

        public AsyncGIS(double latitud, double longitud) {
            this.lat = latitud;
            this.lon = longitud;
        }

        protected void onPreExecute() {
            // para el progress dialog
            pDialog = new ProgressDialog(AddElemActivityCamacho.getInstance());
            pDialog.setMessage("Conectando al servidor, por favor espere");
            pDialog.setIndeterminate(true);
            pDialog.setCancelable(false);
            pDialog.show();
        }

        @Override
        protected JSONArray doInBackground(Void... strings) {
            // DINIGO - Mantis 0005119 - Si no tiene internet no realizamos la comprobación ya que genera que el sistema cierre la aplicación por el retraso.
            if (!MainActivity.getInstance().isNetworkAvailable()) return null;

            ClientWebSvc sweb = new ClientWebSvc();

            String url = Config.getInstance().getValue("webSvc", "");
            if (url.equals(""))
                return null;

            url = url + "/api/gis/calle/" + this.lat + "/" + this.lon;

            HttpPost post = new HttpPost(url);

            // Preparo la llamada para recibir datos
            post.setHeader("User-Agent", "java-Android");
            post.setHeader("Content-type", "application/json");
            post.setHeader("Accept-encoding", "gzip");
            post.setHeader("Authorization", "Bearer " + sweb.token);

            // Establezco timeout alto
            HttpParams parametros = new BasicHttpParams();

            int timeoutConnection = 10000;
            HttpConnectionParams.setConnectionTimeout(parametros, timeoutConnection);
            int timeoutSocket = 10000;
            HttpConnectionParams.setSoTimeout(parametros, timeoutSocket);
            DefaultHttpClient httpClient = new DefaultHttpClient(parametros);
            HttpResponse response;

            boolean salir = false;

            // lo intento 2 veces, en caso contrario sigo sin indicar el municipio, tendrá que ponerlo manualmente.
            for (int i = 0; i < 2 && !salir; i++) {
                try {
                    response = httpClient.execute(post);
                    int result = response.getStatusLine().getStatusCode();

                    switch (result) {
                        case 200: // Respuesta correcta
                            InputStream inStream = response.getEntity().getContent();

                            Header contentEncoding = response.getFirstHeader("Content-Encoding");
                            if (contentEncoding != null && contentEncoding.getValue().equalsIgnoreCase("gzip")) {
                                inStream = new GZIPInputStream(inStream);
                            }

                            String strResponse = sweb.convertStreamToString(inStream);

                            try {
                                // En principio intento recuperar los datos como un array JSON si falla lo intento como JSON
                                jsonData = new JSONArray(strResponse);
                                salir = true;

                            } catch (JSONException e) {
                                try {
                                    // Recupero la respuesta JSON
                                    jsonData = new JSONArray()
                                            .put(new JSONObject(strResponse));
                                    salir = true;
                                } catch (Exception ex) {
                                    MyLoggerHandler.getInstance().error(ex);
                                }
                            }
                            break;

                        case 401: // El token ya no es vilido
                            /*sweb.token = "";
                            sweb.refreshToken();
                            */
                            MyLoggerHandler.getInstance().info("[TOKEN]: Atención, en AddElemActivityCamacho el token ha expirado.");
                            break;

                        default:
                            try {
                                Thread.sleep(5000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                    }
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                } catch (ClientProtocolException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    if (e instanceof HttpHostConnectException) {
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException e1) {
                            e1.printStackTrace();
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return jsonData;
        }

        protected void onPostExecute(JSONArray result) {
            try {
                if (result != null)
                    municipio = result.getJSONObject(0).get("Municipio").toString();
            } catch (JSONException e) {
                e.printStackTrace();
            }

            pDialog.dismiss();
            if (municipio == null)
                municipio = "";

            // si vamos a actualizar el elemento....
            if (elemento != null && param.update) {
                changeVisibilityNombre(false);

                textMatricula.setText(elemento.getMatricula());
                textMatricula.setSelection(elemento.getMatricula().length());

                cbRotativo.setChecked(elemento.esRotativo() == 1);
                cbVaciaBajoDemanda.setChecked(elemento.getVaciaBajoDemanda() == 1);
                spinnerTipoZona.setSelection(elemento.getTipoZona());

                if (elemento.getDescripcion() != null && elemento.getDescripcion().length() > 0) {
                    textDescripcion.setText(elemento.getDescripcion());
                }

                if (elemento.getTieneImagen())
                    btExisteImagen.setImageResource(R.drawable.ic_menu_report_image_si);

                if (modelos != null && modelos.size() > 0) {
                    // Recorro los modelos para seleccionar en el combo el iltimo
                    for (ElementoModelo modelo : modelos) {
                        if (modelo.getIdExterno() == elemento.getModelo()) {
                            spinnerModelos.setSelection(modelos.indexOf(modelo));
                            break;
                        }
                    }
                }

                DBElemento db = new DBElemento();
                Elemento elemento_similar = db.getByIdExterno(elemento.getIdElementoSimilar(), MainActivity.getInstance().getEmpresa());
                db.close();

                // SI EXISTE UN ELEMENTO SIMILAR, CARGO EL NOMBRE
                if (elemento_similar != null) {
                    spinnerProvinciasSimilar.setVisibility(View.GONE);
                    spinnerMunicipioSimilar.setVisibility(View.GONE);
                    textPrefijoSimilar.setText(elemento_similar.getNombre());
                    textPrefijoSimilar.setEnabled(false);

                    String[] arrayValores = elemento_similar.getNombre().split(" ");
                    if (arrayValores.length > 1) {
                        // como el municipio puede contener varias palabras, damos por hecho que los
                        // últimos dígitos es el nº del elemento, el resto el municipio, que voy concatenando las palabras que contenga
                        StringBuilder muni = new StringBuilder();
                        for (int i = 0; i < arrayValores.length - 1; i++) {
                            muni.append(arrayValores[i]).append(" ");
                        }

                        textPrefijoSimilar.setText(muni.toString().trim());
                        textEditElementoSimilar.setText(arrayValores[arrayValores.length - 1]);
                    }
                } else {
                    // cargo el municipio para que indique el número
                    if (municipio.isEmpty()) {
                        spinnerProvinciasSimilar.setVisibility(View.VISIBLE);
                        setProvincias(spinnerProvinciasSimilar, true);
                    } else {
                        spinnerProvinciasSimilar.setVisibility(View.GONE);
                        spinnerMunicipioSimilar.setVisibility(View.GONE);
                        textPrefijoSimilar.setEnabled(false);
                        textPrefijoSimilar.setText(municipio.toUpperCase());
                    }
                }

            } else { // si se trata de un nuevo elemento....
                if (municipio.isEmpty()) {
                    changeVisibilityNombre(true);
                    setProvincias(spinnerProvincias, false);

                    spinnerProvinciasSimilar.setVisibility(View.VISIBLE);
                    setProvincias(spinnerProvinciasSimilar, true);

                } else {
                    changeVisibilityNombre(false);
                    textPrefijo.setVisibility(View.VISIBLE);
                    textNombre.setVisibility(View.VISIBLE);

                    textPrefijo.setEnabled(false);
                    textPrefijoSimilar.setEnabled(false);

                    spinnerProvinciasSimilar.setVisibility(View.GONE);
                    spinnerMunicipioSimilar.setVisibility(View.GONE);

                    textPrefijo.setText(municipio.toUpperCase());
                    textPrefijoSimilar.setText(municipio.toUpperCase());
                }
            }

            // SI ES UNA MODIFICACIÓN, NO MUESTRO EL NOMBRE, PUES NO SE PUEDE MODIFICAR
            /*if (update_elemento){
                setFieldNombreVisibility(false);
            } else {

                    spinnerProvinciasSimilar.setVisibility(View.VISIBLE);
                    spinnerProvincias.setVisibility(View.VISIBLE);
                    setProvincias(spinnerProvincias);
                    setProvincias(spinnerProvinciasSimilar);

            }*/

           /* if (!municipio.equals("")) {

                textPrefijo.setText(municipio.toUpperCase());
                textPrefijo.setEnabled(false);
                textPrefijoSimilar.setText(municipio.toUpperCase());
                textPrefijoSimilar.setEnabled(false);
                spinnerPrefijo.setVisibility(View.GONE);
                textPrefijoSimilar.setText(municipio.toUpperCase());
                textPrefijoSimilar.setEnabled(false);

            } else {

                spinnerProvinciasSimilar.setVisibility(View.VISIBLE);
                setProvincias(spinnerProvinciasSimilar);
            }*/

        }

        @Override
        protected void onCancelled() {
            Toast.makeText(AddElemActivityCamacho.getInstance(), "Tarea cancelada!",
                    Toast.LENGTH_SHORT).show();
        }
    }

    private void changeVisibilityNombre(boolean b) {
        labelNombre.setVisibility(!b ? View.GONE : View.VISIBLE);
        textNombre.setVisibility(!b ? View.GONE : View.VISIBLE);
        textPrefijo.setVisibility(!b ? View.GONE : View.VISIBLE);
        spinnerProvincias.setVisibility(!b ? View.GONE : View.VISIBLE);
        spinnerMunicipio.setVisibility(!b ? View.GONE : View.VISIBLE);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Logg.info(TAG, "[onKeyDown] keyCode: " + keyCode);

        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            foto = null;
            finish();
        }

        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onReaded134TagData(final byte[] buffer, final int size) {
        Logg.info(TAG + "-" + getActivityName(), "[onReaded134TagData] buffer: " + Arrays.toString(Arrays.copyOfRange(buffer, 0, size)) + " size: " + size);
        runOnUiThread(() -> {
            ReadingTagToast.get().cancel();
            Tag134 tag134 = null;
            byte[] id = new byte[size];
            if (size <= 0) {
                TagNotFoundToast.get().showToast(getBaseContext());
            } else {
                System.arraycopy(buffer, 0, id, 0, size);
                if (!LFByteUtils.hasValue(id)) {
                    MainActivity.getInstance().showMessage("Aléjese del tag, pulse el botón y acerque el lector de nuevo.", Toast.LENGTH_LONG);
                } else {
                    String tagRead = LFByteUtils.showResultASCII(id);
                    DBTags dbTags = new DBTags();
                    tag134 = new Tag134("", tagRead, Utilss.now());
                    Tags tag = dbTags.getByTag(tag134, MainActivity.getInstance().getEmpresa());
                    dbTags.close();

                    TagSendSensor.execute(tag134, tag);
                    SoundManager.getInstance(getBaseContext()).play();

                    if (tag != null) { // Si el tag si que está en la base de datos entonces se muestra la matrícula.
                        textMatricula.setText(tag.getMatricula());
                        labelMatricula.setText("Matrícula");
                        // Se obtiene el elemento asociado al tag
                        DBElemento dbElemento = new DBElemento();
                        Elemento elemento = dbElemento.getElementoByTag(tag);
                        dbElemento.close();
                        textNombre.setText(elemento.getNombre());
                        textDescripcion.setText(elemento.getDescripcion());
                    }
                }
            }
        });
    }

}
