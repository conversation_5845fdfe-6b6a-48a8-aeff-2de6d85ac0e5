package com.movisat.ecosat;

import android.app.Activity;
import android.os.Bundle;
import android.view.KeyEvent;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.movisat.adapter.SelectionAdapter;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBUsuario;
import com.movisat.database.Elemento;
import com.movisat.database.Incidencia;
import com.movisat.database.Usuario;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;

import java.util.ArrayList;

public class SeleccionUsuarioIncidenciaActivity extends BaseActivity {
    private static Elemento elemento = null;
    SelectionAdapter<String> mAdapter;
    ArrayList<Usuario> usuarios;
    private Button mBtnVolver;
    private Button mBtnAceptar;

    public Elemento getElemento() {
        return elemento;
    }

    public void setElemento(Elemento elemento) {
        SeleccionUsuarioIncidenciaActivity.elemento = elemento;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.seleccion_usuario_incidencia);

        DBUsuario dbUsuario = new DBUsuario();
        usuarios = dbUsuario.getAll(MainActivity.getInstance().getEmpresa());
        dbUsuario.close();

        final ListView list = (ListView) findViewById(R.id.lvUsuarios);
        TextView txt = (TextView) findViewById(R.id.textViewTitulo);
        mBtnVolver = (Button) findViewById(R.id.btnVolver);
        mBtnAceptar = (Button) findViewById(R.id.btnAceptar);

        Bundle bundle = getIntent().getExtras();
        int incidenciaId = bundle.getInt("incidencia");
        int incidenciaIdExterno = bundle.getInt("incidenciaExt");

        DBIncidencia dbIncidencia = new DBIncidencia();
        Incidencia incidencia = incidenciaIdExterno > 0
              ? dbIncidencia.getByIdExterno(incidenciaIdExterno, MainActivity.getInstance().getEmpresa())
              : dbIncidencia.getByIdInterno(incidenciaId, MainActivity.getInstance().getEmpresa());
        String nombrePropietario = dbIncidencia.getNombrePropietario(incidencia);
        dbIncidencia.close();

        txt.setText(incidencia.toString());

        String[] nombresUsuarios = new String[usuarios.size()];
        for (int i = 0; i < usuarios.size(); i++) {
            nombresUsuarios[i] = usuarios.get(i).getNombre();
        }

        mAdapter = new SelectionAdapter<String>(this, android.R.layout.simple_list_item_1, nombresUsuarios);

        list.setAdapter(mAdapter);

        list.setOnItemClickListener((adapterView, view, i, l) -> {
            mAdapter.setIndexSeleccionado(i);
            mAdapter.notifyDataSetChanged();
            mBtnAceptar.setEnabled(true);
        });

        mBtnVolver.setOnClickListener(view -> {
            setResult(Activity.RESULT_CANCELED);
            finish();
        });

        mBtnAceptar.setOnClickListener(view -> {
            mBtnAceptar.setEnabled(false);
            if (mAdapter != null && mAdapter.getIndexSeleccionado() >= 0) {
                Usuario usuario = usuarios.get(mAdapter.getIndexSeleccionado());
                incidencia.setPropietario(usuario.getIdExterno());

                DBPacket dbPacket = new DBPacket();
                dbPacket.insert(new Packet(Packet.INCIDENCIA_ASIGNAR_USUARIO, Packet.PRIORIDAD_NORMAL, incidencia));
                dbPacket.close();

                Toast.makeText(getApplicationContext(), "Incidencia asignada a " + usuario.getNombre(), Toast.LENGTH_LONG).show();

                // Cerramos actividad.
                setResult(Activity.RESULT_OK);
                finish();
            }
        });

        for (int i = 0; i < usuarios.size(); i++) {
            if (usuarios.get(i).getNombre().equals(nombrePropietario)) {
                mAdapter.setIndexSeleccionado(i);
                mBtnAceptar.setEnabled(true);
                break;
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

}
