package com.movisat.ecosat;

import static com.movisat.ecosat.NotificacionesActivity.ID_INCIDENCIA;

import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.app.NotificationManager;
import android.app.SearchManager;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.SearchView;

import com.environment.Environment;
import com.google.android.gms.maps.model.LatLng;
import com.movisat.adapter.AsignarIncidenciasModelAdapter;
import com.movisat.database.DBEstados;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBIncidenciaModelo;
import com.movisat.database.DBIncidenciaMotivo;
import com.movisat.database.DBIncidenciaTipo;
import com.movisat.database.Estado;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaModelo;
import com.movisat.database.IncidenciaMotivo;
import com.movisat.database.IncidenciaTipo;
import com.movisat.database.IncidentFilterCriteria;
import com.movisat.events.OnIncidenciaAsignada;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Locale;
import java.util.Set;

public class AsignarIncidenciasActivity extends BaseActivity implements SearchView.OnQueryTextListener {
    private static final int REQUEST_UPDATE_ESTADO_INCIDENCIA = 1001;
    private static AsignarIncidenciasActivity instance = null;
    private static ArrayList<Incidencia> incidencias;
    private static Incidencia incidencia;
    public static MenuItem searchMenuItem;
    private TextView titulo;
    private Button btAsignadas;
    private ListView listaIncidencias;
    private AsignarIncidenciasModelAdapter adapter;
    private boolean mFromNotification = false;
    public SearchView searchView;
    private boolean mostrarIncidenciasAsignadas = true;
    private int selectedIncidentIndex = -1;
    private Toast toast;
    private IncidentFilterCriteria currentFilterCriteria;
    private static final String FILTER_STATE_KEY = "filter_criteria";
    
    // Filter indicator
    private MenuItem filterMenuItem;
    private View filterDot;
    
    // Filter data for correct mapping
    private ArrayList<Estado> estadosList;
    private ArrayList<IncidenciaTipo> tiposList;
    private ArrayList<String> ownersList;

    public static AsignarIncidenciasActivity getInstance() {
        return instance;
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            NotificationManager notificationManager = (NotificationManager) this.getSystemService(Context.NOTIFICATION_SERVICE);
            try {
                notificationManager.cancelAll();
                if (getIntent().hasExtra("idIncidencia")) {
                    // Se comprueba si la Activity se ha iniciado desde una notificación de nuevas incidencias
                    int incidencia_id = getIntent().getExtras().getInt("idIncidencia");
                    mFromNotification = (incidencia_id == ID_INCIDENCIA);
                }
            } catch (Exception e1) {
                MyLoggerHandler.getInstance().error(e1);
            }

            instance = this;
            incidencia = null;
            
            // Initialize filter criteria from saved state or create new
            if (savedInstanceState != null && savedInstanceState.containsKey(FILTER_STATE_KEY)) {
                currentFilterCriteria = savedInstanceState.getParcelable(FILTER_STATE_KEY);
            } else {
                currentFilterCriteria = new IncidentFilterCriteria();
            }

            setContentView(R.layout.list_asignar_incidencias_layout);
            titulo = findViewById(R.id.titulo);
            btAsignadas = findViewById(R.id.btAsignadas);

            btAsignadas.setOnClickListener(v -> {
                mostrarIncidenciasAsignadas = !mostrarIncidenciasAsignadas;
                adapter.setMostrarIncidenciasAsignadas(mostrarIncidenciasAsignadas);
                if (mostrarIncidenciasAsignadas) {
                    btAsignadas.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_all, 0);
                } else {
                    btAsignadas.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
                }
                adapter.getFilter().filter(searchView.getQuery());
            });

            // LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver,
            // new IntentFilter("incidencias-event"));

            // Evento botin centrar
            findViewById(R.id.btCentrar).setOnClickListener(v -> {
                if (incidencia != null) {
                    LatLng posicion = incidencia.getPosition();
                    MyBroadCastManager.getInstance().sendBroadCastCenterMapBy(posicion, 18, "", true);
                    finish();
                }
            });

            // Evento botin salir
            findViewById(R.id.btSalir).setOnClickListener(v -> finish());

            listaIncidencias = findViewById(R.id.lvIncidencias);
            listaIncidencias.setOnItemClickListener((list, view, index, id) -> {
                incidencia = (Incidencia) listaIncidencias.getItemAtPosition(index);

                AsignarIncidenciasModelAdapter adapter = (AsignarIncidenciasModelAdapter) listaIncidencias.getAdapter();
                adapter.setIndexSeleccionado(index);
                selectedIncidentIndex = index;

                Intent intent = new Intent(this, UpdateEstadoIncidencia.class);
                intent.putExtra("incidencia", incidencia.getId());
                intent.putExtra("incidenciaExt", incidencia.getIdExterno());
                intent.putExtra("fromAsignarIncidencias", true);
                startActivityForResult(intent, REQUEST_UPDATE_ESTADO_INCIDENCIA);
            });
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        EventBus.getDefault().register(this);

        if (adapter == null) {
            cargarIncidencias();
            adapter = new AsignarIncidenciasModelAdapter(this, incidencias);
            listaIncidencias.setAdapter(adapter);
            listaIncidencias.setTextFilterEnabled(true);
            
            // Apply any existing filter criteria
            if (currentFilterCriteria != null && currentFilterCriteria.hasActiveFilters()) {
                adapter.applyAdvancedFilter(currentFilterCriteria);
            }
            
            // Update filter dot
            updateFilterDot();
        }

        if (incidencias == null || incidencias.isEmpty()) {
            Toast.makeText(this, getResources().getString(R.string.avisoNoHayIncidencias), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_UPDATE_ESTADO_INCIDENCIA) {
            if (selectedIncidentIndex >= 0 && adapter != null) {
                adapter.setIndexSeleccionado(selectedIncidentIndex);
                adapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        EventBus.getDefault().unregister(this);
    }

    private void cargarIncidencias() {
        // Si la Activity se ha abierto desde una notificación, se muestran todas las incidencias
        boolean filtrar = !mFromNotification;
        DBIncidencia dbIncidencia = new DBIncidencia();
        incidencias = dbIncidencia.getAllIncidenciasWithNombrePropietario(MainActivity.getInstance().getEmpresa(), MainActivity.getInstance().getUsuario(), filtrar);
        dbIncidencia.close();
        setNumIncidencias(incidencias.size());
        Collections.sort(incidencias);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.search_menu, menu);

        SearchManager searchManager = (SearchManager) getSystemService(Context.SEARCH_SERVICE);
        searchMenuItem = menu.findItem(R.id.search);
        searchView = (SearchView) searchMenuItem.getActionView();

        searchView.setSearchableInfo(searchManager.getSearchableInfo(getComponentName()));
        //      searchView.setSubmitButtonEnabled(true);
        searchView.setOnQueryTextListener(this);

        // Setup filter menu item
        filterMenuItem = menu.findItem(R.id.filter);
        setupFilterDot();

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.filter:
                showFilterDialog();
                return true;
            default:
                return super.onOptionsItemSelected(item);
        }
    }

    @Override
    public boolean onQueryTextSubmit(String query) {
        adapter.getFilter().filter(query);
        return true;
    }

    @Override
    public boolean onQueryTextChange(String newText) {
        adapter.getFilter().filter(newText);
        return true;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * Este metodo se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    public void setNumIncidencias(int size) {
        titulo.setText(String.format("%s: %d", getString(R.string.total), size));
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (currentFilterCriteria != null) {
            outState.putParcelable(FILTER_STATE_KEY, currentFilterCriteria);
        }
    }

    private void showFilterDialog() {
        LayoutInflater inflater = getLayoutInflater();
        View dialogView = inflater.inflate(R.layout.dialog_incident_filter, null);

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.filtros_de_incidencias));
        builder.setView(dialogView);

        // Get references to dialog elements
        RadioGroup rgDateFilter = dialogView.findViewById(R.id.rgDateFilter);
        LinearLayout llCustomDateRange = dialogView.findViewById(R.id.llCustomDateRange);
        Button btnFromDate = dialogView.findViewById(R.id.btnFromDate);
        Button btnToDate = dialogView.findViewById(R.id.btnToDate);
        RadioGroup rgSortOrder = dialogView.findViewById(R.id.rgSortOrder);
        Spinner spinnerStatus = dialogView.findViewById(R.id.spinnerStatus);
        Spinner spinnerIncidentType = dialogView.findViewById(R.id.spinnerIncidentType);
        LinearLayout llOwnerFilterSection = dialogView.findViewById(R.id.llOwnerFilterSection);
        Spinner spinnerOwner = dialogView.findViewById(R.id.spinnerOwner);
        CheckBox cbWithElementOnly = dialogView.findViewById(R.id.cbWithElementOnly);
        Button btnClearFilters = dialogView.findViewById(R.id.btnClearFilters);
        Button btnCancelFilter = dialogView.findViewById(R.id.btnCancelFilter);
        Button btnApplyFilter = dialogView.findViewById(R.id.btnApplyFilter);

        // Check if owner filter should be visible based on menu permissions
        MainActivity mainActivity = MainActivity.getInstance();
        boolean showOwnerFilter = mainActivity != null && mainActivity.isValidMenu(MainActivity.MENU_ASIGNAR_INCIDENCIAS, "");
        
        if (showOwnerFilter) {
            llOwnerFilterSection.setVisibility(View.VISIBLE);
        } else {
            llOwnerFilterSection.setVisibility(View.GONE);
        }

        // Initialize spinners
        setupStatusSpinner(spinnerStatus);
        setupIncidentTypeSpinner(spinnerIncidentType);
        if (showOwnerFilter) {
            setupOwnerSpinner(spinnerOwner);
        }

        // Set current filter values
        setDialogValues(rgDateFilter, llCustomDateRange, btnFromDate, btnToDate, 
                       rgSortOrder, spinnerStatus, spinnerIncidentType, 
                       spinnerOwner, cbWithElementOnly, showOwnerFilter);

        AlertDialog dialog = builder.create();

        // Handle custom date range visibility
        rgDateFilter.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.rbDateCustom) {
                llCustomDateRange.setVisibility(View.VISIBLE);
            } else {
                llCustomDateRange.setVisibility(View.GONE);
            }
        });

        // Date picker listeners
        btnFromDate.setOnClickListener(v -> showDatePicker(btnFromDate, true));
        btnToDate.setOnClickListener(v -> showDatePicker(btnToDate, false));

        // Button listeners
        btnClearFilters.setOnClickListener(v -> {
            currentFilterCriteria.reset();
            if (adapter != null) {
                adapter.applyAdvancedFilter(currentFilterCriteria);
            }
            setDialogValues(rgDateFilter, llCustomDateRange, btnFromDate, btnToDate, 
                           rgSortOrder, spinnerStatus, spinnerIncidentType, 
                           spinnerOwner, cbWithElementOnly, showOwnerFilter);
            updateFilterDot();
        });

        btnCancelFilter.setOnClickListener(v -> dialog.dismiss());

        btnApplyFilter.setOnClickListener(v -> {
            applyFiltersFromDialog(rgDateFilter, btnFromDate, btnToDate, 
                                 rgSortOrder, spinnerStatus, spinnerIncidentType, 
                                 spinnerOwner, cbWithElementOnly, showOwnerFilter);
            if (adapter != null) {
                adapter.applyAdvancedFilter(currentFilterCriteria);
            }
            updateFilterDot();
            dialog.dismiss();
        });

        dialog.show();
    }

    private void setupStatusSpinner(Spinner spinner) {
        try {
            DBEstados dbEstados = new DBEstados();
            estadosList = dbEstados.getAll(MainActivity.getInstance().getEmpresa());
            dbEstados.close();

            ArrayList<String> statusNames = new ArrayList<>();
            statusNames.add(getString(R.string.todos_los_estados));
            for (Estado estado : estadosList) {
                statusNames.add(estado.getNombre());
            }

            ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statusNames);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinner.setAdapter(adapter);
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            if (estadosList == null) {
                estadosList = new ArrayList<>();
            }
        }
    }

    private void setupIncidentTypeSpinner(Spinner spinner) {
        try {
            DBIncidenciaTipo dbTipos = new DBIncidenciaTipo();
            tiposList = dbTipos.getAll(MainActivity.getInstance().getEmpresa());
            dbTipos.close();

            ArrayList<String> typeNames = new ArrayList<>();
            typeNames.add(getString(R.string.todos_los_tipos));
            for (IncidenciaTipo tipo : tiposList) {
                typeNames.add(tipo.getNombre());
            }

            ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, typeNames);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinner.setAdapter(adapter);
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            if (tiposList == null) {
                tiposList = new ArrayList<>();
            }
        }
    }

    private void setupOwnerSpinner(Spinner spinner) {
        try {
            // Get unique owners from current incidents
            Set<String> uniqueOwners = new HashSet<>();
            for (Incidencia incidencia : incidencias) {
                if (incidencia.getNombrePropietario() != null && !incidencia.getNombrePropietario().isEmpty()) {
                    uniqueOwners.add(incidencia.getNombrePropietario());
                }
            }

            ownersList = new ArrayList<>();
            ownersList.add(getString(R.string.todos_los_propietarios));
            ownersList.addAll(uniqueOwners);
            Collections.sort(ownersList.subList(1, ownersList.size()));

            ArrayAdapter<String> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, ownersList);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            spinner.setAdapter(adapter);
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            if (ownersList == null) {
                ownersList = new ArrayList<>();
            }
        }
    }

    private void setDialogValues(RadioGroup rgDateFilter, LinearLayout llCustomDateRange,
                                Button btnFromDate, Button btnToDate, RadioGroup rgSortOrder,
                                Spinner spinnerStatus, Spinner spinnerIncidentType,
                                Spinner spinnerOwner, CheckBox cbWithElementOnly, boolean showOwnerFilter) {
        
        // Set date filter
        switch (currentFilterCriteria.getDateFilter()) {
            case ALL:
                rgDateFilter.check(R.id.rbDateAll);
                llCustomDateRange.setVisibility(View.GONE);
                break;
            case TODAY:
                rgDateFilter.check(R.id.rbDateToday);
                llCustomDateRange.setVisibility(View.GONE);
                break;
            case YESTERDAY:
                rgDateFilter.check(R.id.rbDateYesterday);
                llCustomDateRange.setVisibility(View.GONE);
                break;
            case LAST_7_DAYS:
                rgDateFilter.check(R.id.rbDateLast7Days);
                llCustomDateRange.setVisibility(View.GONE);
                break;
            case CUSTOM:
                rgDateFilter.check(R.id.rbDateCustom);
                llCustomDateRange.setVisibility(View.VISIBLE);
                break;
        }

        // Set custom date values
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
        if (currentFilterCriteria.getFromDate() != null) {
            btnFromDate.setText(dateFormat.format(currentFilterCriteria.getFromDate()));
        } else {
            btnFromDate.setText(getString(R.string.seleccionar_fecha));
        }
        
        if (currentFilterCriteria.getToDate() != null) {
            btnToDate.setText(dateFormat.format(currentFilterCriteria.getToDate()));
        } else {
            btnToDate.setText(getString(R.string.seleccionar_fecha));
        }

        // Set sort order
        if (currentFilterCriteria.getSortOrder() == IncidentFilterCriteria.SortOrder.NEWEST_FIRST) {
            rgSortOrder.check(R.id.rbSortNewestFirst);
        } else {
            rgSortOrder.check(R.id.rbSortOldestFirst);
        }

        // Set spinner selections - Map idExterno back to spinner positions
        
        // Status spinner
        int statusFilterId = currentFilterCriteria.getStatusFilter();
        if (statusFilterId == -1) {
            spinnerStatus.setSelection(0); // "Todos los estados"
        } else if (estadosList != null) {
            int statusPosition = 0; // Default to "Todos los estados"
            for (int i = 0; i < estadosList.size(); i++) {
                if (estadosList.get(i).getIdExterno() == statusFilterId) {
                    statusPosition = i + 1; // +1 because position 0 is "Todos los estados"
                    break;
                }
            }
            spinnerStatus.setSelection(statusPosition);
        } else {
            spinnerStatus.setSelection(0);
        }
        
        // Incident type spinner
        int typeFilterId = currentFilterCriteria.getIncidentTypeFilter();
        if (typeFilterId == -1) {
            spinnerIncidentType.setSelection(0); // "Todos los tipos"
        } else if (tiposList != null) {
            int typePosition = 0; // Default to "Todos los tipos"
            for (int i = 0; i < tiposList.size(); i++) {
                if (tiposList.get(i).getIdExterno() == typeFilterId) {
                    typePosition = i + 1; // +1 because position 0 is "Todos los tipos"
                    break;
                }
            }
            spinnerIncidentType.setSelection(typePosition);
        } else {
            spinnerIncidentType.setSelection(0);
        }
        
        // Owner spinner - Map owner name back to spinner position (only if visible)
        if (showOwnerFilter) {
            String ownerFilterName = currentFilterCriteria.getOwnerName();
            if (ownerFilterName == null || ownerFilterName.isEmpty()) {
                spinnerOwner.setSelection(0); // "Todos los propietarios"
            } else if (ownersList != null) {
                int ownerPosition = 0; // Default to "Todos los propietarios"
                for (int i = 0; i < ownersList.size(); i++) {
                    if (ownersList.get(i).equals(ownerFilterName)) {
                        ownerPosition = i;
                        break;
                    }
                }
                spinnerOwner.setSelection(ownerPosition);
            } else {
                spinnerOwner.setSelection(0);
            }
        }
        
        // Set checkbox
        cbWithElementOnly.setChecked(currentFilterCriteria.isWithElementOnly());
    }

    private void showDatePicker(Button targetButton, boolean isFromDate) {
        Calendar calendar = Calendar.getInstance();
        
        Date currentDate = isFromDate ? currentFilterCriteria.getFromDate() : currentFilterCriteria.getToDate();
        if (currentDate != null) {
            calendar.setTime(currentDate);
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(
            this,
            (view, year, month, dayOfMonth) -> {
                Calendar selectedDate = Calendar.getInstance();
                selectedDate.set(year, month, dayOfMonth);
                
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());
                targetButton.setText(dateFormat.format(selectedDate.getTime()));
                
                if (isFromDate) {
                    selectedDate.set(Calendar.HOUR_OF_DAY, 0);
                    selectedDate.set(Calendar.MINUTE, 0);
                    selectedDate.set(Calendar.SECOND, 0);
                    selectedDate.set(Calendar.MILLISECOND, 0);
                    currentFilterCriteria.setFromDate(selectedDate.getTime());
                } else {
                    selectedDate.set(Calendar.HOUR_OF_DAY, 23);
                    selectedDate.set(Calendar.MINUTE, 59);
                    selectedDate.set(Calendar.SECOND, 59);
                    selectedDate.set(Calendar.MILLISECOND, 999);
                    currentFilterCriteria.setToDate(selectedDate.getTime());
                }
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        );

        datePickerDialog.show();
    }

    private void applyFiltersFromDialog(RadioGroup rgDateFilter, Button btnFromDate, Button btnToDate,
                                      RadioGroup rgSortOrder, Spinner spinnerStatus,
                                      Spinner spinnerIncidentType, Spinner spinnerOwner,
                                      CheckBox cbWithElementOnly, boolean showOwnerFilter) {
        
        // Apply date filter
        int checkedDateId = rgDateFilter.getCheckedRadioButtonId();
        if (checkedDateId == R.id.rbDateAll) {
            currentFilterCriteria.setDateFilter(IncidentFilterCriteria.DateFilter.ALL);
        } else if (checkedDateId == R.id.rbDateToday) {
            currentFilterCriteria.setDateFilter(IncidentFilterCriteria.DateFilter.TODAY);
        } else if (checkedDateId == R.id.rbDateYesterday) {
            currentFilterCriteria.setDateFilter(IncidentFilterCriteria.DateFilter.YESTERDAY);
        } else if (checkedDateId == R.id.rbDateLast7Days) {
            currentFilterCriteria.setDateFilter(IncidentFilterCriteria.DateFilter.LAST_7_DAYS);
        } else if (checkedDateId == R.id.rbDateCustom) {
            currentFilterCriteria.setDateFilter(IncidentFilterCriteria.DateFilter.CUSTOM);
        }

        // Apply sort order
        int checkedSortId = rgSortOrder.getCheckedRadioButtonId();
        if (checkedSortId == R.id.rbSortNewestFirst) {
            currentFilterCriteria.setSortOrder(IncidentFilterCriteria.SortOrder.NEWEST_FIRST);
        } else {
            currentFilterCriteria.setSortOrder(IncidentFilterCriteria.SortOrder.OLDEST_FIRST);
        }

        // Apply other filters - Map spinner positions to real idExterno values
        
        // Status filter
        int statusPosition = spinnerStatus.getSelectedItemPosition();
        if (statusPosition == 0) {
            // "Todos los estados" selected
            currentFilterCriteria.setStatusFilter(-1);
        } else if (estadosList != null && statusPosition <= estadosList.size()) {
            // Get the idExterno of the selected estado
            Estado selectedEstado = estadosList.get(statusPosition - 1);
            currentFilterCriteria.setStatusFilter(selectedEstado.getIdExterno());
        } else {
            currentFilterCriteria.setStatusFilter(-1);
        }
        
        // Incident type filter
        int typePosition = spinnerIncidentType.getSelectedItemPosition();
        if (typePosition == 0) {
            // "Todos los tipos" selected
            currentFilterCriteria.setIncidentTypeFilter(-1);
        } else if (tiposList != null && typePosition <= tiposList.size()) {
            // Get the idExterno of the selected tipo
            IncidenciaTipo selectedTipo = tiposList.get(typePosition - 1);
            currentFilterCriteria.setIncidentTypeFilter(selectedTipo.getIdExterno());
        } else {
            currentFilterCriteria.setIncidentTypeFilter(-1);
        }
        
        // Owner filter - store the selected owner name for filtering (only if visible)
        if (showOwnerFilter) {
            int ownerPosition = spinnerOwner.getSelectedItemPosition();
            if (ownerPosition == 0 || ownersList == null) {
                // "Todos los propietarios" selected
                currentFilterCriteria.setOwnerName("");
            } else if (ownerPosition <= ownersList.size()) {
                // Store the selected owner name (not position)
                String selectedOwnerName = ownersList.get(ownerPosition);
                currentFilterCriteria.setOwnerName(selectedOwnerName);
            } else {
                currentFilterCriteria.setOwnerName("");
            }
        } else {
            // If owner filter is not visible, ensure it's cleared
            currentFilterCriteria.setOwnerName("");
        }
        
        currentFilterCriteria.setWithElementOnly(cbWithElementOnly.isChecked());
    }

    private void setupFilterDot() {
        if (filterMenuItem != null && filterMenuItem.getActionView() != null) {
            View actionView = filterMenuItem.getActionView();
            
            filterDot = actionView.findViewById(R.id.vFilterDot);
            
            // Set click listener for the custom action view
            actionView.setOnClickListener(v -> showFilterDialog());
            
            // Update initial state
            updateFilterDot();
        }
    }

    private void updateFilterDot() {
        if (filterDot != null) {
            boolean hasActiveFilters = currentFilterCriteria != null && currentFilterCriteria.hasActiveFilters();
            filterDot.setVisibility(hasActiveFilters ? View.VISIBLE : View.GONE);
        }
    }

    // Recibo el broadcast cuando se actualiza una incidencia
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventOnIncidenciaAsignada(OnIncidenciaAsignada event) {
        adapter.actualizarIncidencia(event.incidencia);
    }
}
