package com.movisat.ecosat;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.os.Vibrator;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.movisat.database.DBElemento;
import com.movisat.database.DBMotivos;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.InfoSustituir;
import com.movisat.database.Motivos;
import com.movisat.database.Tags;
import com.movisat.events.OnReadedTag;
import com.movisat.log.Logg;
import com.movisat.managers.TagReaderManager;
import com.movisat.managers.UHFManager;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.synchronize.DBSynchro;
import com.movisat.tags.ITag;
import com.movisat.use_case.TagSendSensor;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Date;
import java.util.List;

/**
 * Created by dsanchez on 09/02/2018.
 */

public class RetirarElemActivity extends BaseActivity {

    private static final String TAG = "RETIRAR";
    private static RetirarElemActivity instance = null;

    private static final int ESTADO_SIN_INICIAR = 0;
    private static final int ESTADO_ESPERANDO_RETIRADO = 1;
    private static final int ESTADO_ESPERANDO_CONFIRMACION = 5;

    private static final int TAG_VIBRATION_MS = 100;
    private static final boolean DEBUG = false;

    private int estado_activity = ESTADO_SIN_INICIAR;
    private Elemento elementoRetirado = null;
    private Tags tagRetirado = null;
    private boolean procesandoTag = false;
    private boolean baja = false;

    private LinearLayout layoutEstadoRetirar;
    private CheckBox checkBaja;
    private ImageView imgEstadoRetirar;
    private TextView textEstadoRetirar;
    private EditText editMatriculaRetirado;
    private TextView textAddMatricula;
    private LinearLayout layoutBotonComprobarMatricula;
    private Button btnComprobarMatricula;
    private LinearLayout layoutTagRetirado;
    private TextView textTagRetirado;
    private Button btnVolver;
    private Button btnAceptar;
    private Button leerTag = null;
    private Tags tag = null;
    private String motivoSeleccionado = null;

    public static RetirarElemActivity getInstance() {
        return instance;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            instance = this;
            baja = false;

            setContentView(R.layout.retirar_depositar_elem_layout);

            // Componentes de la UI para indicar la fase del proceso
            layoutEstadoRetirar = (LinearLayout) findViewById(R.id.layoutEstado);
            imgEstadoRetirar = (ImageView) findViewById(R.id.imgEstado);
            textEstadoRetirar = (TextView) findViewById(R.id.textEstado);
            textAddMatricula = (TextView) findViewById(R.id.textAddMatricula);

            // Componentes de la UI para mostrar la información del elemento leído
            editMatriculaRetirado = (EditText) findViewById(R.id.editMatricula);
            textTagRetirado = (TextView) findViewById(R.id.textTag);
            leerTag = (Button) findViewById(R.id.leerTag);
            checkBaja = findViewById(R.id.baja);

            leerTag.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    updateTag();
                }
            });

            // Layout de botón de comprobar e información de tag (para mostrarlo y ocultarlos)
            layoutBotonComprobarMatricula = (LinearLayout) findViewById(R.id.layoutBotonComprobarMatriculaCrearElemento);
            layoutTagRetirado = (LinearLayout) findViewById(R.id.layoutTag);

            // Botones de la UI
            btnComprobarMatricula = (Button) findViewById(R.id.btnComprobarMatriculaCrearElemento);
            btnVolver = (Button) findViewById(R.id.btnVolver);
            btnAceptar = (Button) findViewById(R.id.btnAceptar);

            addListeners();

            // Se establecen los estados iniciales de la Activity
            setEstadoActivity(ESTADO_SIN_INICIAR);
            setEstadoActivity(ESTADO_ESPERANDO_RETIRADO);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }


    /**
     * Añade los listeners para las distintas funciones de la UI.
     */
    private void addListeners() {

        editMatriculaRetirado.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                // Se habilita/deshabilita el botón de comprobar matrícula según haya o no texto
                btnComprobarMatricula.setEnabled(charSequence.length() > 0);
                btnComprobarMatricula.setBackgroundResource(charSequence.length() > 0 ? R.drawable.bg_key : R.color.gris);
            }

            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void afterTextChanged(Editable editable) {
            }
        });

        btnComprobarMatricula.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                comprobarMatricula();
            }
        });

        btnVolver.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        btnAceptar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (saveChanges()) {
                    MainActivity.getInstance().showMessage(getString(R.string.retirado_ok));
                    finish();
                } else {
                    MainActivity.getInstance().showMessage(R.string.retirado_error, Toast.LENGTH_LONG);
                }
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Logg.info(TAG + "-" + getActivityName(), "[onKeyDown] keyCode: " + keyCode);
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            //fotos.clear();
            //UHFManager.get().release();
            finish();
        }

        return super.onKeyDown(keyCode, event);
    }


    /**
     * Toma la referencia del elemento que va a ser depositado y su tag y muestra sus datos en la UI.
     *
     * @param tag      Tag del elemento depositado.
     * @param elemento Elemento depositado.
     */
    private void setElementoRetirado(Tags tag, Elemento elemento) {
        editMatriculaRetirado.setText(tag.getMatricula());
        textTagRetirado.setText(tag.getTag());
        elementoRetirado = elemento;
        tagRetirado = tag;
    }

    public void bajaSeleccionado(View view) {
        baja = !baja;
        if (baja) displayMotivos();
    }


    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }


    @Override
    protected void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }

    private void comprobarMatricula() {

        try {
            String matricula = editMatriculaRetirado.getText().toString().trim();

            if (matricula.equals("")) {
                MainActivity.getInstance().showMessage("Introduzca una matrícula");
                return;
            }

            // Se obtiene el tag a partir de la matrícula
            DBTags dbTags = new DBTags();
            Tags tag = dbTags.getByMatricula(matricula, MainActivity.getInstance().getEmpresa());
            dbTags.close();

            if (tag == null) {
                MainActivity.getInstance().showMessage("No existe ningún elemento con esta matrícula");
                return;
            }

            DBElemento dbElemento = new DBElemento();
            Elemento elemento = dbElemento.getByIdExterno(tag.getIdExternoElemento(), MainActivity.getInstance().getEmpresa());
            dbElemento.close();

            if (elemento == null) {
                MainActivity.getInstance().showMessage("No existe ningún elemento con esa matrícula");
                return;
            }

            processElementoRetirado(tag, elemento);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Esta función está suscrita al EventBus y recibe de tags leídos por el lector Bluetooth.
     *
     * @param event Contiene el tag leído por el lector.
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRfidTagRead(OnReadedTag event) {
        try {
            // Se obtiene el tag leído de la base de datos
            DBTags dbTags = new DBTags();
            Tags tag = dbTags.getByTag(event.tag, MainActivity.getInstance().getEmpresa());
            dbTags.close();

            // Se procesa el tag leído
            tagRead(tag, event.tag);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Procesa el tag indicado en función del estado actual de la Activity (esperando o no tags)
     * y del elemento esperado (retirado o depositado).
     *
     * @param tag Tag que va a ser procesado.
     */
    private void tagRead(Tags tag, ITag iTag) {
        Logg.info(TAG, "[tagRead] iTag: " + iTag.get() + " - tag: " + tag.toString());

        try {
            procesandoTag = false;
            // Se hace vibrar el dispositivo ante la lectura de un tag
            try {
                Vibrator v = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
                v.vibrate(TAG_VIBRATION_MS);
            } catch (Exception e) {
                MyLoggerHandler.getInstance().error(e);
                e.printStackTrace();
            }

            // Si ya se ha leído el elementos retirado, no se aceptan más lecturas
            if (estado_activity > ESTADO_ESPERANDO_RETIRADO) {
                if (DEBUG)
                    Toast.makeText(this, "(Debug) Ya no se aceptan más lecturas de tags.", Toast.LENGTH_SHORT).show();
                return;
            }

            // Si la lectura ocurre antes de completar la anterior (por ejemplo, si se ha mostrado
            // un cuadro de diálogo), se indica y se ignora la lectura actual.
            if (procesandoTag) {
                MainActivity.getInstance().showMessage(R.string.no_se_puede_procesar);
                return;
            }

            TagSendSensor.execute(iTag, tag);

            // No se puede trabajar con un tag que no está en la base de datos
            if (tag == null)
                return;


            procesandoTag = true;

            // Se obtiene el elemento asociado al tag
            DBElemento dbElemento = new DBElemento();
            Elemento elemento = dbElemento.getElementoByTag(tag);
            dbElemento.close();

            if (elemento == null)
                MainActivity.getInstance().showMessage(
                        getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()),
                        Toast.LENGTH_LONG);

            editMatriculaRetirado.setText(elemento.getMatricula());

            switch (estado_activity) {

                case ESTADO_SIN_INICIAR:
                case ESTADO_ESPERANDO_CONFIRMACION:
                    procesandoTag = false;
                    break;

                case ESTADO_ESPERANDO_RETIRADO:
                    processElementoRetirado(tag, elemento);
                    break;
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }


    /**
     * Comprueba si el elemento es válido para ser retirado y realiza las acciones adecuadas.
     * El elemento a retirar debe existir y estar activo (si está inactivo, no tendría sentido
     * retirarlo)
     *
     * @param tag      Tag del elemento.
     * @param elemento Elemento asociado al tag.
     */
    private void processElementoRetirado(Tags tag, Elemento elemento) {
        // El elemento a retirar debe existir y estar activo (si está inactivo, no tendría sentido retirarlo)

        if (elemento == null) {

            // No se ha encontrado elemento -> No hacer nada
            MainActivity.getInstance().showMessage(getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()), Toast.LENGTH_LONG);
            procesandoTag = false;

        } else if (elemento.getEstado() == Elemento.ESTADO_ACTIVO) {

            // Elemento activo -> Se añade el elemento y se avanza a la fase de confirmación
            setElementoRetirado(tag, elemento);
            setEstadoActivity(ESTADO_ESPERANDO_CONFIRMACION);

        } else {

            // Elemento inactivo -> No hacer nada
            MainActivity.getInstance().showMessage(R.string.elemento_inactivo, Toast.LENGTH_LONG);
            procesandoTag = false;
        }
    }


    /**
     * Cambia el estado de la Activity y modifica los objetos y la UI en función del nuevo estado.
     *
     * @param estado Estado que tomará la Activity.
     */
    private void setEstadoActivity(int estado) {

        estado_activity = estado;

        switch (estado_activity) {

            case ESTADO_SIN_INICIAR:
                // Se establece la Activity a su estado inicial
                editMatriculaRetirado.setFocusable(true);
                editMatriculaRetirado.setText("");
                layoutBotonComprobarMatricula.setVisibility(View.VISIBLE);
                layoutTagRetirado.setVisibility(View.GONE);
                btnComprobarMatricula.setBackgroundResource(R.color.gris);
                btnComprobarMatricula.setEnabled(false);
                btnAceptar.setBackgroundResource(R.color.gris);
                btnAceptar.setEnabled(false);
                break;

            case ESTADO_ESPERANDO_RETIRADO:
                // Se establece la Activity al estado de espera de elemento a retirar
                layoutEstadoRetirar.setBackgroundResource(R.color.gris);
                textEstadoRetirar.setText(R.string.escanear_elem_retirado_o_matricula);
                imgEstadoRetirar.setImageResource(R.drawable.ic_retirar_black_60dp);
                break;

            case ESTADO_ESPERANDO_CONFIRMACION:
                // Se establece la Activity al estado de espera de confirmación
                layoutEstadoRetirar.setBackgroundResource(R.color.theme_principal);
                textEstadoRetirar.setText(R.string.confirmar_retirada);
                imgEstadoRetirar.setImageResource(R.drawable.ic_check_black_60dp);
                editMatriculaRetirado.setFocusable(false);
                layoutBotonComprobarMatricula.setVisibility(View.GONE);
                layoutTagRetirado.setVisibility(View.VISIBLE);
                btnAceptar.setBackgroundResource(R.drawable.bg_key);
                btnAceptar.setEnabled(true);
                break;
        }

        procesandoTag = false;
    }


    /**
     * Retira el elemento seleccionado. Se ejecutan los cambios pertinentes, se guardan en la
     * base de datos local y se mandan a la bandeja de salida.
     *
     * @return True si ha ido bien, False en otro caso.
     */
    private boolean saveChanges() {

        boolean ret = false;

        try {
            if (elementoRetirado == null) return false;

            DBElemento dbElemento = new DBElemento();

            // Se manda el elemento a reparación
            elementoRetirado.setEstado(Elemento.ESTADO_TALLER);

            if (baja) {
                elementoRetirado.setEstado(Elemento.ESTADO_BAJA);
            }

            elementoRetirado.setPosition(0.0, 0.0);
            dbElemento.update(elementoRetirado);
            dbElemento.close();

            // Se abre la bandeja de salida para mandar al servidor los elementos modificados/creados
            DBPacket dbPacket = new DBPacket();

            InfoSustituir data = new InfoSustituir(elementoRetirado, null, new Date());

            if (baja && motivoSeleccionado != null) {
                Motivos motivo = new DBMotivos().getByNombre(motivoSeleccionado);
                data.setMotivo(motivo.getId());
            }

            dbPacket.insert(new Packet(Packet.ELEMENTO_RETIRAR, Packet.PRIORIDAD_NORMAL, data));

            dbPacket.close();

            //sincro
            if (MainActivity.getInstance().isNetworkAvailable()) {
                if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                    DBSynchro.getInstance().forceSync();
                }
            }

            // Se actualiza el mapa
            MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

            ret = true;

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return ret;
    }

    private void displayMotivos() {
        AlertDialog.Builder builderSingle = new AlertDialog.Builder(this);
        builderSingle.setIcon(R.drawable.ic_launcher);
        builderSingle.setTitle("Seleccione el motivo de la baja");

        DBMotivos dbMotivos = new DBMotivos();
        List<Motivos> motivos = dbMotivos.getAll();

        final ArrayAdapter<String> arrayAdapter = new ArrayAdapter<String>(this,
                android.R.layout.select_dialog_singlechoice);
        for (Motivos motivo : motivos) {
            arrayAdapter.add(motivo.getNombre());
        }

        builderSingle.setNegativeButton("Cancelar", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        builderSingle.setAdapter(arrayAdapter, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                String strName = arrayAdapter.getItem(which);
                AlertDialog.Builder builderInner = new AlertDialog.Builder(RetirarElemActivity.getInstance());
                builderInner.setMessage(strName);
                builderInner.setTitle("El motivo seleccionado es");
                builderInner.setPositiveButton("Aceptar", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        motivoSeleccionado = strName;
                        dialog.dismiss();
                    }
                });
                builderInner.setNegativeButton("Atrás", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        runOnUiThread(() -> checkBaja.setActivated(false));
                        dialog.dismiss();
                    }
                });
                builderInner.show();
            }
        });
        builderSingle.show();
    }

    private void updateTag() {
        Logg.info(TAG, "[updateTag] Tag == null: " + (tag == null));
        if (tag == null) {

            // Comprobar si estoy utilizando el smartphone U9000
            if (Build.MODEL.equals("PDA")) {
                Logg.info(TAG, "[updateTag] U9000 - Leyendo 134 o UHF");
                TagReaderManager.read134orUHF((buffer, size) -> onReaded134TagData(buffer, size));
            }

            //leer tag
            else if (UHFManager.get().isReading()) {
                UHFManager.get().stopReadLoop();
            } else {
                // Se inicia la lectura UHF si el usuario está identificado en la aplicación
                MainActivity mainActivity = MainActivity.getInstance();
                if (mainActivity != null && mainActivity.getUsuario() > 0) {
                    UHFManager.get().readSingleTag(3000);
                }
            }
        }
    }


}
