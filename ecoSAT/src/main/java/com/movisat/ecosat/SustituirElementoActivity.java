package com.movisat.ecosat;

import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.database.Cursor;
import android.location.Location;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.cursoradapter.widget.SimpleCursorAdapter;

import com.movisat.database.DBElemento;
import com.movisat.database.DBMunicipios;
import com.movisat.database.DBProvincias;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoSustitucion;
import com.movisat.events.onChangeLocation;
import com.movisat.fragment.GestionElementos;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.zip.GZIPInputStream;


public class SustituirElementoActivity extends BaseActivity {
    private static SustituirElementoActivity instance = null;
    private long timeCallApi = 0;

    final int DISTANCIA = 30;
    EditText numeroElementoRetirado, numeroElementoDepositado;
    Spinner spinnerMunicipios;
    Spinner spinnerProvincias;
    String municipio;
    Switch switchButton;
    TextView textInfo = null;
    TextView titulo;
    Button btnProcesa = null;
    String stringMunicipio, elemento_nombre;
    DBProvincias dbProvincias;
    int elemento_id = 0, elemento_id_externo = 0;
    TextView lblTituloSwitch;
    String tipoSoft = "";
    String nombre_elemento_depositado = "";
    int codido_elemento_depositado = 0;

    public static final int CAMACHO_JEFE = 202;
    private asyncGIS syncro = null;
    private ProgressDialog pDialog = null;

    private String SIN_CONEXION_GIS = "Sin conexión al servidor GIS";
    private boolean isLoadingMunicipio = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        instance = this;

        setContentView(R.layout.sustituir_elemento_layout);

        OnClickListener clickProcesaListener = null;

        elemento_id = getIntent().getIntExtra("elem_id", 0);
        elemento_id_externo = getIntent().getIntExtra("elem_idext", 0);
        elemento_nombre = getIntent().getStringExtra("elem_nombre");

        numeroElementoRetirado = (EditText) findViewById(R.id.textCensoRetirado);
        numeroElementoRetirado.setText(elemento_nombre);

        numeroElementoDepositado = (EditText) findViewById(R.id.textCensoDepositado);
        spinnerMunicipios = (Spinner) findViewById(R.id.sMunicipios);
        spinnerProvincias = (Spinner) findViewById(R.id.sProvincias);
        lblTituloSwitch = (TextView) findViewById(R.id.tvTituloSwitch);
        switchButton = (Switch) findViewById(R.id.switchMunicipios);
        titulo = (TextView) findViewById(R.id.lblTitulo);


        tipoSoft = Config.getInstance().getValue("tipoSoft", "0");

        btnProcesa = (Button) findViewById(R.id.btnAceptar);
        Button btnVolver = (Button) findViewById(R.id.btnVolver);

        btnVolver.setOnClickListener(new clickVolverMapa());

        clickProcesaListener = new ClickProcesar();
        btnProcesa.setOnClickListener(clickProcesaListener);

        dbProvincias = new DBProvincias();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        //setMunicipio();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(onChangeLocation event) {

        // Cada minuto se llama al GIS para obtener el municipio
        if (System.currentTimeMillis() - timeCallApi > 60000) {
            if (timeCallApi > 0)
                setMunicipio(event.location.getLatitude(), event.location.getLongitude());
            timeCallApi = System.currentTimeMillis();
        }
    }

    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }

    private void setMunicipio(double latitud, double longitud) {

        try {
            if (latitud == 0 && longitud == 0) {
                latitud = Double.parseDouble(Config.getInstance().getValueUsuario("miLat",
                        "0"));
                longitud = Double.parseDouble(Config.getInstance().getValueUsuario("miLon",
                        "0"));
            }
        } catch (Exception e) {
            MyLoggerHandler.getInstance().info("Error en setMunicipio - " + e.getMessage());
        }


        try {
            if (syncro != null)
                syncro.cancel(true);


            /*ClientWebSvc sweb = new ClientWebSvc();
            sweb.refreshToken();*/

            syncro = new asyncGIS(latitud, longitud);
            syncro.execute();

        } catch (Exception e) {
            Log.e("", "error: " + e.getMessage());
        }

    }


    private void setMunicipio() {
        setMunicipio(0, 0);
    }

    private void setProvincias() {
        try {

            Cursor cursor = dbProvincias.getAllCursor();
            String[] from = new String[]{"provincia"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(this,
                    android.R.layout.simple_spinner_item, cursor, from, to, 0);

            // set layout for activated adapter
            dataAdapter
                    .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            spinnerProvincias.setAdapter(dataAdapter);
            OnItemSelectedListener selectedProvincia = new SelectedProvincia();
            spinnerProvincias.setOnItemSelectedListener(selectedProvincia);

            int indexPos = Integer.valueOf(Config.getInstance().getValueUsuario(
                    "SelectedProvincia", "-1"));
            if (indexPos > -1)
                spinnerProvincias.setSelection(indexPos);

        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public class SelectedMunicipio implements OnItemSelectedListener {

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int i, long l) {
            stringMunicipio = ((TextView) view).getText().toString();
            titulo.setText(getString(R.string.niveles) + " " + stringMunicipio);
            textInfo = ((TextView) view);
        }

        @Override
        public void onNothingSelected(AdapterView<?> adapterView) {
        }
    }

    public class SelectedProvincia implements OnItemSelectedListener {

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int pos,
                                   long id) {
            Cursor c = ((SimpleCursorAdapter) parent.getAdapter()).getCursor();
            c.moveToPosition(pos);

            DBMunicipios managerMunicipios = new DBMunicipios();
            int idProvincia = c.getInt(0);
            Cursor cursor = managerMunicipios.getAllCursorBy(idProvincia);
            String[] from = new String[]{"municipio"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(instance,
                    android.R.layout.simple_spinner_item, cursor, from, to, 0);

            // set layout for activated adapter
            dataAdapter
                    .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            spinnerMunicipios.setAdapter(dataAdapter);
            managerMunicipios.close();

            //int indexOf = Integer.valueOf(Config.getInstance().getValueUsuario(
            //"SelectedMunicipio", "-1"));

            int indexOf = -1;
            cursor.moveToFirst();
            for (int i = 0; i < cursor.getCount(); i++) {
                String temp = cursor.getString(1);
                if (temp.contentEquals(municipio.toUpperCase())) {
                    indexOf = i;
                    break;
                }
                cursor.moveToNext();
            }

            if (indexOf > -1) {
                try {
                    if (indexOf < spinnerMunicipios.getCount())
                        spinnerMunicipios.setSelection(indexOf);
                } catch (Exception ex) {

                }
            }

            Config.getInstance().setValueUsuario("SelectedProvincia",
                    String.valueOf(pos));
        }

        @Override
        public void onNothingSelected(AdapterView<?> arg0) {
            // TODO Auto-generated method stub

        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        ClientWebSvc sweb = new ClientWebSvc();
        try {
            sweb.refreshToken();
        } catch (Exception e) {
            e.printStackTrace();
        }
        numeroElementoDepositado.setText(null);
        setMunicipio();

    }

    @Override
    public void finish() {
        super.finish();

        if (dbProvincias != null)
            dbProvincias.close();

    }

    private class ClickProcesar implements OnClickListener {
        @Override
        public void onClick(View v) {

            String municipioNew = municipio;
            if ((municipioNew.equals("") || tipoSoft.equals("202")) && textInfo != null) {
                municipioNew = textInfo.getText().toString();
                Config.getInstance().setValueUsuario(
                        "SelectedMunicipio",
                        String.valueOf(spinnerMunicipios
                                .getSelectedItemPosition()));
            }
            // Buscar elemento a partir del nombre.
            DBElemento dbElemento = new DBElemento();
            Elemento elemento = dbElemento.getElementoBy(municipioNew + " "
                    + numeroElementoDepositado.getText(), MainActivity.getInstance()
                    .getEmpresa());
            dbElemento.close();

            boolean isEnRadio = false;


            if (elemento != null) {
                nombre_elemento_depositado = elemento.getNombre();
                codido_elemento_depositado = elemento.getIdExterno();

                // Buscar elemento en un radio de 50 metros.
                GestionElementos.GPSInfo infoGPS = GestionElementos.ultGpsPos;

                if (infoGPS != null) {
                    float distancia = GetDistanceBy(
                            infoGPS.getPosition().latitude,
                            infoGPS.getPosition().longitude,
                            elemento.getPosition().latitude,
                            elemento.getPosition().longitude);

                    if (distancia <= DISTANCIA) { // Avisamos que no esta dentro de la
                        //  distancia y salimos.
                        isEnRadio = true;
                    }
                }
            } else {
                if (numeroElementoDepositado.getText().length() > 0)
                    nombre_elemento_depositado = municipioNew + " " + numeroElementoDepositado.getText();

            }

            String textoAviso = "¿Desea sustituir el elemento " + numeroElementoRetirado.getText();
            textoAviso += nombre_elemento_depositado != "" ? " por el elemento " + municipioNew + " "
                    + numeroElementoDepositado.getText() + "?" : " por uno nuevo? (el servidor generará un nº de elemento consecutivo)";


            new InfoDialog(getInstance(), getString(R.string.atencion),
                    textoAviso, InfoDialog.ICON_QUESTION,
                    new OnInfoDialogSelect() {

                        @Override
                        public void onSelectOption(int option) {

                            if (option == InfoDialog.BUTTON_YES) {

                                DBPacket dbPacket = new DBPacket();
                                ElementoSustitucion s = new ElementoSustitucion(elemento_id_externo, nombre_elemento_depositado, codido_elemento_depositado);

                                dbPacket.insert(new Packet(
                                        Packet.ELEMENTO_SUSTITUIR_CON_BAJA, Packet.PRIORIDAD_NORMAL, s));
                                dbPacket.close();
                                finish();

                            } else {
                                return;
                            }

                        }

                    }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                    InfoDialog.POSITION_CENTER).show();

        }
    }


    private class MyDialogNivelLlenado implements OnInfoDialogSelect {

        private Elemento elemento;

        public MyDialogNivelLlenado(Elemento elemento) {
            this.elemento = elemento;
        }

        @Override
        public void onSelectOption(int option) {
            if (option == InfoDialog.BUTTON_ACCEPT) {

                Toast.makeText(
                        instance,
                        "Enviamos sustitución del elemento",
                        Toast.LENGTH_SHORT).show();

                finish();

            }
        }

    }

    public float GetDistanceBy(double Lat1, double Lon1, double Lat2,
                               double Lon2) {
        Location locationA = new Location("point A");
        locationA.setLatitude(Lat1);
        locationA.setLongitude(Lon1);
        Location locationB = new Location("point B");
        locationB.setLatitude(Lat2);
        locationB.setLongitude(Lon2);
        float distance = locationA.distanceTo(locationB);
        return distance;
    }

    public static SustituirElementoActivity getInstance() {

        if (instance == null)
            instance = new SustituirElementoActivity();
        return instance;
    }


    private class clickVolverMapa implements OnClickListener {
        @Override
        public void onClick(View v) {
            finish();
        }
    }

    class asyncGIS extends AsyncTask<Void, String, JSONArray> {


        private double lat, lon;
        JSONArray jsonData = null;

        public asyncGIS(double latitud, double longitud) {
            isLoadingMunicipio = true;
            this.lat = latitud;
            this.lon = longitud;
        }

        @Override
        protected void onPreExecute() {
            // para el progress dialog
            pDialog = new ProgressDialog(SustituirElementoActivity.getInstance());
            pDialog.setMessage("Conectando al servidor, por favor espere.");
            //pDialog.setIndeterminate(true);
            pDialog.setCancelable(false);
            pDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Cancelar y reintentar.", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    pDialog.cancel();
                    pDialog.dismiss();
                    isLoadingMunicipio = false;
                }
            });

            pDialog.show();
        }


        @Override
        protected JSONArray doInBackground(Void... strings) {

            // DINIGO - Mantis 0005119 - Si no tiene internet no realizamos la comprobación ya que genera que el sistema cierre la aplicación por el retraso.
            if (!MainActivity.getInstance().isNetworkAvailable()) return null;

            ClientWebSvc sweb = new ClientWebSvc();

            String url = Config.getInstance().getValue("webSvc", "");
            if (url.equals(""))
                return null;

            url = url + "/api/gis/calle/" + this.lat + "/" + this.lon;

            HttpPost post = new HttpPost(url);

            // Preparo la llamada para recibir datos
            post.setHeader("User-Agent", "java-Android");
            post.setHeader("Content-type", "application/json");
            post.setHeader("Accept-encoding", "gzip");
            post.setHeader("Authorization", "Bearer " + sweb.token);

            // Establezco timeout alto
            HttpParams parametros = new BasicHttpParams();

            int timeoutConnection = 10000;
            HttpConnectionParams.setConnectionTimeout(parametros, timeoutConnection);
            int timeoutSocket = 10000;
            HttpConnectionParams.setSoTimeout(parametros, timeoutSocket);
            DefaultHttpClient httpClient = new DefaultHttpClient(parametros);
            HttpResponse response;

            boolean salir = false;

            // lo intento 4 veces, en caso contrario sigo sin indicar el municipio, tendrá que ponerlo manualmente.
            for (int i = 0; i < 4 && !salir; i++) {
                try {

                    response = httpClient.execute(post);
                    int result = response.getStatusLine().getStatusCode();
                    MyLoggerHandler.getInstance().info(String.valueOf(result));
                    //if(i < 2) result = 401;

                    switch (result) {
                        case 200: // Respuesta correcta

                            InputStream inStream = response.getEntity().getContent();

                            Header contentEncoding = response.getFirstHeader("Content-Encoding");
                            if (contentEncoding != null && contentEncoding.getValue().equalsIgnoreCase("gzip")) {
                                inStream = new GZIPInputStream(inStream);
                            }

                            String strResponse = sweb.convertStreamToString(inStream);

                            try {
                                // En principio intento recuperar los datos como un array JSON si falla lo intento como JSON
                                jsonData = new JSONArray(strResponse);
                                salir = true;

                            } catch (JSONException e) {

                                try {
                                    // Recupero la respuesta JSON
                                    jsonData = new JSONArray()
                                            .put(new JSONObject(strResponse));

                                } catch (Exception ex) {
                                    MyLoggerHandler.getInstance().error(
                                            ex);
                                }

                                salir = true;
                            }
                            break;

                        case 401: //<<El token ya no es valido
                            /*synchronized (this){
                                sweb.token = "";
                                sweb.refreshToken();
                            }*/
                            MyLoggerHandler.getInstance().info("[TOKEN]: En ProcesarElemento se ha expirado el token.");
                            break;


                        default:
                            try {

                                Thread.sleep(5000);

                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                    }


                } catch (UnsupportedEncodingException e) {
                    MyLoggerHandler.getInstance().error(
                            e);
                } catch (ClientProtocolException e) {
                    MyLoggerHandler.getInstance().error(
                            e);
                } catch (IOException e) {
                    if (e instanceof HttpHostConnectException) {
                        try {
                            Thread.sleep(2000);

                            // si se cancela el progressbar, y pilla intentando conectar al gis
                            // cancelo para que salga.
                            if (!isLoadingMunicipio)
                                break;

                        } catch (InterruptedException e1) {
                            MyLoggerHandler.getInstance().error(
                                    e1);
                        }
                    }

                } catch (Exception e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            }


            return jsonData;
        }

        @Override
        protected void onPostExecute(JSONArray result) {

            try {
                if (result != null)
                    municipio = result.getJSONObject(0).get("Municipio").toString();
            } catch (JSONException e) {
                MyLoggerHandler.getInstance().error(
                        e);
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(
                        e);
            }

            try {
                if (municipio == null || result == null)
                    municipio = SIN_CONEXION_GIS;

                titulo.setText("Municipio: " + municipio);
                spinnerMunicipios.setVisibility(View.GONE);
                spinnerProvincias.setVisibility(View.GONE);

                if (result == null)
                    setProvincias();

                int soft = Integer.parseInt(tipoSoft);
                if (!municipio.equals("") && !municipio.equals(SIN_CONEXION_GIS) && (soft != CAMACHO_JEFE && soft != 223 && soft != 226 && soft != 219)) {

                    lblTituloSwitch.setVisibility(View.GONE);
                    switchButton.setVisibility(View.GONE);

                } else {

                    lblTituloSwitch.setVisibility(View.VISIBLE);
                    switchButton.setVisibility(View.VISIBLE);
                    switchButton.setChecked(false);

                    switchButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {

                        @Override
                        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                            if (isChecked) {
                                spinnerMunicipios.setVisibility(View.VISIBLE);
                                spinnerProvincias.setVisibility(View.VISIBLE);
                                setProvincias();
                                OnItemSelectedListener selectedMunicipio = new SelectedMunicipio();
                                spinnerMunicipios.setOnItemSelectedListener(selectedMunicipio);
                                textInfo = (TextView) spinnerMunicipios
                                        .getSelectedView();
                            } else {
                                spinnerMunicipios.setVisibility(View.GONE);
                                spinnerProvincias.setVisibility(View.GONE);
                                textInfo = null;
                                titulo.setText("Municipio: " + municipio);
                            }
                        }

                    });

                }
            } catch (Exception e) {
                MyLoggerHandler.getInstance().error(
                        e);
            }

            if (pDialog != null) {
                pDialog.dismiss();
                pDialog.cancel();
            }

            isLoadingMunicipio = false;

        }

        @Override
        protected void onCancelled() {
            Toast.makeText(SustituirElementoActivity.getInstance(), "Tarea cancelada!",
                    Toast.LENGTH_SHORT).show();
            pDialog.dismiss();
            pDialog.cancel();
            municipio = SIN_CONEXION_GIS;
        }
    }

}