package com.movisat.ecosat;

import android.content.res.Configuration;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.style.RelativeSizeSpan;
import android.view.KeyEvent;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.environment.Environment;
import com.movisat.adapter.InciTiposAdapter;
import com.movisat.database.DBIncidenciaTipo;
import com.movisat.database.IncidenciaTipo;
import com.movisat.fragment.GestionElementos;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import java.util.ArrayList;
import java.util.Collections;

public class FilterInciTiposActivity extends BaseActivity {
	private InciTiposAdapter tiposAdapter;
	private ListView listaTipos;
	private Button todos;
	private Button ninguno;
	private Button confirmar;
	private TextView tvTitulo;

	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.filter_inci_tipos_layout);
		try {
			listaTipos = findViewById(R.id.lvTipos);
			todos = findViewById(R.id.btTodos);
			ninguno = findViewById(R.id.btNinguno);
			confirmar = findViewById(R.id.btConfirmarFiltro);
			tvTitulo = findViewById(R.id.headerLayout);

			listaTipos.setOnItemClickListener(getOnItemClickListenerListaTipos());
			todos.setOnClickListener((v) -> selectAll(true));
			ninguno.setOnClickListener(v -> selectAll(false));
			confirmar.setOnClickListener((v) -> onConfirm());

			// Recupero todos los tipos
			DBIncidenciaTipo dbIncidenciaTipo = new DBIncidenciaTipo();
			ArrayList<IncidenciaTipo> tipos = dbIncidenciaTipo.getAll(MainActivity.getInstance().getEmpresa());
			dbIncidenciaTipo.close();

			if (tipos == null || tipos.isEmpty()) {
				Toast.makeText(this, getResources().getString(R.string.avisoNoHayIncidenciasTipos), Toast.LENGTH_LONG).show();
				finish();
			}

			tiposAdapter = new InciTiposAdapter(MainActivity.getInstance(), tipos);
			listaTipos.setAdapter(tiposAdapter);
			
			// Verificar estado inicial y marcar checkboxes correspondientes
			updateCheckboxStatesOnInit();
			
			// Actualizar contador
			updateCounter();
		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}
	}

	private OnItemClickListener getOnItemClickListenerListaTipos() {
		return (list, view, index, id) -> {
			IncidenciaTipo item = (IncidenciaTipo) list.getItemAtPosition(index);
			MyBroadCastManager.getInstance().sendBroadCastToggleVisibleTipoIncidencia(item.getIdExterno());
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
			updateCounter();
			refresh();
		};
	}

	private void selectAll(boolean isTodos) {
		for (int i = 0; i < listaTipos.getCount(); i++) {
			IncidenciaTipo item = (IncidenciaTipo) listaTipos.getItemAtPosition(i);
			MyBroadCastManager.getInstance().sendBroadCastSetVisibleTipoIncidencia(item.getIdExterno(), isTodos);
		}
		if (isTodos) {
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_all, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
		} else {
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_all, 0);
		}
		updateCounter();
		refresh();
	}

	private void refresh() {
		MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
		listaTipos.invalidateViews();
		tiposAdapter.notifyDataSetChanged();
	}

	private void updateCheckboxStatesOnInit() {
		int visibleCount = 0;
		int totalCount = listaTipos.getCount();
		
		// Contar cuántos tipos están visibles
		for (int i = 0; i < totalCount; i++) {
			IncidenciaTipo item = (IncidenciaTipo) listaTipos.getItemAtPosition(i);
			if (GestionElementos.isVisibleTipoIncidencia(item.getIdExterno())) {
				visibleCount++;
			}
		}
		
		// Marcar checkbox según el estado actual
		if (visibleCount == totalCount) {
			// Todos están seleccionados
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_all, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
		} else if (visibleCount == 0) {
			// Ninguno está seleccionado
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_all, 0);
		} else {
			// Selección parcial - sin marcar ningún checkbox
			todos.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_action_check_nothing, 0, 0, 0);
			ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_action_check_nothing, 0);
		}
	}

	private void updateCounter() {
		int visibleCount = 0;
		int totalCount = listaTipos.getCount();
		
		// Contar cuántos tipos están visibles
		for (int i = 0; i < totalCount; i++) {
			IncidenciaTipo item = (IncidenciaTipo) listaTipos.getItemAtPosition(i);
			if (GestionElementos.isVisibleTipoIncidencia(item.getIdExterno())) {
				visibleCount++;
			}
		}

		// Crear texto con contador
		String tituloBase = getResources().getString(R.string.txt_tipos);
		String contador = " (" + visibleCount + ")";
		String textoCompleto = tituloBase + contador;
		
		SpannableString spannableString = new SpannableString(textoCompleto);

		int inicioContador = tituloBase.length();
		int finContador = textoCompleto.length();
		spannableString.setSpan(new RelativeSizeSpan(0.9f), inicioContador, finContador, 0);
		
		tvTitulo.setText(spannableString);
	}

	private void onConfirm() {
		new InfoDialog(this, getString(R.string.atencion),
				getString(R.string.confirmar_seleccion), InfoDialog.ICON_QUESTION,
				new OnInfoDialogSelect() {
					@Override
					public void onSelectOption(int option) {
						if (option == InfoDialog.BUTTON_YES) {
							int vis = 0;
							int inv = 0;
							StringBuilder tipos = new StringBuilder();
							// Compruebo los tipos seleccionados
							for (int i = 0; i < listaTipos.getCount(); i++) {
								IncidenciaTipo item = (IncidenciaTipo) listaTipos.getItemAtPosition(i);
								// Compruebo la visibilidad del tipo
								if (GestionElementos.isVisibleTipoIncidencia(item.getIdExterno())) {
									if (tipos.length() == 0)
										tipos.append(item.getIdExterno());
									else
										tipos.append(",").append(item.getIdExterno());
									vis++;
								} else
									inv++;
							}
							// Guardo la configuracion del usuario
							if (vis == listaTipos.getCount())
								Config.getInstance().setValueUsuario("tiposInciVisibles", "todos");
							else if (inv == listaTipos.getCount())
								Config.getInstance().setValueUsuario("tiposInciVisibles", "ninguno");
							else
								Config.getInstance().setValueUsuario("tiposInciVisibles", tipos.toString());
							MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
							finish();
						}
					}
				}, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
				InfoDialog.POSITION_CENTER).show();
	}

	@Override
	public boolean onKeyDown(int keyCode, KeyEvent event) {
		if (keyCode == KeyEvent.KEYCODE_BACK) onConfirm();
		return super.onKeyDown(keyCode, event);
	}

	/**
	 * Este metodo se ejecuta cada vez que se gira la pantalla
	 */
	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
	}

}