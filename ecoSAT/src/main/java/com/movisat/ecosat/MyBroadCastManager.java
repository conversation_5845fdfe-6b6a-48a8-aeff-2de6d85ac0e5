package com.movisat.ecosat;

import android.content.Intent;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.util.Log;
import android.widget.Toast;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.bll.SensoresManager;
import com.movisat.database.DBElemento;
import com.movisat.database.Elemento;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaFoto;
import com.movisat.database.Tags;
import com.movisat.fragment.GestionElementos;
import com.movisat.tags.ITag;
import com.movisat.utilities.Utils;

public class MyBroadCastManager {

	private MyBroadCastManager() {

	}

	private static MyBroadCastManager instance = null;

	public static MyBroadCastManager getInstance() {
		if (instance == null)
			instance = new MyBroadCastManager();

		return instance;
	}

	/**
	 * Enviar mensaje para refrescar incidencia.
	 * 
	 * @param incidencia
	 */
	public void sendBroadCastRefreshIncidencia(Incidencia incidencia) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_UPDATE_INCIDENCIA_BR);
			intent.putExtra("item", incidencia);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Envia mensaje para refrescar el cluster.
	 */
	public void sendBroadCastRefreshCluster() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REFRESH_CLUSTER_BR);

			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Refrescar incidencias.
	 */
	public void sendBroadCastRefreshIncidencias() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REFRESH_INCIDENCIAS_BR);
			LocalBroadcastManager.getInstance(AppContext.getContext()).sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para aiadir incidencia de foto.
	 * 
	 * @param incidenciaFoto
	 */
	public void sendBroadCastAddIncidenciaFoto(IncidenciaFoto incidenciaFoto) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_ADD_INCIDENCIA_FOTO_BR);
			intent.putExtra("item", incidenciaFoto);

			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para cambiar el tipo de mapa.
	 * 
	 * @param TypeMap
	 */
	public void sendBroadCastChangeMapType(int TypeMap) {
		try {
			Intent intentMap = new Intent(
					GestionElementos.EVENT_GESTION_ELEMENTOS);
			intentMap.setAction(GestionElementos.MSG_SET_MAP_TYPE_BR);
			intentMap.putExtra("type", TypeMap);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intentMap);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para centrar la posiciin en el mapa.
	 * 
	 * @param isCenter
	 */
	public void sendBroadCastCenterPosition(boolean isCenter) {
		try {
			Intent intentMap = new Intent(
					GestionElementos.EVENT_GESTION_ELEMENTOS);
			intentMap.setAction(GestionElementos.MSG_CENTER_GPS_POS_BR);
			intentMap.putExtra("is-center", isCenter);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intentMap);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para activar el seguimiento GPS o desactivarlo.
	 * 
	 * @param enabled
	 */
	public void sendBroadCastEnabledSeguimientoGPS(boolean enabled) {
		try {
			Intent intentMap = new Intent(
					GestionElementos.EVENT_GESTION_ELEMENTOS);
			intentMap.setAction(GestionElementos.MSG_GPS_TRACK_BR);
			intentMap.putExtra("is-checked", enabled);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intentMap);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para centrar el mapa en una "posicion" a un determinado
	 * "zoom".
	 * 
	 * @param posicion
	 * @param zoom
	 */
	public void sendBroadCastCenterMapBy(LatLng posicion, int zoom,
			String title, boolean addMarker) {
		try {
			Intent intentMap = new Intent(
					GestionElementos.EVENT_GESTION_ELEMENTOS);
			intentMap.setAction(GestionElementos.MSG_CENTER_MAP_BR);
			intentMap.putExtra("position", posicion);
			intentMap.putExtra("zoom", zoom);
			intentMap.putExtra("title", title);
			intentMap.putExtra("addMarker", addMarker);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intentMap);

		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para marcar un estado de incidencia como visible.
	 * 
	 * @param idExterno
	 * @param isVisible
	 */
	public void sendBroadCastSetVisibleEstadoIncidencia(int idExterno,
			boolean isVisible) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_SET_VISIBLE_ESTADO_INCIDENCIA_BR);
			intent.putExtra("is-visible", isVisible);
			intent.putExtra("id-estado", idExterno);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Cambia la visibilidad de un estado de incidencia.
	 * 
	 * @param idExterno
	 */
	public void sendBroadCastToggleVisibleEstadoIncidencia(int idExterno) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_TOGGLE_VISIBLE_ESTADO_INCIDENCIA_BR);
			intent.putExtra("id-estado", idExterno);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para marcar un tipo de incidencia como visible.
	 * 
	 * @param idTipo
	 * @param isVisible
	 */
	public void sendBroadCastSetVisibleTipoIncidencia(int idTipo,
			boolean isVisible) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_SET_VISIBLE_TIPO_INCIDENCIA_BR);
			intent.putExtra("is-visible", isVisible);
			intent.putExtra("id-tipo", idTipo);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Cambia la visibilidad de un tipo de incidencia.
	 * 
	 * @param idTipo
	 */
	public void sendBroadCastToggleVisibleTipoIncidencia(int idTipo) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_TOGGLE_VISIBLE_TIPO_INCIDENCIA_BR);
			intent.putExtra("id-tipo", idTipo);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para refrescar el mapa.
	 */
	public void sendBroadCastRefreshMap() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REFRESH_MAP_BR);

			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para cambiar la visibilidad del modelo
	 * 
	 * @param idExterno
	 * @param isVisible
	 */
	public void sendBroadCastSetVisibleModeloElemento(int idExterno,
			boolean isVisible) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_SET_VISIBLE_MODELO_ELEMENTO_BR);
			intent.putExtra("id-modelo", idExterno);
			intent.putExtra("is-visible", isVisible);

			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}


	public void sendBroadCastLoadModels() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REFRESH_MODELS);

			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para cambiar la visibilidad del modelo.
	 * 
	 * @param idExterno
	 */
	public void sendBroadCastToggleVisibleModeloElemento(int idExterno) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_TOGGLE_VISIBLE_MODELO_ELEMENTO_BR);
			intent.putExtra("id-modelo", idExterno);

			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para quitar elemento del cluster.
	 * 
	 * @param idInterno
	 */
	public void sendBroadCastDeleteItemCluster(int idInterno) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_DELETE_ITEM_CLUSTER_BR);
			intent.putExtra("id-item", idInterno);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para añadir elemento al cluster.
	 * 
	 * @param elemento
	 */
	public void sendBroadCastInsertItemCluster(Elemento elemento) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_ADD_ITEM_CLUSTER_BR);
			intent.putExtra("item", elemento);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Actualiza elemento en el cluster.
	 * 
	 * @param elemento
	 */
	public void sendBroadCastUpdateItemCluster(Elemento elemento) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_UPDATE_ITEM_CLUSTER_BR);
			intent.putExtra("id-item", elemento.getId());
			intent.putExtra("item", elemento);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Cambia el click del mapa
	 * 
	 */
	public void sendBroadCastToggleClickMap() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_TOGGLE_CLICKMAP);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCreateMarkerCrearElemento() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CREATE_MARKER_CREAR_ELEMENTO);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCreateMarkerDepositarElemento() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CREATE_MARKER_DEPOSITAR_ELEMENTO);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCreateMarkerCrearIncidencia() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CREATE_MARKER_CREAR_INCIDENCIA);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCreateMarkerAsignarIncidencia() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CREATE_BUTTON_ASIGNAR_INCIDENCIA);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadRemoveMarkerCrearElemento() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REMOVE_MARKER_CREAR_ELEMENTO);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadRemoveMarkerDepositarElemento() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REMOVE_MARKER_DEPOSITAR_ELEMENTO);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadRemoveMarkerCrearIncidencia() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REMOVE_MARKER_CREAR_INCIDENCIA);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadRemoveMarkerAsignarIncidencia() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REMOVE_BUTTON_ASIGNAR_INCIDENCIA);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCrearElemento(Tags tag, ITag iTag) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CLICK_FLOAT_BUTTON_CREAR_ELEMENTO);
			intent.putExtra("tag", tag);
			intent.putExtra("iTag", iTag);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadDepositarElemento(Tags tag, ITag iTag) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CLICK_FLOAT_BUTTON_DEPOSITAR_ELEMENTO);
			intent.putExtra("tag", tag);
			intent.putExtra("iTag", iTag);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadDepositarElemento() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CLICK_FLOAT_BUTTON_DEPOSITAR_ELEMENTO);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCrearIncidencia() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CLICK_FLOAT_BUTTON_CREAR_INCIDENCIA);
			LocalBroadcastManager.getInstance(AppContext.getContext()).sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadAsignarIncidencia() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_CLICK_FLOAT_BUTTON_ASIGNAR_INCIDENCIA);
			LocalBroadcastManager.getInstance(AppContext.getContext()).sendBroadcastSync(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCastUpdateElemento(Elemento elemento) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_UPDATE_ELEMENTO);
			intent.putExtra("elemento", elemento);
			LocalBroadcastManager.getInstance(AppContext.getContext()).sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCastNivelLlenado(Elemento elemento, boolean versionJefe) {
		try {
			NivelLlenadoActivity.getInstance().setElemento(elemento);
			Intent intent = new Intent(MainActivity.getInstance(), NivelLlenadoActivity.class);
			intent.putExtra("version_jefe", versionJefe);

			MainActivity.getInstance().startActivity(intent);

		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCastLavado(Elemento elemento) {
		try {
			// enviamos evento de lavado por la API.
			SensoresManager.getInstance().sendSensorLavado(elemento);
			DBElemento dbElemento = new DBElemento();
			elemento.setFechaUltLavado(Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));
			dbElemento.update(elemento);
			dbElemento.close();
			Toast.makeText(
				ProcesarElementoActivity.getInstance(),
				MainActivity.getInstance().getString(R.string.elemento) + elemento.toString() +
				MainActivity.getInstance().getString(R.string.marcadoLavado), Toast.LENGTH_LONG).show();

		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	public void sendBroadCastIncidenciaElemento(Elemento elemento) {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_INCIDENCIA_ELEMENTO);
			intent.putExtra("elemento", elemento);
			LocalBroadcastManager.getInstance(AppContext.getContext())
					.sendBroadcast(intent);
		} catch (Throwable e) {
			e.printStackTrace();
		}
	}

	/**
	 * Enviar mensaje para cambiar el tipo de mapa.
	 */
	public void sendBroadCastRemoveMarkersFlota() {
		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REMOVE_MARKERS_FLOTA);
			LocalBroadcastManager.getInstance(AppContext.getContext()).sendBroadcast(intent);
		} catch (Throwable e) {
			Log.e("Error: ", e.getMessage());
		}
	}

	/*
	 * Enviar mensaje para mostrar una ruta en el mapa
	 */

	public void sendBroadCastRefreshHistoricoRuta(int rutaId, int tiempoParada) {

		try {
			Intent intent = new Intent(GestionElementos.EVENT_GESTION_ELEMENTOS);
			intent.setAction(GestionElementos.MSG_REFRESH_HISTORICO_RUTAS);
			intent.putExtra("rutaId", rutaId);
			intent.putExtra("tiempoParada", tiempoParada);
			LocalBroadcastManager.getInstance(AppContext.getContext()).sendBroadcast(intent);
			MainActivity.getInstance().setMenuLimpiarFlotaVisibility(true);
		} catch (Throwable e) {
			Log.e("Error: ", e.getMessage());
		}



    }
}
