package com.movisat.ecosat;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/**
 * Esta clase es necesaria para forzar el inicio de la app en background al reiniciarse
 * el dispositivo debido a que falla la primera vez el módulo de lectura de tags.
  */

public class RestartReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
       if (intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED)) {
           // Añado esto para evitar warning de seguridad
       }
    }
}