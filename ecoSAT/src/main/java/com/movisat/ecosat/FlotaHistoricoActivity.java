package com.movisat.ecosat;

import android.app.Dialog;
import android.app.ProgressDialog;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.TimePicker;
import android.widget.Toast;

import com.movisat.database.DBFlotaPosiciones;
import com.movisat.database.FlotaPosicionesHistorico;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Utils;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.zip.GZIPInputStream;


public class FlotaHistoricoActivity extends  BaseActivity{

    private static FlotaHistoricoActivity instance;
    public Dialog dialog;
    public View vistaDialog;
    private DatePicker dp;
    private TimePicker tp;
    private int movilId=0;
    private Timestamp fecha;
    private EditText desde, hasta, parada;

    public static FlotaHistoricoActivity getInstance() {
        return instance;
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            instance = this;
            setContentView(R.layout.flota_historico_layout);

            desde = (EditText) findViewById(R.id.editFechaDesde);
            hasta = (EditText) findViewById(R.id.editFechaHasta);
            parada = (EditText) findViewById(R.id.editTiempoParada);

            // evento botón aceptar
            findViewById(R.id.btnAceptar).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    int tiempoParada=0;

                    try{

                        tiempoParada = Integer.parseInt(parada.getText().toString());

                    } catch (Throwable e){

                        Toast.makeText(FlotaHistoricoActivity.getInstance(), "Debe introducir un nº",
                                Toast.LENGTH_SHORT).show();

                    }

                    new asyncPosiciones(desde.getText().toString(),hasta.getText().toString(), tiempoParada , movilId).execute();
                }
            });


            // Evento boton salir
            findViewById(R.id.btnVolver)
                    .setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {

                            finish();
                        }
                    });


            View.OnClickListener clickDesde = new clickDesde();
            View.OnClickListener clickHasta = new clickHasta();

            desde.setOnClickListener(clickDesde);
            hasta.setOnClickListener(clickHasta);



            if (getIntent().hasExtra("movil")) {
                // Se comprueba si la Activity se ha iniciado desde una notificación de nuevas incidencias
                movilId = getIntent().getExtras().getInt("movil");
            }

            if (getIntent().hasExtra("fecha")) {
                // Se comprueba si la Activity se ha iniciado desde una notificación de nuevas incidencias
                DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date d = (Date) formatter.parse(getIntent().getExtras().getString("fecha"));
                fecha = new Timestamp(d.getTime());

                if (fecha!=null) {

                    Calendar cal = Calendar.getInstance();       // get calendar instance
                    cal.setTimeInMillis(d.getTime());

                    cal.set(Calendar.HOUR_OF_DAY, 0);            // set hour to midnight
                    cal.set(Calendar.MINUTE, 0);                 // set minute in hour
                    cal.set(Calendar.SECOND, 0);                 // set second in minute
                    desde.setText(formatter.format(cal.getTimeInMillis()));

                    cal.set(Calendar.HOUR_OF_DAY, 23);            // set hour to midnight
                    cal.set(Calendar.MINUTE, 59);                 // set minute in hour
                    cal.set(Calendar.SECOND, 59);                 // set second in minute
                    hasta.setText(formatter.format(cal.getTimeInMillis()));

                }

            }


        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            new InfoDialog(this, getString(R.string.atencion),
                    getString(R.string.problema) + e.getMessage(),
                    InfoDialog.ICON_STOP, new OnInfoDialogSelect() {

                @Override
                public void onSelectOption(int option) {

                    finish();
                }

            }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER)
                    .show();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    public class clickDesde implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            showTimePickerDialog(v,1);
        }

    }


    public class clickHasta implements View.OnClickListener {

        @Override
        public void onClick(View v) {
            showTimePickerDialog(v, 2);
        }

    }


    public void showTimePickerDialog(View v, int range) {

        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        vistaDialog = v;
        dialog = new Dialog(this);

        dialog.setContentView(R.layout.date_time_layout);
        dialog.setTitle(getString(R.string.choseDate));
        dp = (DatePicker) dialog.findViewById(R.id.datePicker1);
        tp = (TimePicker) dialog.findViewById(R.id.timePicker1);
        tp.setIs24HourView(true);

        try {

            Date d = (Date) formatter.parse(range==1?desde.getText().toString(): hasta.getText().toString());
            Calendar c = Calendar.getInstance();
            c.setTimeInMillis(d.getTime());
            dp.init(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), null);
            tp.setHour(c.get(Calendar.HOUR_OF_DAY));
            tp.setMinute(c.get(Calendar.MINUTE));

        } catch (ParseException e) {
            e.printStackTrace();
        }


        View.OnClickListener clickOkDialog = new clickTimePicker();
        Button ok = (Button) dialog.findViewById(R.id.OkDateTime);
        ok.setOnClickListener(clickOkDialog);

        View.OnClickListener clickCancelarDialog = new FlotaHistoricoActivity.clickCancelarTimePicker();
        Button cancelar = (Button) dialog.findViewById(R.id.CancelarDateTime);
        cancelar.setOnClickListener(clickCancelarDialog);

        dialog.show();

    }



    public class clickCancelarTimePicker implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            dialog.dismiss();
        }
    }


    public class clickTimePicker implements View.OnClickListener {

        @Override
        public void onClick(View v) {

            Calendar c = Calendar.getInstance();
            int iSeconds = c.get(Calendar.SECOND);

            String minutes = (tp.getCurrentMinute() > 9) ? ""
                    + tp.getCurrentMinute() : "0" + tp.getCurrentMinute();
            String months = (dp.getMonth() + 1) > 9 ? "" + (dp.getMonth() + 1)
                    : "0" + (dp.getMonth() + 1);
            String days = (dp.getDayOfMonth() > 9) ? "" + dp.getDayOfMonth()
                    : "0" + dp.getDayOfMonth();
            String hours = tp.getCurrentHour() > 9 ? "" + tp.getCurrentHour()
                    : "0" + tp.getCurrentHour();

            String seconds = iSeconds > 9 ? "" + iSeconds : "0" + iSeconds;

            String strDateTime = dp.getYear() + "-" + months + "-" + days + " "
                    + hours + ":" + minutes + ":" + seconds;

            EditText edit = (EditText) vistaDialog;
            edit.setText(strDateTime);

            dialog.dismiss();

        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        ClientWebSvc sweb = new ClientWebSvc();
        try {
            sweb.refreshToken();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    class asyncPosiciones extends AsyncTask<Void, String, JSONArray> {


        private ProgressDialog pDialog;
        private String fechaIni, fechaFin;
        private int movilId = 0, tiempoParadaEdit=0;


        public asyncPosiciones(String fechaIni, String fechaFin, int tiempoParada, int movilId) {

            try {
                Date desde = Utils.stringToDate(fechaIni, "yyyy-MM-dd HH:mm:ss");
                Date hasta = Utils.stringToDate(fechaFin, "yyyy-MM-dd HH:mm:ss");

                this.fechaIni = Utils.datetimeToString(desde, "yyyy-MM-dd HH:mm:ss");
                this.fechaFin = Utils.datetimeToString(hasta, "yyyy-MM-dd HH:mm:ss");
                this.movilId = movilId;
                this.tiempoParadaEdit = tiempoParada;

                if (hasta.getTime()- desde.getTime()> 86400000) {
                    Toast.makeText(FlotaHistoricoActivity.getInstance(), "No puede establecer un periodo de más de 24 horas.",
                            Toast.LENGTH_SHORT).show();
                    cancel(true);

                }
            }catch (Throwable e){
                cancel(true);
                MyLoggerHandler.getInstance().error(e);
            }
        }

        JSONArray jsonData = null;


        protected void onPreExecute() {
            // para el progress dialog
            pDialog = new ProgressDialog(FlotaHistoricoActivity.getInstance());
            pDialog.setMessage("Recuperando información de la ruta, por favor espere");
            pDialog.setIndeterminate(true);
            pDialog.setCancelable(false);
            pDialog.show();
        }


        @Override
        protected JSONArray doInBackground(Void... strings) {

            ClientWebSvc sweb = new ClientWebSvc();

            String url = Config.getInstance().getValue("webSvc", "");
            if (url.equals(""))
                return null;

            url = url + "/api/posiciones";

            HttpPost post = new HttpPost(url);

            // Preparo la llamada para recibir datos
            post.setHeader("User-Agent", "java-Android");
            post.setHeader("Content-type", "application/json");
            post.setHeader("Accept-encoding", "gzip");
            post.setHeader("Authorization", "Bearer " + sweb.token);

            try {
                JSONObject jsonObject = new JSONObject();

                jsonObject.put("movil", this.movilId);
                jsonObject.put("desde", this.fechaIni);
                jsonObject.put("hasta", this.fechaFin);

                post.setEntity(new StringEntity(jsonObject.toString()));

            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            } catch (JSONException e) {
                e.printStackTrace();
            }

            // Establezco timeout alto
            HttpParams parametros = new BasicHttpParams();

            int timeoutConnection = 10000;
            HttpConnectionParams.setConnectionTimeout(parametros, timeoutConnection);
            int timeoutSocket = 10000;
            HttpConnectionParams.setSoTimeout(parametros, timeoutSocket);
            DefaultHttpClient httpClient = new DefaultHttpClient(parametros);
            HttpResponse response;

            boolean salir=false;

            // lo intento 2 veces, en caso contrario mostraré un mensaje para que lo intente nuevamente.
            for (int i = 0; i < 2 && !salir; i++) {
                try {
                    response = httpClient.execute(post);
                    int result = response.getStatusLine().getStatusCode();

                    switch (result) {
                        case 200: // Respuesta correcta

                            InputStream inStream = response.getEntity().getContent();

                            Header contentEncoding = response.getFirstHeader("Content-Encoding");
                            if (contentEncoding != null && contentEncoding.getValue().equalsIgnoreCase("gzip")) {
                                inStream = new GZIPInputStream(inStream);
                            }

                            String strResponse = sweb.convertStreamToString(inStream);

                            try {
                                // En principio intento recuperar los datos como un array JSON si falla lo intento como JSON
                                jsonData = new JSONArray(strResponse);
                                salir=true;

                            } catch (JSONException e) {

                                try {
                                    // Recupero la respuesta JSON
                                    jsonData = new JSONArray()
                                            .put(new JSONObject(strResponse));
                                    salir=true;
                                } catch (Exception ex) {
                                    MyLoggerHandler.getInstance().error(
                                            ex);
                                }
                            }
                            break;

                        case 401: // El token ya no es valido
                            /*sweb.token = "";
                            sweb.refreshToken();*/
                            MyLoggerHandler.getInstance().info("[TOKEN]: En FlotaHistoricoActivity el token ha expirado.");
                            break;

                        default:
                            try {

                                Thread.sleep(5000);

                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                    }


                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                } catch (ClientProtocolException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    if (e instanceof HttpHostConnectException){
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException e1) {
                            e1.printStackTrace();
                        }
                    }

                } catch (Exception e) {
                    MyLoggerHandler.getInstance().error(e);
                    cancel(true);
                }
            }
            return jsonData;
        }


        protected void onPostExecute(JSONArray result) {

            try {
                if (result!=null && result.length()>0){

                    // Guardamos la ruta, y al terminar, notificamos al mapa para que la muestre
                    DBFlotaPosiciones db = new DBFlotaPosiciones();
                    //int idRuta = db.getMaxRutaHistorico()+1;
                    db.deletePosicionesHistorico();

                    boolean hayParada=false;
                    Date fechaUltParada = new Date();
                    DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    long tiempoParada = 0;
                    Date sFecha = new Date();


                    long secondsInMilli = 1000;
                    long minutesInMilli = secondsInMilli * 60;
                    int empresa = MainActivity.getInstance().getEmpresa();

                    for (int i=0;i<result.length();i++) {

                        int movilId = (int) result.getJSONObject(i).get("Movil");
                        double lat = (double) result.getJSONObject(i).get("Lat");
                        double lon = (double) result.getJSONObject(i).get("Lng");
                        int vel = (int) result.getJSONObject(i).get("Velocidad");
                        int altura = (int) result.getJSONObject(i).get("Altura");
                        int rumbo = (int) result.getJSONObject(i).get("Rumbo");
                        String fecha = (String) result.getJSONObject(i).get("Fecha");


                        try {
                            sFecha = (Date) formatter.parse(fecha);
                        } catch (Throwable e){

                        }


                        if (hayParada && vel==0){

                            // no hago nada

                        } else if(!hayParada && vel==0){
                            hayParada = true;
                            fechaUltParada = sFecha;


                        } else if (hayParada && vel>0){

                            tiempoParada =  sFecha.getTime() - fechaUltParada.getTime();

                            hayParada=false;
                        }

                        FlotaPosicionesHistorico f =
                                new FlotaPosicionesHistorico(1, movilId, empresa, lat, lon,
                                                              vel, altura, rumbo, fecha, tiempoParada);
                        tiempoParada=0;
                        db.insert(f);
                    }

                    db.close();


                    pDialog.dismiss();

                    MyBroadCastManager.getInstance()
                            .sendBroadCastRefreshHistoricoRuta(1, tiempoParadaEdit);

                    finish();


                } else {
                    Toast.makeText(FlotaHistoricoActivity.getInstance(), "No se ha recuperado ninguna ruta con los parámetros establecidos.",
                            Toast.LENGTH_SHORT).show();
                    pDialog.dismiss();

                }

            } catch (JSONException e) {
                MyLoggerHandler.getInstance().error(e);
            }

        }

        @Override
        protected void onCancelled() {

            if (pDialog!=null)
                pDialog.dismiss();
        }
    }

}
