package com.movisat.ecosat;

import static java.lang.Integer.parseInt;

import android.app.Activity;
import android.app.Dialog;
import android.database.Cursor;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.cursoradapter.widget.SimpleCursorAdapter;

import com.environment.Environment;
import com.movisat.adapter.SelectionAdapter;
import com.movisat.bll.SensoresManager;
import com.movisat.database.DBElementoModeloNivelLlenado;
import com.movisat.database.DBFlota;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModeloNivelLlenado;
import com.movisat.database.SensorNivelLLenado;
import com.movisat.database.Tags;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.utilities.Config;
import com.movisat.utilities.Utils;

import java.sql.Date;
import java.util.Calendar;

public class NivelLlenadoActivity extends BaseActivity {
    private static NivelLlenadoActivity instance = null;

    private static Elemento elemento = null;
    private static boolean fromRfID = false;
    private boolean vaciado_parcial = false;
    private Button mBtnVolver;
    private Button mBtnAceptar;
    SelectionAdapter<String> mAdapter;
    private DatePicker dp;
    private TimePicker tp;

    public Elemento getElemento() {
        return elemento;
    }

    public void setElemento(Elemento elemento) {
        NivelLlenadoActivity.elemento = elemento;
    }

    public void setFromRfID(boolean valor) {
        fromRfID = valor;
    }
    private boolean procesado=true;
    public static NivelLlenadoActivity getInstance() {
        if (instance == null)
            instance = new NivelLlenadoActivity();
        return instance;
    }

    EditText desde;
    Spinner flota;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        instance = this;

        OnItemClickListener clickLista = null;

        setContentView(R.layout.seleccion_nivel_llenado);

        DBElementoModeloNivelLlenado db = new DBElementoModeloNivelLlenado();
        ElementoModeloNivelLlenado nivelLlenado = db.getByCodigoModelo(
                elemento.getModelo(), MainActivity.getInstance().getEmpresa());
        db.close();
        if (nivelLlenado == null || nivelLlenado.getFracciones() == 0) {
            try {
                Toast.makeText(
                        this,
                        R.string.noNivelesModelo,
                        Toast.LENGTH_LONG).show();
                setResult(Activity.RESULT_CANCELED);
                this.finish();
                return;

            } catch (Throwable e) {
               //
            }
        }

        final ListView list = (ListView) findViewById(R.id.lvniveles);
        TextView txt = (TextView) findViewById(R.id.textViewTitulo);
        desde = (EditText) findViewById(R.id.editDesde);
        flota = (Spinner) findViewById(R.id.cbFlota);

        OnClickListener clickDesde = new clickDesde();

        // Se obtiene si la opción escogida es de una versión jefe
        boolean version_jefe = getIntent().getBooleanExtra("version_jefe", false);
        procesado = getIntent().getBooleanExtra("procesado", true);
        if (!procesado) this.setTitle(R.string.lectura_llenado_title);
        vaciado_parcial = getIntent().getBooleanExtra("vaciado_parcial", false);

        if (version_jefe) {
            // TODO: Cargar los equipos.
            setFlota();
            desde.setOnClickListener(clickDesde);
        } else {
            flota.setVisibility(View.GONE);
            desde.setEnabled(false);
        }

        desde.setText(Utils.datetimeToString(
                new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss"));

        DBTags dbTags = new DBTags();
        Tags tag = dbTags.getByMatricula(elemento.getMatricula(), elemento.getEmpresa());
        if(tag != null) elemento.setTag(tag.getTag());
        txt.setText(elemento.toString());

        // Para recycling camacho ordenamos de mayor a menor., si se ha indicado vaciado parcial
        // marcando el check correspondiente en la ventana anterior, se mostrarán en orden, pero solo los 3 primeros niveles

        int tamNiveles= vaciado_parcial ? 3 : nivelLlenado.getFracciones();
        String[] niveles = new String[tamNiveles];

        if (Environment.isSoftCamacho && !vaciado_parcial ) {
            for (int i = nivelLlenado.getFracciones()-1; i >=0 ; i--) {
                niveles[i] = getString(R.string.fraccion) + (nivelLlenado.getFracciones() - i);
            }
        } else{
            for (int i = 0; i < tamNiveles; i++) {
                niveles[i] = getString(R.string.fraccion) + (i + 1);
                //if (vaciado_parcial && i==2)
                    //break;
            }
        }

        mAdapter = new SelectionAdapter<String>(this,
                android.R.layout.simple_list_item_1, niveles);

        list.setAdapter(mAdapter);

        list.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
                mAdapter.setIndexSeleccionado(i);
                mAdapter.notifyDataSetChanged();
                mBtnAceptar.setEnabled(true);
            }
        });

        mBtnVolver = (Button) findViewById(R.id.btnVolver);
        mBtnAceptar = (Button) findViewById(R.id.btnAceptar);

        mBtnVolver.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                setResult(Activity.RESULT_CANCELED);
                getInstance().finish();
            }
        });

        mBtnAceptar.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                mBtnAceptar.setEnabled(false);
                if (mAdapter != null && mAdapter.getIndexSeleccionado() >= 0) {
                    // para versión Reciclyng Camacho, muestro las fracciones de mayor a menor
                    process(mAdapter.getIndexSeleccionado(), false);


                    // Cerramos actividad.
                    setResult(Activity.RESULT_OK);
                    getInstance().finish();

                }
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
    		if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
        finish();
    }
        return super.onKeyDown(keyCode, event);
    }

    private void setFlota() {
        DBFlota manager = new DBFlota();
        try {
            Cursor cursor = manager.getAllCursorBy(MainActivity.getInstance().getEmpresa());
            String[] from = new String[]{"nombre"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(this,
                    android.R.layout.simple_spinner_item, cursor, from, to, 0);

            // set layout for activated adapter
            dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            flota.setAdapter(dataAdapter);

            int Index = Integer.valueOf(Config.getInstance()
                    .getValueUsuario("FlotaSelect", "-1"));
            if (Index > -1 && Index < cursor.getCount()) {
                flota.setSelection(Index);
            }

            //Guardamos el index del dispositivo
            int indexDispositivo = saveIndexDispotivo(cursor);
            if (Index == -1 && indexDispositivo < cursor.getCount()) {
                flota.setSelection(indexDispositivo);
            }
        } catch (Exception e){}

        manager.close();
    }

    private int saveIndexDispotivo(Cursor cursor) {
        String nombreDispositivo = null;
        String nombre = Config.getInstance().getValue("nombreDispositivo", "");
        int index = 0;
        if (cursor.moveToFirst())
            do {
                nombreDispositivo = cursor.getString(1);
                if (nombreDispositivo.equals(nombre)) {
                    Config.getInstance().setValueUsuario("indexDispositivo", "" + index);
                    return index;
                }
                index++;

            } while (cursor.moveToNext());
        return -1;

    }

    Dialog dialog;
    View vistaDialog;

    public void showTimePickerDialog(View v) {

        vistaDialog = v;
        dialog = new Dialog(this);

        dialog.setContentView(R.layout.date_time_layout);
        ((TextView) dialog.findViewById(R.id.dialogTitle)).setText(R.string.choseDate);
        dp = (DatePicker) dialog.findViewById(R.id.datePicker1);
        tp = (TimePicker) dialog.findViewById(R.id.timePicker1);
        tp.setIs24HourView(true);

        OnClickListener clickOkDialog = new clickTimePicker();
        OnClickListener clickCancelarDialog = new clickCancelarTimePicker();
        Button ok = (Button) dialog.findViewById(R.id.OkDateTime);
        ok.setOnClickListener(clickOkDialog);

        Button cancelar = (Button) dialog.findViewById(R.id.CancelarDateTime);
        cancelar.setOnClickListener(clickCancelarDialog);

        dialog.show();

    }

    public class clickDesde implements OnClickListener {

        @Override
        public void onClick(View v) {
            showTimePickerDialog(v);
        }

    }

    public class clickCancelarTimePicker implements OnClickListener {

        @Override
        public void onClick(View v) {
            dialog.dismiss();
        }
    }

    public class clickTimePicker implements OnClickListener {

        @Override
        public void onClick(View v) {

            Calendar c = Calendar.getInstance();
            int iSeconds = c.get(Calendar.SECOND);

            String minutes = (tp.getCurrentMinute() > 9) ? ""
                    + tp.getCurrentMinute() : "0" + tp.getCurrentMinute();
            String months = (dp.getMonth() + 1) > 9 ? "" + (dp.getMonth() + 1)
                    : "0" + (dp.getMonth() + 1);
            String days = (dp.getDayOfMonth() > 9) ? "" + dp.getDayOfMonth()
                    : "0" + dp.getDayOfMonth();
            String hours = tp.getCurrentHour() > 9 ? "" + tp.getCurrentHour()
                    : "0" + tp.getCurrentHour();

            String seconds = iSeconds > 9 ? "" + iSeconds : "0" + iSeconds;

            String strDateTime = dp.getYear() + "-" + months + "-" + days + " "
                    + hours + ":" + minutes + ":" + seconds;

            EditText edit = (EditText) vistaDialog;
            edit.setText(strDateTime);

            dialog.dismiss();

        }

    }


    /**
     * Envía un nivel de llenado sobre la fracción en la posición especificada.
     * @param position  Índice de la fracción.
     */
    private void process(int position, boolean orden_inverso) {

        int fraccion = 0;

        if (orden_inverso)
            fraccion = position + 1;

        else {
            String res=mAdapter.getItem(position);
            fraccion = Integer.parseInt(res.substring("Fracción".length(),res.length()));
        }

        long fecha = Utils.StringToDateTime(
                desde.getText().toString()).getTime();
        Cursor c = null;
        int codigoMovil = 0;
        if (flota.getVisibility() != View.GONE) {
            if (flota.getCount() <= 0 || flota.getSelectedItemPosition() < 0) {
                Toast.makeText(NivelLlenadoActivity.getInstance(),
                        R.string.nivelNoEnviado,
                        Toast.LENGTH_LONG).show();
                return;
            }
            c = ((SimpleCursorAdapter) flota.getAdapter()).getCursor();
            if (c != null) {
                c.moveToPosition(flota.getSelectedItemPosition());
                codigoMovil = c.getInt(0);

                Config.getInstance().setValueUsuario("FlotaSelect",
                        String.valueOf(flota.getSelectedItemPosition()));
            }
        } else {
            codigoMovil = parseInt(Config.getInstance().getValue("codigoDispositivo", "0"));
        }

        // si marcamos que el elemento no ha de procesarse, enviamos solo el nivel, sin que conlleve en el servidor a marcar dicho elemento como procesado.
        if (!procesado){

            SensorNivelLLenado sensor= new SensorNivelLLenado(getElemento().getId(),codigoMovil,MainActivity.getInstance().getEmpresa(),fraccion, fecha, vaciado_parcial?1:0);

            // Se envía un mensaje al servidor
            DBPacket dbPacket = new DBPacket();
            dbPacket.insert(new Packet(
                    Packet.SENSOR_NIVEL_LLENADO_SIN_PROCESAR, Packet.PRIORIDAD_NORMAL, sensor));
            dbPacket.close();

            Toast.makeText(NivelLlenadoActivity.getInstance(),
                    "Lectura de llenado enviada al servidor.",
                    Toast.LENGTH_LONG).show();
        } else {
            if (SensoresManager.getInstance().sendSensorNivelLlenado(elemento,
                  codigoMovil, fraccion, fecha, vaciado_parcial ? 1 : 0)) {
                Toast.makeText(NivelLlenadoActivity.getInstance(),
                      R.string.nivelEnviado,
                      Toast.LENGTH_LONG).show();
            }
        }
    }

}
