package com.movisat.ecosat;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

public class CallApiActivity extends Activity {
    private static volatile boolean stop;
    private static CallApiActivity instance = null;


    public static CallApiActivity getInstance() {

        return instance;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        instance = this;

        setContentView(R.layout.activity_call_api);
        final Button btnCancelar = (Button) findViewById(R.id.btCancelar);

        stop = false;

        btnCancelar.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                stop = true;
                finish();
            }
        });

        new Thread() {
            public void run() {
                while (!stop) {
                    try {
                        sleep(100);
                    } catch (Throwable e) {
                    }
                }
                finish();
            }
        }.start();
    }

    public static synchronized void close() {

        new Thread() {
            public void run() {
                try {
                    for (int i = 0; i < 2; i++)
                        sleep(1000);
                } catch (Throwable e) {
                }
                stop = true;
            }
        }.start();
    }
}
