package com.movisat.ecosat;

import static com.movisat.ecosat.AddInciActivity.MAX_NUM_IMAGES;

import android.app.AlertDialog;
import android.app.Fragment;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.LinearLayout.LayoutParams;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.environment.Environment;
import com.movisat.adapter.EstadosModelAdapter;
import com.movisat.database.DBEstados;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBIncidenciaEstado;
import com.movisat.database.DBIncidenciaFoto;
import com.movisat.database.Estado;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaEstado;
import com.movisat.database.IncidenciaFoto;
import com.movisat.log.Logg;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.synchronize.DBSynchro;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Photo;
import com.movisat.utilities.Utils;
import com.movisat.utilities.ZoomPhotoDialog;
import com.movisat.utils.GlobalUtils;
import com.movisat.utils_android.PhotoService;
import com.movisat.utils_android.UtilssAndroid;

import java.io.ByteArrayOutputStream;
import java.sql.Date;
import java.util.ArrayList;

public class UpdateEstadoIncidencia extends BaseActivity {

    public static final String EVENT_UPDATE_ESTADO_INCIDENCIA = "update.estado.incidencia.event";
    public static final String MSG_SET_ID_FOTO_BR = "set.id.foto";
    private static UpdateEstadoIncidencia instance = null;
    private static Spinner spinnerEstados;
    private static Button btnAceptar;
    private static ArrayList<Estado> estados;
    private static ArrayList<Bitmap> fotos;
    private static ArrayList<Long> fotosFecha;
    private TextView textInformacionIncidencia;
    private static TextView lblFoto;
    private static TextView txtImgNumero;
    private static CheckBox cdEsAvisoFalso;
    public int indexSelected = 0;
    private receiverUpdateEstado receiver = null;
    private int countDBFotos = 0;
    private EstadosModelAdapter modelAdapter = null;
    private ClickImageView eventoClick = new ClickImageView();
    private LongClickImageView eventoLongClick = new LongClickImageView();
    private EditText etObserv;
    private boolean isProcessed = false;
    private IncidenciaEstado incidenciaEstadoAnterior = null;
    private boolean isViewOnlyMode = false;

    private static final int RESULT_GALLERY = 0;

    public static UpdateEstadoIncidencia getInstance() {
        return instance;
    }

    // Our handler for received Intents. This will be called whenever an Intent
    // with an action named "custom-event-name" is broadcasted.
    public class receiverUpdateEstado extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            String message = intent.getAction();
            if (message.equals(MSG_SET_ID_FOTO_BR)) {
                int idInternoFoto = (Integer) intent.getExtras().get("id-foto");
                IncidenciaFoto incidenciaFoto = (IncidenciaFoto) intent
                        .getExtras().get("item");
                incidenciaFoto.setId(idInternoFoto);

                DBPacket dbPacket = new DBPacket();
                Packet paquete = new Packet(Packet.INCIDENCIA_INSERTAR_FOTO,
                        Packet.PRIORIDAD_NORMAL, incidenciaFoto);
                dbPacket.insert(paquete);
                dbPacket.close();
                MyLoggerHandler.getInstance().info(String.format("Añadida foto a incidencia con código %d.",
                        incidenciaFoto.getIncidenciaExterno()));
            }
        }
    }

    @Override
    protected void onDestroy() {

        super.onDestroy();
        // receiverUpdateEstado receiver = new receiverUpdateEstado();
        if (receiver != null)
            LocalBroadcastManager.getInstance(AppContext.getContext())
                    .unregisterReceiver(receiver);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        setContentView(R.layout.update_inci_layout);
        instance = this;
        boolean isThrowException = false;
        isProcessed = false;
        
        isViewOnlyMode = getIntent().getBooleanExtra("fromAsignarIncidencias", false);

        try {

            createBottomButtons();
            startClassFields();
            checkSavedInstanceState(savedInstanceState);
            getPhotos();
            getStates();
            onConfirmButtonPressed();
            onAddPhotoPressed();
            checkLastState();
            initGallery();
            
            if (isViewOnlyMode) {
                setupViewOnlyMode();
            }

        } catch (Exception e) {

            isThrowException = true;
            String message = GlobalUtils.isNullOrEmptyString(e.getMessage()) ? getResources()
                    .getString(R.string.errorDesconocido) : e.getMessage();
            Toast.makeText(
                    this, message,
                    Toast.LENGTH_LONG).show();
        } catch (Throwable e) {
            isThrowException = true;
            MyLoggerHandler.getInstance().error(e);
        } finally {
            if (isThrowException)
                this.finish();

        }

    }

    private void checkSavedInstanceState(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            getFragmentManager().beginTransaction()
                    .add(R.id.container, new PlaceholderFragment()).commit();
        }
    }

   private void checkLastState() throws Exception {

      try {
         // selecciono el ultimo estado.
         Bundle bundle = getIntent().getExtras();
         int incidenciaId = bundle.getInt("incidencia");
         int incidenciaIdExt = bundle.getInt("incidenciaExt");
         int empresaId = MainActivity.getInstance().getEmpresa();

         DBIncidenciaEstado dbIncidenciaEstado = new DBIncidenciaEstado();
         incidenciaEstadoAnterior = dbIncidenciaEstado
               .getLastEstadoIncidencia(incidenciaId, empresaId, false);

         // Recojo por temporal
         if (incidenciaEstadoAnterior == null)
            incidenciaEstadoAnterior = dbIncidenciaEstado.getLastEstadoIncidencia(
                  incidenciaIdExt, empresaId, true);
         dbIncidenciaEstado.close();

         DBIncidencia dbIncidencia = new DBIncidencia();
         Incidencia incidencia;
         if (incidenciaIdExt != 0) {
            incidencia = dbIncidencia.getByIdExterno(incidenciaIdExt, empresaId);
         } else {
            incidencia = dbIncidencia.getByIdInterno(incidenciaId, empresaId);
         }
         incidencia.setNombrePropietario(dbIncidencia.getNombrePropietario(incidencia));
         dbIncidencia.close();

         // Si aun asi es nulo, cerramos la activity
         if (incidenciaEstadoAnterior == null) {
            // Borrar incidencia y sincronizar desde esa fecha
             String fecha = Utils.datetimeToString(new Date(incidencia.getFechaUltimoEstado() - 60000), "yyyy-MM-dd HH:mm:ss");
             dbIncidencia.delete(incidencia);
             dbIncidencia.close();
             Config.getInstance().setValueUsuario("ultSincroIncidenciasHistorico", fecha);
             Config.getInstance().setValueUsuario("ultSincroIncidenciasTipo", fecha);
             Config.getInstance().setValueUsuario("ultSincroIncidenciasModelo", fecha);
             Config.getInstance().setValueUsuario("ultSincroIncidenciasMotivo", fecha);
             Config.getInstance().setValueUsuario("ultSincroIncidenciasEstado", fecha);
             Config.getInstance().setValueUsuario("ultSincroInciEnt", fecha);
             if (MainActivity.getInstance().isNetworkAvailable()) {
                if (MainActivity.getInstance().isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                   DBSynchro.getInstance().forceSync();
                }
             }
             MainActivity.getInstance().showMessage("No se ha encontrado el estado de la incidencia. Sincronizando. Por favor, espere.", Toast.LENGTH_LONG);
            throw new Exception("No se ha encontrado un estado asociado a esta incidencia");
         }

         textInformacionIncidencia.setText(incidencia.getFormattedIncidentInfo());
         cdEsAvisoFalso.setChecked(incidenciaEstadoAnterior.esAvisoFalso() == 1);

         // Recorro los modelos para seleccionar en el combo el iltimo
         for (Estado estado : estados) {
            if (estado.getIdExterno() == incidenciaEstadoAnterior.getIdEstado()) {
               spinnerEstados.setSelection(estados.indexOf(estado));
               break;
            }
         }
      } catch (Exception e) {
         MyLoggerHandler.getInstance().error(e);
         throw new Exception("Error al comprobar el último estado");
      }
   }

    private void onAddPhotoPressed() {
        // Evento botón añadir foto
        findViewById(R.id.btnAddFoto).setOnClickListener(v -> {
            if (!checkNumImages()) return;


            if (UtilssAndroid.isAndroidGreater11())
                PhotoService.get().takePhoto(instance);
            else
                Photo.getInstance().dispatchTakePictureIntent(instance);
        });
    }

    private void onConfirmButtonPressed() {

        try {
            btnAceptar.setOnClickListener(
                    view -> {
                        if (!isProcessed) {
                            isProcessed = true;

                            if (spinnerEstados.getSelectedItem() != null) {
                               Bundle bundle = getIntent().getExtras();
                               int incidenciaId = bundle.getInt("incidencia");
                               int incidenciaIdExterno = bundle.getInt("incidenciaExt");

                               int empresaId = MainActivity.getInstance().getEmpresa();

                               int estadoId = ((Estado) spinnerEstados.getSelectedItem()).getIdExterno();
                               int esFalso = cdEsAvisoFalso.isChecked() ? 1 : 0;
                               String observaciones = etObserv.getText().toString();

                               DBIncidenciaEstado dbIncidenciaEstado = new DBIncidenciaEstado();
                               IncidenciaEstado ultimoIncidenciaEstado =
                                     dbIncidenciaEstado.getLastEstadoIncidencia(incidenciaId, empresaId, false);
                               IncidenciaEstado nuevoIncidenciaEstado;

                               boolean isSameState = ultimoIncidenciaEstado != null && estadoId == ultimoIncidenciaEstado.getIdEstado();
                               boolean isSameObserv = ultimoIncidenciaEstado != null && observaciones.equals(ultimoIncidenciaEstado.getObservacion());

                               //PMARCO MANTIS 5268
                               if (isSameState) {
                                  //Si ya había el estado seleccionado y no se actualiza foto u observaciones no dejamos guardar
                                  if (fotos.size() == 0 && isSameObserv) {
                                     MainActivity.getInstance().showMessage(R.string.estado_incidencia_igual);
                                     isProcessed = false;
                                     return;
                                  }
                                  ultimoIncidenciaEstado.setObservacion(observaciones);
                                  ultimoIncidenciaEstado.setEsAvisoFalso(esFalso); // No se debería
                                  dbIncidenciaEstado.update(ultimoIncidenciaEstado, false);
                                  nuevoIncidenciaEstado = ultimoIncidenciaEstado;
                               } else {
                                  long now = System.currentTimeMillis();
                                  String strDate = Utils.datetimeToString(new Date(now), "yyyy-MM-dd HH:mm:ss");
                                  nuevoIncidenciaEstado = new IncidenciaEstado(0, 0,
                                        empresaId, incidenciaId, incidenciaIdExterno, estadoId, strDate, esFalso, observaciones);
                                  int newId = (int) dbIncidenciaEstado.insert(nuevoIncidenciaEstado);
                                  nuevoIncidenciaEstado.setId(newId);
//                               // Actualizar la incidencia
                                  DBIncidencia dbIncidencia = new DBIncidencia();
                                  Incidencia incidencia = dbIncidencia.getByIdInterno(incidenciaId, empresaId);

                                  if (incidencia != null) {
                                     incidencia.setFechaUltimoEstado(now / 1000);
                                     incidencia.setUltimoEstado(estadoId);
                                     // Actualizamos la incidencia en base de datos
                                     dbIncidencia.update(incidencia);
                                     // Actualizamos la incidencia en el mapa
                                     MyBroadCastManager.getInstance().sendBroadCastRefreshIncidencia(incidencia);
                                  }
                                  dbIncidencia.close();
                               }
                               dbIncidenciaEstado.close();

                               DBPacket dbPacket = new DBPacket();
                               dbPacket.insert(new Packet(Packet.INCIDENCIA_NUEVO_ESTADO,
                                     Packet.PRIORIDAD_NORMAL, nuevoIncidenciaEstado));
                               dbPacket.close();

                               MyLoggerHandler.getInstance().info(String.format(
                                     "Nuevo estado de incidencia con código %d.",
                                     nuevoIncidenciaEstado.getIdIncidenciaExterno()));

                               if (fotos.size() > 0) {

                                  int i = 0;
                                  DBIncidenciaFoto dbFoto = null;
                                  int idInternoFoto = 0;
                                  for (Bitmap foto : fotos) {

                                     // Creamos los paquetes de las fotos.
                                     IncidenciaFoto incidenciaFoto =
                                                new IncidenciaFoto(
                                                        0,
                                                        0,
                                                        incidenciaId,
                                                        incidenciaIdExterno,
                                                        MainActivity.getInstance().getEmpresa(),
                                                        MainActivity.getInstance().getUsuario(),
                                                        UtilssAndroid.bitmapToBase64(foto));

                                        incidenciaFoto.setFecha(fotosFecha.get(i));

                                        ByteArrayOutputStream stream = new ByteArrayOutputStream();
                                        foto.compress(
                                                Bitmap.CompressFormat.JPEG,
                                                Utils.getCalidadImagenSegunCobertura(getApplicationContext()),
                                                stream);

                                        dbFoto = new DBIncidenciaFoto();

                                        // inten
                                        if (!dbFoto.update(incidenciaFoto)) {
                                            idInternoFoto = (int) dbFoto.insert(incidenciaFoto);
                                            incidenciaFoto.setId(idInternoFoto);
                                        }

                                        if (incidenciaFoto.hasImageBase64()) {
                                            DBPacket dbp = new DBPacket();
                                            Packet paquete =
                                                    new Packet(
                                                            Packet.INCIDENCIA_INSERTAR_FOTO,
                                                            Packet.PRIORIDAD_NORMAL,
                                                            incidenciaFoto);
                                            dbp.insert(paquete);
                                            dbp.close();
                                            MyLoggerHandler.getInstance()
                                                    .info(
                                                            String.format(
                                                                    "Añadida foto a incidencia con codigo %d.",
                                                                    incidenciaFoto.getIncidenciaExterno()));

                                            i++;
                                        } else {
                                            MyLoggerHandler.getInstance()
                                                    .info("No se crea el paquete de insertar imagen pues esta no existe");
                                        }
                                    }

                                    if (dbFoto != null) dbFoto.close();
                                }

                                // if (MainActivity.getInstance().mapaElementos != null)
                                // GestionElementos.getInstance().updateIncidencia(inci);

                                modelAdapter.notifyDataSetChanged();

                                finish();

                            } else {

                                new InfoDialog(
                                        instance,
                                        getString(R.string.atencion),
                                        getString(R.string.sinEstadoIncidencia),
                                        InfoDialog.ICON_STOP,
                                        new OnInfoDialogSelect() {

                                            @Override
                                            public void onSelectOption(int option) {
                                            }
                                        },
                                        InfoDialog.BUTTON_ACCEPT,
                                        InfoDialog.POSITION_CENTER)
                                        .show();
                            }
                        }
                    });

        } catch (Exception e) {
            Logg.error('[' + this.getClass().getName() + "] " + e.getMessage());
            MyLoggerHandler.getInstance().error(e);
        }
    }

    private void getStates() throws Exception {
        try {
            // Recupero los estados posibles.
            DBEstados dbEstados = new DBEstados();
            estados = dbEstados.getAll(MainActivity.getInstance().getEmpresa());
            dbEstados.close();
            // Relleno el combo de estados
            modelAdapter = new EstadosModelAdapter(MainActivity.getInstance(), estados);
            spinnerEstados.setAdapter(modelAdapter);
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            throw new Exception("Error al obtener los estados");
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    private void getPhotos() {
        try {
            // Recupero el numero de fotos
            LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll);
            TextView texto = (TextView) findViewById(R.id.textoImagenesNumero);

            // Recuperamos las fotos almacenadas y las mostramos en el layout
            DBIncidenciaFoto dbFotos = new DBIncidenciaFoto();
            countDBFotos = dbFotos.getCountIncidencias(getIntent().getExtras()
                    .getInt("incidencia"));
            txtImgNumero.setText(Integer.toString(countDBFotos));

            ArrayList<IncidenciaFoto> listFotosDB = dbFotos.getAll(MainActivity
                            .getInstance().getEmpresa(),
                    getIntent().getExtras().getInt("incidencia"));

            if (listFotosDB != null) {

                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inPurgeable = true;

                for (IncidenciaFoto incidenciaFoto : listFotosDB) {

                    // Ahora se crea el bitmap a partir de una imagen física en el dispositivo, y no
                    // de un array de bytes de la imagen de base de datos, como antes.
                    try {


                        if (incidenciaFoto.hasImageBase64()) {
                            Bitmap foto = UtilssAndroid.base64ToBitmap(incidenciaFoto.getImageBase64());
                            ImageView imageView = new ImageView(instance);
                            LayoutParams params = new LayoutParams(200, 200);
                            params.setMargins(5, 5, 5, 5);
                            imageView.setLayoutParams(params);
                            imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
                            imageView.setImageBitmap(foto);
                            imageView.setTag(0);
                            imageView.setOnClickListener(eventoClick);
                            imageView.setOnLongClickListener(eventoLongClick);
                            layout.addView(imageView);
                        }

                    } catch (Throwable e) {

                        MyLoggerHandler.getInstance().error(e);
                    }
                }
            }
            dbFotos.close();
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    private void startClassFields() {
        fotos = new ArrayList<Bitmap>();
        fotosFecha = new ArrayList<Long>();
        btnAceptar = (Button) findViewById(R.id.btnAceptarAdd);
        lblFoto = (TextView) findViewById(R.id.lblNumeroFotos);
        txtImgNumero = (TextView) findViewById(R.id.textoImagenesNumero);
        spinnerEstados = (Spinner) findViewById(R.id.cbEstado);
        spinnerEstados.setOnItemSelectedListener(onItemClickListenerEstados());
        textInformacionIncidencia = findViewById(R.id.textInformacionIncidencia);
        etObserv = (EditText) findViewById(R.id.etObserv);
        cdEsAvisoFalso = findViewById(R.id.checkBoxEsAvisoFalso);
        if (Environment.isVisibleAvisoFalso)
            cdEsAvisoFalso.setVisibility(View.VISIBLE);

        if (Environment.isSoftIndra) {
            findViewById(R.id.textInformacionIncidenciaLabel).setVisibility(View.GONE);
            textInformacionIncidencia.setVisibility(View.GONE);
            ((TextView) findViewById(R.id.textObservacionesEstadoLabel)).setText(getString(R.string.observ_inci));
        }

    }

    private AdapterView.OnItemSelectedListener onItemClickListenerEstados() {
        return new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parentView, View selectedItemView, int position, long id) {            
                    if (incidenciaEstadoAnterior == null || incidenciaEstadoAnterior.getIdEstado() != id) {
                        etObserv.setText("");
                    } else {
                        etObserv.setText(incidenciaEstadoAnterior.getObservacion());
                    }                
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        };
    }

    private void createBottomButtons() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(EVENT_UPDATE_ESTADO_INCIDENCIA);
        filter.addAction(MSG_SET_ID_FOTO_BR);
        receiver = new receiverUpdateEstado();
        LocalBroadcastManager.getInstance(AppContext.getContext())
                .registerReceiver(receiver, filter);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {

        boolean isFromCamera = requestCode == Photo.REQUEST_IMAGE_CAPTURE || requestCode == PhotoService.REQUEST_IMAGE_CAPTURE;
        // Insertamos foto desde la camara
        if (isFromCamera && resultCode == RESULT_OK) {
            Bitmap foto = null;

            if (UtilssAndroid.isAndroidGreater11())
                foto = PhotoService.get().setPic();
            else
                foto = Photo.getInstance().setPic();

            if (foto != null) {
                fotos.add(foto);
                fotosFecha.add(System.currentTimeMillis());
                lblFoto.setText(getString(R.string.adds) + fotos.size() + getString(R.string.fotos));

                int total = countDBFotos;
                txtImgNumero.setText(Integer.toString(total));
                insertFotoGallery(foto);
            }
        }
        // Insertamos foto cargandola desde la galeria
        else if (requestCode == UpdateEstadoIncidencia.RESULT_GALLERY) {
            if (null != data) {
                Uri imageUri = data.getData();
                try {
                    Bitmap foto = Utils.decodeBitmapFromFile(
                            Utils.getRealPathFromURI(imageUri, this), 800, 600);

                    fotos.add(foto);
                    fotosFecha.add(System.currentTimeMillis());
                    lblFoto.setText(getString(R.string.adds) + fotos.size() + getString(R.string.fotos));
                    int total = countDBFotos;
                    txtImgNumero.setText(Integer.toString(total));
                    insertFotoGallery(foto);
                } catch (Throwable e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            }
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {

        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.update_estado_incidencia, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        /*int id = item.getItemId();
        if (id == R.id.action_settings)
            return true;*/

        return super.onOptionsItemSelected(item);
    }

    private void initGallery() {
        ImageView imagen;

        /* Botin Papelera */
        imagen = (ImageView) findViewById(R.id.papelera);
        imagen.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {

                // Si hay fotos seleccionadas preguntamos si queremos eliminar
                if (HayFotosSeleccionadas()) {

                    AlertDialog.Builder builder = new AlertDialog.Builder(
                            instance);

                    builder.setMessage(getString(R.string.questionEliminarFotos));
                    builder.setPositiveButton(getString(R.string.yes),
                            new DialogInterface.OnClickListener() {

                                public void onClick(DialogInterface dialog,
                                                    int which) {

                                    EliminarFotos();
                                }

                            });

                    builder.setNegativeButton(getString(R.string.no),
                            new DialogInterface.OnClickListener() {

                                @Override
                                public void onClick(DialogInterface dialog,
                                                    int which) {

                                    dialog.dismiss();
                                }
                            });

                    AlertDialog alert = builder.create();
                    alert.show();

                } else {
                    // Mostramos advertencia de que no hay imagenes
                    // seleccionadas
                    Toast.makeText(instance, R.string.noFotosSeleccionadas,
                            Toast.LENGTH_SHORT).show();
                }

            }
        });

        /* Boton seleccion todo */
        imagen = (ImageView) findViewById(R.id.selectall);
        imagen.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                SellectAllFotos(true);
            }
        });

        /* Boton desseleccion todo */
        imagen = (ImageView) findViewById(R.id.desselectall);
        imagen.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                SellectAllFotos(false);
            }
        });

        /* Boton galeria */
        imagen = (ImageView) findViewById(R.id.gallery);
        imagen.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                OpenGallery();
            }
        });

    }

    private class ClickImageView implements OnClickListener {
        private ImageView view;

        @Override
        public void onClick(View v) {
            view = (ImageView) v;

            // Siempre mostrar vista flotante con zoom
            Drawable drawable = view.getDrawable();
            Bitmap bitmap;
            if (drawable instanceof BitmapDrawable) {
                bitmap = ((BitmapDrawable) drawable).getBitmap();
            } else {
                // Fallback por seguridad
                view.setDrawingCacheEnabled(true);
                bitmap = Bitmap.createBitmap(view.getDrawingCache());
                view.setDrawingCacheEnabled(false);
            }
            
            // Mostrar dialog con zoom
            ZoomPhotoDialog.showPhoto(instance, bitmap);
        }

    }

    private class LongClickImageView implements View.OnLongClickListener {
        @Override
        public boolean onLongClick(View v) {
            ImageView imageView = (ImageView) v;
            
            if (imageView.getTag() == (Integer) 1) {
                // Deseleccionar
                imageView.clearColorFilter();
                imageView.setTag(0);
            } else {
                // Seleccionar
                imageView.setColorFilter(0xAA26FFFF);
                imageView.setTag(1);
            }
            
            v.performHapticFeedback(android.view.HapticFeedbackConstants.LONG_PRESS);
            return true;
        }
    }

    private void insertFotoGallery(Bitmap foto) {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll2);

        ImageView imageView = new ImageView(instance);
        LayoutParams params = new LayoutParams(200, 200);
        params.setMargins(5, 5, 5, 5);
        imageView.setLayoutParams(params);
        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
        imageView.setImageBitmap(foto);
        imageView.setTag(0);

        layout.addView(imageView);

        TextView texto = (TextView) findViewById(R.id.textoImagenesModNumero);
        texto.setText("(" + layout.getChildCount() + ")");

        imageView.setOnClickListener(eventoClick);
        imageView.setOnLongClickListener(eventoLongClick);
    }

    private void EliminarFotos() {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll2);
        int numImagenes = layout.getChildCount();
        ImageView img;

        for (int i = numImagenes - 1; i >= 0; i--) {

            img = (ImageView) layout.getChildAt(i);

            if (img.getTag() == (Integer) 1) {

                layout.removeViewAt(i);
                fotos.remove(i);
                fotosFecha.remove(i);
            }

        }

        TextView texto = (TextView) findViewById(R.id.textoImagenesModNumero);
        texto.setText("(" + layout.getChildCount() + ")");
    }

    private boolean HayFotosSeleccionadas() {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll2);
        int numImagenes = layout.getChildCount();
        ImageView img;

        for (int i = 0; i < numImagenes; i++) {

            img = (ImageView) layout.getChildAt(i);

            if (img.getTag() == (Integer) 1)
                return true;
        }

        return false;
    }

    private void SellectAllFotos(boolean seleccionar) {
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll2);
        int numImagenes = layout.getChildCount();
        ImageView img;

        for (int i = 0; i < numImagenes; i++) {

            img = (ImageView) layout.getChildAt(i);

            if (seleccionar) {

                img.setColorFilter(0xAA26FFFF);
                img.invalidate();
                img.setTag(1);

            } else {

                img.clearColorFilter();
                img.invalidate();
                img.setTag(0);
            }

        }
    }

    private void OpenGallery() {
        if (!checkNumImages()) return;

        Intent galleryIntent = new Intent(Intent.ACTION_PICK,
                android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(galleryIntent, RESULT_GALLERY);
    }

    /**
     * Indica si el número de imágenes añadidas es válido. Si no lo es, muestra un diálogo
     * informativo.
     *
     * @return true si el númeor de imágenes añadidas es válido, false en caso contrario.
     */
    public boolean checkNumImages() {

        if (fotos.size() >= MAX_NUM_IMAGES) {

            new InfoDialog(instance,
                    getString(R.string.atencion),
                    "No puede enviar más de " + MAX_NUM_IMAGES + " imágenes por incidencia o estado.",
                    InfoDialog.ICON_STOP,
                    new OnInfoDialogSelect() {
                        @Override
                        public void onSelectOption(int option) {
                        }
                    },
                    InfoDialog.BUTTON_ACCEPT,
                    InfoDialog.POSITION_CENTER)
                    .show();

            return false;
        }

        return true;
    }

    private void setupViewOnlyMode() {
        setTitle(getString(R.string.infomacion_incidencia));
        
        findViewById(R.id.titulo).setVisibility(View.GONE);
        findViewById(R.id.textUpdateEstado).setVisibility(View.GONE);
        findViewById(R.id.cbEstado).setVisibility(View.GONE);
        findViewById(R.id.checkBoxEsAvisoFalso).setVisibility(View.GONE);
        findViewById(R.id.spaceCheckBoxEsAvisoFalso).setVisibility(View.GONE);
        findViewById(R.id.textObservacionesEstadoLabel).setVisibility(View.GONE);
        findViewById(R.id.etObserv).setVisibility(View.GONE);
        findViewById(R.id.imagesContainer).setVisibility(View.GONE);
        findViewById(R.id.layoutUpdateEstadoIncidenciaAceptar).setVisibility(View.GONE);
    }

    /**
     * A placeholder fragment containing a simple view.
     */
    public static class PlaceholderFragment extends Fragment {

        public PlaceholderFragment() {
        }

        @Override
        public View onCreateView(LayoutInflater inflater, ViewGroup container,
                                 Bundle savedInstanceState) {

            View rootView = inflater.inflate(
                    R.layout.fragment_update_estado_incidencia, container,
                    false);

            return rootView;
        }
    }

}
