package com.movisat.ecosat;

import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.location.Location;
import android.location.LocationManager;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Toast;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.adapter.AreasAdapter;
import com.movisat.database.Area;
import com.movisat.database.DBArea;
import com.movisat.fragment.GesElemMapFragment;
import com.movisat.utilities.Config;

import java.util.List;

public class AreasActivity extends BaseActivity {
    private static AreasActivity instance = null;
    private static ListView listaAreas;
    private static List<Area> areas;
    AreasAdapter modelAdapter;
    private boolean isMedida = false;

    public static AreasActivity getInstance() {
        return instance;
    }

    private EditText buscador;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {

            instance = this;
            setContentView(R.layout.areas_layout);

            listaAreas = (ListView) findViewById(R.id.lvAreas);

            buscador = (EditText) findViewById(R.id.BuscadorAreas);

            buscador.addTextChangedListener(new MyTextWatcher());

            Button todos = (Button) findViewById(R.id.btTodos);
            Button ninguno = (Button) findViewById(R.id.btNinguno);

            // Deshabilitar boton back

            isMedida = this.getIntent().getExtras().getBoolean("IS_MEDIDA");

            if (isMedida) {
                todos.setVisibility(View.GONE);
                //ninguno.setVisibility(View.GONE);
                ninguno.setText("Cancelar");

            }

            // Evento botán todos
            (todos).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {

                    // Pongo todos las areas visibles
                    for (int i = 0; i < listaAreas.getCount(); i++) {

                        Area item = (Area) listaAreas.getItemAtPosition(i);

                        GesElemMapFragment.getInstance().showArea(
                                item.getCodigo(), true, Color.GREEN);

                    }

                    // Para que se repinte la lista
                    modelAdapter.notifyDataSetChanged();
                }
            });

            // Evento botán ninguno
            (ninguno).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!isMedida) {
                        // Pongo todos las áreas invisibles
                        for (int i = 0; i < listaAreas.getCount(); i++) {

                            Area item = (Area) listaAreas.getItemAtPosition(i);
                            GesElemMapFragment.getInstance().showArea(
                                    item.getCodigo(), false, Color.GREEN);

                        }

                        modelAdapter.notifyDataSetChanged();
                    } else {
                        MainActivity.getInstance().adapterMenuDerecho.notifyDataSetChanged();
                        finish();
                    }
                }
            });

            listaAreas.setOnItemClickListener(new OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> list, View view,
                                        int index, long id) {

                    Area item = (Area) list.getItemAtPosition(index);

                    int codigo = item.getCodigo();

                    int codigoAreaMedir = Integer.parseInt(Config.getInstance()
                            .getValueUsuario("areaMedir", "0"));

                    if (!isMedida) {
                        // Cambio la visibilidad del area si no es la misma de
                        // la de medir
                        if (codigo != codigoAreaMedir)
                            GesElemMapFragment.getInstance().showArea(codigo,
                                    !GesElemMapFragment.isVisibleArea(codigo),
                                    Color.GREEN);
                        else {
                            Toast.makeText(
                                    getInstance(),
                                    R.string.noAreaDesmarcableMedidaIniciada,
                                    Toast.LENGTH_LONG).show();
                        }

                        // Para que se repinte la lista
                        modelAdapter.notifyDataSetChanged();
                    } else {

                        // Si estoy aqui tengo que comprobar si estoy dentro del
                        // area seleccionada
                        // y ademas si en esa area estoy cerca de un punto de
                        // control.
                        LocationManager lm = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
                        Location location = lm.getLastKnownLocation(LocationManager.GPS_PROVIDER);
                        double longitude = -1;
                        double latitude = -1;
                        if (location != null) {
                            longitude = location.getLongitude();
                            latitude = location.getLatitude();
                        }

                        LatLng punto = new LatLng(latitude, longitude);
                        if (item.isInside(punto)) {
                            // radio de 100 metros para cualquier punto de
                            // control
                            if (item.isCercaPuntoControl(punto, 100)) {
                                GesElemMapFragment.getInstance().showArea(
                                        codigo, true, Color.MAGENTA);

                                Config.getInstance()
                                        .setValueUsuario("areasVisibles",
                                                String.valueOf(codigo));

                                // Guardar en configuracián área seleccionada.
                                Config.getInstance().setValueUsuario(
                                        "areaMedir", String.valueOf(codigo));

                                Config.getInstance().setValueUsuario(
                                        "inicioMedida",
                                        String.valueOf(System
                                                .currentTimeMillis()));

                                Config.getInstance().setValueUsuario(
                                        "medidaIniciada", "1");
                                finish();
                            } else {
                                Toast.makeText(
                                        getInstance(),
                                        R.string.noCercaPuntoControlMedida,
                                        Toast.LENGTH_LONG).show();
                            }
                        } else {
                            Toast.makeText(getInstance(),
                                    R.string.noDentroAreaMedida,
                                    Toast.LENGTH_LONG).show();
                        }
                    }

                }
            });
            LoadAreas();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }

    @Override
    public void onStart() {
        super.onStart();
      //  EventBus.getDefault().register(this);
    }

    @Override
    public void onStop() {
    //    EventBus.getDefault().unregister(this);
        super.onStop();
    }

    public void LoadAreas() {
        // Recupero todas las Areas con puntos de control.
        DBArea dbAreas = new DBArea();
        areas = dbAreas.getAll(true);
        dbAreas.close();

        if (areas == null) {

            Toast.makeText(this,
                    getResources().getString(R.string.avisoNoHayAreas),
                    Toast.LENGTH_LONG).show();
            finish();
        }

        // Relleno la lista de modelos

        modelAdapter = new AreasAdapter(MainActivity.getInstance(), areas);

        listaAreas.setAdapter(modelAdapter);
        modelAdapter.notifyDataSetChanged();
    }

    public class MyTextWatcher implements TextWatcher {

        @Override
        public void afterTextChanged(Editable s) {

        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before,
                                  int count) {
            modelAdapter.getFilter().filter(s.toString());
        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        switch (keyCode) {
            case KeyEvent.KEYCODE_BACK:

                int vis = 0;
                int inv = 0;
                StringBuilder models = new StringBuilder();
                boolean isVisible = false;
                // Compruebo las areas seleccionadas
                for (int i = 0; i < listaAreas.getCount(); i++) {

                    Area item = (Area) listaAreas.getItemAtPosition(i);

                    // Compruebo la visibilidad del modelo
                    isVisible = GesElemMapFragment.isVisibleArea(item.getCodigo());
                    if (isVisible) {

                        if (models.length() == 0)
                            models.append(item.getCodigo());
                        models.append("," + item.getCodigo());

                        vis++;

                    } else
                        inv++;

                }

                if (isMedida) {
                    // Cancelar evento
                    Toast.makeText(this, R.string.debeSeleccionarAreaMedir,
                            Toast.LENGTH_LONG).show();
                    return false;
                }

			/*
             * if (isMedida && vis == 1) { // Guardar en configuracián área
			 * seleccionada. Config.getInstance().setValueUsuario("areaMedir",
			 * models.toString());
			 * 
			 * Config.getInstance().setValueUsuario("inicioMedida",
			 * String.valueOf(System.currentTimeMillis()));
			 * 
			 * Config.getInstance().setValueUsuario("medidaIniciada", "1");
			 * 
			 * }
			 */
                if (!isMedida) {
                    // Guardo la configuracián del usuario
                    if (vis == listaAreas.getCount())
                        Config.getInstance().setValueUsuario("areasVisibles",
                                "todos");
                    else if (inv == listaAreas.getCount())
                        Config.getInstance().setValueUsuario("areasVisibles",
                                "ninguno");
                    else
                        Config.getInstance().setValueUsuario("areasVisibles",
                                models.toString());
                }

                // Refresco el mapa
                // MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
        }

        return super.onKeyDown(keyCode, event);
    }

    /**
     * Este mátodo se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

}
