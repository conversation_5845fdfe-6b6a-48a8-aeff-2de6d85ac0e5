package com.movisat.ecosat;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ListView;
import android.widget.TextView;

import com.movisat.adapter.TagModelAdapter;
import com.movisat.database.DBTags;
import com.movisat.database.Tags;
import com.movisat.events.OnReadedTag;
import com.movisat.use_case.TagSendSensor;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;

public class TagNotFoundListActivity extends BaseActivity {
    private static TagNotFoundListActivity instance = null;
    private static ListView lvTag = null;
    private static ArrayList<String> listaTag = new ArrayList<String>();
    public static TagModelAdapter modelAdapter = null;
    public static TextView titulo;

    public static TagNotFoundListActivity getInstance() {

        return instance;
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {

            String tag = "";
            Bundle extras = getIntent().getExtras();
            if (extras != null)
                tag = (String) extras.get("tag");
            else {
                finish();
                return;
            }

            listaTag.add(tag);

            instance = this;
            setContentView(R.layout.tag_not_found_layout);

            lvTag = (ListView) findViewById(R.id.lvTAG);
            titulo = (TextView) findViewById(R.id.titulo);

            titulo.setText("Lectura de Identificadores");

            // Evento botón salir
            findViewById(R.id.btSalir)
                    .setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            listaTag = new ArrayList<String>();
                            instance = null;
                            finish();
                        }
                    });

            modelAdapter = new TagModelAdapter(MainActivity.getInstance(), listaTag);
            lvTag.setAdapter(modelAdapter);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            new InfoDialog(this, getString(R.string.atencion),
                    getString(R.string.problema) + e.getMessage(),
                    InfoDialog.ICON_STOP, new OnInfoDialogSelect() {

                @Override
                public void onSelectOption(int option) {
                    finish();
                }

            }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER)
                    .show();
        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK) {
            listaTag = new ArrayList<String>();
            instance = null;
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }


    public void addTag(String tag) {
        listaTag.add(tag);
        modelAdapter.notifyDataSetChanged();
    }


    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }

    /**
     * Este método se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(OnReadedTag event) {
        DBTags dbTags = new DBTags();
        Tags tag = dbTags.getByTag(event.tag, MainActivity.getInstance().getEmpresa());
        TagSendSensor.execute(event.tag, tag);
    }
}