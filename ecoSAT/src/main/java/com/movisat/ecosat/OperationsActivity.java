package com.movisat.ecosat;

import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.adapter.OperationsDoneAdapter;
import com.movisat.database.DBElemento;
import com.movisat.database.DBOperationsDone;
import com.movisat.database.Elemento;
import com.movisat.database.OperationsDone;
import com.movisat.database.OperationsEnum;
import com.movisat.utilities.HelperDates;

import java.util.ArrayList;
import java.util.Date;

public class OperationsActivity extends BaseActivity {
	private OperationsDone operationDoneSelected;
	private Toast toast;

	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);

		try {
			setContentView(R.layout.layout_operations_mode);
			TextView titulo = (TextView) findViewById(R.id.titulo);
			ListView listaOperations = (ListView) findViewById(R.id.listOperationsDones);

			// LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver,
			// new IntentFilter("operationsDones-event"));

			SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
			int levelHours = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_LEVEL, "18").trim());
			int cleanHours = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_CLEAN, "18").trim());

			Date fromDateLevel = HelperDates.getInstance().addHours(new Date(), -levelHours);
			Date fromDateClean = HelperDates.getInstance().addHours(new Date(), -cleanHours);

			DBOperationsDone dbOperationsDone = new DBOperationsDone();
			ArrayList<OperationsDone> operationsDones = dbOperationsDone.getFromDate(fromDateLevel.getTime());
			operationsDones.removeIf(operationsDone -> operationsDone.getCodeOperation() == OperationsEnum.CLEAN.getKey());
			ArrayList<OperationsDone> operationsClean = dbOperationsDone.getFromDate(fromDateClean.getTime());
			operationsClean.removeIf(operationsDone -> operationsDone.getCodeOperation() == OperationsEnum.LEVEL.getKey()
					|| operationsDone.getCodeOperation() == OperationsEnum.COLLECT.getKey());
			dbOperationsDone.close();

			operationsDones.addAll(operationsClean);
			operationsDones.trimToSize();

			if (operationsDones.size() > 0) {
				operationsDones.sort((o1, o2) -> Long.compare(o2.getDate(), o1.getDate()));
				OperationsDoneAdapter modelAdapter = new OperationsDoneAdapter(MainActivity.getInstance(), operationsDones);
				listaOperations.setAdapter(modelAdapter);
				titulo.setText(String.format("%s: %d", getString(R.string.msg_operations_done), operationsDones.size()));
			} else {
				Toast.makeText(this, getResources().getString(R.string.msg_no_data), Toast.LENGTH_LONG).show();
				finish();
			}

			listaOperations.setOnItemClickListener((list, view, index, id) -> {
				operationDoneSelected = (OperationsDone) listaOperations.getItemAtPosition(index);

				LayoutInflater inflater = getLayoutInflater();
				View layout = inflater.inflate(R.layout.toast_custom, (ViewGroup) findViewById(R.id.toast_layout_root));
				
				TextView text = (TextView) layout.findViewById(R.id.text);
				text.setText(operationDoneSelected.toString());
				text.setTextColor(getResources().getColor(R.color.default_color));

				if (toast != null) {
					toast.cancel();
				}
				toast = new Toast(getApplicationContext());
				toast.setView(layout);
				toast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
				toast.setDuration(Toast.LENGTH_LONG);
				toast.show();

				OperationsDoneAdapter modelAdapter = (OperationsDoneAdapter) listaOperations.getAdapter();
				modelAdapter.setIndexSeleccionado(index);
				modelAdapter.notifyDataSetChanged();
			});

			findViewById(R.id.btCentrar).setOnClickListener(v -> {
				if (operationDoneSelected != null) {
					if (operationDoneSelected.getLat() == 0 || operationDoneSelected.getLng() == 0) {
						DBElemento dbElemento = new DBElemento();
						Elemento elemento = dbElemento.getByIdInterno(operationDoneSelected.getIdInternoElemento(), MainActivity.getInstance().getEmpresa());
						dbElemento.close();
						if (elemento == null) {
							Toast.makeText(this, "No se ha encontrado el elemento asociado.", Toast.LENGTH_LONG).show();
							return;
						}
						if (elemento.getEstado() != Elemento.ESTADO_ACTIVO) {
							Toast.makeText(this, "El elemento no está activo.", Toast.LENGTH_LONG).show();
							return;
						}
						operationDoneSelected.setLat(elemento.getPosition().latitude);
						operationDoneSelected.setLng(elemento.getPosition().longitude);
						DBOperationsDone db = new DBOperationsDone();
						db.update(operationDoneSelected);
						db.close();
					}
					LatLng posicion = new LatLng(operationDoneSelected.getLat(), operationDoneSelected.getLng());
					MyBroadCastManager.getInstance().sendBroadCastCenterMapBy(posicion, 18, "", true);
					finish();
				}
			});

			findViewById(R.id.btSalir).setOnClickListener(v -> finish());

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
			Toast.makeText(this, getResources().getString(R.string.error), Toast.LENGTH_LONG).show();
			finish();
		}

	}

	/**
	 * Este mitodo se ejecuta cada vez que se gira la pantalla
	 */
	@Override
	public void onConfigurationChanged(Configuration newConfig) {
		super.onConfigurationChanged(newConfig);
	}

	@Override
	public boolean onKeyDown(int keyCode, KeyEvent event) {
		if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
			finish();
		}
		return super.onKeyDown(keyCode, event);
	}

}
