package com.movisat.ecosat;

import android.content.Context;
import android.graphics.Color;
import android.location.Location;
import android.location.LocationManager;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.adapter.GrupoAreasAdapter;
import com.movisat.database.Area;
import com.movisat.database.DBArea;
import com.movisat.database.DBGrupoAreas;
import com.movisat.database.GrupoAreas;
import com.movisat.events.onFinishGrupoArea;
import com.movisat.fragment.GesElemMapFragment;
import com.movisat.utilities.Config;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


public class GruposActivity extends BaseActivity {

    //    private static GruposActivity instance = null;
    private static ListView listaGrupos;
    private static GruposActivity instance;


    private final String TODOS = "todos";
    private final String NINGUNO = "ninguno";
    private final String IS_MEDIDA = "IS_MEDIDA";
    private final String IS_MEDIDA_INICIADA = "medidaIniciada";
    private final String FECHA_MEDIDA = "inicioMedida";
    private final String GRUPO_MEDIR = "grupoMedir";
    private final String MODO_RUTA = "modo_ruta";
    private final String MODO_RUTA_PEATON = "peaton";
    private final String GRUPO_AREAS_VISIBLES = "grupoAreasVisibles";

    private static final int METROS_RADIO = 100;

    public List<Integer> getCodigosGruposSeleccionados() {
        return codigosGruposSeleccionados;
    }

    public void setCodigosGruposSeleccionados(List<Integer> codigosGruposSeleccionados) {
        this.codigosGruposSeleccionados = codigosGruposSeleccionados;
    }

    private List<Integer> codigosGruposSeleccionados;
    GrupoAreasAdapter modelAdapter;
    private EditText buscador;
    private boolean isMedida = false;

    public static GruposActivity getInstance() {
        return instance;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_grupos);
        instance = this;
        initCodigosGruposSeleccionados();
        listaGrupos = (ListView) findViewById(R.id.lvGruposAreas);

        buscador = (EditText) findViewById(R.id.BuscadorAreas);
        buscador.addTextChangedListener(new MyTextWatcher());


        if (codigosGruposSeleccionados == null)
            codigosGruposSeleccionados = new ArrayList<Integer>();

        Button todos = (Button) findViewById(R.id.btTodos);
        Button volver = (Button) findViewById(R.id.btVolver);
        Button ninguno = (Button) findViewById(R.id.btNinguno);

        isMedida = this.getIntent().getExtras().getBoolean(IS_MEDIDA);
        String modoRuta = Config.getInstance().getValue(MODO_RUTA, "");

        if (isMedida) {
            if (!modoRuta.equals(MODO_RUTA_PEATON)) {
                Toast.makeText(this,
                        getResources().getString(R.string.avisoModoPeatonNoDisponible),
                        Toast.LENGTH_LONG).show();

                finish();
            }

            ninguno.setText("Distancia");
            ninguno.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ico_center_gps, 0);
            ninguno.setOnClickListener(new CalculateDistanceClick());
            todos.setVisibility(View.GONE);
        } else {

            ninguno.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    if (!isMedida) {
                        // Pongo todos las áreas invisibles
                        // EcoSATApplication.getInstance().showProgressBar(instance, "Quitando zonas", "Quitando las zonas, por favor espere");
                        if (codigosGruposSeleccionados.size() > 0)
                            Toast.makeText(instance, "Limpiando zonas del mapa.", Toast.LENGTH_LONG).show();
                        for (int i = 0; i < codigosGruposSeleccionados.size(); i++) {
                            Integer item = codigosGruposSeleccionados.get(i);
                            GesElemMapFragment.getInstance().showAreasByGrupo(item, false, Color.GREEN);
                        }
                        modelAdapter.notifyDataSetChanged();
                    } else {
                        MainActivity.getInstance().adapterMenuDerecho.notifyDataSetChanged();
                        finish();
                    }
                }
            });

            todos.setOnClickListener(new MyClickTodos());
        }

        volver.setOnClickListener(new MyClickVolver());

        // Relleno la lista de modelos
        modelAdapter = new GrupoAreasAdapter(MainActivity.getInstance(),
                MainActivity.getInstance().getEmpresa());
        listaGrupos.setAdapter(modelAdapter);
        listaGrupos.setOnItemClickListener(new ClickGrupoArea());

        //Introducimos en el buscador el último texto guardado.
        buscador.setText(Config.getInstance().getValueUsuario("filtro_grupo", ""));
    }

    private void initCodigosGruposSeleccionados() {
        String grupos = Config.getInstance().getValueUsuario(GRUPO_AREAS_VISIBLES, NINGUNO);

        if (!grupos.equals(TODOS) && !grupos.equals(NINGUNO)) {
            //Iniciamos la variable de codigosSeleccionados.
            List<Integer> codigosGrupos = new ArrayList<Integer>();
            String[] numbers = grupos.split(",");
            for (String number : numbers) {
                codigosGrupos.add(Integer.parseInt(number));
            }

            if (codigosGrupos.size() > 0)
                setCodigosGruposSeleccionados(codigosGrupos);
        }

    }

    @Override
    protected void onStop() {
        int vis = 0;
        int inv = 0;
        StringBuilder models = new StringBuilder();

        boolean isMedidaIniciada = (Config.getInstance().getValueUsuario(
                IS_MEDIDA_INICIADA, "0").equals("1")) ? true : false;
        boolean isVisible = false;
        // Compruebo las areas seleccionadas
        for (int i = 0; i < listaGrupos.getCount(); i++) {

            GrupoAreas item = (GrupoAreas) listaGrupos.getItemAtPosition(i);

            // Compruebo la visibilidad del modelo
            isVisible = GesElemMapFragment.isVisibleGrupoArea(item.getId());
            if (isVisible) {

                if (models.length() == 0)
                    models.append(item.getId());
                else
                    models.append("," + item.getId());

                vis++;

            } else
                inv++;

        }
        if (isMedida) {
            if (isMedidaIniciada) {
                Toast.makeText(this, R.string.medidaIniciada,
                        Toast.LENGTH_LONG).show();
            } else {
                Toast.makeText(this, R.string.medidaCancelada,
                        Toast.LENGTH_LONG).show();
            }
        }


        if (!isMedida) {
            // Guardo la configuracián del usuario
            if (vis == listaGrupos.getCount())
                Config.getInstance().setValueUsuario("grupoAreasVisibles",
                        TODOS);
            else if (inv == listaGrupos.getCount())
                Config.getInstance().setValueUsuario("grupoAreasVisibles",
                        NINGUNO);
            else
                Config.getInstance().setValueUsuario("grupoAreasVisibles",
                        models.toString());
        } else
            EventBus.getDefault().post(new onFinishGrupoArea());
        super.onStop();
    }

    public class ClickGrupoArea implements AdapterView.OnItemClickListener {

        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            GrupoAreas grupoArea = (GrupoAreas) parent.getItemAtPosition(position);
            DBArea dbAreas = new DBArea();
            List<Area> areas = dbAreas.getAreasBy(grupoArea.getId());
            dbAreas.close();

            boolean areaSeleccionada = false;

            int grupoAreaMedir = Integer.parseInt(Config.getInstance()
                    .getValueUsuario(GRUPO_MEDIR, "0"));
            int codigo = 0;

            int grupo = grupoArea.getId();

            DBGrupoAreas dbGrupoAreas = new DBGrupoAreas();
            dbGrupoAreas.AddClick(grupoArea.getEmpresa(), grupoArea.getId());
            dbGrupoAreas.close();

            if (areas == null || areas.size() == 0) {

                Toast.makeText(instance,
                        getResources().getString(R.string.avisoNoHayAreas),
                        Toast.LENGTH_LONG).show();
                return;
            }

            //Tenemos que obtener las areas de ese grupo y mostrarlas
            if (isMedida) {
                // Si estoy aqui tengo que comprobar si en todas las areas de ese grupo hay un punto de control cercano a la latitud longitud
                LatLng punto = getLastLatLng();


                for (Area item :
                        areas) {
                    //  grupo = item.getGrupo();
                    codigo = item.getCodigo();

                    // if (item.isInside(punto)) {
                    if (item.isCercaPuntoControl(punto, METROS_RADIO)) {
                        if (!areaSeleccionada) {
                            //Ok me quedo con esta ?rea
                            // Guardar en configuraci?n ?rea seleccionada.
                            Config.getInstance().setValueUsuario(
                                    GRUPO_MEDIR, String.valueOf(grupo));

                            Config.getInstance().setValueUsuario(
                                    FECHA_MEDIDA,
                                    String.valueOf(System
                                            .currentTimeMillis()));

                            Config.getInstance().setValueUsuario(
                                    IS_MEDIDA_INICIADA, "1");
                            areaSeleccionada = true;
                        }
                    }

                    //}

                }

                if (!areaSeleccionada) {
                    Toast.makeText(
                            getInstance(),
                            R.string.noCercaPuntoControlMedida,
                            Toast.LENGTH_LONG).show();
                } else {
                    //tneemos que mostrar todas la areas que pertenece a ese grupo.
                    //Ademas si o si, no podemos iniciar una medida con visible false
                    GesElemMapFragment.getInstance().showAreasByGrupo(grupo,
                            true,
                            Color.MAGENTA);

                    finish();
                }
            } else {


                if (codigosGruposSeleccionados.indexOf(codigo) == -1)
                    codigosGruposSeleccionados.add(grupo);


                //if (grupo != grupoAreaMedir) {
                GesElemMapFragment.getInstance().showAreasByGrupo(grupo,
                        !GesElemMapFragment.isVisibleGrupo(grupo),
                        Color.GREEN);
                //}

            }


            // Para que se repinte la lista
            modelAdapter.notifyDataSetChanged();
        }
    }

    @NonNull
    private LatLng getLastLatLng() {
        LocationManager lm = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        Location location = null;
        if (lm != null) {
            //noinspection ResourceType
            location = lm.getLastKnownLocation(LocationManager.PASSIVE_PROVIDER);
        }

        double longitude = -1;
        double latitude = -1;
        if (location != null) {
            longitude = location.getLongitude();
            latitude = location.getLatitude();
        }

        return new LatLng(latitude, longitude);
    }

    public class MyTextWatcher implements TextWatcher {

        @Override
        public void afterTextChanged(Editable s) {

        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before,
                                  int count) {
            modelAdapter.getFilter().filter(s.toString());
        }

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_grupos, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        /*int id = item.getItemId();

        //noinspection SimplifiableIfStatement
        if (id == R.id.action_settings) {
            return true;
        }*/

        return super.onOptionsItemSelected(item);
    }


    private class MyClickTodos implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            // Pongo todos los grupos visibles
            for (int i = 0; i < listaGrupos.getCount(); i++) {

                GrupoAreas item = (GrupoAreas) listaGrupos.getItemAtPosition(i);

                GesElemMapFragment.getInstance().showAreasByGrupo(
                        item.getId(), true, Color.GREEN);

            }

            // Para que se repinte la lista
            modelAdapter.notifyDataSetChanged();
        }
    }

    private class MyClickVolver implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            finish();
        }
    }

    private class CalculateDistanceClick implements View.OnClickListener {
        @Override
        public void onClick(View v) {
            //Coger la posición actual y calcular la distancia
            // entre la posición actual GPS al área mas cercana de cada grupo.
            List<GrupoAreas> lista = modelAdapter.getAll();
            LatLng position = getLastLatLng();

            DBGrupoAreas dbGrupoAreas = new DBGrupoAreas();
            int distance = 0;

            if (position == null) return;
            for (GrupoAreas item :
                    lista) {
                distance = dbGrupoAreas.calculateDistance(item, position);
                item.setDistance(distance);
            }

            //Ordenar por distancia.
            Collections.sort(lista);
            dbGrupoAreas.close();

            modelAdapter.notifyDataSetChanged();
        }
    }

}
