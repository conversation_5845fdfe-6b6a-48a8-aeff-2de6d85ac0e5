package com.movisat.ecosat;

import com.movisat.database.Tags;
import com.movisat.tags.ITag;
import com.movisat.tags.TagUnknow;

public class AddElementParam {
    // Los valores por defecto son los que devuelve el bundle cuando no existe el campo.

    public Tags tags = null;
    public double longitude = 0;
    public double latitude = 0;
    public boolean insertImage = false;
    public boolean showNameField = false;
    public boolean update = false;
    public int externalId = 0;
    public int internalId = 0;
    private ITag iTag = null;

    // Poner valores por defecto si existían en el modo anterior
    public AddElementParam(double longitude, double latitude, boolean insertImage, boolean showNameField, ITag tag, Tags tags) {
        this.longitude = longitude;
        this.latitude = latitude;
        this.insertImage = insertImage;
        this.showNameField = showNameField;
        setITag(tag);
        this.tags = tags;
    }

    public AddElementParam(double longitude, double latitude, boolean insertImage, boolean showNameField, boolean update, int externalId, int internalId, ITag tag, Tags tags) {
        this.longitude = longitude;
        this.latitude = latitude;
        this.insertImage = insertImage;
        this.showNameField = showNameField;
        this.update = update;
        this.externalId = externalId;
        this.internalId = internalId;
        setITag(tag);
        this.tags = tags;
    }

    public ITag getITag() {
        if (iTag == null) return new TagUnknow("", "", null);
        return iTag;
    }

    public void setITag(ITag iTag) {
        this.iTag = iTag;
    }

    @Override
    public String toString() {
        return "AddElementParam{" +
                "tags=" + tags +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", insertImage=" + insertImage +
                ", showNameField=" + showNameField +
                ", update=" + update +
                ", externalId=" + externalId +
                ", internalId=" + internalId +
                ", iTag=" + iTag +
                '}';
    }
}
