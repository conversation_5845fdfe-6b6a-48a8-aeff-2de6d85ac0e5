package com.movisat.ecosat;

import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.Window;
import android.widget.ArrayAdapter;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.annotation.StringRes;

import com.environment.Environment;
import com.movisat.apis.LoginIndraApi;
import com.movisat.database.DBEmpresa;
import com.movisat.database.DBUsuario;
import com.movisat.database.Empresa;
import com.movisat.database.Usuario;
import com.movisat.events.OnExitApplication;
import com.movisat.events.onChangeLoading;
import com.movisat.events.onLoginLoading;
import com.movisat.outbox.OutBox;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utils.GlobalUtils;
import com.movisat.utils.Utilss;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;

import fr.castorflex.android.smoothprogressbar.SmoothProgressBar;
import fr.castorflex.android.smoothprogressbar.SmoothProgressDrawable;


public class LoginActivity extends BaseActivity {
    private static LoginActivity instance = null;
    private static Spinner cbEmpresa = null;
    private static ArrayAdapter<Empresa> adapter = null;
    private static ArrayList<Empresa> empresas = null;
    public static boolean isVisibleCompanies = false;

    public final static int MESSAGE_RELLENA_EMPRESAS = 0;
    public final static int MESSAGE_AVISA_USUARIO_NO_VALIDO = 1;
    public final static int MESSAGE_CLOSE_APP = 2;

    private final static String FAKE_PASSWORD = "XXXXXXXXXX";

    private EditText edPassword;

    private String lastLogin = "";
    private String passwordSaved = "";
    private boolean passwordChanged = true;
    private boolean showPasswordPressed = false;
    private boolean isEcoMovilInstalled = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {

            instance = this;

            requestWindowFeature(Window.FEATURE_INDETERMINATE_PROGRESS);

            try {
                setContentView(R.layout.login_layout);
                MyLoggerHandler.getInstance().info("Iniciado EcoSAT Móvil");

                edPassword = (EditText) findViewById(R.id.edPassw);

                isVisibleCompanies = true;

                // Cojo los datos del último usuario que accedió
                int idUsu = Integer.parseInt(Config.getInstance().getValue(
                        "loginUsuario", "-1"));
                int idEmp = Integer.parseInt(Config.getInstance().getValue(
                        "loginEmpresa", "-1"));
                String passw = Config.getInstance().getValue("passwUsuario", "");

                // Cargo los datos del usuario
                DBUsuario dbUsu = new DBUsuario();
                Usuario usu = dbUsu.getByID(idUsu, idEmp);
                dbUsu.close();

                // Comprobamos si EcoMovil está instalado para desactivar las mayúsculas automáticas.
                try {
                    isEcoMovilInstalled = GlobalUtils.isAppInstalled("com.movisat.ecomovil1", this);

                    if (isEcoMovilInstalled) {
                        // Cuando EcoSat trabaja con EcoSat móvil, desactivamos las mayúsculas automáticas,
                        // porque se usará el login de indra y ellos no escriben _todo en mayúscula.
                        ((EditText) findViewById(R.id.edLogin)).setInputType(InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS);
                        ((EditText) findViewById(R.id.edPassw)).setInputType(InputType.TYPE_CLASS_TEXT |
                                InputType.TYPE_TEXT_VARIATION_PASSWORD);
                    }
                } catch (Exception e) {

                }


                if (usu != null) {

                    // Pongo el usuario y posiciono el cursor en el campo Contraseia
                    // Para el login de Indra, se guarda en un campo para no usar el login del modelo
                    // de usuarios que cambia a mayúsculas.
                    if (usu.isIndra()) {
                        String value = Config.getInstance().getValue("loginIndra", "");
                        ((EditText) findViewById(R.id.edLogin)).setText(value);
                    } else {
                        ((EditText) findViewById(R.id.edLogin)).setText(usu.getLogin());
                    }


                    // Si hay contraseña guardada es porque hay que recordarla
                    if (!passw.equals("")) {

                        // Se almacena internamente la contraseña guardada (no debe ponerse
                        // directamente en el EditText). passwordChanged indica si se ha modificado
                        // el campo (en ese caso, passwordSaved será borrada).
                        passwordSaved = usu.getIdIndra() == null || usu.getIdIndra().isEmpty() ?
                                usu.getPasswDecode() :
                                Config.getInstance().getValue("passwUsuario", "");
                        passwordChanged = false;

                        // Se establece una contraseña falsa para que no pueda recuperarse del EditText
                        edPassword.setText(FAKE_PASSWORD);

                        ((CheckBox) findViewById(R.id.chbRecordar)).setChecked(true);
                    }

                    edPassword.requestFocus();
                    edPassword.setSelection(edPassword.getText().length());
                }

            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
            }

            // Se da funcionalidad al campo de mostrar contraseña
            setupPasswordField();

            // Creo el combo para las empresas
            cbEmpresa = (Spinner) findViewById(R.id.cbEmpresa);

            findViewById(R.id.btEntrar)
                    .setOnClickListener(new OnClickListener() {

                        @Override
                        public void onClick(View v) {
                            String login = ((EditText) findViewById(R.id.edLogin)).getText().toString();

                            if (login == null || login.trim().isEmpty())
                                return;

                            DBUsuario dbUsu = new DBUsuario();
                            Usuario user = dbUsu.getIndra(login);
                            dbUsu.close();

                            if (user != null && user.isIndra()) {
                                indraLogin(login, user);
                            } else {
                                // Cuando accede con un usuario que no es de indra, borramos la preferencia.
                                Config.getInstance().setValue("loginIndra", "");
                                LoginActivity.getInstance().setSyncProgressBarVisibility(true);
                                findViewById(R.id.btEntrar).setEnabled(false);
                                defaultLogin(login);
                            }
                        }

                    });

            // Compruebo si se ha activado la aplicación
            boolean appActive = MainActivity.getInstance().isAppActive();
            Log.i("Activa", "" + appActive);
            // Activo el botón solo si la aplicación se ha activado
            findViewById(R.id.btEntrar).setEnabled(appActive);

            // Si la aplicación no está activada aparecerá el diálogo de activación
            if (!appActive) {

                Intent i = new Intent(MainActivity.getInstance(), Activacion.class);
                i.putExtra("reactivate", true);

                startActivityForResult(i, 1);
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    private void indraLogin(String login, Usuario user) {

        try {
            if (Utilss.isNullOrEmptyTrim(login)) {
                showDialogMessage(R.string.noSeHaIndicadoElUsuario);
                return;
            }

            // La contraseña no viene en el objeto del usuario.
            String password = "";

            if (passwordSaved.equals(""))
                password = edPassword.getText().toString();
            else
                password = passwordSaved;

            if (Utilss.isNullOrEmptyTrim(password)) {
                showDialogMessage(R.string.noSeHaIndicadoContraseña);
                return;
            }

            String url = Config.getInstance().getValue("webSvc", "");

            String finalPassword = password;

            OutBox.wsc.getToken();

            if (Utilss.isNullOrEmpty(url)) {
                showDialogMessage(R.string.noSeHanObtenidoLosDatosDelServidor);
                return;
            }

            if (Utilss.isNullOrEmpty(OutBox.wsc.token)) {
                showDialogMessage(R.string.noSeHanPodidoConectarConElServidor);
                return;
            }

            if (user.getEmpresa() < 0) {
                showDialogMessage(R.string.laEmpresaNoEsValida);
                return;
            }

            // Para identificar usamos lo que el usuario ha escrito en el campo. EcoSat modifica el campo login de los modelos
            // de usuarios y los pone en mayúsculas cuando hay alguna modificación.
            new LoginIndraApi().execute(
                    url,
                    new LoginIndraApi.Request(login, password, user.getEmpresa()),
                    OutBox.wsc.token,
                    value -> {
                        if (!value) {
                            showDialogMessage(R.string.usuarioOContraseniaNoValidos);
                        } else {

                            isVisibleCompanies = false;
                            setResult(RESULT_OK);
                            Config.getInstance().setValue("loginIndra", login);

                            savePassword(finalPassword);
                            MainActivity.getInstance().setEmpresa(user.getEmpresa());
                            MainActivity.getInstance().setUsuario(user.getIdExterno());
                            MainActivity.getInstance().setUsuAdmin(user.getAdmin() > 0);

                            String subtitulo = Config.getInstance()
                                    .getValueUsuario(
                                            "menuActionBarSubTitle", "");

                            // Asignamos el subtitulo
                            MainActivity
                                    .getInstance()
                                    .getSupportActionBar()
                                    .setSubtitle(
                                            subtitulo.equals("") ? null
                                                    : subtitulo);

                            // Finalizo la actividad
                            finish();
                        }
                    }

            );
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            showDialogMessage(R.string.errorDesconocido);
        }

    }

    private void savePassword(String password) {
        // Compruebo si tengo que recordar la contraseia
        if (((CheckBox) findViewById(R.id.chbRecordar))
                .isChecked())
            Config.getInstance().setValue(
                    "passwUsuario", password);
        else
            Config.getInstance().setValue(
                    "passwUsuario", "");
    }

    private void defaultLogin(String login) {
        String passw;
        if (passwordSaved.equals(""))
            passw = edPassword.getText().toString();
        else
            passw = passwordSaved;

        Empresa empresa = null;
        Usuario usu = null;

        if (!login.equals(lastLogin)) {
            lastLogin = login;
        }

        if (cbEmpresa.getVisibility() != View.VISIBLE) {
            DBUsuario dbUsuario = new DBUsuario();
            ArrayList<Usuario> users = dbUsuario.getByLogin(login);
            dbUsuario.close();
            if (users.size() == 1) {
                usu = users.get(0);
                DBEmpresa dbEmpresa = new DBEmpresa();
                empresa = dbEmpresa.getByID(usu.getEmpresa());
                dbEmpresa.close();
            } else {
                rellenaComboEmpresas(login);
                return;
            }
        }

        // Cargo los datos del usuario
        if (empresa == null && cbEmpresa.getSelectedItem() != null) {
            empresa = ((Empresa) cbEmpresa.getSelectedItem());
            DBUsuario dbUsu = new DBUsuario();
            usu = dbUsu.getByLogin(login, empresa.getId());
            dbUsu.close();
        }

        if (usu == null) {
            if (empresa != null) {
                MyLoggerHandler.getInstance().info(
                        String.format("Intentando entrar con usuario %s y clave %s en empresa %s",
                                login, passw, empresa.toString()));
            }
        }

        // Compruebo si coincide la contraseia
        if (usu != null
                && (usu.getPasswDecode().equals(passw) || usu.getPassw().equals(passw))) {
            MyLoggerHandler.getInstance().info(
                    String.format("Entrada con usuario %s y clave %s en empresa %s",
                            login, passw, empresa.toString()));

            savePassword(usu.getPassw());

            isVisibleCompanies = false;

            setResult(RESULT_OK);


            // Fuerzo la sincronizaciin para que se
            // sincronicen todos los datos de la empresa
            // actual
            // if (!DBSynchro.getInstance().getSynchro())
            //     DBSynchro.getInstance().forceSync();

            // Asigno la empresa y el ususario activo
            MainActivity.getInstance().setEmpresa(usu.getEmpresa());
            MainActivity.getInstance().setUsuario(usu.getIdExterno());
            MainActivity.getInstance().setUsuAdmin(usu.getAdmin() > 0);

            // TODO: REDUCIDOS: Eliminar esta lógica cuando el nuevo endpoint esté en todos los servidores
            Environment.hasNewElementEndpoint = Config.getInstance().getValueEmpresa("elementosReducidos", "false").equals("true");

            String subtitulo = Config.getInstance()
                    .getValueUsuario(
                            "menuActionBarSubTitle", "");

            // Asignamos el subtitulo
            MainActivity
                    .getInstance()
                    .getSupportActionBar()
                    .setSubtitle(
                            subtitulo.equals("") ? null
                                    : subtitulo);
            /*
             * int idMenu = MainActivity.getInstance()
             * .getIdItemMenu();
             * MainActivity.getInstance().callActionMenu(
             * idMenu);
             */

            // Finalizo la actividad
            finish();

        } else {
            LoginActivity.getInstance().setSyncProgressBarVisibility(false);
            findViewById(R.id.btEntrar).setEnabled(true);
            showDialogMessage(R.string.usuarioClaveNoValida);
        }
    }

    public static LoginActivity getInstance() {

     /*   if (instance == null) {
            instance = new LoginActivity();
        }*/

        return instance;
    }

    /*
     * @Override public boolean onCreateOptionsMenu(Menu menu) {
     *
     * MenuInflater inflater = getMenuInflater(); inflater.inflate(R.menu.main,
     * menu);
     *
     * return super.onCreateOptionsMenu(menu); }
     */

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {

        return super.onPrepareOptionsMenu(menu);
    }

    /*
     * @Override public boolean onOptionsItemSelected(MenuItem item) {
     *
     * // Barra de acciones if (item.getTitle().equals("Activaciin")) {
     *
     * Intent i = new Intent(MainActivity.getInstance(), Activacion.class);
     * i.putExtra("reactivate", true);
     *
     * startActivityForResult(i, 1);
     *
     * return true; }
     *
     * return super.onOptionsItemSelected(item); }
     */

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (MainActivity.getInstance() == null) return;
        switch (requestCode) {
            case 1: // Diálogo de activación de producto
                if (MainActivity.getInstance().checkLicense()) {

                    // Activo el botón solo si la aplicación se ha activado
                    findViewById(R.id.btEntrar).setEnabled(true);

                    MainActivity.getInstance().setAppActive(true);

                    new InfoDialog(
                            instance,
                            instance.getString(R.string.msg_aviso_activacion),
                            instance.getString(R.string.msg_agradecimiento_movisat),
                            InfoDialog.ICON_INFO, new OnInfoDialogSelect() {

                        @Override
                        public void onSelectOption(int option) {
                            MainActivity.getInstance().restart(instance);
                        }

                    }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER)
                            .show();
                    // instance.finish();

                } else
                    finish();

                break;
        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        switch (keyCode) {
            case KeyEvent.KEYCODE_MENU:
                return false;

            case KeyEvent.KEYCODE_BACK:
                isVisibleCompanies = false;
                close();
                //finish();
        }

        return super.onKeyDown(keyCode, event);
    }

    public static void close() {
        Log.e("", "close app");
        LoginActivity.getInstance().finish();
        //MainActivity.getInstance().finish();

        if (MainActivity.getInstance() != null)
            MainActivity.getInstance().closeApp();

    }

    @Override
    public void finish() {
        super.finish();
        instance = null;
    }

    @Override
    public void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
        EventBus.getDefault().post(new onLoginLoading());

    }

    @Override
    public void onStop() {
        EventBus.getDefault().unregister(this);
        super.onStop();
    }

    // This method will be called when a MessageEvent is posted
    @Subscribe
    public void onEvent(onChangeLoading event) {
        setSyncProgressBarVisibility(event.isVisible);
    }

    @Subscribe
    public void onEvent(OnExitApplication event) {
        android.os.Process.killProcess(android.os.Process.myPid());
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        startActivity(intent);
    }

    public static final Handler handler = new Handler() {

        @Override
        public void handleMessage(final Message message) {
            if (MainActivity.getInstance() == null) return;
            Resources res = MainActivity.getInstance().getResources();
            try {

                synchronized (instance) {

                    if (!MainActivity.getInstance().getIntranet())
                        return;

                    switch (message.what) {
                        case MESSAGE_AVISA_USUARIO_NO_VALIDO:
                            Toast.makeText(
                                    MainActivity.getInstance(),
                                    getInstance().getResources().getString(
                                            R.string.avisoMovilNoAlta),
                                    Toast.LENGTH_LONG).show();
                            break;
                 /*       case MESSAGE_ACTIVE_LOADING:
                            instance.setSyncProgressBarVisibility(true);
                            break;
                        case MESSAGE_DESACTIVE_LOADING:
                            instance.setSyncProgressBarVisibility(false);
                            break;*/
                        case MESSAGE_CLOSE_APP:

                            new InfoDialog(MainActivity.getInstance(),
                                    res.getString(R.string.appSeCerrara),
                                    res.getString(R.string.appCerrar), InfoDialog.ICON_QUESTION,
                                    new OnInfoDialogSelect() {
                                        @Override
                                        public void onSelectOption(int option) {
                                            if (option == InfoDialog.BUTTON_ACCEPT)
                                                close();
                                        }

                                    }, InfoDialog.BUTTON_ACCEPT,
                                    InfoDialog.POSITION_CENTER).show();

                            break;

                        case MESSAGE_RELLENA_EMPRESAS:

                            if (isVisibleCompanies) {

                                // Se recuperan las empresas asociadas al usuario
                                if (message.obj == null || !(message.obj instanceof String))
                                    return;
                                String login = (String) message.obj;
                                DBUsuario dbUsuario = new DBUsuario();
                                ArrayList<Usuario> users = dbUsuario.getByLogin(login);
                                dbUsuario.close();

                                // Recupero el código de empresa del usuario que
                                // accedió la última vez
                                int lastEmp = Integer.parseInt(Config.getInstance()
                                        .getValue("loginEmpresa", "-1"));

                                // Solo se muestra el combo de empresas si hay más de una
                                if (users != null && users.size() > 1) {

                                    // Se recuperan las empresas
                                    empresas = new ArrayList<>();
                                    DBEmpresa dbEmpresas = new DBEmpresa();
                                    for (Usuario user : users) {
                                        Empresa empresa = dbEmpresas.getByID(user.getEmpresa());
                                        if (empresa != null)
                                            empresas.add(empresa);
                                    }
                                    dbEmpresas.close();

                                    // Relleno el combo de empresas
                                    adapter = new ArrayAdapter<Empresa>(
                                            MainActivity.getInstance(),
                                            android.R.layout.simple_list_item_1,
                                            empresas);

                                    cbEmpresa.setAdapter(adapter);

                                    // Recorro las empresas para seleccionar en el combo la última
                                    for (Empresa empresa : empresas) {

                                        if (empresa.getId() == lastEmp)
                                            cbEmpresa.setSelection(adapter.getPosition(empresa));
                                    }

                                    if (empresas.size() > 1)
                                        cbEmpresa.setVisibility(View.VISIBLE);
                                    else
                                        cbEmpresa.setVisibility(View.GONE);
                                } else {
                                    LoginActivity.getInstance().showDialogMessage(R.string.usuarioClaveNoValida);
                                }
                            }
                            LoginActivity.getInstance().findViewById(R.id.btEntrar).setEnabled(true);
                            LoginActivity.getInstance().setSyncProgressBarVisibility(false);
                            break;

                        default:
                            break;
                    }

                }

            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
            }

        }

    };

    public static void rellenaComboEmpresas(String login) {

        try {

            Message msg = new Message();

            msg.what = MESSAGE_RELLENA_EMPRESAS;
            msg.obj = login;

            handler.sendMessage(msg);
            // handler.sendEmptyMessage();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    /**
     * Este mitodo se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }


    /**
     * Establece la funcionalidad del campo de contraseña.
     * Si había contraseña recordada, ésta se elimina si el campo se modifica.
     * Si se pulsa el botón se mostrar contraseña, se muestra; si se levanta o se sale, se oculta.
     */
    private void setupPasswordField() {

        edPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void afterTextChanged(Editable editable) {

                try {


                    // Si el cambio de texto no se ha debido a que se ha pulsado el botón de mostrar
                    // contraseña y la contraseña no ha cambiado, se elimina el texto.
                    if (!showPasswordPressed && !passwordChanged) {
                        passwordChanged = true;
                        passwordSaved = "";
                        edPassword.removeTextChangedListener(this);
                        String text = "";
                        if (editable.length() > FAKE_PASSWORD.length())
                            text = editable.toString().substring(editable.length() - 1);
                        edPassword.setText(text);
                        edPassword.setSelection(text.length());
                        edPassword.addTextChangedListener(this);
                    }
                } catch (Exception e) {
                }
            }
        });

        ImageButton btnShowPassword = (ImageButton) findViewById(R.id.btnShowPassword);

        btnShowPassword.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                int selectionIndexPass;

                try {

                    // Se guarda la posición del cursor porque se resetea en cada cambio.
                    if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {

                        showPasswordPressed = true;
                        selectionIndexPass = edPassword.getSelectionStart();

                        int inputType = 0;

                        if (isEcoMovilInstalled) {
                            inputType = InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS |
                                    InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD;

                        } else {
                            inputType = InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS |
                                    InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD |
                                    InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS;
                        }
                        edPassword.setInputType(inputType);
                        edPassword.setSelection(selectionIndexPass);

                    } else if (motionEvent.getAction() == MotionEvent.ACTION_UP |
                            motionEvent.getAction() == MotionEvent.ACTION_CANCEL) {

                        selectionIndexPass = edPassword.getSelectionStart();
                        edPassword.setInputType(InputType.TYPE_CLASS_TEXT |
                                InputType.TYPE_TEXT_VARIATION_PASSWORD);
                        edPassword.setSelection(selectionIndexPass);
                        showPasswordPressed = false;
                    }

                } catch (Exception e) {
                }

                return false;
            }
        });
    }


    private void showDialogMessage(@StringRes int resId) {
        try {
            new InfoDialog(
                    instance,
                    getString(R.string.atencion),
                    getString(resId),
                    InfoDialog.ICON_STOP,
                    new OnInfoDialogSelect() {
                        @Override
                        public void onSelectOption(
                                int option) {
                        }

                    }, InfoDialog.BUTTON_ACCEPT,
                    InfoDialog.POSITION_CENTER).show();
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }


    /**
     * Establece la visibilidad de la barra de progreso que indica el proceso de sincronización.
     *
     * @param visible true para mostrar la barra de progreso, false para ocultarla.
     */
    public void setSyncProgressBarVisibility(boolean visible) {
        try {
            SmoothProgressBar progressBar = findViewById(R.id.progressBarSync);
            if (progressBar != null) {
                progressBar.setSmoothProgressDrawableCallbacks(new SmoothProgressDrawable.Callbacks() {
                    @Override
                    public void onStop() {
                        progressBar.setVisibility(View.GONE);
                    }

                    @Override
                    public void onStart() {
                        progressBar.setVisibility(View.VISIBLE);
                    }
                });

                if (visible) {
                    progressBar.progressiveStart();
                } else {
                    progressBar.progressiveStop();
                }
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }
}
