package com.movisat.ecosat;

import android.app.ProgressDialog;
import android.content.Intent;
import android.database.Cursor;
import android.location.Location;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.cursoradapter.widget.SimpleCursorAdapter;

import com.environment.Environment;
import com.movisat.bll.SensoresManager;
import com.movisat.database.DBElemento;
import com.movisat.database.DBMunicipios;
import com.movisat.database.DBProvincias;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.Tags;
import com.movisat.events.OnReadedTag;
import com.movisat.events.onChangeLocation;
import com.movisat.fragment.GestionElementos;
import com.movisat.log.Logg;
import com.movisat.services.PositionService;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.use_case.TagSendSensor;
import com.movisat.utilities.Config;
import com.movisat.utilities.GPS;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Utils;

import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.zip.GZIPInputStream;


public class ProcesarElementoActivity extends BaseActivity {
    private static ProcesarElementoActivity instance = null;
    private static final String TAG = "ProcesarElementoActivity";

    private boolean isCheckedManuallyProvinceMunicipality = false;
    private long timeCallApi = 0;
    public static final int CAMACHO_JEFE = 202;
    private asyncGIS syncro = null;
    private ProgressDialog pDialog = null;

    final int DISTANCIA = 30;
    private String SIN_CONEXION_GIS = "Sin conexión al servidor GIS";
    private boolean isLoadingMunicipio = false;

    public enum ModoProcesado {

        NivelLlenado, Lavado

    }

    private static ModoProcesado _modo;
    EditText numeroElemento;
    Spinner spinnerMunicipios;
    Spinner spinnerProvincias;
    TextView lblTituloSwitch;
    TextView titulo;
    String municipio;
    String tipoSoft = "0";
    Switch switchButton;
    TextView textInfo = null;
    Button btnProcesa = null;
    String stringMunicipio;
    CheckBox cbProcesado = null;
    CheckBox cbVaciadoParcial = null;
    private boolean mVersionJefe;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        instance = this;
        tipoSoft = Config.getInstance().getValue("tipoSoft", "0");

        setContentView(R.layout.buscar_elemento_cercano);

        GPS.isActiveGPS(instance);

        OnClickListener clickProcesaListener = null;

        // Se obtiene si la opción escogida es de una versión jefe
        mVersionJefe = getIntent().getBooleanExtra("version_jefe", false);

        numeroElemento = (EditText) findViewById(R.id.numeroElemento);
        titulo = (TextView) findViewById(R.id.lblTitulo);
        btnProcesa = (Button) findViewById(R.id.btnProcesa);
        Button btnVolver = (Button) findViewById(R.id.btnVolver);
        cbProcesado = (CheckBox) findViewById(R.id.checkProcesado);
        cbVaciadoParcial = (CheckBox) findViewById(R.id.checkVaciadoParcial);
        spinnerMunicipios = (Spinner) findViewById(R.id.eMunicipios);
        spinnerProvincias = (Spinner) findViewById(R.id.eProvincias);
        lblTituloSwitch = (TextView) findViewById(R.id.lblTituloSwitch);
        switchButton = (Switch) findViewById(R.id.btnSwitch);


        cbProcesado.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                if (isChecked)
                    cbVaciadoParcial.setVisibility(View.VISIBLE);
                else {
                    cbVaciadoParcial.setVisibility(View.INVISIBLE);
                    cbVaciadoParcial.setChecked(false);
                }

            }
        });

        btnVolver.setOnClickListener(new clickVolverMapa());

        clickProcesaListener = new ClickProcesar();
        btnProcesa.setOnClickListener(clickProcesaListener);

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
    }

    @Subscribe
    public void onEvent(onChangeLocation event) {

        try {
            // si es una versión Jefe y no está marchado el check de selección manual de municipio, no recalculo éste
            if (switchButton.isChecked())
                return;

            // Cada minuto se llama al GIS para obtener el municipio
            if (System.currentTimeMillis() - timeCallApi > 2000) {
                if (timeCallApi > 0) {
                    if (!isLoadingMunicipio)
                        setMunicipio();
                }
                timeCallApi = System.currentTimeMillis();
            }
        } catch (Exception e) {
            MyLoggerHandler.getInstance().info("Error en onEvent - " + e.getMessage());
        }
    }

    @Override
    /*protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }*/

    protected void onStart() {
        super.onStart();
        ClientWebSvc sweb = new ClientWebSvc();
        try {
            sweb.refreshToken();
            EventBus.getDefault().register(this);
            numeroElemento.setText(null);
            // DINIGO - Mantis 0005122 - Evitamos que se vuelva a calcular el municipio si está marcado el selector manual.
            if (!isCheckedManuallyProvinceMunicipality) {
                setMunicipio();
                while (municipio.equals("")) {
                    Thread.sleep(2000);
                    setMunicipio();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /*@Override
    protected void onResume() {
        super.onResume();
        try {
            if (!isCheckedManuallyProvinceMunicipality) {
                setMunicipio();
                while (municipio.equals("")) {
                    Thread.sleep(2000);
                    setMunicipio();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }*/

    @Override
    protected void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);

        if (pDialog != null && pDialog.isShowing()) {
            pDialog.cancel();
            pDialog = null;
        }

        /*if (syncro!= null){
            syncro.cancel(true);
        }*/

    }


    class asyncGIS extends AsyncTask<Void, String, JSONArray> {


        private double lat, lon;
        JSONArray jsonData = null;

        public asyncGIS(double latitud, double longitud) {
            isLoadingMunicipio = true;
            this.lat = latitud;
            this.lon = longitud;
        }

        @Override
        protected void onPreExecute() {
            // para el progress dialog
            /*pDialog = new ProgressDialog(ProcesarElementoActivity.getInstance());
            pDialog.setMessage("Conectando al servidor, por favor espere.");
            //pDialog.setIndeterminate(true);
            pDialog.setCancelable(false);
            pDialog.setButton(DialogInterface.BUTTON_NEGATIVE, "Cancelar y reintentar.", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    pDialog.cancel();
                    dialog.dismiss();
                    isLoadingMunicipio = false;
                }
            });

            pDialog.show();*/
        }

        @Override
        protected JSONArray doInBackground(Void... strings) {

            // DINIGO - Mantis 0005119 - Si no tiene internet no realizamos la comprobación ya que genera que el sistema cierre la aplicación por el retraso.
            if (!MainActivity.getInstance().isNetworkAvailable()) return null;

            ClientWebSvc sweb = new ClientWebSvc();

            String url = Config.getInstance().getValue("webSvc", "");
            if (url.equals(""))
                return null;

            url = url + "/api/gis/calle/" + this.lat + "/" + this.lon;

            HttpPost post = new HttpPost(url);

            // Preparo la llamada para recibir datos
            post.setHeader("User-Agent", "java-Android");
            post.setHeader("Content-type", "application/json");
            post.setHeader("Accept-encoding", "gzip");
            post.setHeader("Authorization", "Bearer " + sweb.token);

            // Establezco timeout alto
            HttpParams parametros = new BasicHttpParams();

            int timeoutConnection = 10000;
            HttpConnectionParams.setConnectionTimeout(parametros, timeoutConnection);
            int timeoutSocket = 10000;
            HttpConnectionParams.setSoTimeout(parametros, timeoutSocket);
            DefaultHttpClient httpClient = new DefaultHttpClient(parametros);
            HttpResponse response;

            boolean salir = false;

            // lo intento 4 veces, en caso contrario sigo sin indicar el municipio, tendrá que ponerlo manualmente.
            for (int i = 0; i < 4 && !salir; i++) {
                try {

                    response = httpClient.execute(post);
                    int result = response.getStatusLine().getStatusCode();
                    MyLoggerHandler.getInstance().info(String.valueOf(result));
                    //if(i < 2) result = 401;

                    switch (result) {
                        case 200: // Respuesta correcta

                            InputStream inStream = response.getEntity().getContent();

                            Header contentEncoding = response.getFirstHeader("Content-Encoding");
                            if (contentEncoding != null && contentEncoding.getValue().equalsIgnoreCase("gzip")) {
                                inStream = new GZIPInputStream(inStream);
                            }

                            String strResponse = sweb.convertStreamToString(inStream);

                            try {
                                // En principio intento recuperar los datos como un array JSON si falla lo intento como JSON
                                jsonData = new JSONArray(strResponse);
                                salir = true;

                            } catch (JSONException e) {

                                try {
                                    // Recupero la respuesta JSON
                                    jsonData = new JSONArray()
                                            .put(new JSONObject(strResponse));

                                } catch (Exception ex) {
                                    MyLoggerHandler.getInstance().error(
                                            ex);
                                }

                                salir = true;
                            }
                            break;

                        case 401: //<<El token ya no es valido
                            /*synchronized (this){
                                sweb.token = "";
                                sweb.refreshToken();
                            }*/
                            MyLoggerHandler.getInstance().info("[TOKEN]: En ProcesarElemento se ha expirado el token.");
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    refreshActivity();
                                }
                            });
                            break;


                        default:
                            try {

                                Thread.sleep(5000);

                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                    }

                } catch (UnsupportedEncodingException e) {
                    MyLoggerHandler.getInstance().error(
                            e);
                } catch (ClientProtocolException e) {
                    MyLoggerHandler.getInstance().error(
                            e);
                } catch (IOException e) {
                    if (e instanceof HttpHostConnectException) {
                        try {
                            Thread.sleep(2000);

                            // si se cancela el progressbar, y pilla intentando conectar al gis
                            // cancelo para que salga.
                            if (!isLoadingMunicipio)
                                break;

                        } catch (InterruptedException e1) {
                            MyLoggerHandler.getInstance().error(
                                    e1);
                        }
                    }

                } catch (Exception e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            }


            return jsonData;
        }

        @Override
        protected void onPostExecute(JSONArray result) {

            try {
                if (result != null)
                    municipio = result.getJSONObject(0).get("Municipio").toString();
            } catch (JSONException e) {
                MyLoggerHandler.getInstance().error(
                        e);
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(
                        e);
            }

            try {

                /*if(municipio.equals("") || (MainActivity.getInstance().isNetworkAvailable() && result == null)){
                    Thread.sleep(5000);
                    refreshActivity();
                }*/

                if (municipio == null || result == null)
                    municipio = SIN_CONEXION_GIS;

                visualizaCampos();

                switch (getModo()) {
                    case Lavado:
                        titulo.setText(getString(R.string.lavando) + " " + municipio);
                        cbProcesado.setVisibility(View.INVISIBLE);
                        cbVaciadoParcial.setVisibility(View.INVISIBLE);
                        break;

                    case NivelLlenado:

                        titulo.setText(getString(R.string.niveles) + " " + municipio);
                        break;
                }


                if (result == null)
                    setProvincias();

                int soft = Integer.parseInt(tipoSoft);
                if (!municipio.equals("") && !municipio.equals(SIN_CONEXION_GIS) && (soft != CAMACHO_JEFE && soft != 223 && soft != 226 && soft != 219)) {

                    lblTituloSwitch.setVisibility(View.GONE);
                    switchButton.setVisibility(View.GONE);

                } else {

                    lblTituloSwitch.setVisibility(View.VISIBLE);
                    switchButton.setVisibility(View.VISIBLE);
                    switchButton.setChecked(isCheckedManuallyProvinceMunicipality);

                    // DINIGO - Mantis 0005122 - Al indicar un nivel de llenado en la siguiente página y volver a la página actual, no se guardaba la provincia seleccionada.
                    if (isCheckedManuallyProvinceMunicipality)
                        onCheckedProvince();

                    switchButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {

                        @Override
                        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                            isCheckedManuallyProvinceMunicipality = isChecked;
                            if (isChecked) {
                                onCheckedProvince();
                            } else {
                                spinnerMunicipios.setVisibility(View.GONE);
                                spinnerProvincias.setVisibility(View.GONE);
                                textInfo = null;
                                titulo.setText(getString(R.string.niveles) + " " + municipio);
                            }
                        }

                    });

                }
            } catch (Exception e) {
                MyLoggerHandler.getInstance().error(
                        e);
            }

            if (pDialog != null) {
                pDialog.dismiss();
                pDialog.cancel();
            }

            isLoadingMunicipio = false;

        }

        private void onCheckedProvince() {
            spinnerMunicipios.setVisibility(View.VISIBLE);
            spinnerProvincias.setVisibility(View.VISIBLE);
            setProvincias();
            OnItemSelectedListener selectedMunicipio = new SelectedMunicipio();
            spinnerMunicipios.setOnItemSelectedListener(selectedMunicipio);
            textInfo = (TextView) spinnerMunicipios
                    .getSelectedView();
        }

        @Override
        protected void onCancelled() {
            Toast.makeText(ProcesarElementoActivity.getInstance(), "Tarea cancelada!",
                    Toast.LENGTH_SHORT).show();
            pDialog.dismiss();
            pDialog.cancel();
            municipio = SIN_CONEXION_GIS;
        }
    }

    private void visualizaCampos() {
        if (Environment.isSoftCamacho) {
            //cbProcesado.setChecked(true);
            cbProcesado.setVisibility(View.VISIBLE);
            cbVaciadoParcial.setVisibility(View.VISIBLE);
        } else {
            cbProcesado.setVisibility(View.GONE);
            cbVaciadoParcial.setVisibility(View.GONE);
        }

        if (!cbProcesado.isChecked()) {
            cbVaciadoParcial.setVisibility(View.INVISIBLE);
            cbVaciadoParcial.setChecked(false);
        }

        spinnerMunicipios.setVisibility(View.GONE);
        spinnerProvincias.setVisibility(View.GONE);
    }

    private void setMunicipio() {

        double latitudTemp = 0;
        double longitudTemp = 0;

        try {

            if (PositionService.getInstance().hasLocationValid() && PositionService.getInstance().isLocationRecent()) {
                latitudTemp = PositionService.getInstance().getLastLocation().getLatitude();
                longitudTemp = PositionService.getInstance().getLastLocation().getLongitude();
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().info("Error en setMunicipio - " + e.getMessage());
        }

        try {
            if (syncro != null)
                syncro.cancel(true);


            /*ClientWebSvc sweb = new ClientWebSvc();
            sweb.refreshToken();*/

            syncro = new asyncGIS(latitudTemp, longitudTemp);
            syncro.execute();

        } catch (Exception e) {
            Log.e("", "error: " + e.getMessage());
        }

    }


    private void setProvincias() {

        DBProvincias dbProvincias = new DBProvincias();

        try {

            Cursor cursor = dbProvincias.getAllCursor();
            String[] from = new String[]{"provincia"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(this,
                    android.R.layout.simple_spinner_item, cursor, from, to, 0);

            // set layout for activated adapter
            dataAdapter
                    .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            spinnerProvincias.setAdapter(dataAdapter);
            OnItemSelectedListener selectedProvincia = new SelectedProvincia();
            spinnerProvincias.setOnItemSelectedListener(selectedProvincia);

            int indexPos = Integer.valueOf(Config.getInstance().getValueUsuario(
                    "SelectedProvincia", "-1"));
            if (indexPos > -1)
                spinnerProvincias.setSelection(indexPos);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(
                    e);
        }

        dbProvincias.close();

    }

    public class SelectedMunicipio implements OnItemSelectedListener {

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int i, long l) {
            try {
                stringMunicipio = ((TextView) view).getText().toString();
                municipio = stringMunicipio;
                titulo.setText(getString(R.string.niveles) + " " + stringMunicipio);
                textInfo = ((TextView) view);

            } catch (Exception ex) {
                MyLoggerHandler.getInstance().error(
                        ex);
            }
        }

        @Override
        public void onNothingSelected(AdapterView<?> adapterView) {
        }
    }

    public class SelectedProvincia implements OnItemSelectedListener {

        @Override
        public void onItemSelected(AdapterView<?> parent, View view, int pos,
                                   long id) {
            Cursor c = ((SimpleCursorAdapter) parent.getAdapter()).getCursor();
            c.moveToPosition(pos);

            DBMunicipios managerMunicipios = new DBMunicipios();
            int idProvincia = c.getInt(0);
            Cursor cursor = managerMunicipios.getAllCursorBy(idProvincia);
            String[] from = new String[]{"municipio"};
            int[] to = new int[]{android.R.id.text1};

            SimpleCursorAdapter dataAdapter = new SimpleCursorAdapter(instance,
                    android.R.layout.simple_spinner_item, cursor, from, to, 0);

            // set layout for activated adapter
            dataAdapter
                    .setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

            spinnerMunicipios.setAdapter(dataAdapter);
            managerMunicipios.close();

            //int indexOf = Integer.valueOf(Config.getInstance().getValueUsuario(
            //"SelectedMunicipio", "-1"));

            int indexOf = -1;
            cursor.moveToFirst();
            for (int i = 0; i < cursor.getCount(); i++) {
                String temp = cursor.getString(1);
                if (temp.contentEquals(municipio.toUpperCase())) {
                    indexOf = i;
                    break;
                }
                cursor.moveToNext();
            }

            if (indexOf > -1) {
                try {
                    if (indexOf < spinnerMunicipios.getCount()) {
                        spinnerMunicipios.setSelection(indexOf);
                    }
                } catch (Exception ex) {

                }
            }

            Config.getInstance().setValueUsuario("SelectedProvincia",
                    String.valueOf(pos));

        }

        @Override
        public void onNothingSelected(AdapterView<?> arg0) {
            // TODO Auto-generated method stub

        }

    }

    @Override
    public void finish() {
        super.finish();

        if (mVersionJefe) {
            //TODO: Si cambian el numero de dispositivos no me valdria hacer esto
            /*String indexDispositivo = Config.getInstance().getValueUsuario("indexDispositivo", "");
            if (indexDispositivo.length() > 0) {
                Config.getInstance()
                        .setValueUsuario("FlotaSelect", indexDispositivo);

            }*/
            Config.getInstance().setValueUsuario("FlotaSelect", "-1");
        }
    }

    private class ClickProcesar implements OnClickListener {
        @Override
        public void onClick(View v) {

            // String municipio2 = municipio.toUpperCase();
            String municipioNew = municipio;

            MyLoggerHandler.getInstance().info("ClickProcesar: Municipio: " + municipioNew);

            if ((municipioNew.equals("") || municipioNew.equals(SIN_CONEXION_GIS) || tipoSoft.equals(String.valueOf(CAMACHO_JEFE))) && textInfo != null) {
                municipioNew = textInfo.getText().toString();
                Config.getInstance().setValueUsuario(
                        "SelectedMunicipio",
                        String.valueOf(spinnerMunicipios
                                .getSelectedItemPosition()));
            }
            // Buscar elemento a partir del nombre.
            DBElemento dbElemento = new DBElemento();
            Elemento elemento = dbElemento.getElementoBy(municipioNew + " "
                    + numeroElemento.getText(), MainActivity.getInstance()
                    .getEmpresa());
            dbElemento.close();

           /* if (elemento!=null)
                MyLoggerHandler.getInstance().info("ClickProcesar: Elemento: "+ elemento.getNombre());
            else
                MyLoggerHandler.getInstance().info("ClickProcesar: Elemento es nulo");*/

            // Si no ha encontrado el elemento mostramos un mensaje y fuera.
            if (elemento == null) {
                Toast.makeText(
                        instance,
                        R.string.noEncontradoElemento,
                        Toast.LENGTH_SHORT).show();
                return;
            }

            boolean isEnRadio = false;
            double latitud;
            double longitud;

            try {
                // Buscar elemento en un radio de 50 metros.
                if (PositionService.getInstance().hasLocationValid() && PositionService.getInstance().isLocationRecent()) {
                    latitud = PositionService.getInstance().getLastLocation().getLatitude();
                    longitud = PositionService.getInstance().getLastLocation().getLongitude();
                    MyLoggerHandler.getInstance().info("Cojo posicion Service");
                } else {
                    GestionElementos.GPSInfo infoGPS = GestionElementos.ultGpsPos;
                    latitud = infoGPS.getPosition().latitude;
                    longitud = infoGPS.getPosition().longitude;
                    MyLoggerHandler.getInstance().info("Cojo posicion Gestion elementos");
                }

                if (latitud != 0) {
                    float distancia = GetDistanceBy(
                            latitud,
                            longitud,
                            elemento.getPosition().latitude,
                            elemento.getPosition().longitude);

                    if (distancia <= DISTANCIA) { // Avisamos que no esta dentro de la
                        //  distancia y salimos.
                        isEnRadio = true;
                    }
                    System.out.println("DISTANCIA entre el elemento y posición GPS: " + distancia);
                }
            } catch (Throwable ex) {
                MyLoggerHandler.getInstance().error(ex);
            }


            // Si lo encuentra en base al modo que estamos editando hago una cosa u otra
            switch (getModo()) {
                case NivelLlenado:
                    if (isEnRadio) {
                        loadActivityNivelLlenado(elemento);

                        // TODO: si queremos que no vuelva al mapa y se quede en esta
                        // pantalla comentar finish()
//                    finish();
                    } else {
                        new InfoDialog(getInstance(), "Atención",
                                "El elemento no esta en un radio de " + DISTANCIA + " mts, ¿Desea continuar?",
                                InfoDialog.ICON_ALERT,
                                new MyDialogNivelLlenado(elemento),
                                InfoDialog.BUTTON_ACCEPT | InfoDialog.BUTTON_CANCEL,
                                InfoDialog.POSITION_CENTER).show();
                    }
                    break;

                case Lavado:
                    // enviamos evento de lavado por la API.

                    if (SensoresManager.getInstance().sendSensorLavado(elemento)) {
                        dbElemento = new DBElemento();
                        elemento.setFechaUltLavado(Utils.datetimeToString(new java.util.Date(), "yyyy-MM-dd HH:mm:ss"));
                        dbElemento.update(elemento);
                        dbElemento.close();
                        Toast.makeText(
                                ProcesarElementoActivity.getInstance(),
                                getString(R.string.elemento) + elemento.toString()
                                        + getString(R.string.marcadoLavado),
                                Toast.LENGTH_LONG).show();
                    }

                    numeroElemento.setText("");
                    numeroElemento.setHint("Introduzca el nº de elemento");

                    // TODO: si queremos que no vuelva al mapa y se quede en esta
                    // pantalla comentar finish()
                    break;

            }
        }

        private void loadActivityNivelLlenado(Elemento elemento) {
            // mostramos actividad de elegir nivel de llenado
            NivelLlenadoActivity.getInstance().setElemento(elemento);
            Intent intent = new Intent(getInstance(), NivelLlenadoActivity.class);
            intent.putExtra("version_jefe", mVersionJefe);
            intent.putExtra("procesado", cbProcesado.isChecked());
            intent.putExtra("vaciado_parcial", cbVaciadoParcial.isChecked());

            startActivity(intent);
        }

        private class MyDialogNivelLlenado implements OnInfoDialogSelect {

            private Elemento elemento;

            public MyDialogNivelLlenado(Elemento elemento) {
                this.elemento = elemento;
            }

            @Override
            public void onSelectOption(int option) {
                if (option == InfoDialog.BUTTON_ACCEPT) {
                    loadActivityNivelLlenado(elemento);
                }
            }
        }
    }

    public float GetDistanceBy(double Lat1, double Lon1, double Lat2,
                               double Lon2) {
        Location locationA = new Location("point A");
        locationA.setLatitude(Lat1);
        locationA.setLongitude(Lon1);
        Location locationB = new Location("point B");
        locationB.setLatitude(Lat2);
        locationB.setLongitude(Lon2);
        float distance = locationA.distanceTo(locationB);
        return distance;
    }

    public static ProcesarElementoActivity getInstance() {

        if (instance == null)
            instance = new ProcesarElementoActivity();
        return instance;
    }

    public ModoProcesado getModo() {
        return _modo;
    }

    public void setModo(ModoProcesado _modo) {
        ProcesarElementoActivity._modo = _modo;
    }

    private class clickVolverMapa implements OnClickListener {
        @Override
        public void onClick(View v) {
            isLoadingMunicipio = true;
            finish();
        }
    }

    /**
     * Este método se ejecuta cuando se lee un TAG con el lector de mano RfID
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(OnReadedTag event) {

        try {

            DBElemento dbElemento = new DBElemento();
            DBTags dbTags = new DBTags();

            Tags tag = dbTags.getByTag(event.tag, MainActivity.getInstance().getEmpresa());
            TagSendSensor.execute(event.tag, tag);

            if (tag == null)
                return;

            if (tag.getIdExternoElemento() > 0) {
                Elemento elemento = dbElemento.getByIdExterno(tag.getIdExternoElemento(),
                        MainActivity.getInstance().getEmpresa());

                switch (getModo()) {
                    case NivelLlenado:
                        MyBroadCastManager.getInstance().sendBroadCastNivelLlenado(elemento, mVersionJefe);
                        break;

                    case Lavado:
                        MyBroadCastManager.getInstance().sendBroadCastLavado(elemento);

                        finish();
                        break;
                }
            }

        } catch (Throwable e) {
            Logg.error(TAG, "[onEventMainThread] " + e.getMessage());
        }
    }

    public void refreshActivity() {
        recreate();
    }
}