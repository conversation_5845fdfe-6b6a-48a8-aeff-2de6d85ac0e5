package com.movisat.ecosat;

import static java.lang.Integer.parseInt;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.NotificationManager;
import android.app.SearchManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Color;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.PowerManager;
import android.os.StrictMode;
import android.os.Vibrator;
import android.preference.PreferenceManager;
import android.provider.Settings.Secure;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.URLSpan;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.CheckBox;
import android.widget.ExpandableListView;
import android.widget.ExpandableListView.OnChildClickListener;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.widget.SearchView;
import androidx.core.view.GravityCompat;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.drawerlayout.widget.DrawerLayout;

import com.environment.Environment;
import com.environment.EnvironmentDebug;
import com.environment.VisibilityElementPlate;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.vending.licensing.AESObfuscator;
import com.google.android.vending.licensing.LicenseChecker;
import com.google.android.vending.licensing.LicenseCheckerCallback;
import com.google.android.vending.licensing.ServerManagedPolicy;
import com.movisat.adapter.SliderAdapter;
import com.movisat.adapter.SliderDerechoExpandableListAdapter;
import com.movisat.adapter.SliderDerechoExpandableListAdapter.ItemSliderDerecho;
import com.movisat.adapter.SliderItem;
// APPCIRCLE_UPDATER
//import com.movisat.app_update_appcircle.AppCircleApi;
//import com.movisat.app_update_appcircle.AppCircleApiDto;
//import com.movisat.app_update_appcircle.Downloader;
import com.movisat.application.EcoSATApplication;
import com.movisat.application.ShutdownReceiver;
import com.movisat.bll.ZonasVerdesManager;
import com.movisat.database.DBElemento;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBError;
import com.movisat.database.DBFlota;
import com.movisat.database.DBFlotaPosiciones;
import com.movisat.database.DBIncidenciaEstado;
import com.movisat.database.DBMunicipios;
import com.movisat.database.DBProvincias;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModelo;
import com.movisat.database.InfoSustituir;
import com.movisat.database.Tags;
import com.movisat.database.Vehiculo;
import com.movisat.ecosat.ProcesarElementoActivity.ModoProcesado;
import com.movisat.events.OnReadedTag;
import com.movisat.events.onChangeLoading;
import com.movisat.events.onFinishGrupoArea;
import com.movisat.events.onLoginLoading;
import com.movisat.fragment.GesElemMapFragment;
import com.movisat.fragment.GestionElementos;
import com.movisat.helpers.CheckPermisos;
import com.movisat.helpers.ElementAreaBoundsValidator;
import com.movisat.log.Logg;
import com.movisat.log_provider_console_android.LoggProviderConsoleAndroid;
import com.movisat.managers.DepositManager;
import com.movisat.managers.DiscoveryManager;
import com.movisat.managers.IntranetManager;
import com.movisat.managers.RFidManager;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.OutBox;
import com.movisat.outbox.Packet;
import com.movisat.services.MyLocationService;
import com.movisat.services.SyncService;
import com.movisat.synchronize.DBSynchro;
import com.movisat.tags.ITag;
import com.movisat.use_case.TagSendSensor;
import com.movisat.utilities.Config;
import com.movisat.utilities.Database;
import com.movisat.utilities.EcoSATLog;
import com.movisat.utilities.GPS;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.MD5;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Phone;
import com.movisat.utilities.Utils;
import com.movisat.utils.GlobalUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.FileInputStream;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import fr.castorflex.android.smoothprogressbar.SmoothProgressBar;
import fr.castorflex.android.smoothprogressbar.SmoothProgressDrawable;

public class MainActivity extends BaseActivity {
    // AppCircle Distribution Testing configuration constants
    public static final String APPCIRCLE_DISTRIBUTION_PROFILE_ID = "e6647e5b-ba87-41e6-8519-049884232f19";
    public static final String APPCIRCLE_DISTRIBUTION_PROFILE_ID_INDRA = "b4e1b861-d385-4e50-8032-a7f798775ebc";
    public static final String APPCIRCLE_PERSONAL_API_TOKEN = "Q0dTUzA5M20yT2dtVHRISm02am95MHU3TnZqdWhxN3FYNWRmMng1YWk4c1RNRjZRQ3dsSUZ2RDFpd2RUQVRKNnwxNzgxODA0NTU1ODYyfGU2MDAzZjI3LTVlMzMtNGFkNC1hNDhlLTZjOTBlYjlkYzk0Zg==";

    private void DEBUG() {
        /*
         *  DEBUG - INIT - Comentar el código antes de compilar para que no se incluya en el código del apk.
         *                 Aunque no se comente, las variables no se asignarán si no se está en modo debug.
         */
        //        String projectKey = null;
        //        final String NIVELES_LLENADO_VERSION = "218";
        //        projectKey = "YJ3YBHPFFFF1BJ2QMLX2GSYXE"; // ServDesarollo
        //        projectKey = "LEUYDTQ4DII223ZI8ZP82ZZK8"; // Gesconsat Beta
        //        projectKey = "6DRTDW34ZIUKYSSJST5WG7LYD"; // Proyecto Cieza
        //        projectKey = "V63CLEHRZ5KSE3UQEYIF5U3LI"; // Consorcio RSU Malaga
        //        projectKey = "SBIT9CALHM9L7HWWMR55GTIDX"; // UTE Alicante
        //        projectKey = "XTDEJEM1A8MCQ1OT11PFPYYMW"; // Camacho recycling
        //        projectKey = "IKBDGK6AM8RFY3U6ZANCMDU9C"; // ASCAN Ecovidrio Madrid
        //        projectKey = "48X89VNF53X19WZUJYIAHCQ52"; // Melilla
        //        projectKey = "89YSM2SKQA889ARVFVC6XGH41"; //LIPASAM
        //        projectKey = "7IPSBA8TCWTKW2SJ5YZPKDSJB"; //UTE-RM2
        //        projectKey = "MVHQ84HH3MN7CNRUKW9VVMVBT"; // UTE INDRA
        //
        //        EnvironmentDebug.enableDebugMode(
        //                new EnvironmentDebugData(
        //                        "6DRTDW34ZIUKYSSJST5WG7LYD",
        //                        null,
        //                        null,
        //                        null,
        //                        null,
        //                        null,
        //                        null,
        //                        null,
        //                        "20;30;70;100;110;120;130;143;144;1000;1010;1020;1030;3001;3004;4000;5000"
        //                )
        //        );

        //
        //        if (EnvironmentDebug.getData().getIsSimulatedTagRead() != null) {
        // OPCIÓN 1: Simular lectura de 1 tag cada cierto tiempo
        //         final Handler handler = new Handler();
        //         handler.postDelayed(new Runnable() {
        //             @Override
        //             public void run() {
        // //                Tag134 tag = new Tag134("", "40EB4B", new Date());
        // //                TagUHF tag = new TagUHF("", "123456789" + String.valueOf(Utilss.now().getTime()), new Date());
        //                 TagUHF tag = new TagUHF("", "E280110520007A82305E0993", new Date());
        //                 EventBus.getDefault().post(new OnReadedTag(tag));
        //                 handler.postDelayed(this, 30000);
        //             }
        //         }, 30000);

        // OPICIÓN 2: Simular lecturas continuas de tags aleatorios desde la base de datos.
        //        final Handler handler = new Handler();
        //        final List<Tags> tags = new DBTags().getAll();
        //        handler.postDelayed(new Runnable() {
        //            @Override
        //            public void run() {
        //                if (Utilss.isNullOrEmpty(tags))
        //                    tags.addAll(new DBTags().getAll());
        //
        //                if (!Utilss.isNullOrEmpty(tags)) {
        //                    int i = Utilss.getRandom(0, tags.size());
        //                    Tags t = tags.get(i);
        //                    ITag tag = null;
        //                    if (t.getTag().length() == 6)
        //                        tag = new Tag134(t.getMatricula(), t.getTag(), new Date());
        //                    else tag =
        //                            new TagUHF(t.getMatricula(), t.getTag(), new Date());
        //                    EventBus.getDefault().post(new OnReadedTag(tag));
        //                }
        //                handler.postDelayed(this, 20000);
        //            }
        //        }, 20000);

        // OPCIÖN 3: Llamar al callback del lector directamete.
        //          final Handler handler = new Handler();
        //          handler.postDelayed(new Runnable() {
        //              @Override
        //              public void run() {
        //
        //                  onReaded134TagData("58085D58085D58085D58085D58085D".getBytes(), 30);
        //                  handler.postDelayed(this, 20000);
        //              }
        //          }, 20000);

        //Dejo este método modificable que se puede usar para pruebas con elementos y tags

        // private boolean checkTagRepeated() {
        //     DBElemento dbElementos = new DBElemento();
        //     int codEmpresa = MainActivity.getInstance().getEmpresa();
        //     List<Tags> allTags = new DBTags().getAll();
        //     List<Tags> distinctTags = allTags.stream().distinct().collect(Collectors.toList());

        //     List<Elemento> elementos = dbElementos.getAll().stream().filter((e) -> e.getEmpresa() == codEmpresa
        //             && Objects.equals(e.getMatricula(), "00A325DC")).collect(Collectors.toList());

        //     List<String> matNotRepeated = new ArrayList<String>();
        //     List<String> matRepeated = new ArrayList<String>();

        //     for (Tags tagCompare : distinctTags) {
        //         Elemento el = dbElementos.getElementoByMatricula(tagCompare.getMatricula(), codEmpresa);
        //         if (el != null) {
        //             String mat = el.getMatricula();
        //             if (!matNotRepeated.contains(mat)) {
        //                 matNotRepeated.add(mat);
        //             } else {
        //                 matRepeated.add(mat);
        //             }
        //         }
        //     }

        //     return matRepeated.size() > 0;
        // }

        /**
         * DEBUG - FIN.
         */
    }

    public void startEnvironment() {
        String softType = Config.getInstance().getValue(Config.TIPO_SOFT, "0");
        Environment.isSoftIndra = softType.equals("247");
        Environment.isSoftCamacho = softType.equals("202") || softType.equals("207");

        Environment.isVisibleAvisoFalso = Config.getInstance().getValue(Config.INCIDENCIA_IMPROCEDENTE, "0").equals("1");
        Environment.isVisibleFrecuenciaProcesado = Config.getInstance().getValue(Config.FREC_PROCESADO, "0").equals("1");
        Environment.isTagUHFLongAndShort = Config.getInstance().getValue(Config.TAG_LARGO_Y_CORTO, "0").equals("1");
        Environment.isTagUHFExtended = Config.getInstance().getValue(Config.TAG_UHF_EXTENDIDO, "0").equals("1");

        String visibleElementPlate = Config.getInstance().getValue("elemVerMatricula", "0");
        if (visibleElementPlate.equals("1"))
            Environment.visibilityElementPlate = VisibilityElementPlate.YES;
        else if (visibleElementPlate.equals("2"))
            Environment.visibilityElementPlate = VisibilityElementPlate.MODIFY_ELEMENT_EVEN_IF_TAG_NOT_EXIST;
        else
            Environment.visibilityElementPlate = VisibilityElementPlate.NO;

        Environment.isTablet = isTablet();
    }

    private static final String TAG = "MAIN";
    private static MainActivity instance = null;
    private DrawerLayout sliderLayout;
    private ListView sliderListLeft;
    public ExpandableListView sliderDerecho;
    private ActionBarDrawerToggle sliderToggle;
    private CharSequence sliderTitle;
    private CharSequence title;
    private License license = new License();
    private String[] sliderOptions;
    public static IntranetNew sincroIntranet;
    public final static int MESSAGE_RESTART = 0;
    public final static int MESSAGE_CLOSE_APP = 1;
    public final static int MESSAGE_NEED_UPDATE = 2;
    private PowerManager.WakeLock wakelock = null;
    private final static int RADIO_METROS = 30;
    Elemento elemSelected;
    private Menu menu;
    SmoothProgressBar progressBar;
    // APPCIRCLE_UPDATER
//    private AppCircleApiDto appCircleApiDto = null;
    private Vibrator vibratorManager = null;
    boolean existeLogin = false;
    // **** LOCALIZACION
    private boolean gps_enabled = false;
    private boolean intranetOk = false;
    private String[] spinnerOptions;
    List<String> listDataHeader;
    HashMap<String, List<ItemSliderDerecho>> listDataChild;
    public static SQLiteDatabase db = null;
    static SliderItemClickListener sliderLeftMenuClickListener = null;
    OnChildClickListener sliderDerechoClickListener = null;
    private int IdMenuDerechoSelected = -1;
    private boolean serviceCreated = false;
    private ItemSliderDerecho menuItemLimpiarFlota;
    // Opciones del slider-menu
    private final int OPT_MAP_ELEMENTS = 0;
    private final int OPT_MESSAGE = 10;
    private final int OPT_SYNCHRONIZE = 900;
    private final int OPT_INFO = 1000;
    private final int OPT_MUNICIPIO = 1100;
    private final int OPT_SETTING = 1200;
    private final int OPT_OPERATIONS_DONE = 1300;
    private final int MESES_ELIMINAR_INCIDENCIAS_SIN_ESTADO = 1;
    private final int OPT_DELETE_OUTBOX = 1400;
    private final int OPT_DELETE_DATA = 1500;
    private final int OPT_PAIR_DEV = 1600;
    // Empresa y usuario activo
    private int empresa;
    private int usuario;
    private boolean usuAdmin;
    public SliderDerechoExpandableListAdapter adapterMenuDerecho;
    Toast toast = null;
    // Objeto para gestionar elementos sobre el mapa
    public static GesElemMapFragment mapaElementos = null;
    // Nombre de las variables que se reciben de la Intranet
    public static final String INTRANET_VARIABLE_MODELO_RFID = "dracoSensor.bthRFID";
    // LICENCIA
    private static String CLAVE_PUBLICA_LICENCIA = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0clcOXfH3003kSP5+0N57XBS2tWL1hlMQjCdDbTxfLaOSeij7eNvjOr4Dw0fAK5hsR0PsSdhgxqFRpsIS+fiUUDeTqo9xtj7JhLb+Zrpq0LzNgXwgk80TK4nOPrTL4mBC/2SAV0bC2oCjLke82fv2XTvIDrvGA6wPk4gTJOZY0ZQzZSSMCY7FxSglKTTOmmp3Z4kyheHF5aHLHLkGo4u621NiLmGb7vhoYqYZSHVTr3HPLy7KT+KDReEahmGbWIo8965d6uhxvNFQclcdCUR742m+IzNwZkRhbZZfYSaSnjZOeVS6oX2sk5RpuwYCP960uvij9B8Fd9vAbG1uhSxiwIDAQAB";
    LicenseChecker comprobarLicencia;
    boolean permitir = false;
    // Generate 20 random bytes, and put them here.
    private static byte[] SALT = new byte[]{-46, 65, 30, -128, -103, -57, 74,
          -64, 51, 88, -95, -45, 77, -117, -36, -113, -11, 32, -64, 89};
    // Si esta variable es true, la vista de crear nuevo elemento mostrará un campo para el nombre
    private boolean menuCrearElementosNombre = false;
    // Si esta variable es true, la vista de crear nuevo elemento mostrará un campo para la imagen del elemento
    private boolean menuCrearElementosImagen = false;
    // si esta variable es true, la vista estará a la espera de marcar un elemento como recogido
    private boolean menuRecogidaElementos = false;
    // Si esta variable es true, la vista de crear nuevo elemento con nombre, mostrará un campo para la imagen del elemento
    private boolean menuCrearElementosNombreImagen = false;
    private boolean menuModificarElementosImagen = false;
    // Para indicar si se ha realizado la primera sincronización de incidencias (para no sincronizar las cerradas)
    private boolean firstSyncIncidencias = false;
    // Indica si el mensaje de recordatorio del vehículo asociado ya se ha mostrado
    private boolean recordatorioVehiculoAsociadoMostrado = false;
    private SearchManager searchManager;
    private SearchView searchView;
    private MenuItem searchMenuItem;
    private MenuItem rightSliderMenuItem;
    private final ArrayList<Elemento> elementosArray = new ArrayList<Elemento>();
    private final ArrayList<Elemento> suggestionsArray = new ArrayList<Elemento>();
    private SearchView.OnQueryTextListener queryTextListener;
    private SuggestAdapter suggestionsAdapter;
    private static final String COLUMN_ID = "_id";
    private static final String COLUMN_TERM = "term";
    private static final String DEFAULT = "default";
    private boolean verHistoricoRuta = false;
    private int tipoSoft;
    private EcoSATLog log = null;

    // Información de la licencia activada
    public static class License {
        boolean active = false;
        String proyectId = "";
        String licenseId = "";
        String description = "";
        String matricula = "";
    }

    Intent servicioLocalizar;
    BroadcastReceiver mReceiver;
    public static final int MENU_SELECCIONAR_OPCION = 0;
    public static final int MENU_CREAR_ELEMENTOS = 10;
    public static final int MENU_MODIFICAR_BORRAR_ELEMENTOS = 20;
    public static final int MENU_MODIFICAR_ELEMENTOS = 80;
    public static final int MENU_SUSTITUIR_ELEMENTOS_LAVADO = 81;
    public static final int MENU_SUSTITUIR_ELEMENTOS_REPARACION = 84;
    public static final int MENU_DEPOSITAR_ELEMENTOS = 82;
    public static final int MENU_RETIRAR_ELEMENTOS = 83;
    public static final int MENU_NIVEL_LLENADO = 40;
    public static final int MENU_NIVEL_LLENADO_NOMBRE_JEFE = 41;
    public static final int MENU_NIVEL_LLENADO_MAPA = 60;
    public static final int MENU_NIVEL_LLENADO_MAPA_JEFE = 61;
    public static final int MENU_NIVEL_LLENADO_PARCIAL = 42;
    public static final int MENU_NIVEL_LLENADO_PARCIAL_JEFE = 44;
    public static final int MENU_LECTURA_LLENADO = 43;
    public static final int MENU_LECTURA_LLENADO_JEFE = 45;
    public static final int MENU_LAVAR_ELEMENTO = 50;
    public static final int MENU_LAVAR_ELEMENTO_MAPA = 70;
    public static final int MENU_FILTRAR_MODELOS = 30;
    public static final int MENU_CREAR_INCIDENCIAS = 100;
    public static final int MENU_MODIFICAR_INCIDENCIAS = 110;
    public static final int MENU_FILTRAR_INCIDENCIAS = 120;
    public static final int MENU_VER_INCIDENCIAS = 130;
    public static final int MENU_ASIGNAR_INCIDENCIAS = 135;
    public static final int MENU_NAVEGAR_INCIDENCIA = 136;
    public static final int MENU_FILTRAR_TIPOS = 137;
    public static final int MENU_VER_FLOTA = 140;
    public static final int MENU_LIMPIAR_FLOTA_MAPA = 141;           // Este menú no se configura en la intranet. Estará siempre que esté MENU_VER_FLOTA.
    public static final int MENU_LIMPIAR_FLOTA_MAPA_HISTORICO = 145; // Este menú no se configura en la intranet. Estará siempre que esté MENU_VER_FLOTA_HISTORICO.
    public static final int MENU_SELECCIONAR_VEHICULO = 142;
    public static final int MENU_VER_FLOTA_HISTORICO = 143;
    public static final int MENU_RECOGIDA_ELEMENTOS = 144;
    public static final int MENU_VISTA_MAPA = 1000;
    public static final int MENU_VISTA_SATELITE = 1010;
    public static final int MENU_CENTRAR_POSICION = 1020;
    public static final int MENU_SEGUIMIENTO_GPS = 1030;
    public static final int MENU_PESAJE_VERTEDERO = 1100;
    public static final int MENU_PESAJE_VERTEDERO_PLANCHADO = 1101;
    public static final int MENU_AREAS_PARQUES_JARDINES = 2000;
    public static final int MENU_PARQUES_JARDINES_MEDIDA = 2010;
    public static final int MENU_CREAR_ELEMENTOS_NOMBRE = 3000;     // Igual que MENU_CREAR_ELEMENTOS pero añadiendo un campo para especificar nombre del nuevo elemento
    public static final int MENU_NAVEGAR_ELEMENTO = 3002;
    public static final int MENU_SUSTITUIR_ELEMENTO = 85; // Para camacho recycling, simplemente se marca un contenedor, éste se da de baja, y se crea uno nuevo con nºmatricula + 1
    public static final int MENU_CREAR_ELEMENTOS_IMAGEN = 3003;
    public static final int MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN = 3004;
    public static final int MENU_ABRIR_ECOMOVIL_1 = 4000;
    public static final int MENU_LEER_TAG = 5000;
    // Identificadores de Activitys
    public static final int ACT_INICIO_SESION = 1;
    public static final int ACT_LOADING = 2;
    public static final int ACT_SEG_GPS = 3;
    public static final int ACT_MODO_RUTA = 4;
    //Fecha para los sensores TAG y PROCESADO
    public static Date fechaProcesado = null;

    public static MainActivity getInstance() {

        if (instance == null)
            instance = new MainActivity();

        return instance;
    }

    /**
     * Asigna la empresa del usuario activo
     */
    public void setEmpresa(int empresa) {
        Config.getInstance().setValue(Config.COMPANY_ID, "" + empresa);
        Environment.isCompanyMadridContenur = empresa == 796;

        this.empresa = empresa;
    }

    public static void playNotificationSound(Context context) {
        try {
            if (context == null)
                context = EcoSATApplication.getInstance().getApplicationContext();
            Uri notificationUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
            Ringtone r = RingtoneManager.getRingtone(context, notificationUri);
            r.setVolume(0.5f);
            r.play();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Carga todos los elementos disponibles en un array, para poder realizar búsquedas
     */
    public synchronized void cargaElementosBusqueda() {
        try {
            new Thread(() -> {
                try {
                    DBElemento dbElemento = new DBElemento(true);
                    ArrayList<Elemento> lista = dbElemento.getAllByModelElemento(getEmpresa(), "todos");
                    dbElemento.close();
                    if (lista != null) {
                        elementosArray.clear();
                        elementosArray.addAll(lista);
                    }
                } catch (Throwable e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            }).start();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void addElementoBusqueda(Elemento elemento) {
        elementosArray.add(elemento);
    }

    public void removeElementoBusqueda(Elemento elemento) {
        for (Elemento e : elementosArray) {
            if (e.getId() == elemento.getId()) {
                elementosArray.remove(e);
                break;
            }
        }
    }

    public void updateElementoBusqueda(Elemento elemento) {
        for (Elemento e : elementosArray) {
            if (e.getId() == elemento.getId()) {
                elementosArray.remove(e);
                elementosArray.add(elemento);
                break;
            }
        }
    }

    public ArrayList<Elemento> getElementos() {
        return elementosArray;
    }

    /**
     * Asigna el usuario activo
     */
    public void setUsuario(int usuario) {
        this.usuario = usuario;
        Config.getInstance().setValue("loginUsuario", "" + usuario);
    }

    /**
     * Asigna el tipo de usuario
     */
    public void setUsuAdmin(boolean admin) {
        this.usuAdmin = admin;
    }

    public boolean getUsuAdmin() {
        return usuAdmin;
    }

    /**
     * Devuelve la empresa del usuario activo
     */
    public int getEmpresa() {

        return empresa;
    }

    /**
     * Devuelve el usuario activo
     */
    public int getUsuario() {
        return usuario;
    }

    /**
     * Devuelve el código del dispositivo, o -1 si no se ha establecido u ocurre un error.
     */
    public int getCodigoDispositivo() {
        return parseInt(Config.getInstance().getValue("codigoDispositivo", "-1"));
    }

    public boolean isGrabarRuta() {
        String saveRuta = Config.getInstance().getValue("save_ruta", "0");

        return saveRuta.equals("1");
    }

    public boolean isValidMenu(int IdMenu, String spinnerOption) {

        String menu = Config.getInstance().getValue("menu", "");

        if (EnvironmentDebug.getData().getMenu() != null)
            menu = EnvironmentDebug.getData().getMenu();

        if (IdMenu == -1) {
            // Ocultamos en menú para abrir EcoMóvil si no tiene submenú.
            if (spinnerOption.contains(";EcoMovil;")) {
                if (!menu.contains("4000"))
                    return false;
            }
            return true;
        }

        if (IdMenu == MENU_LIMPIAR_FLOTA_MAPA || IdMenu == MENU_LIMPIAR_FLOTA_MAPA_HISTORICO)
            return true;

        String[] array = menu.split(";");
        int menuId = -1;
        for (int i = 0; array != null && i < array.length; i++) {
            if (array[i].length() > 0) {
                menuId = Integer.parseInt(array[i].toString());
                if ((int) menuId == (int) IdMenu) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Preparamos datos del menu derecho
     */
    public void prepareMenuDerecho() {

        try {
            listDataHeader = new ArrayList<String>();
            listDataChild = new HashMap<String, List<ItemSliderDerecho>>();
            List<ItemSliderDerecho> items = new ArrayList<ItemSliderDerecho>();
            ItemSliderDerecho item;
            int isCheckBox = 0;
            int isZoom = 0;
            int isClose = 0;
            boolean isCargar = false;
            boolean isRoot = false;
            int isDejarMarcado = 0;
            int isDejarMarcadoAnterior = 0;
            int j = 0;
            int IdMenu = 0;

            boolean menuFlotaCargado = false;
            boolean menuFlotaHistoricoCargado = false;

            for (int i = 0; i < spinnerOptions.length; i++) {
                IdMenu = Integer.parseInt(spinnerOptions[i].split(";")[0]);
                isCargar = isValidMenu(IdMenu, spinnerOptions[i]); // Integer.parseInt(spinnerOptions[i].split(";")[7]);
                if (!isCargar)
                    continue;

                switch (IdMenu) {
                    case MENU_CREAR_ELEMENTOS_NOMBRE:
                        menuCrearElementosNombre = true;
                        IdMenu = MENU_CREAR_ELEMENTOS;      // Se inserta el item en el menú como MENU_CREAR_ELEMENTOS
                        break;

                    case MENU_CREAR_ELEMENTOS_IMAGEN:
                        menuCrearElementosImagen = true;
                        IdMenu = MENU_CREAR_ELEMENTOS_IMAGEN;
                        break;

                    case MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN:
                        menuCrearElementosNombre = true;
                        menuCrearElementosNombreImagen = true;
                        IdMenu = MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN;
                        break;

                    case MENU_VER_FLOTA:
                        // Si el menú "Ver flota" está visible, se cargará también el de limpiar flota del mapa
                        menuFlotaCargado = true;
                        break;
                    case MENU_VER_FLOTA_HISTORICO:
                        menuFlotaHistoricoCargado = true;
                        break;

                    case MENU_RECOGIDA_ELEMENTOS:
                        menuRecogidaElementos = true;
                        IdMenu = MENU_RECOGIDA_ELEMENTOS;
                        break;

                    case MENU_LIMPIAR_FLOTA_MAPA:
                        if (!menuFlotaCargado)
                            continue;
                        break;

                    case MENU_LIMPIAR_FLOTA_MAPA_HISTORICO:
                        if (!menuFlotaHistoricoCargado)
                            continue;
                        break;
                }

                isRoot = (Integer.parseInt(spinnerOptions[i].split(";")[0]) == -1);
                if (isRoot) {
                    if (j > 0) {
                        // Añadimos anterior listDataHeader con los items y vaciamos items.
                        if (items.size() != 0) {
                            listDataChild.put(listDataHeader.get(j - 1), items);
                        } else {
                            listDataHeader.remove(listDataHeader.get(j - 1));
                            j--;
                        }
                        items = new ArrayList<ItemSliderDerecho>();
                    }
                    listDataHeader.add(spinnerOptions[i].split(";")[1]);
                    j++;

                    continue;
                }
                isCheckBox = Integer.parseInt(spinnerOptions[i].split(";")[3]);
                isZoom = Integer.parseInt(spinnerOptions[i].split(";")[4]);
                isClose = Integer.parseInt(spinnerOptions[i].split(";")[5]);
                isDejarMarcado = Integer
                      .parseInt(spinnerOptions[i].split(";")[6]);
                isDejarMarcadoAnterior = Integer.parseInt(spinnerOptions[i]
                      .split(";")[8]);

                item = new ItemSliderDerecho(IdMenu,
                      spinnerOptions[i].split(";")[1],
                      spinnerOptions[i].split(";")[2],
                      isCheckBox == 1,
                      isClose == 1,
                      isZoom == 1,
                      isDejarMarcado == 1,
                      isCargar,
                      isDejarMarcadoAnterior == 1);

                items.add(item);

                // Se oculta el menú "Limpiar mapa" del grupo "Flota"
                if (IdMenu == MENU_LIMPIAR_FLOTA_MAPA || IdMenu == MENU_LIMPIAR_FLOTA_MAPA_HISTORICO) {
                    menuItemLimpiarFlota = item;
                    menuItemLimpiarFlota.set_visible(false);
                }
            }
            listDataChild.put(listDataHeader.get(j - 1), items);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    // @Override
    public void onReaded134TagData(final byte[] buffer, final int size) {
        super.onReaded134TagData(buffer, size);
        //        Logg.info(TAG, "[onReaded134TagData] buffer: " + Arrays.toString(Arrays.copyOfRange(buffer, 0, size)) + " size: " + size);
        //
        //        runOnUiThread(() -> {
        //            ReadingTagToast.get().cancel();
        //            Tag134 tag134 = null;
        //            byte[] id = new byte[size];
        //            if (size <= 0) {
        //                TagNotFoundToast.get().showToast(getBaseContext());
        //            } else {
        //                System.arraycopy(buffer, 0, id, 0, size);
        //                if (!LFByteUtils.hasValue(id)) {
        //                    showMessage("Aléjese del tag, pulse el botón y acerque el lector de nuevo.", Toast.LENGTH_LONG);
        //                } else {
        //                    String tagRead = "";
        //                    Tags tagR = null;
        //                    SoundManager.getInstance(getBaseContext()).play();
        //                    if (MainActivity.getInstance().getEmpresa() == 661) { //Lipasam
        //                        tagRead = "00400000" + LFByteUtils.showLIPASAMResultASCII(id);
        //                        tagRead = LFByteUtils.swapString(tagRead);
        //                    } else {
        //                        tagRead = LFByteUtils.showResultASCII(id);
        //                    }
        //                    tag134 = new Tag134("", tagRead, Utilss.now());
        //                    DBTags dbTags = new DBTags();
        //                    tagR = dbTags.getByTag(tag134, MainActivity.getInstance().getEmpresa());
        //                    dbTags.close();
        //
        //                    TagSendSensor.execute(tag134, tagR);
        //
        //                    if (MainActivity.getInstance().getIdItemMenu() == MainActivity.MENU_LEER_TAG) {
        //                        return;
        //                    }
        //
        //                    if (tagR == null) {
        //                        boolean abiertaPaginaCrearElemento = false;
        //
        //                        switch (MainActivity.getInstance().getIdItemMenu()) {
        //                            case MainActivity.MENU_CREAR_ELEMENTOS:
        //                            case MainActivity.MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN:
        //                            case MainActivity.MENU_CREAR_ELEMENTOS_IMAGEN:
        //                            case MainActivity.MENU_CREAR_ELEMENTOS_NOMBRE:
        //                                if (Environment.visibilityElementPlate == VisibilityElementPlate.MODIFY_ELEMENT_EVEN_IF_TAG_NOT_EXIST)
        //                                    abrirAddElementActivity(tag134, null);
        //                                break;
        //                            default:
        //                                break;
        //                        }
        //
        //                    } else { // Si el tag si que está en la base de datos entonces se muestra la matrícula.
        //                        // Se obtiene el elemento asociado al tag
        //                        DBElemento dbElemento = new DBElemento();
        //                        Elemento elemento = dbElemento.getElementoByTag(tagR);
        //                        dbElemento.close();
        //                        if (elemento != null) {
        //                            //showMessage("El Tag leído es: "+tagR, Toast.LENGTH_LONG);
        //                            tag134 = new Tag134(elemento.getMatricula(), tagRead, new Date());
        //                        } else {
        //                            tag134 = new Tag134("", tagRead, new Date());
        //                        }
        //                        // Manejo de la información
        //                        EventBus.getDefault().post(new OnReadedTag(tag134));
        //                    }
        //                }
        //            }
        //        });
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        DEBUG();
        log = EcoSATLog.getInstance();

        try {
            instance = this;

            // Internamente gestiona los mensajes para imprimirlos solo en modo debug.
            Logg.addProvider(new LoggProviderConsoleAndroid(instance));

            requestWindowFeature(Window.FEATURE_INDETERMINATE_PROGRESS);
            setContentView(R.layout.main_layout);

            int currentapiVersion = Build.VERSION.SDK_INT;
            if (currentapiVersion < android.os.Build.VERSION_CODES.M) {
                inicializaApp();
            } else {
                // Esto simplemente ignorará la exposición de URI y obtendrá el acceso.
                // Sí, sé que esta no es la mejor práctica. Pero solo quería darle una alternativa.
                // Aún así, la forma recomendada es mediante el uso FileProvider.
                if (Build.VERSION.SDK_INT >= 24) {
                    try {
                        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
                        StrictMode.setVmPolicy(builder.build());

                        Method m = StrictMode.class.getMethod("disableDeathOnFileUriExposure");
                        m.invoke(null);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                checkPermissions();
            }

            // Inicia las variables de entorno globales.
            startEnvironment();

            // Compruebo si se ha configurado que no se apague la pantalla
            displayKeepAlive();

            progressBar = findViewById(R.id.progressBarSync);
            progressBar.setSmoothProgressDrawableCallbacks(new SmoothProgressDrawable.Callbacks() {
                @Override
                public void onStart() {
                    progressBar.setVisibility(View.VISIBLE);
                }

                @Override
                public void onStop() {
                    progressBar.setVisibility(View.GONE);
                }
            });
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            new InfoDialog(this, getString(R.string.atencion),
                  getString(R.string.problema) + e.getMessage() + getString(R.string.seCerrara),
                  InfoDialog.ICON_STOP, option -> closeApp(),
                  InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER).show();
        }
    }

    private void tryInstallVersion() {
        if (!MainActivity.getInstance().isNetworkAvailable()) {
            new InfoDialog(instance, instance
                  .getString(R.string.atencion), instance
                  .getString(R.string.falloConexionServidor),
                  InfoDialog.ICON_ALERT,
                  new OnInfoDialogSelect() {
                      @Override
                      public void onSelectOption(
                            int option) {
                      }
                  }, InfoDialog.BUTTON_ACCEPT,
                  InfoDialog.POSITION_CENTER).show();
            return;
        }
        
        // APPCIRCLE_UPDATER
//        if (appCircleApiDto != null) {
//            new InfoDialog(instance, instance
//                    .getString(R.string.atencion), instance
//                    .getString(R.string.quiereActualizarLaApp),
//                    InfoDialog.ICON_QUESTION,
//                    new OnInfoDialogSelect() {
//                        @Override
//                        public void onSelectOption(int option) {
//                            if (option == InfoDialog.BUTTON_YES) {
//                                String fileName = "EcoSat_Movil_" + appCircleApiDto.getDisplayVersion() + ".apk";
//                                new Downloader().execute(
//                                        appCircleApiDto.downloadUrl,
//                                        fileName,
//                                        MainActivity.getInstance()
//                                );
//                            }
//                        }
//                    }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
//                    InfoDialog.POSITION_CENTER).show();
//        }
    }

    private void checkLatestVersion() {
        if (menu == null)
            return;
        
        // APPCIRCLE_UPDATER
        // Solo se puede ejecutar en versiones 3XXXXXXX o 4XXXXXXX (versiones que no se publican en Google Play, para el lector UHF U9000 o versión de Indra).
//        final String versionCode = LicenseChecker.getVersionCode(this, this.getPackageName());
//        final boolean isCorrectVersion = versionCode.startsWith("3") || versionCode.startsWith("4");
//        if (!isCorrectVersion) return;
//
//        final boolean isIndraVersion = versionCode.startsWith("4");
//        final String profileId = isIndraVersion ? APPCIRCLE_DISTRIBUTION_PROFILE_ID_INDRA : APPCIRCLE_DISTRIBUTION_PROFILE_ID;
//
//        new AppCircleApi().execute(
//                profileId,
//                APPCIRCLE_PERSONAL_API_TOKEN,
//                response -> {
//                    if (response != null && response.isNewerThan(versionCode)) {
//                        appCircleApiDto = response;
//                        MenuItem m = menu.findItem(R.id.action_update_install);
//                        if (m != null)
//                            m.setVisible(true);
//                    }
//                }
//        );
    }

    @SuppressLint("InvalidWakeLockTag")
    public void displayKeepAlive() {
        try {
            // Compruebo si se ha configurado que no se apague la pantalla
            SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(this);
            int keepAlive = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_KEEP_ALIVE, "0"));

            if (wakelock != null)
                wakelock.release();
            wakelock = null;

            if (keepAlive > 0) {
                PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
                wakelock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK, "WL");
                wakelock.acquire();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    private void inicializaApp() {
        vibratorManager = (Vibrator) getSystemService(VIBRATOR_SERVICE);
        getSupportActionBar().setTitle("ECOSAT MOVIL");

        // Chequeo la base de de datos, si es necesario se crea  y se actualizan las tablas
        boolean isDatabaseOpen = Database.init();
        while (!isDatabaseOpen) {
            isDatabaseOpen = Database.init();
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        // Lanzamos loading
        Intent loadingApp = new Intent(this, MyLoadingActivity.class);
        startActivityForResult(loadingApp, ACT_LOADING);

        /*AccountManager am = AccountManager.get(this.getApplicationContext());
        int numAccounts = am.getAccountsByType("com.google").length;

        if (numAccounts == 0) {
            noAccountDialog();
        } else {
            // Do the license check as you have an account
            checkLicenseGooglePlay();
        }*/

        IntranetManager.getInstance();
        //cargaElementosBusqueda();

    }

    private void noAccountDialog() {
        //TODO: De momento no muestro información.
    }

    private void checkLicenseGooglePlay() {
        String idDispositivo = Secure.getString(getContentResolver(), Secure.ANDROID_ID);

        generateSALT();

        ServerManagedPolicy politica = new ServerManagedPolicy(this,
              new AESObfuscator(SALT, getPackageName(), idDispositivo));

        comprobarLicencia = new LicenseChecker(this, politica,
              CLAVE_PUBLICA_LICENCIA);

        // dialogo = new ProgressDialog(this);
        // dialogo.setTitle("comprobando licencia");
        // dialogo.setIndeterminate(true);

        MyLicenseCheckerCallback callback = new MyLicenseCheckerCallback();
        comprobarLicencia.checkAccess(callback);
    }

    private class MyLicenseCheckerCallback implements LicenseCheckerCallback {
        @Override
        public void allow(int reason) {
            permitir = true;
        }

        @Override
        public void dontAllow(int reason) {
            permitir = false;
        }

        @Override
        public void applicationError(int errorCode) {
            int err = errorCode;
        }
    }

    private void generateSALT() {
        Random random = new Random();
        random.setSeed(System.currentTimeMillis());
        byte[] buf = new byte[20];
        random.nextBytes(buf);
        SALT = buf;
    }

    boolean isInitReaders = false;

    @Override
    public void onStart() {
        super.onStart();

        // Inicializo el receiver para detectar cuando se apaga el dispositivo
        IntentFilter filter = new IntentFilter(Intent.ACTION_SHUTDOWN);
        mReceiver = new ShutdownReceiver();
        registerReceiver(mReceiver, filter);

        EventBus.getDefault().register(this);

        // Mantis 6382
        boolean isU9000UHF = Build.MODEL.equals("PDA") && android.os.Build.VERSION.SDK_INT == 28;
        boolean isInitU9000UHF = Environment.hasReaderUHFU9000 && Environment.hasReaderNFC;

        // Inicializo lectores NFC, UHFC71, LFChainway y UHFU9000
        if (!isInitReaders || (isU9000UHF && !isInitU9000UHF)) {
            isInitReaders = true;
            EcoSATApplication.getInstance().initReaders();
        }

        // Inicializo las conexiones con perifericos bluetooth
        initializeRFid();
    }

    @Override
    public void onStop() {
        setSyncProgressBarVisibility(false);
        if (mReceiver != null)
            unregisterReceiver(mReceiver);
        EventBus.getDefault().unregister(this);
        super.onStop();
    }

    private void initializeRFid() {
        try {
            String rfidModel = Config.getInstance().getValue(INTRANET_VARIABLE_MODELO_RFID, "");
            if (rfidModel != null && !rfidModel.isEmpty()) {
                RFidManager.start(rfidModel);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private static void finalizeRFid() {
        try {
            RFidManager.stop();
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    @Subscribe
    // This method will be called when a MessageEvent is posted
    public void onEvent(onChangeLoading event) {
        setSyncProgressBarVisibility(event.isVisible);
    }

    @Subscribe
    public void onEvent(onFinishGrupoArea event) {
        boolean isMedidaIniciada = Config.getInstance().getValueUsuario(
              "medidaIniciada", "0").equals("1");

        if (!isMedidaIniciada) {
            ActionBar actionBar = MainActivity.getInstance().getSupportActionBar();
            actionBar.setSubtitle(null);
            Config.getInstance().setValueUsuario("menuActionBarSubTitle",
                  "");

            Config.getInstance().setValueUsuario("idMenuAnterior", "0");
            Config.getInstance().setValueUsuario("ultOpcSubmenu", "" + 0);

            terminarSeleccion = true;

            adapterMenuDerecho.notifyDataSetChanged();
        } else {

            // tenemos que poner el filtro de papeleras y refrescar cluster.
            toggleFiltroPapeleraParquesYJardines();
        }
    }

    private void toggleFiltroPapeleraParquesYJardines() {
        String filtroModelos = Config.getInstance().getValueUsuario("filtroModeloPapelera", "todos");
        String filtroActual = Config.getInstance().getValueUsuario(
              "modelVisibles", "todos");

        filtroModelos = filtroModelos.replace(";", ",");
        filtroActual = filtroActual.replace(";", ",");
        Config.getInstance().setValueUsuario("filtroModeloPapelera", filtroActual);
        Config.getInstance().setValueUsuario("modelVisibles", filtroModelos);
        MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
    }

    @Subscribe
    public void onEvent(onLoginLoading event) {
        // Creo el hilo que sincroniza la base de datos
        if (!isAppActive())
            return;
        if (!DBSynchro.init())
            try {
                throw new Exception(getString(R.string.noIniciaSincronizar));
            } catch (Exception e) {
                e.printStackTrace();
            }

        // Creo el hilo que envia datos al servidor
        if (!OutBox.init())
            try {
                throw new Exception(getString(R.string.noIniciaBandejaSalida));
            } catch (Exception e) {
                e.printStackTrace();
            }

        //Grabar ruta solo lo activamos cuando nos logueamos correctamente.
        //        // Si tienen activado grabar ruta...
        //        if (isGrabarRuta()) {
        //
        //            servicioLocalizar = new Intent(this, MyLocationService.class);
        //            servicioLocalizar.putExtra("modo_ruta", Config.getInstance()
        //                    .getValue("modo_ruta", "vehiculo"));
        //            if (startService(servicioLocalizar) != null) {
        //                Log.d("Servicio localizar", "iniciado");
        //            } else {
        //                Log.d("Servicio localizar", "fallido");
        //            }
        //        }
        // Preparamos los sliders derechos e izquierdo.
        prepareSliders();
        sliderToggle.syncState();
    }

    public synchronized void LoadApp() throws Exception {

        if (!existeLogin) {
            existeLogin = true;
            // Lanzo actividad de inicio de sesiin
            Intent inicioSesion = new Intent(this, LoginActivity.class);
            startActivityForResult(inicioSesion, ACT_INICIO_SESION);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isAppActive() && (DBSynchro.getInstance() != null)) {
            setSyncProgressBarVisibility(DBSynchro.getInstance().getSynchro());
        }

        try {
            checkLatestVersion();
            if (mapaElementos == null) {
                mapaElementos = new GesElemMapFragment();
                getFragmentManager().beginTransaction().add(R.id.content_frame, mapaElementos).commit();
            }
        } catch (Exception e) {
            Logg.error(TAG, "Error al comprobar la versión: " + e.getMessage());
        }
    }

    private void prepareSliders() {
        // Creo los slider-menu izquierdo y derecho.
        title = sliderTitle = getTitle();
        sliderOptions = getResources().getStringArray(R.array.slider_options);
        sliderLayout = (DrawerLayout) findViewById(R.id.drawer_layout);

        String tipoSoftStr = Config.getInstance().getValue("tipoSoft", "0");
        tipoSoft = Utils.parseStringToInt(tipoSoftStr, 0);

        List<String> tempList = new ArrayList<String>(Arrays.asList(sliderOptions));
        if (Environment.isSoftIndra) {
            for (int i = 0; i < tempList.size(); i++) {
                if (tempList.get(i).startsWith("1400"))
                    tempList.remove(i);
            }
        } else {
            for (int i = 0; i < tempList.size(); i++) {
                if (tempList.get(i).startsWith("1600"))
                    tempList.remove(i);
            }
        }
        sliderOptions = tempList.toArray(new String[0]);

        sliderListLeft = (ListView) findViewById(R.id.left_drawer);
        sliderDerecho = (ExpandableListView) findViewById(R.id.right_drawer);

        sliderLayout.setDrawerShadow(R.drawable.drawer_shadow, GravityCompat.START);

        // Creo la lista de opciones del slider-menu
        ArrayList<SliderItem> items = obtenerItems(sliderOptions);

        SliderAdapter adapterMenuIzquierdo = new SliderAdapter(this, items);

        spinnerOptions = getResources().getStringArray(R.array.spinner_elementos);

        // Preparamos datos del meni derecho.
        prepareMenuDerecho();

        adapterMenuDerecho = new SliderDerechoExpandableListAdapter(this,
              listDataHeader, listDataChild);

        sliderDerecho.setAdapter(adapterMenuDerecho);
        sliderListLeft.setAdapter(adapterMenuIzquierdo);

        sliderDerechoClickListener = new SliderDerechoClickListener();

        sliderDerecho.setOnChildClickListener(sliderDerechoClickListener);

        sliderLeftMenuClickListener = new SliderItemClickListener();
        sliderListLeft.setOnItemClickListener(sliderLeftMenuClickListener);

        sliderToggle = new ActionBarDrawerToggle(this, sliderLayout,
              R.string.slider_open, R.string.slider_close) {
            public void onDrawerClosed(View view) {

                // if (getIdItemMenu() == -1)
                // getSupportActionBar().setTitle(title);

                //                invalidateOptionsMenu();

                // Muestro el submenu
                // getSupportActionBar().setNavigationMode(
                // ActionBar.NAVIGATION_MODE_LIST);
            }

            public void onDrawerOpened(View drawerView) {

                // if (getIdItemMenu() == -1)
                // getSupportActionBar().setTitle(sliderTitle);
                //                invalidateOptionsMenu();

                // Oculto el submenu
                // getSupportActionBar().setNavigationMode(
                // ActionBar.NAVIGATION_MODE_STANDARD);
            }
        };

        sliderToggle.setDrawerIndicatorEnabled(false);

        // Creo el action-bar
        getSupportActionBar().setHomeButtonEnabled(true);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setHomeAsUpIndicator(R.drawable.ic_toolbar);

        sliderLayout.setDrawerListener(sliderToggle);
    }

    /**
     * Devuele si esti activada o no la aplicaciin
     */
    public boolean isAppActive() {

        return license.active;
    }

    /**
     * Dice si esti activada o no la aplicaciin
     */
    public void setAppActive(boolean active) {

        license.active = active;
    }

    /**
     * Devuelve si se ha conectado ya con la intranet o no
     */
    public boolean getIntranet() {

        return intranetOk;
    }

    /**
     * Marca si se ha realizado la conexión con la intranet
     */
    public void setIntranet(boolean status) {

        intranetOk = status;
    }

    public static final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(final Message message) {

            try {

                synchronized (instance) {

                    if (!MainActivity.getInstance().getIntranet())
                        return;

                    Resources res = MainActivity.getInstance().getResources();

                    switch (message.what) {
                        case MESSAGE_RESTART:

                            new InfoDialog(
                                  MainActivity.getInstance(),
                                  res.getString(R.string.questionAppRestart),
                                  res.getString(R.string.restartApp),
                                  InfoDialog.ICON_QUESTION,
                                  new OnInfoDialogSelect() {
                                      @Override
                                      public void onSelectOption(int option) {

                                          if (option == InfoDialog.BUTTON_ACCEPT)
                                              MainActivity.getInstance().restart(MainActivity.getInstance());
                                      }
                                  }, InfoDialog.BUTTON_ACCEPT,
                                  InfoDialog.POSITION_CENTER).show();

                            break;

                        case MESSAGE_CLOSE_APP:
                            new InfoDialog(MainActivity.getInstance(),
                                  res.getString(R.string.questionAppClose),
                                  res.getString(R.string.closeApp), InfoDialog.ICON_QUESTION,
                                  new OnInfoDialogSelect() {
                                      @Override
                                      public void onSelectOption(int option) {
                                          if (option == InfoDialog.BUTTON_ACCEPT)
                                              close();
                                      }
                                  }, InfoDialog.BUTTON_ACCEPT,
                                  InfoDialog.POSITION_CENTER).show();

                            break;
                        case MESSAGE_NEED_UPDATE:
                            //"Es necesario actualizar la aplicación. Por favor, instale la actualización de Ecosat Móvil en Google Play Store",

                            //final SpannableString m = new SpannableString(message);
                            //Linkify.addLinks(m, Linkify.WEB_URLS);

                            SpannableString s = new SpannableString("Es necesario actualizar la aplicación. Por favor, instale la actualización de Ecosat Móvil en Google Play Store. Puede encontrarla pulsando aquí.");
                            s.setSpan(new URLSpan("https://play.google.com/store/apps/details?id=com.movisat.ecosat&hl=es"), 131, 144, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

                            new InfoDialog(MainActivity.getInstance(),
                                  "Actualización disponible",
                                  s,
                                  InfoDialog.ICON_QUESTION,
                                  new OnInfoDialogSelect() {
                                      @Override
                                      public void onSelectOption(int option) {
                                          if (option == InfoDialog.BUTTON_ACCEPT) {
                                              close();
                                              System.exit(-1);
                                          }
                                      }
                                  }, InfoDialog.BUTTON_ACCEPT,
                                  InfoDialog.POSITION_CENTER, false).show();

                            break;

                        default:
                            break;
                    }
                }
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
                e.printStackTrace();
            }
        }
    };

    public void restart(Context context) {
        close();
        Intent intent = new Intent(context, MainActivity.class);
        startActivity(intent);
        System.exit(0);
    }

    public static void close() {

        // Finalizo las conexiones con perifericos bluetooth
        finalizeRFid();

        MainActivity.getInstance().finish();

        if (MyLoadingActivity.getInstance() != null) {
            MyLoadingActivity.getInstance().finish();
        }
    }

    /**
     * Metodo privado que carga los nombres y las imagenes del slider lateral a
     * partir de los datos del fichero strings.xml
     */
    private ArrayList<SliderItem> obtenerItems(String[] sliderOptions) {

        ArrayList<SliderItem> arrayItems = new ArrayList<SliderItem>();

        for (int i = 0; i < sliderOptions.length; i++) {

            String arrayOpt[] = sliderOptions[i].split(";");

            SliderItem item = new SliderItem();

            item.setId(Long.parseLong(arrayOpt[0]));
            item.setTitle(arrayOpt[1]);
            item.setImagen(arrayOpt[2]);

            arrayItems.add(item);
        }

        return arrayItems;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // Si estamos en modo debug, no hacemos nada para facilitar la depuración.
        if (BuildConfig.DEBUG)
            return;
        try {
            Log.e(TAG, "onDestroy");
            closeApp();
        } catch (Throwable e) {
            Log.e(TAG, "Error onDestroy: " + e.getMessage());
            finish();
            System.exit(0);
        }
    }


    public void checkQueues() {
        try {
            Config.getInstance().processQueue();
            new DBError().processQueue();
        } catch (Throwable e) {
            Log.e(TAG, "Error checkQueues: " + e.getMessage());
            MyLoggerHandler.getInstance().error(e);
        }
    }

    /**
     * Metodo que cierra la aplicacion
     */
    public void closeApp() {
        Log.e(TAG, "Cerrando aplicación");
        MyLoggerHandler.getInstance().info("Cerrando aplicación");

        SyncService.stopService(this);
        checkQueues();

        // cancelamos todas las notificaciones, si existieran.
        try {
            NotificationManager notificationManager = (NotificationManager) getInstance().getSystemService(Context.NOTIFICATION_SERVICE);
            notificationManager.cancelAll();
        } catch (Exception e) {
        }

        try {
            if (wakelock != null)
                wakelock.release();

            finalizeRFid();

            if (isGrabarRuta() && serviceCreated) {
                MyLocationService.CreatePacketPosiciones();
                stopService(servicioLocalizar);
            }

            // Finalizo la gestiin de elementos

            if (GesElemMapFragment.getInstance() != null) {
                GesElemMapFragment.getInstance().end();
            }

            // GestionElementos.mCrearElemento = null;

            // Paro el hilo que sincroniza la base de datos
            DBSynchro.end();

            // Paro el hilo que sincroniza la base de datos
            OutBox.end();

            // Paro el hilo que conecta con la Intranet
            // Intranet.end();
            if (sincroIntranet != null) {
                sincroIntranet.setInterrupted(true);
                sincroIntranet.cancel(true);
            }
            // Cierro la conexiin con la base de datos y finalizo la aplicaciin
            if (db != null)
                db.close();

            if (mapaElementos != null) {
                // getFragmentManager().beginTransaction().remove(mapaElementos).commit();

                mapaElementos = null;
            }

            if (comprobarLicencia != null)
                comprobarLicencia.onDestroy();

            // elimino todas las notificaciones
            NotificationManager notificationManager = (NotificationManager) this
                  .getSystemService(Context.NOTIFICATION_SERVICE);
            try {

                //int incidencia_id = getIntent().getExtras().getInt("idIncidencia");
                notificationManager.cancelAll();
            } catch (Exception e1) {
                Log.e(TAG, "Error closeApp notificationManager.cancelAll(): " + e1.getMessage());
            }

            finish();
            System.exit(0);
        } catch (Throwable e) {
            Log.e(TAG, "Error closeApp: " + e.getMessage());
            MyLoggerHandler.getInstance().error(e);
            finish();
            System.exit(0);
        }
    }

    /**
     * Recibe los resultados de lanzar otras actividades
     */
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        switch (requestCode) {
            case ACT_INICIO_SESION: // Inicio de sesión
                if (resultCode != Activity.RESULT_OK)
                    closeApp();
                GestionElementos.getInstance().onLogin();
                //                 if (sliderLeftMenuClickListener != null) {
                //                     // Genero la pulsación de la opción del menú
                //                     sliderLeftMenuClickListener.onItemClick(null, null, 0, OPT_MAP_ELEMENTS);
                //                 }
                double lat = 0, lon = 0;
                float actualZoom = 6;

                // Recupero la ultima informacion del usuario en cuanto a posicion
                // del mapa y zoom
                lat = Double.parseDouble(Config.getInstance().getValueUsuario(
                      "miLat", "0"));
                lon = Double.parseDouble(Config.getInstance().getValueUsuario(
                      "miLon", "0"));
                actualZoom = Float.parseFloat(Config.getInstance().getValueUsuario(
                      "miZoom", "6"));

                // Si es la primera vez que entra el usuario centro en
                // la Puerta del Sol de Madrid
                if (lat == 0 && lon == 0) {

                    lat = 40.416876;
                    lon = -3.703304;

                    Config.getInstance().setValueUsuario("miLat", "" + lat);
                    Config.getInstance().setValueUsuario("miLon", "" + lon);
                    Config.getInstance().setValueUsuario("miZoom", "" + actualZoom);
                }

                String mapType = Config.getInstance().getValueUsuario("vistaMapa", "normal");

                // Se establece el tipo de mapa
                if (mapType.equals("satelite"))
                    GestionElementos.getInstance().setMapType(GoogleMap.MAP_TYPE_SATELLITE);

                // Centro el mapa sobre la iltima posiciin del usuario
                GestionElementos.getInstance().centerMap(new LatLng(lat, lon), actualZoom);

                // Si es la primera sincro evitamos que se pare en segundo plano y notificamos al usuario
                if (Config.getInstance().getValueEmpresa("ultSincro", null) == null) {
                    SyncService.startService(this);
                }

                //Sincronizamos los datos.
                if (resultCode == Activity.RESULT_OK && !isFinishing()) {

                    // Se muestra el recordatorio de vehículo asociado
                    checkVehiculoAsociado();
                    cargaElementosBusqueda();

                    // Si el usuario es administrador y es la primera sincronización, se sincronizan
                    // y se mantienen solo las incidencias no cerradas de los últimos 3 meses (mantis 3533)

                    // quitamos la restricción de borrado de incidencias solo para usuarios
                    //if (usuAdmin) {
                    // Se eliminan los estados de incidencias de los últimos tres meses
                    removeLastIncidencias(MESES_ELIMINAR_INCIDENCIAS_SIN_ESTADO);

                    //                    String fechaSincEstadosInc = Config.getInstance().getValueUsuario("ultSincroIncidenciasEstado", "");
                    String fechaSincHistoricoInc = Config.getInstance().getValueUsuario("ultSincroIncidenciasHistorico", "");

                    if (fechaSincHistoricoInc.equals("")) {

                        firstSyncIncidencias = true;

                        Calendar c = Calendar.getInstance();
                        c.setTime(new Date());
                        c.add(Calendar.MONTH, -1);
                        String syncDateStr = Utils.datetimeToString(c.getTime(), "yyyy-MM-dd HH:mm:ss");

                        //                        if (fechaSincEstadosInc.equals(""))
                        //                            Config.getInstance().setValueUsuario("ultSincroIncidenciasEstado", syncDateStr);

                        //                        if (fechaSincHistoricoInc.equals(""))
                        Config.getInstance().setValueUsuario("ultSincroIncidenciasHistorico", syncDateStr);
                    }
                    //}

                    if (isAppActive()) {
                        if (!DBSynchro.getInstance().getSynchro()) {
                            DBSynchro.getInstance().forceSync();
                        }

                        if (Environment.isSoftIndra) {
                            if (DiscoveryManager.getInstance().checkBond())
                                DiscoveryManager.getInstance().run();
                        }

                        // Si tienen activado grabar ruta...
                        if (isGrabarRuta()) {

                            servicioLocalizar = new Intent(this, MyLocationService.class);
                            servicioLocalizar.putExtra("modo_ruta", Config.getInstance()
                                  .getValue("modo_ruta", "vehiculo"));
                            if (startService(servicioLocalizar) != null) {
                                serviceCreated = true;
                                Log.d("Servicio localizar", "iniciado");
                            } else {
                                Log.d("Servicio localizar", "fallido");
                            }
                        }
                    }
                }

                break;
            case ACT_LOADING:

                if (resultCode != Activity.RESULT_OK)
                    closeApp();

                // Cargo los datos de la licencia y la activación
                checkLicense();
                try {
                    LoadApp();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;

            case ACT_SEG_GPS:
                gps_enabled = GPS.isActiveGPS(instance);

                if (!gps_enabled) {

                    new InfoDialog(
                          MainActivity.getInstance(),
                          getString(R.string.atencion),
                          MainActivity.getInstance().getString(
                                R.string.seguimiento_gps),
                          InfoDialog.ICON_QUESTION,
                          new OnInfoDialogSelect() {
                              @Override
                              public void onSelectOption(int option) {
                                  if (option == InfoDialog.BUTTON_YES) {
                                      Intent intent = new Intent(
                                            android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                      startActivityForResult(intent, ACT_SEG_GPS);
                                  } else {
                                      if (checkSelect != null)
                                          checkSelect.setChecked(false);
                                      Config.getInstance().setValueUsuario(
                                            "segGps", "0");
                                      MyBroadCastManager
                                            .getInstance()
                                            .sendBroadCastEnabledSeguimientoGPS(
                                                  false);
                                      if (adapterMenuDerecho != null)
                                          adapterMenuDerecho.notifyDataSetChanged();
                                  }
                              }
                          }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                          InfoDialog.POSITION_CENTER).show();
                }
                break;
            case ACT_MODO_RUTA:
                checkModoRuta();
                break;
        }
    }

    private void checkModoRuta() {
        gps_enabled = GPS.isActiveGPS(instance);

        String saveRuta = Config.getInstance().getValue("save_ruta", "0");
        if (saveRuta.equals("1")) {

            if (!gps_enabled) {

                new InfoDialog(MainActivity.getInstance(), getString(R.string.atencion),
                      MainActivity.getInstance().getString(
                            R.string.grabar_gps), InfoDialog.ICON_QUESTION,
                      new OnInfoDialogSelect() {
                          @Override
                          public void onSelectOption(int option) {
                              if (option == InfoDialog.BUTTON_YES) {
                                  Intent intent = new Intent(
                                        android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                  MainActivity.getInstance()
                                        .startActivityForResult(intent,
                                              MainActivity.ACT_MODO_RUTA);
                              }
                          }
                      }, InfoDialog.BUTTON_YES, InfoDialog.POSITION_CENTER)
                      .show();
            }
        }
    }

    /**
     * Eventos de teclado
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (event.getRepeatCount() == 0) {
            Logg.info(TAG, "[onKeyDown] keyCode: " + keyCode);
            switch (keyCode) {
                case KeyEvent.KEYCODE_MENU:
                    if (sliderLayout.isDrawerOpen(sliderListLeft))
                        sliderLayout.closeDrawer(sliderListLeft);
                    else
                        sliderLayout.openDrawer(sliderListLeft);

                    return true;

                case KeyEvent.KEYCODE_BACK:
                    if (!sliderLayout.isDrawerOpen(sliderListLeft)) {
                        DBPacket dbPacket = new DBPacket();
                        final boolean isPendiente = dbPacket.isPendienteEnvioNivelesLlenado();
                        dbPacket.close();
                        if (isPendiente) {
                            new InfoDialog(this, getString(R.string.atencion),
                                  getString(R.string.salir_nivel_llenado), InfoDialog.ICON_QUESTION,
                                  new OnInfoDialogSelect() {
                                      @Override
                                      public void onSelectOption(int option) {

                                          if (option == InfoDialog.BUTTON_YES)
                                              closeApp();
                                          else {
                                              if (isAppActive()) {
                                                  if (!DBSynchro.getInstance().getSynchro())
                                                      DBSynchro.getInstance().forceSync();
                                              }
                                          }
                                      }
                                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                                  InfoDialog.POSITION_CENTER).show();
                        } else {

                            new InfoDialog(this, getString(R.string.atencion),
                                  getString(R.string.salir), InfoDialog.ICON_QUESTION,
                                  new OnInfoDialogSelect() {
                                      @Override
                                      public void onSelectOption(int option) {

                                          if (option == InfoDialog.BUTTON_YES)
                                              closeApp();
                                      }
                                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                                  InfoDialog.POSITION_CENTER).show();
                        }
                    }
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main, menu);

        this.menu = menu;

        //        checkLatestVersion();

        rightSliderMenuItem = menu.findItem(R.id.action_slider_derecho);

        // Associate searchable configuration with the SearchView
        searchManager = (SearchManager) getSystemService(Context.SEARCH_SERVICE);
        searchMenuItem = menu.findItem(R.id.action_slider_search_derecho);

        if (searchMenuItem != null) {
            searchView = (SearchView) searchMenuItem.getActionView();
        }

        if (searchView != null) {
            searchView.setSearchableInfo(searchManager.getSearchableInfo(getComponentName()));

            queryTextListener = new SearchView.OnQueryTextListener() {
                @Override
                public boolean onQueryTextChange(String newText) {
                    suggestionsArray.clear();

                    if (newText.length() < 2) {
                        return false;
                    }

                    Map<Integer, Elemento> mapa = new HashMap<>(); // Para evitar duplicados

                    for (int i = 0; i < elementosArray.size(); i++) {
                        if (containsIgnoreCase(elementosArray.get(i).getNombre(), newText) ||
                              containsIgnoreCase(elementosArray.get(i).getDescripcion(), newText)) {
                            if (GestionElementos.isVisibleModeloElemento(elementosArray.get(i).getModelo())) {
                                mapa.put(elementosArray.get(i).getId(), elementosArray.get(i));
                            }
                        }
                    }
                    suggestionsArray.addAll(mapa.values());

                    MatrixCursor matrixCursor;

                    if (suggestionsArray.isEmpty() && !newText.isEmpty()) {
                        matrixCursor = new MatrixCursor(new String[]{COLUMN_ID, COLUMN_TERM});
                        matrixCursor.addRow(new Object[]{0, DEFAULT});
                    } else {
                        matrixCursor = getCursor(suggestionsArray);
                    }

                    suggestionsAdapter = new SuggestAdapter(getInstance(), matrixCursor, suggestionsArray);
                    searchView.setSuggestionsAdapter(suggestionsAdapter);
                    suggestionsAdapter.notifyDataSetChanged();
                    return true;
                }

                @Override
                public boolean onQueryTextSubmit(String query) {
                    return true;
                }
            };
            searchView.setOnQueryTextListener(queryTextListener);
            searchView.setOnSearchClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (rightSliderMenuItem != null)
                        rightSliderMenuItem.setVisible(false);
                }
            });
            searchView.setOnCloseListener(new SearchView.OnCloseListener() {
                @Override
                public boolean onClose() {
                    if (rightSliderMenuItem != null)
                        rightSliderMenuItem.setVisible(true);
                    return false;
                }
            });
        }

        final MatrixCursor matrixCursor = getCursor(suggestionsArray);

        suggestionsAdapter = new SuggestAdapter(this, matrixCursor, suggestionsArray);
        if (searchView != null)
            searchView.setSuggestionsAdapter(suggestionsAdapter);

        return true;
    }

    private MatrixCursor getCursor(final ArrayList<Elemento> suggestions) {

        final String[] columns = new String[]{COLUMN_ID, COLUMN_TERM};
        final Object[] object = new Object[]{0, DEFAULT};

        final MatrixCursor matrixCursor = new MatrixCursor(columns);

        for (int i = 0; i < suggestions.size(); i++) {

            object[0] = i;
            object[1] = suggestions.get(i);

            matrixCursor.addRow(object);
        }

        return matrixCursor;
    }

    private class SuggestAdapter extends CursorAdapter implements View.OnClickListener {
        private final ArrayList<Elemento> mObjects;
        private final LayoutInflater mInflater;
        private TextView tvSearchTerm;

        public SuggestAdapter(final Context ctx, final Cursor cursor, final ArrayList<Elemento> mObjects) {
            super(ctx, cursor, 0);

            this.mObjects = mObjects;
            this.mInflater = (LayoutInflater) ctx.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        }

        @Override
        public View newView(final Context ctx, final Cursor cursor, final ViewGroup parent) {
            final View view = mInflater.inflate(R.layout.list_item_search, parent, false);

            tvSearchTerm = (TextView) view.findViewById(R.id.lblListItemSearch);

            return view;
        }

        @Override
        public int getCount() {
            return super.getCount();
        }

        @Override
        public void bindView(final View view, final Context ctx, final Cursor cursor) {

            tvSearchTerm = (TextView) view.findViewById(R.id.lblListItemSearch);
            final int position = cursor.getPosition();

            // Haya o no resultados siempre se añade uno vacío, si sólo hay uno mostramos feedback
            if (getCount() == 1) {
                tvSearchTerm.setText("No se han encontrado resultados");
                view.setTag(position);
            }

            if (cursorInBounds(position)) {

                final String term = mObjects.get(position).getNombre().length() > 0 ? "Nombre: " + mObjects.get(position).getNombre() +
                      (mObjects.get(position).getDescripcion().length() > 0 ? "\nDescripción: " + mObjects.get(position).getDescripcion() : "") :
                      mObjects.get(position).getDescripcion().length() > 0 ? mObjects.get(position).getDescripcion() : "";

                tvSearchTerm.setText(term);
                view.setTag(position);
                view.setOnClickListener(this);
            } else {
                // Something went wrong
            }
        }

        private boolean cursorInBounds(final int position) {
            return position < mObjects.size();
        }

        @Override
        public void onClick(final View view) {

            final int position = (Integer) view.getTag();

            if (cursorInBounds(position)) {

                final Elemento elementoSeleccionado = mObjects.get(position);
                if (elementoSeleccionado.getEstado() == Elemento.ESTADO_TALLER) {
                    showMessage("El elemento seleccionado se encuentra en taller:\n" + elementoSeleccionado);
                } else if (elementoSeleccionado.getPosition().latitude == 0 && elementoSeleccionado.getPosition().longitude == 0) {
                    showMessage("El elemento seleccionado no tiene posición:\n" + elementoSeleccionado);
                } else {
                    MyBroadCastManager
                          .getInstance()
                          .sendBroadCastCenterMapBy(
                                elementoSeleccionado.getPosition(),
                                18,
                                "",
                                true);
                }

                // cierro el teclado
                InputMethodManager imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
            }
        }
    }

    @Override
    protected void onPause() {
        setSyncProgressBarVisibility(false);
        super.onPause();
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {

        // Muestro u oculto la barra de acciones dependiendo
        // del estado del meni
        // menu.findItem(R.id.action_settings).setVisible(
        // !sliderLayout.isDrawerOpen(sliderList));

        return super.onPrepareOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {

        // Handle presses on the action bar items
        int id = item.getItemId();

        if (id == R.id.slider_item_layout) {
            // sliderToggle.onOptionsItemSelected(item);
            if (sliderLayout.isDrawerOpen(sliderListLeft))
                sliderLayout.closeDrawer(sliderListLeft);
            else {
                sliderLayout.openDrawer(sliderListLeft);
                sliderLayout.closeDrawer(sliderDerecho);
            }
            return true;
        } else if (id == R.id.action_slider_derecho) {
            // sliderToggle.onOptionsItemSelected(item);
            if (sliderLayout.isDrawerOpen(sliderDerecho))
                sliderLayout.closeDrawer(sliderDerecho);
            else {
                sliderLayout.openDrawer(sliderDerecho);
                sliderLayout.closeDrawer(sliderListLeft);
            }

            return true;
        } else if (id == android.R.id.home) {
            if (searchView.isIconified()) {
                // Si la búsqueda no está activa...
                if (sliderLayout.isDrawerOpen(sliderListLeft))
                    sliderLayout.closeDrawer(sliderListLeft);
                else {
                    sliderLayout.openDrawer(sliderListLeft);
                    sliderLayout.closeDrawer(sliderDerecho);
                }
            } else {
                // Si la búsqueda está activa, se cierra al pulsar "atrás"
                searchView.setIconified(true);
            }
        } else if (id == R.id.action_update_install) {
            tryInstallVersion();
        }

        return super.onOptionsItemSelected(item);
    }

    //	int idMenuAnterior = -1;
    private class SliderDerechoClickListener implements OnChildClickListener {
        @Override
        public boolean onChildClick(ExpandableListView parent, View v,
              int groupPosition, int childPosition, long id) {

            ActionBar actionBar = null;
            actionBar = MainActivity.getInstance().getSupportActionBar();

            ItemSliderDerecho item = (ItemSliderDerecho) parent
                  .getExpandableListAdapter().getChild(groupPosition,
                        childPosition);
            int idMenu = item.get_idMenu();
            int idMenuAnterior = Integer.parseInt(Config.getInstance()
                  .getValueUsuario("idMenuAnterior", "-1"));

            //Si hay que cerrar el menú
            if (item.is_closeMenu())
                sliderLayout.closeDrawer(sliderDerecho);

            // Reset status menu

            isMedidaIniciada = (Config.getInstance().getValueUsuario(
                  "medidaIniciada", "0").equals("1")) ? true : false;

            resetStatusMenu(item, idMenu, idMenuAnterior);

            // Comprobar si tiene iniciada una medida.
            return afterResetStatusMenu(item, idMenu, idMenuAnterior);
        }
    }

    private boolean afterResetStatusMenu(ItemSliderDerecho item, int idMenu, int idMenuAnterior) {

        ActionBar actionBar = null;
        actionBar = MainActivity.getInstance().getSupportActionBar();
        String tipoSoft = Config.getInstance().getValue("tipoSoft", "0");

        if (!terminarSeleccion)
            return false;

        if (isMedidaIniciada)
            return false;
        idMenu = setSubTitleActionBarByAction(item, idMenu);

        // si se deselecciona el menú de Nivel de llenado, no guardo el valor del vehículo de la flota actual
        // esto es para el caso de la versión jefe, que puede ir asignando niveles a distintoc vehículos de la flota
        // y si se sale de ese menú, no se debe guardar el último vehículo sobre el que ha introducido un nivel.
        if (tipoSoft.equals("203") && idMenu == 0 && (idMenuAnterior == 60 || idMenuAnterior == 61 || idMenuAnterior == 40 || idMenuAnterior == 41)) {
            Config.getInstance()
                  .setValueUsuario("FlotaSelect", "-1");
        }

        boolean isActionCompleted = callActionMenu(idMenu, idMenuAnterior,
              item);

        if (!isActionCompleted) {
            sliderLayout.closeDrawer(sliderDerecho);
            adapterMenuDerecho.notifyDataSetChanged();
            actionBar.setSubtitle(null);
            return false;
        }

        adapterMenuDerecho.notifyDataSetChanged();

        return true;
    }

    public synchronized int setSubTitleActionBarByAction(ItemSliderDerecho item, int idMenu) {

        ActionBar actionBar = null;
        actionBar = MainActivity.getInstance().getSupportActionBar();
        //actionBar.setSubtitle(null);

        //Si el item tien la opcion de quedarse marcado establezco el titulo al action bar
        //Y lo meto en configuraciin
        if (item.is_dejarMarcado()) {
            actionBar.setSubtitle(item.get_nameMenu());

            Config.getInstance().setValueUsuario("menuActionBarSubTitle", item.get_nameMenu());
        } else if (item.is_dejarMarcadoAnterior()) {
            return idMenu;
        } else {
            actionBar.setSubtitle(null);
            Config.getInstance().setValueUsuario("menuActionBarSubTitle", "");
        }

        idMenu = resetMenuTitle(item, idMenu);

        //Si tengo que dejar marcado el anterior.
        if (!item.is_dejarMarcadoAnterior()) {

            IdMenuDerechoSelected = idMenu;
            Config.getInstance().setValueUsuario("ultOpcSubmenu", "" + idMenu);
            MyBroadCastManager.getInstance().sendBroadRemoveMarkerCrearElemento();
            MyBroadCastManager.getInstance().sendBroadRemoveMarkerDepositarElemento();
            MyBroadCastManager.getInstance().sendBroadRemoveMarkerCrearIncidencia();
            MyBroadCastManager.getInstance().sendBroadRemoveMarkerAsignarIncidencia();
        }

        //Si el item no es dejar marcado y tampoco tengo que dejar marcado el anterior.
        if (!item.is_dejarMarcado() && !item.is_dejarMarcadoAnterior()) {
            Config.getInstance().setValueUsuario("ultOpcSubmenu", "" + 0);
        }

        return idMenu;
    }

    private synchronized int resetMenuTitle(ItemSliderDerecho item, int idMenu) {
        //Si la ultima opciin es la misma que la actual estan desmarcando.
        if (getIdItemMenu() == idMenu && item.is_dejarMarcado()) {
            idMenu = 0;
            Config.getInstance().setValueUsuario("menuActionBarSubTitle", "");
        }
        return idMenu;
    }

    CheckBox checkSelect = null;
    boolean terminarSeleccion = true;

    public synchronized boolean callActionMenu(int idMenu, int idMenuAnterior, ItemSliderDerecho item) {

        LinearLayout contenedor = (LinearLayout) findViewById(R.id.contenedor_Controls);

        if (contenedor != null && checkSelect == null)
            checkSelect = (CheckBox) (contenedor.findViewById(R.id.chkSelect));

        ActionBar actionBar = null;
        actionBar = MainActivity.getInstance().getSupportActionBar();

        Config.getInstance().setValueUsuario("idMenuAnterior", String.valueOf(idMenu));
        Intent intent;
        switch (idMenu) {
            case MENU_SELECCIONAR_OPCION: // Nada

                if (terminarSeleccion)
                    actionBar.setSubtitle(null);

                break;
            case MENU_CREAR_ELEMENTOS:// Crear elementos
                menuCrearElementosImagen = false;
                menuCrearElementosNombreImagen = false;
                MyBroadCastManager.getInstance().sendBroadCreateMarkerCrearElemento();
                break;

            case MENU_CREAR_ELEMENTOS_IMAGEN:
                menuCrearElementosImagen = true;
                MyBroadCastManager.getInstance().sendBroadCreateMarkerCrearElemento();
                break;

            case MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN:
                menuCrearElementosNombreImagen = true;
                MyBroadCastManager.getInstance().sendBroadCreateMarkerCrearElemento();
                break;
            case MENU_SUSTITUIR_ELEMENTO:
                MyLoggerHandler.getInstance().info("Sustitución de elementos...");
                break;
            case MENU_MODIFICAR_BORRAR_ELEMENTOS: // Modificar/borrar elementos
            case MENU_MODIFICAR_ELEMENTOS: // Modificar elementos
                MyLoggerHandler.getInstance().info("Editando elementos...");
                if (menuCrearElementosNombreImagen || menuCrearElementosImagen) {
                    menuModificarElementosImagen = true;
                }
                break;
            case MENU_NIVEL_LLENADO_MAPA:
            case MENU_NIVEL_LLENADO_MAPA_JEFE:
            case MENU_LAVAR_ELEMENTO_MAPA:
            case MENU_NIVEL_LLENADO_PARCIAL:
            case MENU_NIVEL_LLENADO_PARCIAL_JEFE:
            case MENU_LECTURA_LLENADO:
            case MENU_LECTURA_LLENADO_JEFE:

                break;

            case MENU_SUSTITUIR_ELEMENTOS_LAVADO:
                intent = new Intent(getInstance(), SustituirElemActivity.class);
                intent.putExtra("destino", Elemento.ESTADO_LAVADO);
                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_SUSTITUIR_ELEMENTOS_REPARACION:
                intent = new Intent(getInstance(), SustituirElemActivity.class);
                intent.putExtra("destino", Elemento.ESTADO_TALLER);
                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_DEPOSITAR_ELEMENTOS:
                MyBroadCastManager.getInstance().sendBroadCreateMarkerDepositarElemento();
                break;

            case MENU_RETIRAR_ELEMENTOS:
                intent = new Intent(getInstance(), RetirarElemActivity.class);
                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_AREAS_PARQUES_JARDINES:
                // Aqui abrimos la actividad para cargar area.
                intent = new Intent(getInstance(), GruposActivity.class); // GruposActivity.class);
                intent.putExtra("IS_MEDIDA", false);
                MainActivity.getInstance().startActivity(intent);

                break;

            case MENU_PARQUES_JARDINES_MEDIDA:
                boolean isSeguimientoGPSEnabled = (Config.getInstance()
                      .getValueUsuario("segGps", "0").equals("0")) ? false : true;

                if (!isSeguimientoGPSEnabled) {

                    Toast.makeText(getInstance(),
                          R.string.atencionGPSMedida,
                          Toast.LENGTH_LONG).show();
                    setSubTitleActionBarByAction(item, idMenu);
                    return false;
                }

                // Aqui abrimos la actividad para cargar area.
                intent = new Intent(getInstance(), GruposActivity.class); // GruposActivity.class);
                intent.putExtra("IS_MEDIDA", true);
                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_PESAJE_VERTEDERO:
                // Aqui abrimos la actividad para enviar pesaje vertedero.
                intent = new Intent(MainActivity.getInstance(), AddPesajeActivity.class);
                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_PESAJE_VERTEDERO_PLANCHADO:
                // Aqui abrimos la actividad para enviar pesaje vertedero.
                intent = new Intent(MainActivity.getInstance(), AddPesajeActivity.class);
                intent.putExtra("planchado", true);
                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_FILTRAR_MODELOS: // Filtrar modelos visibles en carto
                intent = new Intent(MainActivity.getInstance(), FilterElemActivity.class);

                MainActivity.getInstance().startActivity(intent);

                break;
            case MENU_LAVAR_ELEMENTO:

                isSeguimientoGPSEnabled = (Config.getInstance()
                      .getValueUsuario("segGps", "0").equals("0")) ? false : true;

                if (!isSeguimientoGPSEnabled) {

                    Toast.makeText(getInstance(),
                          R.string.atencionGPSLavado,
                          Toast.LENGTH_LONG).show();
                    setSubTitleActionBarByAction(item, idMenu);
                    return false;
                }

                ProcesarElementoActivity.getInstance().setModo(ModoProcesado.Lavado);
                intent = new Intent(MainActivity.getInstance(), ProcesarElementoActivity.class);

                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_NIVEL_LLENADO:
            case MENU_NIVEL_LLENADO_NOMBRE_JEFE:

                // gps_enabled = GestionElementos.getInstance().locationManager
                // .isProviderEnabled(LocationManager.GPS_PROVIDER);

                //Tenemos que tener el seguimiento para el nivel de llenado activo.
                isSeguimientoGPSEnabled = (Config.getInstance()
                      .getValueUsuario("segGps", "0").equals("0")) ? false : true;

                if (!isSeguimientoGPSEnabled) {

                    Toast.makeText(getInstance(),
                          R.string.atencionGPSNivelLlenado,
                          Toast.LENGTH_LONG).show();
                    setSubTitleActionBarByAction(item, idMenu);
                    return false;
                }

                long diffTime = 0;
                if (GestionElementos.ultGpsPos != null) {
                    diffTime = (System.currentTimeMillis() - GestionElementos.ultGpsPos
                          .getDatetime());
                } else {
                    diffTime = -1;
                }

                if (diffTime > 10 * 60 * 1000 || diffTime == -1) {

                    Toast.makeText(
                          instance,
                          R.string.atencionGPSObsoletaPosicion,
                          Toast.LENGTH_SHORT).show();
                }

                ProcesarElementoActivity.getInstance().setModo(ModoProcesado.NivelLlenado);
                intent = new Intent(MainActivity.getInstance(), ProcesarElementoActivity.class);

                intent.putExtra("version_jefe", idMenu == MENU_NIVEL_LLENADO_NOMBRE_JEFE);

                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_CREAR_INCIDENCIAS: // Crear incidencias
                MyBroadCastManager.getInstance().sendBroadCreateMarkerCrearIncidencia();

                break;
            case MENU_MODIFICAR_INCIDENCIAS: // Modificar/borrar incidencias
                // lastOptionBar = actionBar.getSelectedNavigationIndex();

                break;

            case MENU_FILTRAR_INCIDENCIAS: // Filtrar incidencias por estado
                intent = new Intent(MainActivity.getInstance(), FilterInciActivity.class);

                MainActivity.getInstance().startActivity(intent);

                break;

            case MENU_VER_INCIDENCIAS: // Ver incidencias sobre la cartografia

                Intent listIncindencias = new Intent(MainActivity.getInstance(),
                      IncidenciasActivity.class);

                MainActivity.getInstance().startActivity(listIncindencias);

                break;

            case MENU_ASIGNAR_INCIDENCIAS: // Asignar incidencias sobre la cartografia
                MyBroadCastManager.getInstance().sendBroadCreateMarkerAsignarIncidencia();
                break;

            case MENU_FILTRAR_TIPOS: // Filtrar tipos incidencias
                intent = new Intent(MainActivity.getInstance(), FilterInciTiposActivity.class);
                MainActivity.getInstance().startActivity(intent);
                break;

            case MENU_ABRIR_ECOMOVIL_1:

                if (GlobalUtils.isAppInstalled("com.movisat.ecomovil1", this)) {
                    GlobalUtils.openApp("com.movisat.ecomovil1", this);
                } else {
                    GlobalUtils.openURL("https://install.appcenter.ms/users/desarrollo-movisat.com/apps/ecomovil-1/distribution_groups/public", this);
                }

                break;

            case MENU_VER_FLOTA_HISTORICO:
                verHistoricoRuta = true;
            case MENU_VER_FLOTA: // Ver ultimas posiciones de la flota.
                DBFlotaPosiciones dbFlota = new DBFlotaPosiciones();
                int nreg = dbFlota.getCount(MainActivity.getInstance().getEmpresa());
                dbFlota.close();

                if (nreg > 0) {

                    Intent flota = new Intent(MainActivity.getInstance(), FlotaActivity.class);
                    if (verHistoricoRuta) {
                        flota.putExtra("verHistorico", true);
                    }

                    MainActivity.getInstance().startActivity(flota);
                } else {

                    new InfoDialog(MainActivity.getInstance(),
                          getString(R.string.atencion),
                          getString(R.string.noPosiciones),
                          InfoDialog.ICON_INFO, new OnInfoDialogSelect() {
                        @Override
                        public void onSelectOption(int option) {
                        }
                    }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER).show();
                }

                break;

            case MENU_LIMPIAR_FLOTA_MAPA:
            case MENU_LIMPIAR_FLOTA_MAPA_HISTORICO:
                MyBroadCastManager.getInstance().sendBroadCastRemoveMarkersFlota();
                setMenuLimpiarFlotaVisibility(false);
                break;

            case MENU_SELECCIONAR_VEHICULO:
                Intent seleccionarMovil = new Intent(MainActivity.getInstance(), SeleccionarVehiculoActivity.class);
                MainActivity.getInstance().startActivity(seleccionarMovil);
                break;

            case MENU_VISTA_MAPA: // Vista mapa
                MyBroadCastManager.getInstance().sendBroadCastChangeMapType(
                      GoogleMap.MAP_TYPE_NORMAL);
                Config.getInstance().setValueUsuario("vistaMapa", "normal");
                break;

            case MENU_VISTA_SATELITE: // Vista satilite
                MyBroadCastManager.getInstance().sendBroadCastChangeMapType(
                      GoogleMap.MAP_TYPE_SATELLITE);
                Config.getInstance().setValueUsuario("vistaMapa", "satelite");
                break;

            case MENU_CENTRAR_POSICION: // Centrar en iltima posiciin GPS
                MyBroadCastManager.getInstance().sendBroadCastCenterPosition(false);
                break;

            case MENU_SEGUIMIENTO_GPS: // Activar/desactivar seguimiento GPS
                toggleSeguimientoGPS();

                break;

            case MENU_RECOGIDA_ELEMENTOS:
                MyLoggerHandler.getInstance().info("Recogida de elementos.");
                break;
            default:
                // lastOptionBar = 0;
        }

        return true;
    }

    public void toggleSeguimientoGPS() {
        boolean currentGpsState = Config.getInstance().getValueUsuario("segGps", "0").equals("1");
        boolean newGpsState = !currentGpsState;

        if (checkSelect != null) {
            checkSelect.setChecked(newGpsState);
        }

        gps_enabled = GPS.isActiveGPS(instance);

        Config.getInstance().setValueUsuario("segGps", newGpsState ? "1" : "0");
        MyBroadCastManager.getInstance().sendBroadCastEnabledSeguimientoGPS(newGpsState);

        if (newGpsState && !gps_enabled) {

            new InfoDialog(
                  MainActivity.getInstance(),
                  getString(R.string.atencion),
                  MainActivity.getInstance().getString(
                        R.string.seguimiento_gps),
                  InfoDialog.ICON_QUESTION,
                  new OnInfoDialogSelect() {
                      @Override
                      public void onSelectOption(int option) {
                          if (option == InfoDialog.BUTTON_YES) {
                              Intent intent = new Intent(
                                    android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                              startActivityForResult(intent, ACT_SEG_GPS);
                          } else {
                              // TODO: Falta que si la medida esta activa
                              // desactivarla.

                              if (checkSelect != null) {
                                  checkSelect.setChecked(false);
                              }
                              Config.getInstance().setValueUsuario("segGps", "0");
                              MyBroadCastManager.getInstance()
                                    .sendBroadCastEnabledSeguimientoGPS(false);

                              if (adapterMenuDerecho != null) {
                                  adapterMenuDerecho.notifyDataSetChanged();
                              }
                          }
                      }
                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO,
                  InfoDialog.POSITION_CENTER).show();
        }
    }

    public boolean isCrearIncidenciasActiveInMedida = false;
    public boolean isCrearElementosActiveInMedida = false;
    public boolean isDepositarElementoActiveInMedida = false;
    boolean isMedidaIniciada;

    private void resetStatusMenu(final ItemSliderDerecho item,
          final int idMenuActual, final int idMenuAnterior) {
        terminarSeleccion = true;
        if (idMenuAnterior == MENU_PARQUES_JARDINES_MEDIDA) {

            if (isMedidaIniciada) {
                // Tengo que preguntar si quiere terminar la medida
                // siempre que el usuario pulse el mismo meni que antes.
                if (idMenuActual == idMenuAnterior) {

                    new InfoDialog(MainActivity.getInstance(),
                          getString(R.string.atencion),
                          "¿Desea enviar medida realizada?, [SI] para enviar medida actual, [NO] para continuar la medida actual, [CANCELAR] medida actual y no enviar datos al servidor.", InfoDialog.ICON_QUESTION,
                          new OnInfoDialogSelect() {
                              @Override
                              public void onSelectOption(int option) {
                                  int codigoGrupo;
                                  if (option != InfoDialog.BUTTON_NO) {
                                      codigoGrupo = Integer.parseInt(Config.getInstance()
                                            .getValueUsuario("grupoMedir", "0"));

                                      terminarSeleccion = true;
                                      // Cerramos crear incidencias y crear y depositar elementos
                                      MyBroadCastManager.getInstance()
                                            .sendBroadRemoveMarkerCrearElemento();
                                      isCrearElementosActiveInMedida = false;

                                      MyBroadCastManager.getInstance()
                                            .sendBroadRemoveMarkerCrearIncidencia();
                                      isCrearIncidenciasActiveInMedida = false;

                                      MyBroadCastManager.getInstance()
                                            .sendBroadRemoveMarkerDepositarElemento();
                                      isDepositarElementoActiveInMedida = false;

                                      GesElemMapFragment.getInstance().showAreasByGrupo(codigoGrupo, false, Color.GREEN);

                                      toggleFiltroPapeleraParquesYJardines();
                                  }

                                  if (option == InfoDialog.BUTTON_YES) {

                                      ZonasVerdesManager.getInstance().sendPacketMedida();
                                      Toast.makeText(getInstance(),
                                                  R.string.medidaEnviada, Toast.LENGTH_LONG)
                                            .show();
                                      isMedidaIniciada = false;
                                  } else if (option == InfoDialog.BUTTON_CANCEL) {
                                      Toast.makeText(getInstance(),
                                                  R.string.medidaCancelada, Toast.LENGTH_LONG)
                                            .show();
                                      isMedidaIniciada = false;
                                  } else {
                                      terminarSeleccion = false;
                                      isMedidaIniciada = true;
                                  }

                                  Config.getInstance().setValueUsuario(
                                        "medidaIniciada", isMedidaIniciada ? "1" : "0");
                                  if (!isMedidaIniciada) {
                                      Config.getInstance()
                                            .setValueUsuario("grupoMedir", "0");
                                      Config.getInstance()
                                            .setValueUsuario("inicioMedida", "0");
                                  }
                                  afterResetStatusMenu(item, idMenuActual, idMenuAnterior);
                              }
                          }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO | InfoDialog.BUTTON_CANCEL,
                          InfoDialog.POSITION_CENTER).show();
                } else {

                    // Solo dejar crear elementos y crear incidencias en
                    // la misma medida.
                    if (idMenuActual == MENU_CREAR_ELEMENTOS) {
                        if (!isCrearElementosActiveInMedida) {
                            MyBroadCastManager.getInstance()
                                  .sendBroadCreateMarkerCrearElemento();
                            isCrearElementosActiveInMedida = true;
                        } else {
                            MyBroadCastManager.getInstance()
                                  .sendBroadRemoveMarkerCrearElemento();
                            isCrearElementosActiveInMedida = false;
                        }
                    } else if (idMenuActual == MENU_DEPOSITAR_ELEMENTOS) {
                        if (!isDepositarElementoActiveInMedida) {
                            MyBroadCastManager.getInstance()
                                  .sendBroadCreateMarkerDepositarElemento();
                            isDepositarElementoActiveInMedida = true;
                        } else {
                            MyBroadCastManager.getInstance()
                                  .sendBroadRemoveMarkerDepositarElemento();
                            isDepositarElementoActiveInMedida = false;
                        }
                    } else if (idMenuActual == MENU_CREAR_INCIDENCIAS) {
                        if (!isCrearIncidenciasActiveInMedida) {
                            MyBroadCastManager.getInstance()
                                  .sendBroadCreateMarkerCrearIncidencia();
                            isCrearIncidenciasActiveInMedida = true;
                        } else {
                            MyBroadCastManager.getInstance()
                                  .sendBroadRemoveMarkerCrearIncidencia();
                            isCrearIncidenciasActiveInMedida = false;
                        }
                    } else {
                        Toast.makeText(
                              getInstance(),
                              R.string.noChangeMenuMedidaIniciada,
                              Toast.LENGTH_LONG).show();
                    }
                    terminarSeleccion = false;
                }
            }
        } else if (idMenuAnterior == MENU_CREAR_ELEMENTOS) {
            if (!item.is_dejarMarcadoAnterior()) {
                MyBroadCastManager.getInstance()
                      .sendBroadRemoveMarkerCrearElemento();
                terminarSeleccion = true;
            }
        } else if (idMenuAnterior == MENU_CREAR_ELEMENTOS_NOMBRE) {
            if (!item.is_dejarMarcadoAnterior()) {
                MyBroadCastManager.getInstance()
                      .sendBroadRemoveMarkerCrearElemento();
                terminarSeleccion = true;
            }
        } else if (idMenuAnterior == MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN) {
            if (!item.is_dejarMarcadoAnterior()) {
                MyBroadCastManager.getInstance()
                      .sendBroadRemoveMarkerCrearElemento();
                terminarSeleccion = true;
            }
        } else if (idMenuAnterior == MENU_DEPOSITAR_ELEMENTOS) {
            if (!item.is_dejarMarcadoAnterior()) {
                MyBroadCastManager.getInstance()
                      .sendBroadRemoveMarkerDepositarElemento();
                terminarSeleccion = true;
            }
        } else if (idMenuAnterior == MENU_CREAR_INCIDENCIAS) {
            if (!item.is_dejarMarcadoAnterior()) {
                MyBroadCastManager.getInstance()
                      .sendBroadRemoveMarkerCrearIncidencia();
                terminarSeleccion = true;
            }
        }
    }

    /**
     * Permite obtener el indice seleccionado actualmente en el meni
     */
    public int getIdItemMenu() {

        IdMenuDerechoSelected = Integer.parseInt(Config.getInstance()
              .getValueUsuario("ultOpcSubmenu", "0"));
        return IdMenuDerechoSelected;
    }

    private class SliderItemClickListener implements
          ListView.OnItemClickListener {
        @Override
        public void onItemClick(AdapterView<?> parent, View view, int option,
              long id) {

            // Opciones del slider-menu
            switch ((int) id) {
                case OPT_MAP_ELEMENTS:
                    // if (mapaElementos == null) {

                    mapaElementos = new GesElemMapFragment();

                    getFragmentManager().beginTransaction()
                          .add(R.id.content_frame, mapaElementos).commit();

                    /*
                     * } else
                     * getFragmentManager().beginTransaction().show(mapaElementos)
                     * .commit();
                     */
                    break;

                case OPT_OPERATIONS_DONE:
                    Intent operationsActivity = new Intent(MainActivity.getInstance(),
                          OperationsActivity.class);

                    MainActivity.getInstance().startActivity(operationsActivity);
                    break;

                case OPT_SYNCHRONIZE:
                    if (isNetworkAvailable()) {
                        if (isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                            DBSynchro.getInstance().forceSync();
                        }
                    } else {
                        Toast.makeText(
                              instance,
                              R.string.falloConexionServidor,
                              Toast.LENGTH_SHORT).show();
                    }
                    break;

                case OPT_INFO:
                    Intent info = new Intent(MainActivity.getInstance(),
                          InfoActivity.class);

                    MainActivity.getInstance().startActivity(info);
                    break;
                case OPT_SETTING:
                    Intent setting = new Intent(MainActivity.getInstance(),
                          SettingsActivity.class);

                    MainActivity.getInstance().startActivity(setting);
                    break;
                case OPT_MUNICIPIO:
                    if (isNetworkAvailable()) {

                        DBMunicipios dbMunicipios = new DBMunicipios();
                        DBProvincias dbProvincias = new DBProvincias();
                        DBFlota dbFlota = new DBFlota();

                        dbProvincias.deleteAll();
                        dbMunicipios.deleteAll();
                        dbFlota.deleteAll();

                        dbProvincias.close();
                        dbMunicipios.close();
                        dbFlota.close();

                        if (isAppActive() && !DBSynchro.getInstance().getSynchro())
                            DBSynchro.getInstance().forceSync();

                        Toast.makeText(instance,
                              R.string.restaurandoMunicipios,
                              Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(
                              instance,
                              R.string.avisoNoRestaurarMunicipios,
                              Toast.LENGTH_SHORT).show();
                    }
                    break;
                case OPT_MESSAGE:
                    break;

                case OPT_DELETE_OUTBOX:
                    new InfoDialog(MainActivity.getInstance(), getString(R.string.atencion),
                          getString(R.string.warningDeleteOutbox),
                          InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                        @Override
                        public void onSelectOption(int option) {
                            if (option == InfoDialog.BUTTON_YES) {

                                DBPacket dbPacket = new DBPacket();
                                dbPacket.deleteAll();
                                dbPacket.close();
                            }
                        }
                    }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();
                    break;

                case OPT_DELETE_DATA:
                    new InfoDialog(MainActivity.getInstance(), getString(R.string.atencion),
                          getString(R.string.warningDeleteData),
                          InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                        @Override
                        public void onSelectOption(int option) {
                            if (option == InfoDialog.BUTTON_YES) {
                                try {
                                    File file = new File("/data/data/com.movisat.ecosat/databases/ecosat.sqlite");
                                    boolean deleted = file.delete();
                                    if (deleted)
                                        restart(MainActivity.getInstance());
                                    else
                                        new InfoDialog(MainActivity.getInstance(), getString(R.string.error),
                                              getString(R.string.errorDeleteData), InfoDialog.ICON_ALERT,
                                              null, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER).show();

                                    /*
                                    Process process = Runtime.getRuntime().exec("su");
                                    String command = "rm /data/data/com.movisat.ecosat/databases/ecosat.sqlite";
                                    if (process != null) {

                                        DataOutputStream os = new DataOutputStream(process.getOutputStream());
                                        BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
                                        os.writeBytes(command + "\n");
                                        os.writeBytes("exit\n");
                                        os.flush();
                                        os.close();

                                        process.waitFor();
                                        process.destroy();
                                    }
                                     */

                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();

                    break;

                case OPT_PAIR_DEV:

                    String tipoSoftStr = Config.getInstance().getValue("tipoSoft", "0");
                    tipoSoft = Utils.parseStringToInt(tipoSoftStr, 0);

                    //pulsera bt
                    if (Environment.isSoftIndra) {
                        if (DiscoveryManager.getInstance().checkBond()) {
                            String mac = DiscoveryManager.getInstance().getMAC();
                            new InfoDialog(MainActivity.getInstance(), getString(R.string.atencion),
                                  getString(R.string.warningExistePulsera) + mac + " ¿Desea conectar la pulsera?", InfoDialog.ICON_QUESTION,
                                  new OnInfoDialogSelect() {
                                      @Override
                                      public void onSelectOption(int option) {
                                          if (option == InfoDialog.BUTTON_YES) {

                                              DiscoveryManager.getInstance().run();
                                          } else if (option == InfoDialog.BUTTON_NO) {
                                              new InfoDialog(MainActivity.getInstance(), getString(R.string.atencion),
                                                    getString(R.string.warningDesvincularPulsera), InfoDialog.ICON_QUESTION,
                                                    new OnInfoDialogSelect() {
                                                        @Override
                                                        public void onSelectOption(int option) {
                                                            if (option == InfoDialog.BUTTON_YES) {
                                                                Intent intentBluetooth = new Intent();
                                                                intentBluetooth.setAction(android.provider.Settings.ACTION_BLUETOOTH_SETTINGS);
                                                                startActivity(intentBluetooth);
                                                            }
                                                        }
                                                    }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();
                                          }
                                      }
                                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();
                        } else {
                            new InfoDialog(MainActivity.getInstance(), getString(R.string.atencion),
                                  getString(R.string.warningVincularPulsera), InfoDialog.ICON_QUESTION,
                                  new OnInfoDialogSelect() {
                                      @Override
                                      public void onSelectOption(int option) {
                                          if (option == InfoDialog.BUTTON_YES) {
                                              Intent intentBluetooth = new Intent();
                                              intentBluetooth.setAction(android.provider.Settings.ACTION_BLUETOOTH_SETTINGS);
                                              startActivity(intentBluetooth);
                                          }
                                      }
                                  }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();
                        }
                    }

                    break;
            }

            // Actualizo el item seleccionado y oculto la barra
            sliderListLeft.setItemChecked(option, true);
            // setTitle(sliderOptions[option].split(";")[1]);
            sliderLayout.closeDrawer(sliderListLeft);
        }
    }

    @Override
    public void setTitle(CharSequence title) {

        this.title = title;
        getSupportActionBar().setTitle(title);
    }

    @Override
    public void finish() {
        super.finish();
        if (MyLoadingActivity.getInstance() != null) {
            MyLoadingActivity.getInstance().finish();
        }
        instance = null;
    }

    public boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager
              .getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        if (sliderToggle != null)
            sliderToggle.syncState();
    }

    /**
     * Este metodo se ejecuta cada vez que se gira la pantalla
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (sliderToggle != null)
            sliderToggle.onConfigurationChanged(newConfig);
    }

    /**
     * Devuelve los datos de la activación
     */
    public License getLicense() {

        return license;
    }

    public void deleteLicense() {
        File file = new File(MainActivity.getInstance().getFilesDir().getPath()
              + "/licencia.txt");

        if (file.exists()) {
            file.delete();
            closeApp();
        }
    }

    public boolean checkLicense() {
        byte[] binBuf = new byte[256];
        int i, j;
        String strBuf, signature;
        FileInputStream in = null;
        MD5 md5;

        try {
            license.active = false;

            File file = new File(MainActivity.getInstance().getFilesDir().getPath()
                  + "/licencia.txt");

            if (!file.exists()) {
                // Si la opción de suplantar dispositivo a partir de la base de datos está
                // establecida, se genera el archivo de licencia a partir de sus datos
                if (Config.getInstance().getValue("fakeDevice", "0").equals("1")) {
                    String proyectKey = Config.getInstance().getValue("proyecto", "");
                    String licenseId = Config.getInstance().getValue("licencia", "");
                    String description = Config.getInstance().getValue("nombreDispositivo", "");
                    String matricula = Config.getInstance().getValue("imei", "");

                    if (proyectKey.length() > 0 && licenseId.length() > 0 &&
                          description.length() > 0 && matricula.length() > 0) {
                        Activacion.createLicenseFile(proyectKey, licenseId, description, matricula);
                    }
                }
            }

            if (file.exists()) {

                in = MainActivity.getInstance().openFileInput("licencia.txt");

                if (in.read(binBuf) > 0) {

                    strBuf = new String(binBuf);

                    if ((i = strBuf.indexOf("DESCRIPCION=")) > -1) {

                        if ((j = strBuf.indexOf("\r\n", i)) > i) {

                            license.description = strBuf.substring(i + 12, j);
                        }
                    }

                    if ((i = strBuf.indexOf("MATRICULA=")) > -1) {

                        if ((j = strBuf.indexOf("\r\n", i)) > i) {

                            license.matricula = strBuf.substring(i + 10, j);
                        }
                    }

                    if ((i = strBuf.indexOf("CLAVEPRO=")) > -1) {

                        if ((j = strBuf.indexOf("\r\n", i)) > i) {

                            license.proyectId = strBuf.substring(i + 9, j);

                            if ((i = strBuf.indexOf("LICENCIA=")) > -1) {

                                if ((j = strBuf.indexOf("\r\n", i)) > i) {

                                    license.licenseId = strBuf.substring(i + 9, j);

                                    if ((i = strBuf.indexOf("FIRMA=")) > -1) {

                                        if ((j = strBuf.indexOf("\r\n", i)) > i) {

                                            signature = strBuf.substring(i + 6, j);

                                            md5 = new MD5();
                                            md5.update(binBuf, i);
                                            md5.update("intra06dracos".getBytes());

                                            if (signature.equals(md5.toString())) {

                                                Config.getInstance().setValue(
                                                      "proyecto",
                                                      license.proyectId);

                                                Config.getInstance().setValue(
                                                      "licencia",
                                                      license.licenseId);

                                                license.active = true;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                in.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return license.active;
    }

    public static void abrirAddElementActivity(ITag iTag, Tags tag) {
        double elemLng = GestionElementos.mCrearElemento.getPosition().longitude;
        double elemLat = GestionElementos.mCrearElemento.getPosition().latitude;
        boolean insertImage = MainActivity.getInstance().getMenuCrearElementosNombreImagen() ||
              MainActivity.getInstance().getMenuCrearElementosImagen();
        boolean withName = MainActivity.getInstance().getMenuCrearElementosNombre();

        if (Environment.isSoftCamacho)
            AddElemActivityCamacho.openWithParam(new AddElementParam(elemLng, elemLat, insertImage, withName, iTag, tag), getInstance());
        else
            AddElemActivity.openWithParam(new AddElementParam(elemLng, elemLat, insertImage, withName, iTag, tag), getInstance());
    }

    public static void abrirAddElementActivityUpdate(Tags tag, ITag iTag, int externalId, int internalId, LatLng location) {
        double elemLng = location.longitude;
        double elemLat = location.latitude;

        boolean insertImage = MainActivity.getInstance().getMenuCrearElementosNombreImagen() ||
              MainActivity.getInstance().getMenuCrearElementosImagen();
        boolean withName = MainActivity.getInstance().getMenuCrearElementosNombre();

        if (Environment.isSoftCamacho)
            AddElemActivityCamacho.openWithParam(new AddElementParam(elemLng, elemLat, insertImage, withName, true, externalId, internalId, iTag, tag), getInstance());
        else
            AddElemActivity.openWithParam(new AddElementParam(elemLng, elemLat, insertImage, withName, true, externalId, internalId, iTag, tag), getInstance());
    }

    /**
     * Este método se ejecuta cuando se lee un TAG con el lector de mano RfID
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventMainThread(OnReadedTag event) {
        DBElemento dbElemento = null;
        DBTags dbTags = null;
        Tags tag;
        Elemento elem = null;

        try {
            Logg.info(TAG, "[onEventMainThread] Tag leído. " + event.tag);

            dbElemento = new DBElemento();
            dbTags = new DBTags();
            tag = dbTags.getByTag(event.tag, MainActivity.getInstance().empresa);

            TagSendSensor.execute(event.tag, tag);

            switch (MainActivity.getInstance().getIdItemMenu()) {
                case MainActivity.MENU_LEER_TAG:
                    break;
                case MainActivity.MENU_CREAR_ELEMENTOS:
                case MainActivity.MENU_CREAR_ELEMENTOS_NOMBRE_IMAGEN:
                case MainActivity.MENU_CREAR_ELEMENTOS_IMAGEN:
                case MainActivity.MENU_CREAR_ELEMENTOS_NOMBRE:

                    // Guardo la posición marcada sobre el mapa.
                    LatLng posMarker = new LatLng(GestionElementos.mCrearElemento.getPosition().latitude, GestionElementos.mCrearElemento.getPosition().longitude);

                    // Comprueba que el elemento se encuentre dentro del área para CONTENUR
                    if (!ElementAreaBoundsValidator.isInside(this, posMarker))
                        return;

                    if (tag == null) {
                        if (Environment.visibilityElementPlate == VisibilityElementPlate.MODIFY_ELEMENT_EVEN_IF_TAG_NOT_EXIST)
                            abrirAddElementActivity(event.tag, tag);
                    } else {
                        elemSelected = dbElemento.getElementoByTag(tag);

                        if (elemSelected == null) {
                            MyBroadCastManager.getInstance().sendBroadCrearElemento(tag, event.tag);
                        } else {
                            final LatLng posElem = elemSelected.getPosition();

                            GestionElementos.GPSInfo posGps = GestionElementos.ultGpsPos;

                            if (MyLocationService.NumSatelites <= 4) {
                                posGps = null;
                            }

                            double dist = Double.MAX_VALUE;
                            if (posGps != null && posElem != null) {
                                LatLng posActual = new LatLng(posGps.getPosition().latitude, posGps.getPosition().longitude);
                                dist = GPS.calcularDistancia(posElem, posActual);
                            } else {
                                dist = GPS.calcularDistancia(posElem, posMarker);
                            }

                            askRelocateElement(posElem, posGps, posMarker, dist, tag);
                        }
                    }
                    break;

                case MainActivity.MENU_DEPOSITAR_ELEMENTOS:
                    // Alguna vez puede estar activada la opción de menú pero no aparece el marcador
                    if (GestionElementos.mDepositarElemento == null)
                        return;

                    // Guardo la posición marcada sobre el mapa.
                    LatLng markerPos = new LatLng(GestionElementos.mDepositarElemento.getPosition().latitude, GestionElementos.mDepositarElemento.getPosition().longitude);

                    if (!ElementAreaBoundsValidator.isInside(this, markerPos))
                        return;

                    if (tag != null) {
                        elemSelected = dbElemento.getElementoByTag(tag);

                        if (elemSelected == null) {
                            showMessage(getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()), Toast.LENGTH_LONG);
                            MyBroadCastManager.getInstance().sendBroadCrearElemento(tag, event.tag);
                        } else {

                            if (elemSelected.getEstado() == Elemento.ESTADO_TALLER || elemSelected.getEstado() == Elemento.ESTADO_LAVADO) {
                                elemSelected.setPosition(markerPos.latitude, markerPos.longitude);
                                elemSelected.setEstado(Elemento.ESTADO_ACTIVO);
                                GestionElementos.getInstance().updateElemento(elemSelected);
                                DBPacket dbp = new DBPacket();
                                dbp.insert(new Packet(
                                      Packet.ELEMENTO_DEPOSITAR,
                                      Packet.PRIORIDAD_NORMAL,
                                      new InfoSustituir(null, elemSelected, new Date())));
                                dbp.close();

                                GestionElementos.getInstance().centerMap(elemSelected.getPosition());
                                showMessage(getString(R.string.depositado_ok) + "\n" +
                                      elemSelected.getNombre() + "\n" + "Mat. " + elemSelected.getMatricula(), Toast.LENGTH_LONG);

                                //sincro
                                if (isNetworkAvailable()) {
                                    if (isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                                        DBSynchro.getInstance().forceSync();
                                    }
                                }
                            } else {
                                new InfoDialog(this, getString(R.string.atencion),
                                      getString(R.string.elem_ya_depositado),
                                      InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                                    @Override
                                    public void onSelectOption(int option) {
                                        if (option == InfoDialog.BUTTON_YES) {
                                            // centrar posicion
                                            GestionElementos.getInstance().centerMap(elemSelected.getPosition());
                                        }
                                    }
                                }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER)
                                      .show();
                                //                                Al reubicar el elemento se reubicaba también el punto de ubicación
                                //                                final LatLng posElem = elemSelected.getPosition();
                                //                                MyLocationService locationService = MyLocationService.getInstance();
                                //
                                //                                GestionElementos.GPSInfo posGps = GestionElementos.ultGpsPos;
                                //
                                //                                if (locationService != null && locationService.NumSatelites <= 4) {
                                //                                    posGps = null;
                                //                                }
                                //
                                //
                                //                                double dist = Double.MAX_VALUE;
                                //                                if (posGps != null && posElem != null) {
                                //                                    LatLng posActual = new LatLng(posGps.getPosition().latitude, posGps.getPosition().longitude);
                                //                                    dist = GPS.calcularDistancia(posElem, posActual);
                                //                                } else {
                                //                                    dist = GPS.calcularDistancia(posElem, markerPos);
                                //                                }
                                //
                                //                                askRelocateElement(posElem, posGps, markerPos, dist, tag);
                            }
                        }
                    }
                    break;
                case MainActivity.MENU_MODIFICAR_ELEMENTOS:
                case MainActivity.MENU_MODIFICAR_BORRAR_ELEMENTOS:
                case MainActivity.MENU_NIVEL_LLENADO:
                case MainActivity.MENU_NIVEL_LLENADO_NOMBRE_JEFE:
                case MainActivity.MENU_NIVEL_LLENADO_MAPA:
                case MainActivity.MENU_NIVEL_LLENADO_MAPA_JEFE:
                case MainActivity.MENU_LAVAR_ELEMENTO:
                case MainActivity.MENU_LAVAR_ELEMENTO_MAPA:
                case MainActivity.MENU_RECOGIDA_ELEMENTOS:
                    elem = dbElemento.getElementoByTag(tag);

                    if (tag != null && elem != null) {
                        if (elem.getEstado() == Elemento.ESTADO_INACTIVO) {
                            showMessage("El elemento " + elem.getNombre() + " se encuentra borrado.", Toast.LENGTH_LONG);
                            break;
                        }
                        if (elem.getIdExterno() < 1) {
                            showMessage("El elemento " + elem.getNombre() + " no se encuentra sincronizado.", Toast.LENGTH_LONG);
                            break;
                        }
                        final Elemento elemento = elem;
                        final LatLng posElem = elem.getPosition();

                        //null locationService
                        MyLocationService locationService = MyLocationService.getInstance();
                        GestionElementos.GPSInfo posGps = GestionElementos.ultGpsPos;
                        LatLng posActual;
                        if (posGps != null) {
                            posActual = new LatLng(posGps.getPosition().latitude, posGps.getPosition().longitude);
                        } else {
                            double lat = Double.parseDouble(Config.getInstance().getValueUsuario("miLat", "0"));
                            double lon = Double.parseDouble(Config.getInstance().getValueUsuario("miLon", "0"));
                            posActual = new LatLng(lat, lon);
                        }

                        double dist = Double.MAX_VALUE;
                        if (posElem != null) {
                            dist = GPS.calcularDistancia(posElem, posActual);
                        }

                        if (MainActivity.getInstance().getIdItemMenu() != MainActivity.MENU_MODIFICAR_ELEMENTOS
                              && MainActivity.getInstance().getIdItemMenu() != MainActivity.MENU_MODIFICAR_BORRAR_ELEMENTOS
                              && elemento.getEstado() == Elemento.ESTADO_TALLER) {
                            if (!elem.getMatricula().equals("")) {
                                showMessage(elem.getNombre() + "\n" +
                                      "Mat. " + elem.getMatricula() + "\n" +
                                      "Tag. " + tag.getTag() + "\n" +
                                      "En taller", Toast.LENGTH_LONG);
                            } else {
                                showMessage(elem.getNombre() + "\nEn taller", Toast.LENGTH_LONG);
                            }
                            break;
                        }

                        //if (log != null) log.write(this, "DISTANCIA entre el elemento y posición GPS: "+ dist);
                        System.out.println("DISTANCIA entre el elemento y posición GPS: " + dist);
                        if (elemento.getEstado() == Elemento.ESTADO_TALLER || elemento.getEstado() == Elemento.ESTADO_LAVADO) {
                            new InfoDialog(this, getString(R.string.atencion),
                                  getString(R.string.elem_taller),
                                  InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                                @Override
                                public void onSelectOption(int option) {
                                    if (option == InfoDialog.BUTTON_YES) {
                                        MyBroadCastManager.getInstance().sendBroadCastUpdateElemento(elemento);
                                    }
                                }
                            }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER)
                                  .show();
                        } else {
                            if (dist > RADIO_METROS) {
                                new InfoDialog(this, getString(R.string.atencion),
                                      getString(R.string.dist_mas_30m),
                                      InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                                    @Override
                                    public void onSelectOption(int option) {
                                        if (option == InfoDialog.BUTTON_YES) {
                                            if (elemento.getEstado() == Elemento.ESTADO_ACTIVO) {
                                                GestionElementos.getInstance().centerMap(posElem);
                                            }
                                            MyBroadCastManager.getInstance().sendBroadCastUpdateElemento(elemento);
                                        }
                                    }
                                }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER)
                                      .show();
                            } else {
                                GestionElementos.getInstance().centerMap(posElem);
                                MyBroadCastManager.getInstance().sendBroadCastUpdateElemento(elem);
                            }
                        }
                    } else {
                        if (tag != null)
                            showMessage(getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()), Toast.LENGTH_LONG);
                    }
                    break;

                case MainActivity.MENU_CREAR_INCIDENCIAS:
                    elem = dbElemento.getElementoByTag(tag);
                    if (tag != null && elem != null) {
                        if (elem.getEstado() == Elemento.ESTADO_TALLER) {
                            showMessage("No se pueden crear incidencias sobre un elemento que se encuentra en taller:\n" + elem);
                            break;
                        }
                        GestionElementos.getInstance().centerMap(elem.getPosition());
                        MyBroadCastManager.getInstance().sendBroadCastIncidenciaElemento(elem);
                    } else {
                        if (tag != null) {

                            new InfoDialog(this, getString(R.string.atencion),
                                  getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()),
                                  InfoDialog.ICON_ALERT, new OnInfoDialogSelect() {
                                @Override
                                public void onSelectOption(int option) {
                                }
                            }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER)
                                  .show();
                        }
                    }
                    break;
                default:
                    elem = dbElemento.getElementoByTag(tag);
                    //String x = elem.getPosition().toString();
                    if (tag != null && elem != null) {
                        if (elem.getPosition().toString().equals("lat/lng: (0.0,0.0)")
                              || elem.getEstado() == Elemento.ESTADO_TALLER || elem.getEstado() == Elemento.ESTADO_LAVADO) {
                            if (!elem.getMatricula().equals("")) {
                                showMessage(elem.getNombre() + "\n" +
                                      "Mat. " + elem.getMatricula() + "\n" +
                                      "Tag. " + tag.getTag() + "\n" +
                                      "En taller", Toast.LENGTH_LONG);
                            } else {
                                showMessage(elem.getNombre() + "\nEn taller", Toast.LENGTH_LONG);
                            }
                        } else {
                            if (elem.getEstado() == Elemento.ESTADO_ACTIVO)
                                GestionElementos.getInstance().centerMap(elem.getPosition());

                            String tipoSoftStr = Config.getInstance().getValue("tipoSoft", "0");
                            tipoSoft = Utils.parseStringToInt(tipoSoftStr, 0);
                            GestionElementos.getInstance().centerMap(elem.getPosition());

                            DBElementoModelo dbElementoModelo = new DBElementoModelo();
                            ElementoModelo modelo = dbElementoModelo.getByID(elem.getModelo(), empresa);
                            if (!elem.getMatricula().equals("")) {
                                if (Environment.isSoftIndra)
                                    showMessage(elem.getNombre() + "\n" +
                                          modelo.getNombre() + "\n" +
                                          "CodFisico. " + elem.getCodFisico() + "\n" +
                                          "Tag. " + tag.getTag(), Toast.LENGTH_LONG);
                                else
                                    showMessage(elem.getNombre() + "\n" +
                                          "Mat. " + elem.getMatricula() + "\n" +
                                          "Tag. " + tag.getTag(), Toast.LENGTH_LONG);
                            } else {
                                if (Environment.isSoftIndra)
                                    showMessage(elem.getNombre() + "\n" +
                                                modelo.getNombre() + "\n" +
                                                "CodFisico. " + elem.getCodFisico(),
                                          Toast.LENGTH_LONG);
                                else
                                    showMessage(elem.getNombre(), Toast.LENGTH_LONG);
                            }
                        }
                    } else {
                        if (tag != null && elem == null)
                            showMessage(getString(R.string.noExisteNingunElementoAsociadoAlTagX, event.tag.get()), Toast.LENGTH_LONG
                            );
                    }

                    break;
            }
        } catch (Throwable e) {
            Logg.error(TAG, "[onEventMainThread] " + e.getMessage());
        }

        if (dbElemento != null) {
            dbElemento.close();
        }

        if (dbTags != null) {
            dbTags.close();
        }
    }

    private void askRelocateElement(LatLng posElem, GestionElementos.GPSInfo posGps, LatLng selectedPos, double dist, Tags tag) {

        if (dist > RADIO_METROS && (posGps != null || selectedPos != null)) {
            //final GestionElementos.GPSInfo finalPosGps = posGps;

            if (elemSelected.getEstado() == Elemento.ESTADO_TALLER) {
                DepositManager.depositAlertElemInGarage(elemSelected, this, tag, selectedPos.latitude, selectedPos.longitude);
                return;
            }

            new InfoDialog(this, getString(R.string.atencion),
                  getString(R.string.reubicar_elemento),
                  InfoDialog.ICON_QUESTION, new OnInfoDialogSelect() {
                @Override
                public void onSelectOption(int option) {
                    if (option == InfoDialog.BUTTON_YES) {
                        // Cambio las coordenadas al elemento por las actuales del GPS
                        //elemSelc.setPosition(finalPosGps.getPosition().latitude, finalPosGps.getPosition().longitude);

                        // Cambio las coordenadas al elemento por las del marcador
                        elemSelected.setPosition(selectedPos.latitude, selectedPos.longitude);

                        // Actualizo la BD y el cluster
                        GestionElementos.getInstance().updateElemento(elemSelected);

                        // Guardo la información para enviar en
                        // la bandeja de salida
                        DBPacket dbp = new DBPacket();
                        dbp.insert(new Packet(
                              Packet.ELEMENTO_MODIFICAR,
                              Packet.PRIORIDAD_NORMAL, elemSelected));
                        dbp.close();

                        GestionElementos.getInstance().centerMap(elemSelected.getPosition());

                        //sincro
                        if (isNetworkAvailable()) {
                            if (isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                                DBSynchro.getInstance().forceSync();
                            }
                        }
                    } else {
                        if (option == InfoDialog.BUTTON_NO)
                            GestionElementos.getInstance().centerMap(posElem);
                    }
                }
            }, InfoDialog.BUTTON_YES | InfoDialog.BUTTON_NO, InfoDialog.POSITION_CENTER).show();
        } else {
            showMessage(getString(R.string.tag_ya_existe, Toast.LENGTH_LONG));
        }
    }

    public void showMessage(int resId) {
        showMessage(getString(resId));
    }

    public void showMessage(int resId, int duration) {
        showMessage(getString(resId), duration);
    }

    public void showMessage(String texto) {
        showMessage(texto, Toast.LENGTH_SHORT);
    }

    public void showMessage(String texto, int duration) {
        try {
            if (toast != null)
                toast.cancel();

            toast = getToastMessage(texto, duration);
            toast.show();
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public void cancelToast() {
        if (toast != null)
            toast.cancel();
    }

    /**
     * Devuelve un objeto Toast con el mensaje especificado y el estilo de la aplicación.
     *
     * @param texto    Texto que se mostrará.
     * @param duration Duración del Toast (Toast.LENGTH_SHORT o Toast.LENGTH_LONG).
     */
    Toast getToastMessage(String texto, int duration) {
        try {
            LayoutInflater inflater = MainActivity.getInstance().getLayoutInflater();
            View layout = inflater.inflate(R.layout.toast_custom,
                  (ViewGroup) MainActivity.getInstance()
                        .findViewById(R.id.toast_layout_root));

            TextView text = (TextView) layout.findViewById(R.id.text);
            text.setText(texto);

            text.setTextColor(MainActivity.getInstance().getResources()
                  .getColor(R.color.white_color));

            Toast toast = new Toast(MainActivity.getInstance().getApplicationContext());
            toast.setView(layout);
            toast.setGravity(Gravity.CENTER_VERTICAL, 0, 0);
            toast.setDuration(duration);
            return toast;
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {

        switch (requestCode) {
            case MY_PERMISSIONS_ECOSAT:
                // If request is cancelled, the result arrays are empty.
                if (grantResults.length > 0) {
                    boolean permisosConcedidos = true;
                    for (int i = 0; i < grantResults.length; i++) {
                        if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                            //Permiso concedidos

                        } else if (grantResults[i] == PackageManager.PERMISSION_DENIED) {
                            //Permisos rechazados
                            permisosConcedidos = false;
                        }
                    }
                    grantPermissions(permisosConcedidos);
                } else {
                    // permission denied!
                    //TODO: Salir de la app.
                    //this.finish();
                }
                break;

            default:
                super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }

        // other 'case' lines to check for other
        // permissions this app might request
    }

    /**
     * Indica si se han concedido o no los permisos de la aplicación.
     *
     * @param granted Si es true, los permisos se han concedido y la aplicación se inicializa.
     *                Si es false, algún permiso no se ha concedido y se sale de la aplicación.
     */
    public void grantPermissions(boolean granted) {
        if (granted) {
            //Si entra por aqui significa que tiene todos los permisos concedidos
            inicializaApp();
        } else {
            //No tenemos concedidos todos los permisos, mostramos mensaje y salimos
            new InfoDialog(
                  instance,
                  instance.getString(R.string.msg_aviso_permisos_titulo),
                  instance.getString(R.string.msg_aviso_permisos_texto),
                  InfoDialog.ICON_INFO, new OnInfoDialogSelect() {
                @Override
                public void onSelectOption(int option) {
                    finish();
                }
            }, InfoDialog.BUTTON_ACCEPT, InfoDialog.POSITION_CENTER)
                  .show();
        }
    }

    /**
     * Establece la visibilidad del menú "Limpiar mapa" del grupo "Flota" del slider derecho.
     */
    public void setMenuLimpiarFlotaVisibility(boolean visible) {
        if (menuItemLimpiarFlota != null) {
            menuItemLimpiarFlota.set_visible(visible);
            adapterMenuDerecho.notifyDataSetChanged();
        }
    }

    /**
     * Indica si, al crear un nuevo elemento, hay que mostrar el campo para insertar nombre.
     */
    public boolean getMenuCrearElementosNombre() {
        return menuCrearElementosNombre;
    }

    /**
     * Indica si, al crear un nuevo elemento, hay que mostrar el campo para insertar su imagen.
     */
    public boolean getMenuCrearElementosImagen() {
        return menuCrearElementosImagen;
    }

    /**
     * Indica si, al crear un nuevo elemento, hay que mostrar el campo para insertar su imagen.
     */
    public boolean getMenuCrearElementosNombreImagen() {
        return menuCrearElementosNombreImagen;
    }

    /**
     * Indica si, al crear un nuevo elemento, hay que mostrar el campo para insertar su imagen.
     */
    public boolean getMenuModificarElementosImagen() {
        return menuModificarElementosImagen;
    }

    /**
     * Indica si se ha realizado la primera sincronización de incidencias (histórico y estados).
     *
     * @return true si no se ha realizado la primera sincronización, false en otro caso.
     */
    public boolean isFirstSyncIncidencias() {
        return firstSyncIncidencias;
    }

    /**
     * Establece que la primera sincronización de incidencias ha sido realizada.
     */
    public void setFirstSyncIncidencias() {
        firstSyncIncidencias = false;
    }

    /**
     * Muestra un mensaje recordatorio del vehículo al que está asociado el dispositivo.
     * Si el dispositivo no está asociado a ningún vehículo, se asocia con el propio dispositivo.
     */
    public void checkVehiculoAsociado() {

        try {
            if (!CheckPermisos.getInstance().isPermisoValid(MainActivity.MENU_SELECCIONAR_VEHICULO))
                return;

            // Si ya se ha mostrado el recordatorio, no se vuelve a mostrar
            if (recordatorioVehiculoAsociadoMostrado)
                return;

            String message = null;

            Vehiculo vehiculoAsociado = Vehiculo.getVehiculoAsociado();

            // Si el dispositivo no está asociado a ningún vehículo, se asocia a sí mismo
            if (vehiculoAsociado == null) {
                int codigoDispositivo = parseInt(Config.getInstance().getValue("codigoDispositivo", "0"));

                if (codigoDispositivo == 0)
                    return;     // El código de dispositivo no se ha sincronizado todavía

                vehiculoAsociado = new Vehiculo(codigoDispositivo, "", empresa,
                      Vehiculo.TIPO_MOVIL, Phone.getInstance().getIMEI());

                // Se establece el dispositivo como el vehículo asociado
                Vehiculo.setVehiculoAsociado(vehiculoAsociado);

                // Se envía un mensaje al servidor
                DBPacket dbPacket = new DBPacket();
                dbPacket.insert(new Packet(Packet.VEHICULO_ASOCIAR, Packet.PRIORIDAD_NORMAL, vehiculoAsociado));
                dbPacket.close();

                //sincro
                if (isNetworkAvailable()) {
                    if (isAppActive() && !DBSynchro.getInstance().getSynchro()) {
                        DBSynchro.getInstance().forceSync();
                    }
                }

                // Se recarga el vehículo desde la base de datos
                vehiculoAsociado = Vehiculo.getVehiculoAsociado();

                if (vehiculoAsociado == null || vehiculoAsociado.getNombre() == null ||
                      vehiculoAsociado.getNombre().equals(""))
                    message = getString(R.string.recordatorio_vehiculo_asociado_dispositivo);
            }

            if (message == null)
                message = getString(R.string.recordatorio_vehiculo_asociado, vehiculoAsociado.getNombre());

            Toast myToast = getToastMessage(message, Toast.LENGTH_SHORT);

            // CountDownTimer para mostrar y refrescar el Toast durante el tiempo especificado
            CountDownTimer countDownTimer = new CountDownTimer(5000, 1500) {
                public void onTick(long millisUntilFinished) {
                    myToast.show();
                }

                public void onFinish() {
                    myToast.cancel();
                }
            };

            // Muestra el Toast e inicia el countdown
            myToast.show();
            countDownTimer.start();

            recordatorioVehiculoAsociadoMostrado = true;
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }

    /**
     * Elimina las incidencias (históricos y estados) anteriores al número de meses especificado y
     * las que están cerradas.
     *
     * @param monthsNum Número de meses de antigüedad máxima de incidencias.
     */
    public void removeLastIncidencias(int monthsNum) {

        DBIncidenciaEstado dbIncidenciaEstado = null;

        try {
            // Se eliminan las incidencias y estados y fotos de las mismas dato un número de meses determinado
            dbIncidenciaEstado = new DBIncidenciaEstado();
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.MONTH, -monthsNum);
            dbIncidenciaEstado.deletePreviousDataToDateBy(c.getTime(), getEmpresa());
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        if (dbIncidenciaEstado != null) {
            dbIncidenciaEstado.close();
        }
    }

    /**
     * Indica si el tipo de software del programa corresponde a una versión de Ecovidrio
     * de contenedores identificados (CAMACHO).
     */
    public static boolean isEcovidrioContIdentificados() {
        final List<Integer> softVersions = Arrays.asList(202, 207, 222, 223, 225, 226, 231, 255);

        try {

            String tipoSoftStr = Config.getInstance().getValue("tipoSoft", "0");
            int tipoSoft = Utils.parseStringToInt(tipoSoftStr, 0);

            return softVersions.contains((Integer) tipoSoft);
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            return false;
        }
    }

    /**
     * Indica si el tipo de software del programa corresponde a una versión de Ecovidrio de
     * contenedores no identificados (ASCAN).
     */
    public static boolean isEcovidrioContNoIdentificados() {
        final List<Integer> softVersions = Arrays.asList(203, 208, 215, 218, 219, 220);

        try {
            String tipoSoftStr = Config.getInstance().getValue("tipoSoft", "0");
            int tipoSoft = Utils.parseStringToInt(tipoSoftStr, 0);
            return softVersions.contains(tipoSoft);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            return false;
        }
    }

    // comprobamos que una cadena contenga la otra sin tener en cuenta mayúsculas y minúsculas
    public static boolean containsIgnoreCase(String str, String searchStr) {
        if (str == null || searchStr == null)
            return false;

        final int length = searchStr.length();
        if (length == 0)
            return true;

        for (int i = str.length() - length; i >= 0; i--) {
            if (str.regionMatches(true, i, searchStr, 0, length))
                return true;
        }
        return false;
    }

    /**
     * Establece la visibilidad de la barra de progreso que indica el proceso de sincronización.
     *
     * @param visible true para mostrar la barra de progreso, false para ocultarla.
     */
    public synchronized void setSyncProgressBarVisibility(boolean visible) {
        try {
            if (progressBar != null) {
                if (visible) {
                    progressBar.progressiveStart();
                } else if (((SmoothProgressDrawable) progressBar.getIndeterminateDrawable()).isRunning()
                      && !((SmoothProgressDrawable) progressBar.getIndeterminateDrawable()).isFinishing()) {
                    progressBar.progressiveStop();
                }
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public boolean hasSincro() {
        String ultSincro = Config.getInstance().getValueEmpresa("ultSincro", "");
        if (ultSincro.isEmpty()) {
            showMessage("Es necesario sincronizar el dispositivo antes de realizar lecturas.");
            return false;
        }
        return true;
    }

    public boolean isTablet() {
        return (getResources().getConfiguration().screenLayout
                & Configuration.SCREENLAYOUT_SIZE_MASK)
                >= Configuration.SCREENLAYOUT_SIZE_LARGE;
    }

}