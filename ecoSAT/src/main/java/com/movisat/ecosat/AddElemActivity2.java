package com.movisat.ecosat;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import com.movisat.adapter.ElemModelAdapter;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBTags;
import com.movisat.database.ElementoModelo;
import com.movisat.database.Tags;
import com.movisat.fragment.GestionElementos;
import com.movisat.log.Logg;
import com.movisat.managers.SoundManager;
import com.movisat.rfid_uhf_u9000.TagNotFoundToast;
import com.movisat.tags.Tag134;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.ReadingTagToast;
import com.movisat.utils.LFByteUtils;
import com.movisat.utils.Utilss;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created by dsanchez on 12/02/2018.
 */

public class AddElemActivity2 extends BaseActivity {
    private static final String TAG = "CREAR";
    private static AddElemActivity2 instance = null;

    private static ArrayList<ElementoModelo> modelos;

    private String matricula;
    private double latitude = 0;
    private double longitude = 0;

    private Spinner spinnerModelos;
    private EditText editDescripcion;
    private Button btnVolver;
    private Button btnAceptar;

    public static AddElemActivity2 getInstance() {
        return instance;
    }

    @Override
    protected void onReaded134TagData(final byte[] buffer, final int size) {
        Logg.info(TAG, "[onReaded134TagData] buffer: " + Arrays.toString(Arrays.copyOfRange(buffer, 0, size)) + " size: " + size);

        runOnUiThread(() -> {
            ReadingTagToast.get().cancel();
            Tag134 tag134 = null;
            byte[] id = new byte[size];
            if (size <= 0) {
                TagNotFoundToast.get().showToast(getBaseContext());
            } else {
                System.arraycopy(buffer, 0, id, 0, size);
                if (!LFByteUtils.hasValue(id)) {
                    MainActivity.getInstance().showMessage("Aléjese del tag, pulse el botón y acerque el lector de nuevo.", Toast.LENGTH_LONG);
                } else {
                    String tagRead = LFByteUtils.showResultASCII(id);
                    DBTags dbTags = new DBTags();
                    tag134 = new Tag134("", tagRead, Utilss.now());
                    Tags tag = dbTags.getByTag(tag134, MainActivity.getInstance().getEmpresa());
                    SoundManager.getInstance(getBaseContext()).play();
                    if (tag == null) { // Si el tag no está en la base de datos se muestra tal cuál en el campo matrícula.
                        //textMatricula.setText(tagRead);
                    } else { // Si el tag si que está en la base de datos entonces se muestra la matrícula.
                        //textMatricula.setText(tag.getMatricula());
                    }
                }
            }
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            instance = this;

            setContentView(R.layout.add_elem_layout_2);

            // Se obtienen los valores especificados
            Intent intent = getIntent();
            matricula = intent.getStringExtra("matricula");
            latitude = intent.getDoubleExtra("latitud", 0);
            longitude = intent.getDoubleExtra("longitud", 0);

            if (matricula == null) matricula = "";

            // Componentes de la UI para la introducci?n de datos
            spinnerModelos = (Spinner) findViewById(R.id.cbModelos);
            editDescripcion = (EditText) findViewById(R.id.editDescripcion);

            initSpinnerModelos();

            // Botones de la UI
            btnVolver = (Button) findViewById(R.id.btnVolver);
            btnAceptar = (Button) findViewById(R.id.btnAceptar);

            btnVolver.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    finish();
                }
            });

            btnAceptar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    returnElemento();
                }
            });

        } catch (Exception e) {
            Log.d(TAG, "Error -> " + e.getMessage());
        }
    }


    /**
     * Cierra la Activity, estableciendo el los extras del como result los datos introducidos.
     */
    private void returnElemento() {

        Intent resultIntent = new Intent();

        int idModelo = ((ElementoModelo) spinnerModelos.getSelectedItem()).getIdExterno();
        String description = editDescripcion.getText().toString();

        resultIntent.putExtra("empresa", MainActivity.getInstance().getEmpresa());
        resultIntent.putExtra("nombre", "");
        resultIntent.putExtra("idModelo", idModelo);
        resultIntent.putExtra("matricula", matricula);
        resultIntent.putExtra("estado", 0);
        resultIntent.putExtra("latitude", latitude);
        resultIntent.putExtra("longitude", longitude);
        resultIntent.putExtra("description", description);

        setResult(RESULT_OK, resultIntent);
        finish();
    }


    /**
     * Carga los tipos de modelo en el spinner.
     */
    private void initSpinnerModelos() {

        // Recupero todos los modelos
        DBElementoModelo dbElementoModelo = new DBElementoModelo();
        modelos = dbElementoModelo.getAll(MainActivity.getInstance().getEmpresa());
        dbElementoModelo.close();

        if (modelos == null) return;

        // Dejo solo los modelos visibles
        for (int i = 0; i < modelos.size(); i++) {

            if (!GestionElementos.isVisibleModeloElemento(modelos
                    .get(i).getIdExterno())) {

                modelos.remove(i);
                i--;
            }
        }

        if (modelos.size() < 1) {

            new InfoDialog(
                    this,
                    getString(R.string.atencion),
                    getString(R.string.noModelDisponible),
                    InfoDialog.ICON_STOP, new OnInfoDialogSelect() {

                @Override
                public void onSelectOption(int option) {

                    finish();
                }

            }, InfoDialog.BUTTON_ACCEPT,
                    InfoDialog.POSITION_CENTER).show();
        }


        ElemModelAdapter modelAdapter;
        modelAdapter = new ElemModelAdapter(MainActivity.getInstance(), modelos, 0);
        spinnerModelos.setAdapter(modelAdapter);
    }
}
