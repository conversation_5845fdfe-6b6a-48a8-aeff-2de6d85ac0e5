package com.movisat.ecosat;

import java.sql.Date;

import android.util.Log;

import com.movisat.database.DBError;
import com.movisat.database.MyError;
import com.movisat.utilities.Utils;

import org.apache.http.HttpException;


public class MyLoggerHandler {

    private static MyLoggerHandler _instance = null;

    private MyLoggerHandler() {

    }

    public static MyLoggerHandler getInstance() {
        if (_instance == null) {
            _instance = new MyLoggerHandler();
        }


        return _instance;
    }

    public void info(String message) {
        synchronized (this) {
            try {
                DBError db = new DBError();

                MyError err = new MyError();
                err.setFecha(Utils.datetimeToString(
                        new Date(System.currentTimeMillis()),
                        "yyyy-MM-dd HH:mm:ss"));
                err.setMessage("Info");
                err.setStacktrace(message);
                db.insert(err);
                db.close();
            } catch (Exception ex) {
                Log.e("info", ex.getMessage());
            }
        }
    }

    public void error(Exception e) {
        synchronized (this) {

            try {
                String message = "";
                String stack = "";
                if (e.getMessage() != null)
                    message = e.getMessage();
                if (e.getStackTrace() != null)

                    stack = Log.getStackTraceString(e);

                DBError db = new DBError();

                MyError err = new MyError();
                err.setFecha(Utils.datetimeToString(
                        new Date(System.currentTimeMillis()),
                        "yyyy-MM-dd HH:mm:ss"));
                err.setMessage(message);
                err.setStacktrace(stack);
                db.insert(err);
                db.close();
            } catch (Exception ex) {

            }
        }

    }

    public void error(Throwable e) {
        e.printStackTrace();
        if (e instanceof HttpException) return;
        synchronized (this) {

            try {
                String message = "";
                String stack = "";
                if (e.getMessage() != null)
                    message = e.getMessage();
                if (e.getStackTrace() != null)
                    stack = Log.getStackTraceString(e);

                if (!message.equals("") || !stack.equals("")) {

                    DBError db = new DBError();

                    MyError err = new MyError();
                    err.setFecha(Utils.datetimeToString(
                            new Date(System.currentTimeMillis()),
                            "yyyy-MM-dd HH:mm:ss"));
                    err.setMessage(message);
                    err.setStacktrace(stack);
                    db.insert(err);
                    db.close();
                }
            } catch (Exception ex) {
            }
        }
    }

}
