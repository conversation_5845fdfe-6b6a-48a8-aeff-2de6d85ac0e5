package com.movisat.ecosat;

import android.app.Activity;
import android.nfc.NfcAdapter;
import android.nfc.Tag;
import android.os.Bundle;

import com.movisat.application.EcoSATApplication;
import com.movisat.events.OnReadedTag;
import com.movisat.log.Logg;
import com.movisat.tags.TagNFC;
import com.movisat.utilities.Utils;
import com.movisat.utils.Timer;

import org.apache.commons.lang3.ArrayUtils;
import org.greenrobot.eventbus.EventBus;

import java.util.Date;

/**
 * Esta Activity se inicia cuando un tag NFC es leído por el dispositivo. Cuando
 * esto ocurre, realiza la lectura del ID, la transmite vía EventBus e inmediatamente se
 * cierra.
 *
 * (Mantis 5848)
 * Cuando el método enableReaderMode() de NfcAdapter (en BaseActivity) no funciona.
 */
public class NfcActivity extends Activity {
   static final String TAG = "NfcActivity";
   static final Timer timer = new Timer(1);

   public static void sendTagNfc(Tag tag) {
      try {
         if (!timer.isOver()) return;
         if (!MainActivity.getInstance().hasSincro()) return;

         byte[] tagIdBytes = tag.getId().clone();
         ArrayUtils.reverse(tagIdBytes); // Se invierte el id
         String tagIdHex = Utils.arrayToHex(tagIdBytes, 0, tag.getId().length);

         Logg.info(TAG, "Tag NFC leído con ID = " + tagIdHex);

         TagNFC tagNFC = new TagNFC("", tagIdHex, new Date());
         EventBus.getDefault().post(new OnReadedTag(tagNFC));
      } catch (Throwable e) {
         Logg.info(TAG, "ERROR: " + e.getMessage());
      }
   }

   @Override
   protected void onCreate(Bundle savedInstanceState) {
      super.onCreate(savedInstanceState);
      MainActivity mainActivity = MainActivity.getInstance();
      if (mainActivity != null && mainActivity.getUsuario() > 0) {
            Tag tag = getIntent().getParcelableExtra(NfcAdapter.EXTRA_TAG);
            if (tag != null) sendTagNfc(tag);
      }
      finish();
   }

}
