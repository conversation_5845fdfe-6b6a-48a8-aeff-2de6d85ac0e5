package com.movisat.ecosat;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.CursorLoader;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.AssetFileDescriptor;
import android.content.res.Configuration;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.BitmapFactory.Options;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemSelectedListener;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.LinearLayout.LayoutParams;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.environment.Environment;
import com.movisat.adapter.InciModelAdapter;
import com.movisat.adapter.InciMotivoAdapter;
import com.movisat.adapter.InciTipoAdapter;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBIncidenciaEstado;
import com.movisat.database.DBIncidenciaFoto;
import com.movisat.database.DBIncidenciaModelo;
import com.movisat.database.DBIncidenciaMotivo;
import com.movisat.database.DBIncidenciaTipo;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaEstado;
import com.movisat.database.IncidenciaFoto;
import com.movisat.database.IncidenciaModelo;
import com.movisat.database.IncidenciaMotivo;
import com.movisat.database.IncidenciaTipo;
import com.movisat.log.Logg;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Photo;
import com.movisat.utilities.Utils;
import com.movisat.utilities.ZoomPhotoDialog;
import com.movisat.utils_android.PhotoService;
import com.movisat.utils_android.UtilssAndroid;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.Collections;

@SuppressLint("SimpleDateFormat")
public class AddInciActivity extends BaseActivity {
   public static final int MAX_NUM_IMAGES = 3;
   private static final String TAG = "AddInciActivity";
   private static final int RESULT_GALLERY = 0;
   private static ArrayList<IncidenciaTipo> tipos;
   private static ArrayList<IncidenciaModelo> modelos;
   private static ArrayList<IncidenciaMotivo> motivos;
   private static ArrayList<Bitmap> fotos;
   private static ArrayList<Long> fotosFecha;
   private final ClickImageView eventoClick = new ClickImageView();
   private final LongClickImageView eventoLongClick = new LongClickImageView();
   private AddInciActivity instance = null;
   private Spinner spinnerTipos;
   private Spinner spinnerModel;
   private Spinner spinnerMotivos;
   //public static final int INCIDENCIA_ABIERTA = 5; // idEstado = 5 -> Abierta

   protected void onCreate(Bundle savedInstanceState) {
      super.onCreate(savedInstanceState);

      String tipoSoft = Config.getInstance().getValue("tipoSoft", "0");

      fotos = new ArrayList<Bitmap>();
      fotosFecha = new ArrayList<Long>();
      instance = this;

      try {
         getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);

         // Para que no gire la pantalla
         setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);

         setContentView(R.layout.add_inci_layout_new);

         spinnerTipos = (Spinner) findViewById(R.id.cbTipoInci);
         spinnerModel = (Spinner) findViewById(R.id.cbModeloInci);
         spinnerMotivos = (Spinner) findViewById(R.id.cbMotInci);

         spinnerTipos.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> item, View view, int pos, long id) {
               modelos = null;
               motivos = null;

               IncidenciaTipo tipo = (IncidenciaTipo) item.getItemAtPosition(pos);

               // Recupero todos los modelos
               DBIncidenciaModelo dbInciModel = new DBIncidenciaModelo();
               if (!Environment.isSoftIndra)
                  modelos = dbInciModel.getAll(MainActivity
                        .getInstance().getEmpresa(), tipo
                        .getIdExterno());
               else
                  modelos = dbInciModel.getAllIndra(MainActivity.getInstance().getEmpresa());
               dbInciModel.close();

               // Relleno el combo de modelos
               InciModelAdapter modelAdapter = new InciModelAdapter(MainActivity.getInstance(), modelos);
               spinnerModel.setAdapter(modelAdapter);

               if (modelos != null) {
                  // Recupero todos los motivos
                  DBIncidenciaMotivo dbInciMot = new DBIncidenciaMotivo();
                  motivos = dbInciMot.getAll(
                        MainActivity.getInstance().getEmpresa(),
                        ((IncidenciaModelo) (spinnerModel.getItemAtPosition(0)))
                              .getIdExterno());
                  dbInciMot.close();
               }

               // Relleno el combo de motivos
               InciMotivoAdapter motAdapter;
               motAdapter = new InciMotivoAdapter(MainActivity.getInstance(), motivos);
               spinnerMotivos.setAdapter(motAdapter);
            }

            @Override
            public void onNothingSelected(AdapterView<?> arg0) {
            }
         });

         if (Environment.isSoftIndra) {
            spinnerTipos.setVisibility(View.GONE);
            findViewById(R.id.txtTipo).setVisibility(View.GONE);
            TextView tipoIndra = findViewById(R.id.TextView01);
            tipoIndra.setText("Tipo");
            TextView subTipoIndra = findViewById(R.id.TextView02);
            subTipoIndra.setText("Subtipo");
         }

         spinnerModel.setOnItemSelectedListener(new OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> item, View view, int pos, long id) {
               IncidenciaModelo model = (IncidenciaModelo) item.getItemAtPosition(pos);

               // Recupero todos los motivos
               DBIncidenciaMotivo dbInciMot = new DBIncidenciaMotivo();
               motivos = dbInciMot.getAll(MainActivity.getInstance().getEmpresa(),
                     model.getIdExterno());
               dbInciMot.close();

               // Relleno el combo de motivos
               InciMotivoAdapter motAdapter;
               motAdapter = new InciMotivoAdapter(MainActivity.getInstance(), motivos);
               spinnerMotivos.setAdapter(motAdapter);
            }

            @Override
            public void onNothingSelected(AdapterView<?> arg0) {
            }
         });

         // Evento botón añadir foto
         findViewById(R.id.btnAddFoto).setOnClickListener(v -> {
            if (!canAddMoreImages()) return;

            if (UtilssAndroid.isAndroidGreater11())
               PhotoService.get().takePhoto(instance);
            else
               Photo.getInstance().dispatchTakePictureIntent(instance);
         });

         // Evento Botón Crear
         findViewById(R.id.btnAceptarAdd).setOnClickListener(v -> {
            try {
               onPressedAccept(tipoSoft);
            } catch (Exception e) {
               Logg.error(TAG, "Error al registrar incidencia: " + e.getMessage());
            }
         });

         // Recupero todos los tipos
         DBIncidenciaTipo dbInciTipo = new DBIncidenciaTipo();
         tipos = dbInciTipo.getAll(MainActivity.getInstance().getEmpresa());
         if (Environment.isSoftIndra) {
            Collections.sort(tipos, (a1, a2) -> a1.getNombre().compareToIgnoreCase(a2.getNombre()));
         }
         dbInciTipo.close();

         // Relleno el combo de tipos
         InciTipoAdapter tiposAdapter;
         tiposAdapter = new InciTipoAdapter(MainActivity.getInstance(), tipos);
         spinnerTipos.setAdapter(tiposAdapter);

         // Recupero todos los modelos
         DBIncidenciaModelo dbInciModel = new DBIncidenciaModelo();
         if (Environment.isSoftIndra) {
            modelos = dbInciModel.getAllIndra(MainActivity.getInstance().getEmpresa());
            Collections.sort(modelos, (a1, a2) -> a1.getNombre().compareToIgnoreCase(a2.getNombre()));
         } else {
            if (spinnerTipos.getSelectedItem() != null)
               modelos = dbInciModel.getAll(MainActivity.getInstance()
                     .getEmpresa(), ((IncidenciaTipo) (spinnerTipos
                     .getSelectedItem())).getIdExterno());
         }
         dbInciModel.close();

         // Relleno el combo de modelos
         InciModelAdapter modelAdapter;
         modelAdapter = new InciModelAdapter(MainActivity.getInstance(), modelos);
         spinnerModel.setAdapter(modelAdapter);

         // Recupero todos los motivos
         DBIncidenciaMotivo dbInciMot = new DBIncidenciaMotivo();
         if (spinnerModel.getSelectedItem() != null)
            motivos = dbInciMot.getAll(
                  MainActivity.getInstance().getEmpresa(),
                  ((IncidenciaModelo) (spinnerModel.getSelectedItem()))
                        .getIdExterno());
         dbInciMot.close();

         // Relleno el combo de motivos
         InciMotivoAdapter motAdapter;
         motAdapter = new InciMotivoAdapter(MainActivity.getInstance(), motivos);
         spinnerMotivos.setAdapter(motAdapter);

         // Se comprueba si se tienen datos de tipos
         // Mantis 6023: Motivos y modelos pueden estar vacíos
         if ((tipos == null)) {
            MainActivity.getInstance().showMessage(
                  R.string.message_sync_incidencias_not_ready, Toast.LENGTH_LONG);
            finish();
            return;
         }

         // Recupero el ultimo tipo, modelo y motivo que se inserto
         int lastTipo = Integer.parseInt(Config.getInstance()
               .getValueUsuario("tipoInci", "-1"));
         int lastModel = Integer.parseInt(Config.getInstance()
               .getValueUsuario("modeloInci", "-1"));
         int lastMot = Integer.parseInt(Config.getInstance()
               .getValueUsuario("motivoInci", "-1"));

         // Recorro los tipos para seleccionar en el combo el iltimo
         for (IncidenciaTipo tipo : tipos) {
            if (tipo.getId() == lastTipo) {
               spinnerTipos.setSelection(tipos.indexOf(tipo));
               break;
            }
         }

         // Recorro los modelos para seleccionar en el combo el iltimo
         for (IncidenciaModelo model : modelos) {
            if (model.getId() == lastModel) {
               spinnerModel.setSelection(modelos.indexOf(model));
               break;
            }
         }

         // Recorro los motivos para seleccionar en el combo el iltimo
         for (IncidenciaMotivo mot : motivos) {
            if (mot.getId() == lastMot) {
               spinnerMotivos.setSelection(motivos.indexOf(mot));
               break;
            }
         }

         initGallery();
      } catch (Throwable e) {
         Logg.error(TAG, "Error en onCreate: " + e.getMessage());
         MyLoggerHandler.getInstance().error(e);
      }
   }

   private void onPressedAccept(String tipoSoft) {
      Bundle bundle = getIntent().getExtras();

      if (spinnerTipos.getSelectedItem() == null
            || spinnerModel.getSelectedItem() == null
            || spinnerMotivos.getSelectedItem() == null) {

         new InfoDialog(
               instance,
               getString(R.string.atencion),
               getString(R.string.faltanDatosIncidencia),
               InfoDialog.ICON_STOP,
               new OnInfoDialogSelect() {

                  @Override
                  public void onSelectOption(int option) {
                  }

               }, InfoDialog.BUTTON_ACCEPT,
               InfoDialog.POSITION_CENTER).show();
         return;
      }

      // Recuperamos datos a insertar en bbdd
      int idTipo = ((IncidenciaTipo) spinnerTipos.getSelectedItem()).getIdExterno();
      int idModelo = ((IncidenciaModelo) spinnerModel.getSelectedItem()).getIdExterno();
      if (Environment.isSoftIndra)
         idTipo = ((IncidenciaModelo) spinnerModel.getSelectedItem()).getTipo();
      int idMotivo = ((IncidenciaMotivo) spinnerMotivos.getSelectedItem()).getIdExterno();
      String observ = ((EditText) findViewById(R.id.ebObserv)).getText().toString();
      double latitud = bundle.getDouble("latitud");
      double longitud = bundle.getDouble("longitud");

      int idElemento = bundle.getInt("elemento");
      int idUsuario = MainActivity.getInstance().getUsuario();
      long fechaUltimoEstado = System.currentTimeMillis() / 1000;

      Incidencia inci = new Incidencia(
            0,
            0,
            MainActivity.getInstance().getEmpresa(),
            idTipo,
            idModelo,
            idMotivo,
            observ,
            1,
            fechaUltimoEstado,
            idElemento,
            latitud,
            longitud,
            idUsuario,
            0,
            0);

      // Inserto en la tabla de configuracion los últimos datos usados para que
      // si volvemos a la actividad se mantengan los mismos
      Config.getInstance().setValueUsuario("tipoInci", "" + idTipo);
      Config.getInstance().setValueUsuario("modeloInci", "" + idModelo);
      Config.getInstance().setValueUsuario("motivoInci", "" + idMotivo);

      // Creo la incidencia
      DBIncidencia dbIncidencia = new DBIncidencia();
      int idInternoInci = (int) dbIncidencia.insert(inci);
      dbIncidencia.close();
      if (idInternoInci == -1) {
         new InfoDialog(
               instance,
               getString(R.string.atencion),
               getString(R.string.errorInsertarIncidencia),
               InfoDialog.ICON_STOP,
               option -> {
               }, InfoDialog.BUTTON_ACCEPT,
               InfoDialog.POSITION_CENTER).show();

         return;
      }
      inci.setId(idInternoInci);

      String strDate = Utils.secondsToDatetimeString(fechaUltimoEstado, "yyyy-MM-dd HH:mm:ss");

      // Se inserta el estado de incidencia como abierta. En el servidor,
      // al mandar el paquete de creación de incidencia, se marca
      // automáticamente como abierta, así que hay que hacerlo aquí también.
      DBIncidenciaEstado dbIncidenciaEstado = new DBIncidenciaEstado();
      IncidenciaEstado estado = new IncidenciaEstado(0, 0,
            inci.getEmpresa(), idInternoInci, 0,
            1, strDate, 0, ""
      );

      int estadoId = (int) dbIncidenciaEstado.insert(estado);
      estado.setId(estadoId);
      dbIncidenciaEstado.close();

      // Actualizamos la incidencia en el mapa
      MyBroadCastManager.getInstance().sendBroadCastRefreshIncidencia(inci);

      // Guardo la información para enviar en la bandeja de salida
      DBPacket dbp = new DBPacket();
      Packet paquete = new Packet(Packet.INCIDENCIA_CREAR, Packet.PRIORIDAD_NORMAL, inci);
      dbp.insert(paquete);
      dbp.close();

      MyLoggerHandler.getInstance().info(String.format("%s incidencia creada.", inci));

      // si se han añadido fotos a la incidencia, las recorro para guardarlas físicamente
      // en el dispositivo y en base de datos su ruta/nombre
      if (fotos.size() > 0) {

         int i = 0;
         int idFoto = 0;
         DBIncidenciaFoto dbFoto = null;
         int idInternoFoto = 0;

         for (Bitmap foto : fotos) {
            // Creamos los paquetes de las fotos.
            IncidenciaFoto incidenciaFoto = new IncidenciaFoto(
                  0, 0, inci.getId(), 0, MainActivity
                  .getInstance()
                  .getEmpresa(),
                  MainActivity.getInstance().getUsuario(),
                  UtilssAndroid.bitmapToBase64(foto)
            );
            incidenciaFoto.setFecha(fotosFecha.get(i));

            // vamos a crear la imagen de la incidencia
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            foto.compress(
                  Bitmap.CompressFormat.JPEG,
                  Utils.getCalidadImagenSegunCobertura(getApplicationContext()),
                  stream);

            dbFoto = new DBIncidenciaFoto();
            // inten
            if (!dbFoto.update(incidenciaFoto)) {
               idInternoFoto = (int) dbFoto.insert(incidenciaFoto);
               incidenciaFoto.setId(idInternoFoto);
            }

            if (incidenciaFoto.hasImageBase64()) {
               DBPacket dbpFoto = new DBPacket();
               paquete = new Packet(
                     Packet.INCIDENCIA_INSERTAR_FOTO,
                     Packet.PRIORIDAD_NORMAL,
                     incidenciaFoto);
               dbpFoto.insert(paquete);
               dbpFoto.close();

               MyLoggerHandler.getInstance().info(String.format("Añadida foto a incidencia con codigo %d.",
                     incidenciaFoto.getIncidenciaExterno()));

               i++;

            } else {
               MyLoggerHandler.getInstance().info("No se crea el paquete de insertar imagen pues esta no existe");
            }
         }

         if (dbFoto != null) {
            dbFoto.close();
         }
      }
      finish();
   }

   @Override
   public boolean onKeyDown(int keyCode, KeyEvent event) {
      if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
         finish();
      }
      return super.onKeyDown(keyCode, event);
   }

   /**
    * Este metodo se ejecuta cada vez que se gira la pantalla
    */
   @Override
   public void onConfigurationChanged(Configuration newConfig) {
      super.onConfigurationChanged(newConfig);
   }

   @Override
   protected void onActivityResult(int requestCode, int resultCode, Intent data) {
      boolean isFromCamera = requestCode == Photo.REQUEST_IMAGE_CAPTURE || requestCode == PhotoService.REQUEST_IMAGE_CAPTURE;
      // Insertamos foto desde la camara
      if (isFromCamera && resultCode == RESULT_OK) {
         Bitmap foto;

         if (UtilssAndroid.isAndroidGreater11())
            foto = PhotoService.get().setPic();
         else
            foto = Photo.getInstance().setPic();
         if (foto != null) {
            fotos.add(foto);
            fotosFecha.add(System.currentTimeMillis());
            insertFotoGallery(foto);
         }
      }
      // Insertamos foto desde la galeria
      else if (requestCode == AddInciActivity.RESULT_GALLERY) {
         if (null != data) {
            Uri imageUri = data.getData();
            try {
               BitmapFactory.Options options = new Options();
               options.inSampleSize = 4;
               AssetFileDescriptor fileDescriptor = null;

               fileDescriptor = this.getContentResolver().openAssetFileDescriptor(imageUri, "r");

                    /*Bitmap foto = BitmapFactory.decodeFileDescriptor(
                            fileDescriptor.getFileDescriptor(), null, options);*/
               // jcaballero , cambio el método estándar de Bitmap factory, por este creado, a partir de la documentaciónd de android
               Bitmap foto = Utils.decodeBitmapFromFile(getRealPathFromURI(imageUri), 1024, 768);

               // MediaStore.Images.Media.getBitmap(this.getContentResolver(),
               // imageUri);

               fotos.add(foto);
               fotosFecha.add(System.currentTimeMillis());
               insertFotoGallery(foto);
            } catch (FileNotFoundException e) {
               MyLoggerHandler.getInstance().error(e);
            } catch (Throwable e) {
               MyLoggerHandler.getInstance().error(e);
            }
         }
      }
   }

   private String getRealPathFromURI(Uri contentUri) {
      String[] proj = {MediaStore.Images.Media.DATA};
      CursorLoader loader = new CursorLoader(this, contentUri, proj, null, null, null);
      Cursor cursor = loader.loadInBackground();
      int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
      cursor.moveToFirst();
      String result = cursor.getString(column_index);
      cursor.close();
      return result;
   }

   private void initGallery() {
      ImageView imagen;

      /* Boton Papelera */
      imagen = (ImageView) findViewById(R.id.papelera);
      imagen.setOnClickListener(v -> {
         // Si hay fotos seleccionadas preguntamos si queremos eliminar
         if (hayFotosSeleccionadas()) {
            AlertDialog.Builder builder = new AlertDialog.Builder(instance);
            builder.setMessage(getString(R.string.questionEliminarImagenesSeleccionadas));
            builder.setPositiveButton(getString(R.string.yes), (dialog, which) -> EliminarFotos());
            builder.setNegativeButton(getString(R.string.no), (dialog, which) -> dialog.dismiss());
            builder.create().show();
         } else {
            // Mostramos advertencia de que no hay imagenes seleccionadas
            Toast.makeText(instance, R.string.noSelectedImages, Toast.LENGTH_SHORT).show();
         }
      });

      /* Boton seleccion todo */
      imagen = (ImageView) findViewById(R.id.selectall);
      imagen.setOnClickListener(v -> sellectAllFotos(true));

      /* Boton desseleccion todo */
      imagen = (ImageView) findViewById(R.id.desselectall);
      imagen.setOnClickListener(v -> sellectAllFotos(false));

      /* Boton galeria */
      imagen = (ImageView) findViewById(R.id.gallery);
      imagen.setOnClickListener(v -> OpenGallery());
   }

   private void OpenGallery() {
      try {
         if (!canAddMoreImages()) return;

         Intent galleryIntent = new Intent(Intent.ACTION_PICK, android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
         startActivityForResult(galleryIntent, RESULT_GALLERY);
      } catch (Exception e) {
         Logg.error(TAG, "Error al obtener la imagen de la galería: " + e.getMessage());
      }
   }

   private void insertFotoGallery(Bitmap foto) {
      LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll);

      ImageView imageView = new ImageView(instance);
      LayoutParams params = new LayoutParams(200, 200);
      params.setMargins(5, 5, 5, 5);
      imageView.setLayoutParams(params);
      imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
      imageView.setImageBitmap(foto);
      imageView.setTag(0);

      layout.addView(imageView);

      TextView texto = (TextView) findViewById(R.id.textoImagenesNumero);
      texto.setText("(" + layout.getChildCount() + ")");

      imageView.setOnClickListener(eventoClick);
      imageView.setOnLongClickListener(eventoLongClick);
   }

   private void EliminarFotos() {
      LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll);
      int numImagenes = layout.getChildCount();
      ImageView img;

      for (int i = numImagenes - 1; i >= 0; i--) {
         img = (ImageView) layout.getChildAt(i);

         if (img.getTag() == (Integer) 1) {
            layout.removeViewAt(i);
            fotos.remove(i);
            fotosFecha.remove(i);
         }
      }
      TextView texto = (TextView) findViewById(R.id.textoImagenesNumero);
      texto.setText("(" + layout.getChildCount() + ")");
   }

   private boolean hayFotosSeleccionadas() {
      LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll);
      int numImagenes = layout.getChildCount();
      ImageView img;

      for (int i = 0; i < numImagenes; i++) {
         img = (ImageView) layout.getChildAt(i);
         if (img.getTag() == (Integer) 1)
            return true;
      }
      return false;
   }

   private void sellectAllFotos(boolean seleccionar) {
      LinearLayout layout = (LinearLayout) findViewById(R.id.layout_scroll);
      int numImagenes = layout.getChildCount();
      ImageView img;

      for (int i = 0; i < numImagenes; i++) {
         img = (ImageView) layout.getChildAt(i);

         if (seleccionar) {
            img.setColorFilter(0xAA26FFFF);
            img.invalidate();
            img.setTag(1);
         } else {
            img.clearColorFilter();
            img.invalidate();
            img.setTag(0);
         }
      }
   }

   /**
    * Indica si el número de imágenes añadidas es válido. Si no lo es, muestra un diálogo
    * informativo.
    *
    * @return si el número de imágenes añadidas es inferior a MAX_NUM_IMAGES.
    */
   public boolean canAddMoreImages() {
      if (fotos.size() < MAX_NUM_IMAGES) return true;
      new InfoDialog(instance,
            getString(R.string.atencion),
            "No puede enviar más de " + MAX_NUM_IMAGES + " imágenes por incidencia",
            InfoDialog.ICON_STOP,
            new OnInfoDialogSelect() {
               @Override
               public void onSelectOption(int option) {
               }
            },
            InfoDialog.BUTTON_ACCEPT,
            InfoDialog.POSITION_CENTER)
            .show();

      return false;
   }

   private class ClickImageView implements OnClickListener {
      private ImageView view;

      @Override
      public void onClick(View v) {
         view = (ImageView) v;

         // Siempre mostrar vista flotante con zoom
         Drawable drawable = view.getDrawable();
         Bitmap bitmap;
         if (drawable instanceof BitmapDrawable) {
            bitmap = ((BitmapDrawable) drawable).getBitmap();
         } else {
            // Fallback por seguridad
            view.setDrawingCacheEnabled(true);
            bitmap = Bitmap.createBitmap(view.getDrawingCache());
            view.setDrawingCacheEnabled(false);
         }
         
         // Mostrar dialog con zoom
         ZoomPhotoDialog.showPhoto(instance, bitmap);
      }
   }

   private class LongClickImageView implements View.OnLongClickListener {
      @Override
      public boolean onLongClick(View v) {
         ImageView imageView = (ImageView) v;
         
         if (imageView.getTag() == (Integer) 1) {
            // Deseleccionar
            imageView.clearColorFilter();
            imageView.setTag(0);
         } else {
            // Seleccionar
            imageView.setColorFilter(0xAA26FFFF);
            imageView.setTag(1);
         }
         
         v.performHapticFeedback(android.view.HapticFeedbackConstants.LONG_PRESS);
         return true;
      }
   }
}
