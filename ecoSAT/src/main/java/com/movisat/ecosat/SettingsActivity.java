package com.movisat.ecosat;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.preference.PreferenceActivity;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.PreferenceManager;

import com.environment.Environment;
import com.movisat.application.EcoSATApplication;
import com.movisat.managers.UHFManager;
import com.movisat.rfid_uhf_u9000.U9000UHFManager;
import com.movisat.synchronize.DBSynchro;

import net.mm2d.preference.Header;
import net.mm2d.preference.PreferenceActivityCompat;

import java.util.List;

/**
 * A {@link PreferenceActivity} that presents a set of application settings. On
 * handset devices, settings are presented as a single list. On tablets,
 * settings are split by category, with category headers shown to the left of
 * the list of settings.
 * <p/>
 * See <a href="http://developer.android.com/design/patterns/settings.html">
 * Android Design: Settings</a> for design guidelines and the <a
 * href="http://developer.android.com/guide/topics/ui/settings.html">Settings
 * API Guide</a> for more information on developing a Settings UI.
 */
public class SettingsActivity extends PreferenceActivityCompat {
   @Override
   protected void onCreate(Bundle savedInstanceState) {
      super.onCreate(savedInstanceState);
      setupActionBar();
   }

   public static final String KEY_PREF_SYNC_CONN = "sync_frequency";
   public static final String KEY_PREF_ZOOM_CLUSTER = "zoom_cluster";
   public static final String KEY_MINZOOM_NOSHOWCLUSTER = "zoom_min_cluster";
   public static final String KEY_PREF_ZOOM_ZONAS = "zoom_zonas";
   public static final String KEY_PREF_TIME_OPERATION_DONE_LEVEL = "time_operation_done_level";
   public static final String KEY_PREF_TIME_OPERATION_DONE_CLEAN = "time_operation_done_clean";
   public static final String KEY_PREF_KEEP_ALIVE = "keep_alive";
   public static final String KEY_PREF_UHF_POWER = "uhf_power";
   public static final String KEY_PREF_SHOW_LEVELS = "show_levels";
   public static final String KEY_PREF_SHOW_COLLECTION_FREQ = "show_collection_freq";
   public static final String KEY_PREF_UHF_U9000_POWER = "uhf_u9000_power";

   /**
    * Set up the {@link ActionBar}, if the API is available.
    */
   private void setupActionBar() {
      //        ActionBar actionBar = getSupportActionBar();
      //        if (actionBar != null) {
      //            // Show the Up button in the action bar.
      //            actionBar.setDisplayHomeAsUpEnabled(false);
      //        }
   }

   @Override
   public boolean onKeyDown(int keyCode, KeyEvent event) {
      if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
         finish();
      }
      return super.onKeyDown(keyCode, event);
   }

   @Override
   public boolean onCreateOptionsMenu(Menu menu) {
      // Se muestra el icono de la aplicación en el ActionBar
      getSupportActionBar().setDisplayOptions(ActionBar.DISPLAY_HOME_AS_UP | ActionBar.DISPLAY_SHOW_TITLE);
      getSupportActionBar().setHomeAsUpIndicator(R.drawable.ic_toolbar2);
      return super.onCreateOptionsMenu(menu);
   }

   @Override
   protected void onResume() {
      super.onResume();

      if (MainActivity.getInstance() != null)
         MainActivity.getInstance().displayKeepAlive();
   }

   /**
    * {@inheritDoc}
    */
   @Override
   public boolean onIsMultiPane() {
      return isXLargeTablet(this);
   }

   /**
    * Helper method to determine if the device has an extra-large screen. For
    * example, 10" tablets are extra-large.
    */
   private static boolean isXLargeTablet(Context context) {
      return (context.getResources().getConfiguration().screenLayout
            & Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_XLARGE;
   }

   /**
    * {@inheritDoc}
    */
   @Override
   @TargetApi(Build.VERSION_CODES.HONEYCOMB)
   public void onBuildHeaders(@NonNull final List<Header> target) {
      loadHeadersFromResource(R.xml.pref_headers, target);
      if (Environment.hasReaderUHFC71)
         loadHeadersFromResource(R.xml.pref_headers_uhf, target);
      // if (Environment.hasReaderUHFU9000)
//      loadHeadersFromResource(R.xml.pref_headers_uhf_u9000, target);
   }

   /**
    * A preference value change listener that updates the preference's summary
    * to reflect its new value.
    */
   private static Preference.OnPreferenceChangeListener sBindPreferenceSummaryToValueListener = (Preference preference, Object value) -> {
      String stringValue = value.toString();
      String trimmedValue = stringValue.trim();

      if (trimmedValue.isEmpty())
         return false;

      if (preference.getKey().equals(KEY_PREF_TIME_OPERATION_DONE_LEVEL)) {
         // Verificar si la entrada es un número.
         try {
            SharedPreferences oldPrefs = PreferenceManager.getDefaultSharedPreferences(
                  EcoSATApplication.getInstance().getApplicationContext());

            int oldValue = Integer.parseInt(oldPrefs.getString(KEY_PREF_TIME_OPERATION_DONE_LEVEL, "18"));
            int newValue = Integer.parseInt(trimmedValue);

            if (oldValue != newValue) {
               new Thread(() -> DBSynchro.getInstance().syncLevelAndCollectOperations(newValue, 0)).start();
            }
            preference.setSummary(newValue + " horas");

            return true;
         } catch (NumberFormatException e) {
            // La entrada no es un número.
            Toast.makeText(preference.getContext(), "Por favor, introduzca solo números.", Toast.LENGTH_SHORT).show();
            return false;
         }

      }

      if (preference.getKey().equals(KEY_PREF_TIME_OPERATION_DONE_CLEAN)) {
         // Verificar si la entrada es un número.
         try {
            SharedPreferences oldPrefs = PreferenceManager.getDefaultSharedPreferences(
                  EcoSATApplication.getInstance().getApplicationContext());

            int oldValue = Integer.parseInt(oldPrefs.getString(KEY_PREF_TIME_OPERATION_DONE_CLEAN, "18"));
            int newValue = Integer.parseInt(trimmedValue);

            if (oldValue != newValue) {
               new Thread(() -> DBSynchro.getInstance().syncCleanOperations(newValue, 0)).start();
            }
            preference.setSummary(newValue + " horas");

            return true;
         } catch (NumberFormatException e) {
            // La entrada no es un número.
            Toast.makeText(preference.getContext(), "Por favor, introduzca solo números.", Toast.LENGTH_SHORT).show();
            return false;
         }
      }

      if (preference instanceof ListPreference) {
         // For list preferences, look up the correct display value in
         // the preference's 'entries' list.
         ListPreference listPreference = (ListPreference) preference;
         int index = listPreference.findIndexOfValue(stringValue);

         // Set the summary to reflect the new value.
         preference.setSummary(index >= 0 ? listPreference.getEntries()[index] : null);
      } else {
         // For all other preferences, set the summary to the value's
         // simple string representation.
         preference.setSummary(stringValue);
      }

      return true;
   };

   /**
    * Binds a preference's summary to its value. More specifically, when the
    * preference's value is changed, its summary (line of text below the
    * preference title) is updated to reflect the value. The summary is also
    * immediately updated upon calling this method. The exact display format is
    * dependent on the type of preference.
    *
    * @see #sBindPreferenceSummaryToValueListener
    */
   private static void bindPreferenceSummaryToValue(Preference preference) {
      // Set the listener to watch for value changes.
      preference.setOnPreferenceChangeListener(sBindPreferenceSummaryToValueListener);

      // Trigger the listener immediately with the preference's
      // current value.
      sBindPreferenceSummaryToValueListener.onPreferenceChange(preference,
            PreferenceManager.getDefaultSharedPreferences(preference.getContext())
                  .getString(preference.getKey(), ""));
   }

   /**
    * This method stops fragment injection in malicious applications.
    * Make sure to deny any unknown fragments here.
    */
   public boolean isValidFragment(String fragmentName) {
      return PreferenceFragmentCompat.class.getName().equals(fragmentName)
            || GeneralPreferenceFragment.class.getName().equals(fragmentName)
            || DataSyncPreferenceFragment.class.getName().equals(fragmentName)
            || UHFReaderPreferenceFragment.class.getName().equals(fragmentName);
//            || UHFReaderU9000PreferenceFragment.class.getName().equals(fragmentName);
   }

   /**
    * This fragment shows general preferences only. It is used when the
    * activity is showing a two-pane settings UI.
    */
   @TargetApi(Build.VERSION_CODES.HONEYCOMB)
   public static class GeneralPreferenceFragment extends PreferenceFragmentCompat {
      @Override
      public void onCreatePreferences(final Bundle savedInstanceState, final String rootKey) {
         //            super.onCreate(savedInstanceState);
         addPreferencesFromResource(R.xml.pref_general);
         setHasOptionsMenu(true);

         // Bind the summaries of EditText/List/Dialog/Ringtone preferences
         // to their values. When their values change, their summaries are
         // updated to reflect the new value, per the Android Design
         // guidelines.
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_ZOOM_CLUSTER));
         bindPreferenceSummaryToValue(findPreference(KEY_MINZOOM_NOSHOWCLUSTER));
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_ZOOM_ZONAS));
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_KEEP_ALIVE));
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_TIME_OPERATION_DONE_LEVEL));
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_TIME_OPERATION_DONE_CLEAN));
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_SHOW_LEVELS));
         if (Environment.isVisibleFrecuenciaProcesado) {
            bindPreferenceSummaryToValue(findPreference(KEY_PREF_SHOW_COLLECTION_FREQ));
            ListPreference showCollectionFreqPref = (ListPreference) findPreference(KEY_PREF_SHOW_COLLECTION_FREQ);
            showCollectionFreqPref.setVisible(true);
         }
      }

      //        @Override
      //        public boolean onOptionsItemSelected(MenuItem item) {
      //            int id = item.getItemId();
      //            if (id == android.R.id.home) {
      //                startActivity(new Intent(getActivity(), SettingsActivity.class));
      //                return true;
      //            }
      //            return super.onOptionsItemSelected(item);
      //        }
   }

   /**
    * This fragment shows data and sync preferences only. It is used when the
    * activity is showing a two-pane settings UI.
    */
   @TargetApi(Build.VERSION_CODES.HONEYCOMB)
   public static class DataSyncPreferenceFragment extends PreferenceFragmentCompat {
      @Override
      public void onCreatePreferences(final Bundle savedInstanceState, final String rootKey) {
         //            super.onCreate(savedInstanceState);
         addPreferencesFromResource(R.xml.pref_data_sync);
         setHasOptionsMenu(true);

         // Bind the summaries of EditText/List/Dialog/Ringtone preferences
         // to their values. When their values change, their summaries are
         // updated to reflect the new value, per the Android Design
         // guidelines.
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_SYNC_CONN));
      }

      @Override
      public boolean onOptionsItemSelected(MenuItem item) {
         int id = item.getItemId();
         if (id == android.R.id.home) {

            startActivity(new Intent(getActivity(), SettingsActivity.class));
            return true;
         }
         return super.onOptionsItemSelected(item);
      }
   }

   /**
    * Este fragmento muestra datos de configuración del lector UHF (si el dispositivo es un
    * smartphone CHAINWAY con lector UHF).
    */
   @TargetApi(Build.VERSION_CODES.HONEYCOMB)
   public static class UHFReaderPreferenceFragment extends PreferenceFragmentCompat
         implements SharedPreferences.OnSharedPreferenceChangeListener {
      @Override
      public void onCreatePreferences(final Bundle savedInstanceState, final String rootKey) {
         //            super.onCreate(savedInstanceState);
         addPreferencesFromResource(R.xml.pref_uhf_reader);
         setHasOptionsMenu(true);

         readDeviceUHFPower();

         // Bind the summaries of EditText/List/Dialog/Ringtone preferences
         // to their values. When their values change, their summaries are
         // updated to reflect the new value, per the Android Design
         // guidelines.
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_UHF_POWER));
      }

      @Override
      public boolean onOptionsItemSelected(MenuItem item) {
         int id = item.getItemId();
         if (id == android.R.id.home) {
            startActivity(new Intent(getActivity(), SettingsActivity.class));
            return true;
         }
         return super.onOptionsItemSelected(item);
      }

      @Override
      public void onResume() {
         super.onResume();
         getPreferenceManager().getSharedPreferences().registerOnSharedPreferenceChangeListener(this);
      }

      @Override
      public void onPause() {
         super.onPause();
         getPreferenceManager().getSharedPreferences().unregisterOnSharedPreferenceChangeListener(this);
      }

      @Override
      public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
         try {
            if (key.equals(KEY_PREF_UHF_POWER) && UHFManager.get().isInitialized()) {
               // Se establece el valor escogido a la potencia del lector UHF
               String value = sharedPreferences.getString(KEY_PREF_UHF_POWER, "15");
               int power = Integer.parseInt(value);
               boolean success = UHFManager.get().getReader().setPower(power);
               Toast.makeText(getContext(),
                     success ? R.string.uhf_power_change_success : R.string.uhf_power_change_failure,
                     Toast.LENGTH_SHORT).show();
            }
         } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
         }
      }

      /**
       * Obtiene del lector UHF la potencia de lectura y la muestra (en lugar del valor guardado)
       */
      private void readDeviceUHFPower() {
         try {
            if (UHFManager.get().isInitialized()) {
               int power = UHFManager.get().getReader().getPower();
               ListPreference editText = (ListPreference) findPreference(KEY_PREF_UHF_POWER);
               editText.setValue(String.valueOf(power));
            }
         } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
         }
      }
   }

   @TargetApi(Build.VERSION_CODES.HONEYCOMB)
   public static class UHFReaderU9000PreferenceFragment extends PreferenceFragmentCompat
         implements SharedPreferences.OnSharedPreferenceChangeListener {
      @Override
      public void onCreatePreferences(final Bundle savedInstanceState, final String rootKey) {
         addPreferencesFromResource(R.xml.pref_uhf_u9000_reader); // Asume que has creado este archivo XML
         setHasOptionsMenu(true);
         bindPreferenceSummaryToValue(findPreference(KEY_PREF_UHF_U9000_POWER));
      }

      @Override
      public boolean onOptionsItemSelected(MenuItem item) {
         int id = item.getItemId();
         if (id == android.R.id.home) {
            startActivity(new Intent(getActivity(), SettingsActivity.class));
            return true;
         }
         return super.onOptionsItemSelected(item);
      }

      @Override
      public void onResume() {
         super.onResume();
         getPreferenceManager().getSharedPreferences().registerOnSharedPreferenceChangeListener(this);
      }

      @Override
      public void onPause() {
         super.onPause();
         getPreferenceManager().getSharedPreferences().unregisterOnSharedPreferenceChangeListener(this);
      }

      @Override
      public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
         if (key.equals(KEY_PREF_UHF_U9000_POWER)) {
            // Aquí, debes agregar la lógica para cambiar la potencia de UHF para el lector U9000
            String value = sharedPreferences.getString(KEY_PREF_UHF_U9000_POWER, "26");
            int power = Integer.parseInt(value);
            boolean isUpdated = U9000UHFManager.get().setPower(power);
            Toast.makeText(getContext(),
                  isUpdated ? R.string.uhf_power_change_success : R.string.uhf_power_change_failure,
                  Toast.LENGTH_SHORT).show();
         }
      }
   }
}
