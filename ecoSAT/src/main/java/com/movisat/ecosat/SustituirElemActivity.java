package com.movisat.ecosat;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Vibrator;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.ContextCompat;

import com.movisat.database.DBElemento;
import com.movisat.database.DBTags;
import com.movisat.database.Elemento;
import com.movisat.database.InfoSustituir;
import com.movisat.database.Tags;
import com.movisat.events.OnReadedTag;
import com.movisat.log.Logg;
import com.movisat.managers.TagReaderManager;
import com.movisat.managers.UHFManager;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.Packet;
import com.movisat.tags.ITag;
import com.movisat.use_case.TagSendSensor;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Date;

/**
 * Created by dsanchez on 06/02/2018.
 */

public class SustituirElemActivity extends BaseActivity {

    private static SustituirElemActivity instance = null;

    private static final int ESTADO_SIN_INICIAR = 0;
    private static final int ESTADO_ESPERANDO_RETIRADO = 1;
    private static final int ESTADO_ESPERANDO_DEPOSITADO = 2;
    private static final int ESTADO_ESPERANDO_CONFIRMACION = 5;

    private static final int TAG_VIBRATION_MS = 100;
    private static final boolean DEBUG = false;

    private int estado_activity = ESTADO_SIN_INICIAR;
    private Elemento elementoRetirado = null;
    private Elemento elementoDepositado = null;
    private Tags tagRetirado = null;
    private Tags tagDepositado = null;
    private boolean cambiarPU = false;
    private boolean crearElemento = false;
    private boolean procesandoTag = false;
    private int destino_retirado;

    private LinearLayout layoutEstadoSustitucion;
    private ImageView imgEstadoSustitucion;
    private TextView textEstadoSustitucion;
    private TextView textRetirado;
    private TextView textDepositado;
    private TextView textMatriculaRetirado;
    private TextView textMatriculaDepositado;
    private TextView textTagRetirado;
    private TextView textTagDepositado;
    private Button btnVolver;
    private Button btnAceptar;

    //--------------------------------------------
    private static final String TAG = "SustituirElemActivity";

    public static SustituirElemActivity getInstance() {
        return instance;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {

            instance = this;
            setContentView(R.layout.sustituir_elem_layout);

            // Se obtiene el destino del elemento que se retire (lavado/taller)
            Intent intent = getIntent();
            destino_retirado = intent.getIntExtra("destino", Elemento.ESTADO_TALLER);

            setTitle(destino_retirado == Elemento.ESTADO_LAVADO ?
                    R.string.sustituir_elemento_lavado : R.string.sustituir_elemento_reparacion);

            // Componentes de la UI para indicar la fase del proceso de sustitución
            layoutEstadoSustitucion = (LinearLayout) findViewById(R.id.layoutEstadoSustitucion);
            imgEstadoSustitucion = (ImageView) findViewById(R.id.imgEstadoSustitucion);
            textEstadoSustitucion = (TextView) findViewById(R.id.textEstadoSustitucion);

            layoutEstadoSustitucion.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    updateTag();
                }
            });

            // Componentes de la UI para mostrar la información de los elementos leídos
            textRetirado = (TextView) findViewById(R.id.textRetirado);
            textDepositado = (TextView) findViewById(R.id.textDepositado);
            textMatriculaRetirado = (TextView) findViewById(R.id.textScannedMatriculaRetirado);
            textMatriculaDepositado = (TextView) findViewById(R.id.textScannedMatriculaDepositado);
            textTagRetirado = (TextView) findViewById(R.id.textScannedTagRetirado);
            textTagDepositado = (TextView) findViewById(R.id.textScannedTagDepositado);

            // Botones de la UI
            btnVolver = (Button) findViewById(R.id.btnVolver);
            btnAceptar = (Button) findViewById(R.id.btnAceptar);

            addListeners();

            // Se establecen los estados iniciales de la Activity
            setEstadoActivity(ESTADO_SIN_INICIAR);
            setEstadoActivity(ESTADO_ESPERANDO_RETIRADO);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }


    /**
     * Añade los listeners para las distintas funciones de la UI.
     */
    private void addListeners() {

        try {

            btnVolver.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    finish();
                }
            });

            btnAceptar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {

                    if (tagRetirado != null && tagDepositado != null) {

                        if (saveChanges()) {
                            MainActivity.getInstance().showMessage(getString(R.string.sustitucion_correcta));
                            finish();
                        } else {
                            MainActivity.getInstance().showMessage(R.string.sustitucion_incorrecta, Toast.LENGTH_LONG);
                        }

                    } else {

                        saveChangesManual();
                    }
                }
            });

            textMatriculaDepositado.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                }

                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                }

                @Override
                public void afterTextChanged(Editable editable) {
                    String retirado = textMatriculaRetirado.getText().toString();
                    String depositado = textMatriculaDepositado.getText().toString();
                    if (depositado != null && retirado != null &&
                            retirado.length() > 0 && depositado.length() > 0) {
                        btnAceptar.setEnabled(true);
                        btnAceptar.setBackgroundResource(R.drawable.bg_key);
                    } else {
                        btnAceptar.setBackgroundResource(R.color.gris);
                        btnAceptar.setEnabled(false);
                    }
                }
            });

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        Logg.info(TAG + "-" + getActivityName(), "[onKeyDown] keyCode: " + keyCode);

        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }

        return super.onKeyDown(keyCode, event);
    }


    private void saveChangesManual() {

        try {

            String matRetirado = textMatriculaRetirado.getText().toString();
            String matDepositado = textMatriculaDepositado.getText().toString();

            // Busco el TAG según la matrícula introducida
            DBTags dbTags = new DBTags();
            tagRetirado = dbTags.getByMatricula(matRetirado, MainActivity.getInstance().getEmpresa());
            tagDepositado = dbTags.getByMatricula(matDepositado, MainActivity.getInstance().getEmpresa());

            // Busco los elementos según el TAG
            DBElemento dbElemento = new DBElemento();
            elementoRetirado = dbElemento.getElementoByTag(tagRetirado);
            elementoDepositado = dbElemento.getElementoByTag(tagDepositado);

            if (elementoRetirado == null)
                MainActivity.getInstance().showMessage(R.string.elemento_retirado_no_existe, Toast.LENGTH_LONG);
            else if (elementoDepositado == null)
                MainActivity.getInstance().showMessage(R.string.elemento_depositado_no_existe, Toast.LENGTH_LONG);
            else if (matRetirado.equals(matDepositado))
                MainActivity.getInstance().showMessage(R.string.elemento_retirado_igual_depositado, Toast.LENGTH_LONG);
            else if (elementoRetirado.getEstado() != Elemento.ESTADO_ACTIVO)
                MainActivity.getInstance().showMessage(R.string.elemento_inactivo, Toast.LENGTH_LONG);
            else {

                setElementoRetirado(tagRetirado, elementoRetirado);
                setElementoDepositado(tagDepositado, elementoDepositado);

                // Elemento activo -> Se ofrece la opción de forzar cambio de ubicación
                if (elementoDepositado.getEstado() == Elemento.ESTADO_ACTIVO) {

                    AlertDialog.Builder builder = new AlertDialog.Builder(this);

                    builder.setMessage(R.string.elem_activo_cambiar_pu)
                            .setTitle(R.string.elem_activo_cambiar_pu_title)
                            .setPositiveButton("Sí", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int id) {

                                    cambiarPU = true;

                                    if (saveChanges()) {
                                        MainActivity.getInstance().showMessage(getString(R.string.sustitucion_correcta));
                                        instance.finish();
                                    } else {
                                        MainActivity.getInstance().showMessage(R.string.sustitucion_incorrecta, Toast.LENGTH_LONG);
                                    }

                                    elementoRetirado = elementoDepositado = null;
                                    tagRetirado = tagDepositado = null;
                                }
                            })
                            .setNegativeButton("No", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int id) {

                                    elementoRetirado = elementoDepositado = null;
                                    tagRetirado = tagDepositado = null;

                                    dialog.cancel();
                                }
                            })
                            .setOnCancelListener(new DialogInterface.OnCancelListener() {
                                @Override
                                public void onCancel(DialogInterface dialogInterface) {

                                    elementoRetirado = elementoDepositado = null;
                                    tagRetirado = tagDepositado = null;
                                }
                            })
                            .create()
                            .show();
                } else {

                    if (saveChanges()) {
                        MainActivity.getInstance().showMessage(getString(R.string.sustitucion_correcta));
                        finish();
                    } else {
                        MainActivity.getInstance().showMessage(R.string.sustitucion_incorrecta, Toast.LENGTH_LONG);
                    }

                    elementoRetirado = elementoDepositado = null;
                    tagRetirado = tagDepositado = null;
                }
            }

        } catch (Throwable e) {

            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }


    /**
     * Toma la referencia del elemento que va a ser retirado y su tag y muestra sus datos en la UI.
     *
     * @param tag      Tag del elemento retirado.
     * @param elemento Elemento retirado.
     */
    private void setElementoRetirado(Tags tag, Elemento elemento) {

        try {

            textMatriculaRetirado.setText(tag.getMatricula());
            textTagRetirado.setText(tag.getTag());
            elementoRetirado = elemento;
            tagRetirado = tag;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Toma la referencia del elemento que va a ser depositado y su tag y muestra sus datos en la UI.
     *
     * @param tag      Tag del elemento depositado.
     * @param elemento Elemento depositado.
     */
    private void setElementoDepositado(Tags tag, Elemento elemento) {

        try {

            textMatriculaDepositado.setText(tag.getMatricula());
            textTagDepositado.setText(tag.getTag());
            elementoDepositado = elemento;
            tagDepositado = tag;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
        EventBus.getDefault().register(this);
    }


    @Override
    protected void onStop() {
        super.onStop();
        EventBus.getDefault().unregister(this);
    }


    /**
     * Esta función está suscrita al EventBus y recibe de tags leídos por el lector Bluetooth.
     *
     * @param event Contiene el tag leído por el lector.
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRfidTagRead(OnReadedTag event) {

        try {
            // Se obtiene el tag leído de la base de datos
            DBTags dbTags = new DBTags();
            Tags tag = dbTags.getByTag(event.tag, MainActivity.getInstance().getEmpresa());
            dbTags.close();

            // Se procesa el tag leído
            tagRead(tag, event.tag);

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Procesa el tag indicado en función del estado actual de la Activity (esperando o no tags)
     * y del elemento esperado (retirado o depositado).
     *
     * @param tag Tag que va a ser procesado.
     */
    private void tagRead(Tags tag, ITag iTag) {

        try {
            procesandoTag = false;
            // Se hace vibrar el dispositivo ante la lectura de un tag
            try {
                Vibrator v = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
                v.vibrate(TAG_VIBRATION_MS);
            } catch (Exception e) {
                MyLoggerHandler.getInstance().error(e);
                e.printStackTrace();
            }

            // Si ya se han leído los elementos retirado y depositado, no se aceptan más lecturas
            if (estado_activity > ESTADO_ESPERANDO_DEPOSITADO) {
                if (DEBUG)
                    Toast.makeText(this, "(Debug) Ya no se aceptan más lecturas de tags.", Toast.LENGTH_SHORT).show();
                return;
            }

            // Si la lectura ocurre antes de completar la anterior (por ejemplo, si se ha mostrado
            // un cuadro de diálogo), se indica y se ignora la lectura actual.
            if (procesandoTag) {
                MainActivity.getInstance().showMessage(R.string.no_se_puede_procesar);
                return;
            }

            TagSendSensor.execute(iTag, tag);

            // No se puede trabajar con un tag que no está en la base de datos
            if (tag == null)
                return;

            // Si para el tag depositado se ha leído el mismo tag que el retirado...
            if (tagRetirado != null && tag.getTag().equals(tagRetirado.getTag())) {
                MainActivity.getInstance().showMessage(R.string.tag_igual_al_retirado, Toast.LENGTH_LONG);
                return;
            }

            procesandoTag = true;

            // Se obtiene el elemento asociado al tag
            DBElemento dbElemento = new DBElemento();
            Elemento elemento = dbElemento.getElementoByTag(tag);
            dbElemento.close();

            //if(elemento == null) MainActivity.getInstance().showMessage(R.string.no_elem_tag_leido, Toast.LENGTH_LONG);

            /*if(retirar){ //he pulsado el botón retirar
                textTagRetirado.setText(elemento.getTag());
                textMatriculaRetirado.setText(elemento.getMatricula());
                //reseteamos valores
                retirar = false; depositar = false;
            }else if(depositar){ //he pulsado el botón depositar
                textTagDepositado.setText(elemento.getTag());
                textMatriculaDepositado.setText(elemento.getTag());
                //reseteamos valores
                depositar = false; retirar = false;
            }*/

            switch (estado_activity) {

                case ESTADO_SIN_INICIAR:
                case ESTADO_ESPERANDO_CONFIRMACION:
                    procesandoTag = false;
                    break;

                case ESTADO_ESPERANDO_RETIRADO:
                    // El elemento a retirar debe existir y estar activo (si está inactivo, no tendría sentido retirarlo)

                    if (elemento == null) {

                        // No se ha encontrado elemento -> No hacer nada
                        MainActivity.getInstance().showMessage(
                                getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()),
                                Toast.LENGTH_LONG);
                        //MainActivity.getInstance().showMessage(R.string.tag_no_existe, Toast.LENGTH_LONG);
                        procesandoTag = false;

                    } else if (elemento.getEstado() == Elemento.ESTADO_ACTIVO) {

                        // Elemento activo -> Se añade el elemento y se avanza a la siguiente fase de la Activity
                        setElementoRetirado(tag, elemento);
                        setEstadoActivity(ESTADO_ESPERANDO_DEPOSITADO);

                    } else {

                        // Elemento inactivo -> No hacer nada
                        MainActivity.getInstance().showMessage(R.string.elemento_inactivo, Toast.LENGTH_LONG);
                        procesandoTag = false;
                    }

                    break;

                case ESTADO_ESPERANDO_DEPOSITADO:
                    // El elemento a depositar debe existir y estar inactivo . Si está activo, se
                    // ofrece la opción de cambiar ubicación a la del elemento retirado. Si no
                    // existe el elemento, se ofrece la opción de crearlo.

                    if (tag == null) {
                        // No se ha encontrado elemento -> Se ofrece la opción de crearlo
                        alertElemNoExisteCrear(tag);
                    }
                    if (elemento == null) {
                        // El tag está en la base de datos, pero no está asociado a ningún elemento
                        MainActivity.getInstance().showMessage(
                                getString(R.string.noExisteNingunElementoAsociadoAlTagX, tag.getTag()),
                                Toast.LENGTH_LONG);

                    } else if (elemento.getEstado() == Elemento.ESTADO_ACTIVO) {

                        // Elemento activo -> Se ofrece la opción de forzar cambio de ubicación
                        alertElemActivoCambiarPU(tag, elemento);

                    } else {

                        // Elemento inactivo -> Se añade el elemento y se avanza a la siguiente fase de la Activity
                        setElementoDepositado(tag, elemento);
                        setEstadoActivity(ESTADO_ESPERANDO_CONFIRMACION);
                    }

                    break;
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

    }


    /**
     * Cambia el estado de la Activity y modifica los objetos y la UI en función del nuevo estado.
     *
     * @param estado Estado que tomará la Activity.
     */
    private void setEstadoActivity(int estado) {

        try {

            estado_activity = estado;

            switch (estado_activity) {

                case ESTADO_SIN_INICIAR:
                    // Se establece la Activity a su estado inicial
                    cambiarPU = false;
                    crearElemento = false;
                    textRetirado.setTextColor(ContextCompat.getColor(this, R.color.gris));
                    textDepositado.setTextColor(ContextCompat.getColor(this, R.color.gris));
                    btnAceptar.setBackgroundResource(R.color.gris);
                    btnAceptar.setEnabled(false);
                    break;

                case ESTADO_ESPERANDO_RETIRADO:
                    // Se establece la Activity al estado de espera de elemento a retirar
                    layoutEstadoSustitucion.setBackgroundResource(R.color.gris);
                    textEstadoSustitucion.setText(R.string.escanear_elemento_retirado);
                    imgEstadoSustitucion.setImageResource(R.drawable.ic_retirar_black_60dp);
                    break;

                case ESTADO_ESPERANDO_DEPOSITADO:
                    // Se establece la Activity al estado de espera de elemento a depositar
                    layoutEstadoSustitucion.setBackgroundResource(R.color.gris);
                    textEstadoSustitucion.setText(R.string.escanear_elemento_depositado);
                    imgEstadoSustitucion.setImageResource(R.drawable.ic_depositar_black_60dp);
                    textRetirado.setTextColor(ContextCompat.getColor(this, R.color.pressed_color));
                    break;

                case ESTADO_ESPERANDO_CONFIRMACION:
                    // Se establece la Activity al estado de espera de confirmación
                    layoutEstadoSustitucion.setBackgroundResource(R.color.theme_principal);
                    textEstadoSustitucion.setText(R.string.confirmar_sustitucion);
                    imgEstadoSustitucion.setImageResource(R.drawable.ic_check_black_60dp);
                    textDepositado.setTextColor(ContextCompat.getColor(this, R.color.pressed_color));
                    btnAceptar.setBackgroundResource(R.drawable.bg_key);
                    btnAceptar.setEnabled(true);
                    break;
            }

            procesandoTag = false;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Muestra un diálogo para indicar que el elemento que se desea depositar no existe,
     * ofreciendo la posibilidad de crearlo.
     *
     * @param tag Tag leído.
     */
    private void alertElemNoExisteCrear(final Tags tag) {

        try {

            AlertDialog.Builder builder = new AlertDialog.Builder(this);

            builder.setMessage(R.string.elem_no_existe_crear)
                    .setTitle(R.string.elem_no_existe_crear_title)
                    .setPositiveButton("Sí", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {
                            // TODO Crear elemento

                            Elemento elemento = createElemento(tag);
                            if (elemento != null) {
                                crearElemento = true;
                                setElementoDepositado(tag, elemento);
                                if (DEBUG)
                                    Toast.makeText(getBaseContext(), "(Debug) Elemento creado", Toast.LENGTH_SHORT).show();
                                setEstadoActivity(ESTADO_ESPERANDO_CONFIRMACION);
                            } else {
                                if (DEBUG)
                                    Toast.makeText(getBaseContext(), "(Debug) ERROR creando elemento", Toast.LENGTH_SHORT).show();
                                procesandoTag = false;
                            }

                        }
                    })
                    .setNegativeButton("No", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {
                            dialog.cancel();
                        }
                    })
                    .setOnCancelListener(new DialogInterface.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialogInterface) {
                            // La activity se queda a la espera de un nuevo tag (no se cambia el estado)
                            if (DEBUG)
                                Toast.makeText(getBaseContext(), "(Debug) Creación cancelada.", Toast.LENGTH_SHORT).show();
                            procesandoTag = false;
                        }
                    })
                    .create()
                    .show();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Muestra un diálogo para indicar que el elemento que se desea depositar ya está activo en
     * otro p.u., ofreciendo la posibilidad de forzar el cambio de p.u.
     *
     * @param tag      Tag leído.
     * @param elemento Elemento asociado al tag leído.
     */
    private void alertElemActivoCambiarPU(final Tags tag, final Elemento elemento) {

        try {

            AlertDialog.Builder builder = new AlertDialog.Builder(this);

            builder.setMessage(R.string.elem_activo_cambiar_pu)
                    .setTitle(R.string.elem_activo_cambiar_pu_title)
                    .setPositiveButton("Sí", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {
                            cambiarPU = true;
                            setElementoDepositado(tag, elemento);
                            if (DEBUG)
                                Toast.makeText(getBaseContext(), "(Debug) El elemento cambiará su p.u.", Toast.LENGTH_SHORT).show();
                            setEstadoActivity(ESTADO_ESPERANDO_CONFIRMACION);
                        }
                    })
                    .setNegativeButton("No", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {

                            dialog.cancel();
                        }
                    })
                    .setOnCancelListener(new DialogInterface.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialogInterface) {
                            // La activity se queda a la espera de un nuevo tag (no se cambia el estado)
                            if (DEBUG)
                                Toast.makeText(getBaseContext(), "(Debug) Cambio de ubicación cancelado.", Toast.LENGTH_SHORT).show();
                            procesandoTag = false;
                        }
                    })
                    .create()
                    .show();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }
    }


    /**
     * Crea un nuevo elemento con los valores del elemento que va a ser retirado, pero
     * no lo introduce en la base de datos.
     *
     * @param tag Tag del elemento que va a ser creado.
     * @return Elemento creado
     */
    private Elemento createElemento(Tags tag) {

        Elemento elemento = null;

        try {
            if (elementoRetirado != null) {
                elemento = new Elemento(0, 0, elementoRetirado.getEmpresa(),
                        "", elementoRetirado.getModelo(), tag.getMatricula(), Elemento.ESTADO_ACTIVO,
                        elementoRetirado.getPosition().latitude, elementoRetirado.getPosition().longitude, "", "", 0, "");
            }

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return elemento;
    }


    /**
     * Realiza la sustitución de los elementos retirado/depositado. Se ejecutan los cambios
     * pertinentes, se guardan en la base de datos local y se mandan a la bandeja de salida.
     *
     * @return True si ha ido bien, False en otro caso.
     */
    private boolean saveChanges() {

        boolean ret = false;

        try {

            if (elementoRetirado == null || elementoDepositado == null)
                return false;

            DBElemento dbElemento = new DBElemento();

            elementoRetirado.setEstado(destino_retirado);
            elementoDepositado.setEstado(Elemento.ESTADO_ACTIVO);

            dbElemento.update(elementoRetirado);
            if (destino_retirado == Elemento.ESTADO_LAVADO)
                MyBroadCastManager.getInstance().sendBroadCastLavado(elementoRetirado);

            // Se asigna a la ubicación del elemento depositado la del elemento retirado
            elementoDepositado.setPosition(elementoRetirado.getPosition().latitude,
                    elementoRetirado.getPosition().longitude);

            if (crearElemento) {
                // Si hay que crear el elemento, se inserta en la base de datos local
                int id = (int) dbElemento.insert(elementoDepositado);
                elementoDepositado.setId(id);

                // Se establece el id interno del elemento creado en su tag
                tagDepositado.setIdInternoElemento(elementoDepositado.getId());
                DBTags dbTags = new DBTags();
                dbTags.update(tagDepositado);
                dbTags.close();
            } else {
                dbElemento.update(elementoDepositado);
            }

            dbElemento.close();

            // Se abre la bandeja de salida para mandar al servidor el elemento modificado/creado
            DBPacket dbPacket = new DBPacket();

            if (crearElemento) {
                dbPacket.insert(new Packet(
                        Packet.ELEMENTO_CREAR,
                        Packet.PRIORIDAD_NORMAL,
                        elementoDepositado));
            }

            dbPacket.insert(new Packet(
                    Packet.ELEMENTO_SUSTITUIR,
                    Packet.PRIORIDAD_NORMAL,
                    new InfoSustituir(elementoRetirado, elementoDepositado, new Date())));

            dbPacket.close();

            // Se actualiza el mapa
            MyBroadCastManager.getInstance().sendBroadCastRefreshMap();

            ret = true;

        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return ret;
    }

    //----------------------------------

    private void updateTag() {
        Logg.info(TAG, "[updateTag]");

        // Comprobar si estoy utilizando el smartphone U9000
        if (Build.MODEL.equals("PDA")) {
            Logg.info(TAG, "[updateTag] U9000 - Leyendo 134 o UHF");
            TagReaderManager.read134orUHF((buffer, size) -> onReaded134TagData(buffer, size));
        }

        //leer tag
        else if (UHFManager.get().isReading()) {
            UHFManager.get().stopReadLoop();
        } else {
            // Se inicia la lectura UHF si el usuario está identificado en la aplicación
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity != null && mainActivity.getUsuario() > 0) {
                UHFManager.get().readSingleTag(3000);
            }
        }
    }

}
