package com.movisat.ecosat;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.CheckedTextView;
import android.widget.TextView;

import com.environment.Environment;
import com.movisat.application.EcoSATApplication;
import com.movisat.database.DBElemento;
import com.movisat.managers.DiscoveryManager;
import com.movisat.outbox.DBPacket;
import com.movisat.services.MyLocationService;
import com.movisat.utilities.Config;
import com.movisat.utilities.Phone;

public class InfoActivity extends BaseActivity {
    private static InfoActivity instance = null;
    private static TextView version;
    private static TextView url;
    private static TextView fechaSincronizacion;
    private static TextView numVersion;
    private static TextView numSatelites;
    private static TextView imei;
    private static TextView tvDescripcion;
    private static TextView tvNumElem;
    private static TextView tvNumMensajesPendientes;
    private static TextView tvMacPulsera;
    private static CheckedTextView checkedTextLectorNFC;
    private static CheckedTextView checkedTextLectorUHFC71;
    private static CheckedTextView checkedTextLectorLFChainway;
    private static CheckedTextView checkedTextLectorUHFU9000;

    public static InfoActivity getInstance() {
        return instance;
    }

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            instance = this;
            setContentView(R.layout.info_activity);

            setVersion(findViewById(R.id.tipoSoftware));
            setUrl(findViewById(R.id.url));
            setImei(findViewById(R.id.imei));
            setFechaSincronizacion(findViewById(R.id.ultsincro));
            setNumSatelites(findViewById(R.id.NumSatelites));
            setNumVersion(findViewById(R.id.numVersion));
            setDescripcion(findViewById(R.id.tvDescripcion));
            setNumElementosSincronizados(findViewById(R.id.tvNumElem));
            setNumElementosPendientes(findViewById(R.id.tvNumMensajes));
            setMacPulsera(findViewById(R.id.tvMacPulsera));
            setLectorNFC(findViewById(R.id.checkedTextLectorNFC));
            setLectorUHFC71(findViewById(R.id.checkedTextLectorUHFC71));
            setLectorLFChainway(findViewById(R.id.checkedTextLectorLFChainway));
            setLectorUHFU9000(findViewById(R.id.checkedTextLectorUHFU9000));

            cargarDatos();


            findViewById(R.id.recargar).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
//                    EcoSATApplication.getInstance().initReaders();
                    cargarDatos();
                }
            });


            // Evento botin volver
            findViewById(R.id.volver).setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    finish();
                }
            });
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    private void cargarDatos() {

        try {

            String urlNoEncontrada = getString(R.string.urlNoEncontrada);
            String versionNoEncontrada = getString(R.string.versionNoEncontrada);
            url.setText(Config.getInstance().getValue("webSvc", urlNoEncontrada));
            version.setText(Config.getInstance().getValue("tipoSoft", versionNoEncontrada));
            imei.setText(Phone.getInstance().getIMEI());
            fechaSincronizacion.setText(Config.getInstance().getValueEmpresa("ultSincro", getString(R.string.noSincronizadoNunca)));
            numSatelites.setText(String.valueOf(MyLocationService.NumSatelites));

            numVersion.setText(Config.getInstance().getValue("versionSoftware", ""));

            int num_elem = 0;

            DBElemento db = new DBElemento();
            num_elem = db.getCount(MainActivity.getInstance().getEmpresa());
            db.close();
            tvNumElem.setText(String.valueOf(num_elem));


            int num_mensajesPendientes = 0;

            DBPacket dbp = new DBPacket();
            num_mensajesPendientes = dbp.getCount(0);
            dbp.close();
            tvNumMensajesPendientes.setText(String.valueOf(num_mensajesPendientes));

            tvDescripcion.setText(Config.getInstance().getValue("nombreDispositivo", ""));

            if (Environment.isSoftIndra) {
                findViewById(R.id.textPulsera).setVisibility(View.VISIBLE);
                tvMacPulsera.setVisibility(View.VISIBLE);

                if (DiscoveryManager.getInstance().checkBond()) {
                    String macPulsera = DiscoveryManager.getInstance().getMAC();
                    tvMacPulsera.setText(macPulsera);
                } else {
                    tvMacPulsera.setText("No hay ninguna pulsera vinculada");
                }
            }

            if (Environment.hasReaderNFC) {
                checkedTextLectorNFC.setCheckMarkDrawable(R.drawable.icon_aceptar);
            }
            if (Environment.hasReaderUHFC71) {
                checkedTextLectorUHFC71.setCheckMarkDrawable(R.drawable.icon_aceptar);
            }
            if (Environment.hasReaderLFChainway && !Environment.hasReaderUHFU9000 && !Environment.hasReaderUHFC71) {
                checkedTextLectorLFChainway.setCheckMarkDrawable(R.drawable.icon_aceptar);
            }
            if (Environment.hasReaderUHFU9000 && !Environment.hasReaderUHFC71) {
                checkedTextLectorUHFU9000.setCheckMarkDrawable(R.drawable.icon_aceptar);
            }


        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);

        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    public static TextView getUrl() {
        return url;
    }

    public static void setUrl(TextView url) {
        InfoActivity.url = url;
    }

    public static TextView getFechaSincronizacion() {
        return fechaSincronizacion;
    }

    public static void setFechaSincronizacion(TextView fechaSincronizacion) {
        InfoActivity.fechaSincronizacion = fechaSincronizacion;
    }

    public static void setNumVersion(TextView numVersion) {
        InfoActivity.numVersion = numVersion;
    }

    public static void setDescripcion(TextView descripcion) {
        tvDescripcion = descripcion;
    }

    public static void setNumElementosSincronizados(TextView numElementos) {
        tvNumElem = numElementos;
    }

    public static void setNumElementosPendientes(TextView numMensajesPendientes) {
        tvNumMensajesPendientes = numMensajesPendientes;
    }

    public static void setNumSatelites(TextView numSatelites) {
        InfoActivity.numSatelites = numSatelites;
    }

    public static void setNumSatelites2(int numSatelites) {
        InfoActivity.numSatelites.setText(String.valueOf(numSatelites));
    }

    public static TextView getImei() {
        return imei;
    }

    public static void setImei(TextView imei) {
        InfoActivity.imei = imei;
    }

    public static TextView getVersion() {
        return version;
    }

    public static void setVersion(TextView version) {
        InfoActivity.version = version;
    }

    public static void setMacPulsera(TextView tvMacPulsera) {
        InfoActivity.tvMacPulsera = tvMacPulsera;
    }

    public static void setLectorNFC(CheckedTextView checkedTextLectorUHF) {
        InfoActivity.checkedTextLectorNFC = checkedTextLectorUHF;
    }

    public static void setLectorUHFC71(CheckedTextView checkedTextLectorUHF) {
        InfoActivity.checkedTextLectorUHFC71 = checkedTextLectorUHF;
    }

    public static void setLectorLFChainway(CheckedTextView checkedTextLector134) {
        InfoActivity.checkedTextLectorLFChainway = checkedTextLector134;
    }

    public static void setLectorUHFU9000(CheckedTextView checkedTextLectorUHFU9000) {
        InfoActivity.checkedTextLectorUHFU9000 = checkedTextLectorUHFU9000;
    }

}
