package com.movisat.ecosat;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.provider.Settings;
import androidx.core.app.NotificationCompat;
import android.os.Build;
import android.media.RingtoneManager;
import android.app.Notification;

import static com.movisat.ecosat.NotificacionesActivity.ID_INCIDENCIA;

public class NotificationHelper {

    private static final String GENERAL_CHANNEL_ID = "general_notifications";

    private Context mContext;
    private NotificationManager mNotificationManager;
    private NotificationCompat.Builder mBuilder;
    public static final String NOTIFICATION_CHANNEL_ID = "10001";

    public NotificationHelper(Context context) {
        mContext = context;
    }

    /**
     * Create and push the notification
     */
    public void createNotification(String title, String message)
    {
        /**Creates an explicit intent for an Activity in your app**/
        Intent resultIntent = new Intent(mContext , IncidenciasActivity.class);
        resultIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        resultIntent.putExtra("idIncidencia", ID_INCIDENCIA);

        ensureChannel();

        PendingIntent resultPendingIntent = PendingIntent.getActivity(mContext,
                0 /* Request code */, resultIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        mBuilder =
                new NotificationCompat.Builder(mContext, GENERAL_CHANNEL_ID);
        mBuilder.setSmallIcon(R.drawable.ic_launcher);
        mBuilder.setContentTitle(title)
                .setContentText(message)
                .setAutoCancel(false)
                .setCategory(NotificationCompat.CATEGORY_MESSAGE)
                .setSound(Settings.System.DEFAULT_NOTIFICATION_URI)
                .setContentIntent(resultPendingIntent)
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setDefaults(Notification.DEFAULT_SOUND | Notification.DEFAULT_VIBRATE | Notification.DEFAULT_LIGHTS)
                .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);

        mNotificationManager = (NotificationManager) mContext.getSystemService(Context.NOTIFICATION_SERVICE);
        assert mNotificationManager != null;
        mNotificationManager.notify((int) System.currentTimeMillis(), mBuilder.build());
    }

    private void ensureChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager nm =
                    (NotificationManager) mContext.getSystemService(Context.NOTIFICATION_SERVICE);
            if (nm.getNotificationChannel(GENERAL_CHANNEL_ID) == null) {
                NotificationChannel ch = new NotificationChannel(
                        GENERAL_CHANNEL_ID,
                        "Notificaciones generales",
                        NotificationManager.IMPORTANCE_HIGH);
                ch.enableVibration(true);
                ch.setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION), null);
                ch.setShowBadge(true);
                nm.createNotificationChannel(ch);
            }
        }
    }
}