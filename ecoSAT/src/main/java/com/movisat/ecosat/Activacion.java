package com.movisat.ecosat;

import static com.movisat.ecosat.SettingsActivity.KEY_MINZOOM_NOSHOWCLUSTER;
import static com.movisat.ecosat.SettingsActivity.KEY_PREF_ZOOM_CLUSTER;

import android.annotation.SuppressLint;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.preference.PreferenceManager;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Toast;

import com.environment.EnvironmentDebug;
import com.movisat.ecosat.MainActivity.License;
import com.movisat.utilities.Config;
import com.movisat.utilities.InfoDialog;
import com.movisat.utilities.MD5;
import com.movisat.utilities.OnInfoDialogSelect;
import com.movisat.utilities.Phone;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

@SuppressLint("WorldWriteableFiles")
public class Activacion extends BaseActivity {
    private Activacion instance = null;
    private InputStream auxIn = null;
    private OutputStream auxOut = null;
    private FileOutputStream outFile = null;
    private License license = null;
    private boolean automatic = false, reactivate = false, init = false;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        Bundle extras;

        super.onCreate(savedInstanceState);

        try {

            instance = this;

            // Llamo a esta funcion para que se rellenen los datos de la
            // clase "license" con los de la iltima activaciin
            license = MainActivity.getInstance().getLicense();

            // Primero intento validar la instalacion, si no, aparecera el
            // dialogo para que el usuario introduzca la clave del proyecto
            if ((extras = getIntent().getExtras()) != null) {

                reactivate = extras.getBoolean("reactivate");

                if (!reactivate) {

                    automatic = true;

                    new ActivacionIntranet().execute(license.proyectId);
                }

            }

            if (!automatic)
                showDialog(license.proyectId, license.description,
                        license.matricula);

        } catch (Throwable e) {

            MyLoggerHandler.getInstance().error(e);
        }

    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getRepeatCount() == 0) {
            finish();
        }
        return super.onKeyDown(keyCode, event);
    }

    private void showDialog(String key, String description, String matricula) {

        try {

            if (!init) {

                init = true;

                setContentView(R.layout.activacion);

                final LinearLayout contenedor = (LinearLayout) findViewById(R.id.contenedor_activacion);

                final EditText text1 = (EditText) contenedor.findViewById(R.id.numeroElemento);
                final EditText descripcionText = (EditText) contenedor.findViewById(R.id.etDescripcion);
                final Button btnOk = (Button) contenedor.findViewById(R.id.btnProcesa);

                if (EnvironmentDebug.getData().getProjectKey() != null)
                    key = EnvironmentDebug.getData().getProjectKey();

                if (key != null) {

                    text1.setText(key);
                    text1.setSelection(text1.length());
                    descripcionText.setText(description);
                    descripcionText.setSelection(descripcionText.length());
                }

                btnOk.setOnClickListener(new View.OnClickListener() {

                    public void onClick(View arg0) {

                        try {

                            if (descripcionText.getText().toString().trim().length() < 1) {

                                new InfoDialog(instance, instance
                                        .getString(R.string.atencion), instance
                                        .getString(R.string.ponga_desc),
                                        InfoDialog.ICON_STOP,
                                        new OnInfoDialogSelect() {

                                            @Override
                                            public void onSelectOption(
                                                    int option) {
                                            }

                                        }, InfoDialog.BUTTON_ACCEPT,
                                        InfoDialog.POSITION_CENTER).show();
                            } else {

                                new ActivacionIntranet()
                                        .execute(text1.getText().toString(),
                                                descripcionText.getText().toString(),
                                                Phone.getInstance().getIMEI());

                            }

                        } catch (Throwable e) {
                            MyLoggerHandler.getInstance().error(e);
                        }

                    }

                });

            }

        } catch (Throwable e) {

            MyLoggerHandler.getInstance().error(e);
        }

    }

    public boolean activateSoftware(String projectKey, String description, String matricula) {
        boolean result = false;
        HttpClient client = new DefaultHttpClient();

        try {
            String url = "http://" + Config.getInstance().getValue("urlIntra", "seguro.movisat.com") + "/dracos/activar.php";
            HttpPost post = new HttpPost(url);
            String imei = Phone.getInstance().getIMEI();

            // Configuración de headers
            post.setHeader("Content-Type", "application/x-www-form-urlencoded");
            post.setHeader("Accept", "application/octet-stream");
            post.setHeader("Cache-Control", "no-cache");
            post.setHeader("Pragma", "no-cache");
            post.setHeader("User-Agent", "Draco Android/" + Config.getInstance().getValue("version", "000000") + imei);
            post.setHeader("Host", Config.getInstance().getValue("urlIntra", "seguro.movisat.com"));

            // Cuerpo de la petición
            MD5 md5 = new MD5();
            // TipoGLS + GlsIMEI + matricula + descripcion + clave + tipoSoft
            md5.update(("14" + imei + matricula + description + projectKey).getBytes());
            md5.update("intra06dracos".getBytes());

            String body = "TipoGLS=14" + "&"
                  + "GlsIMEI=" + imei + "&"
                  + "matricula=" + matricula + "&"
                  + "descripcion=" + description + "&"
                  + "clave=" + projectKey + "&"
//                  + "tipoSoft=" + 0 + "&"
                  + "Firma=" + md5;
            post.setEntity(new StringEntity(body));

            // Envío de la petición
            HttpResponse response = client.execute(post);
            String responseText = EntityUtils.toString(response.getEntity());

            // Procesamiento de la respuesta
            if (responseText.contains("EST=OK")) {
                String licenseId = responseText.substring(responseText.indexOf("LIC=") + 4);
                createLicenseFile(projectKey, licenseId, description, matricula);
                result = MainActivity.getInstance().checkLicense();
                if (result) {
                    showMessage(getString(R.string.activacionCorrecta));
                }
            } else {
                if (responseText.contains("ERROR=")) {
                    int errorCode = Integer.parseInt(responseText.substring(responseText.indexOf("ERROR=") + 6, responseText.indexOf("</BODY")));
                    handleActivationError(errorCode, projectKey);
                } else {
                   showMessage(getString(R.string.errorDesconocido) + " Contacte con el soporte técnico.");
                }
            }
        } catch (IOException e) {
            MyLoggerHandler.getInstance().error(e);
            showMessage(getString(R.string.errorConexion) + e.getMessage());
        } finally {
            client.getConnectionManager().shutdown();
        }

        return result;
    }

    private void handleActivationError(int errorCode, String projectKey) {
        boolean deactivate = false;
        switch (errorCode) {
            case 10:
                showError(errorCode, getString(R.string.valorTipoGlsNoValido));
                break;
            case 11:
                showError(errorCode, getString(R.string.tipoGlsNoValido));
                break;
            case 15:
                showError(errorCode, getString(R.string.imeiNoValido));
                break;
            case 16:
                showError(errorCode, getString(R.string.claveNoValida));
                deactivate = true;
                break;
            case 105:
                showError(errorCode, getString(R.string.claveNoEncontrada));
                deactivate = true;
                break;
            case 200:
                showError(errorCode, getString(R.string.asignadoAOtroProyecto));
                deactivate = true;
                break;
            case 301:
                showError(errorCode, getString(R.string.sinLicenciaTipoProducto));
                deactivate = true;
                break;
            case 302:
                showError(errorCode, getString(R.string.sinLicenciaLibre));
                deactivate = true;
                break;
            case 402:
                showError(errorCode, getString(R.string.descripcionDuplicada));
                deactivate = true;
                break;
            default:
                showError(errorCode, getString(R.string.errorDesconocido));
        }
        if (deactivate) {
            try {
                deactivateSoftware(projectKey);
            } catch (Exception e) {
                MyLoggerHandler.getInstance().error(e);
            }
        }
    }

    private void showError(int errorCode, String message) {
        showMessage(getString(R.string.error) + errorCode + "; " + message);
    }

    private void deactivateSoftware(String projectKey) throws IOException {
        File file = new File(MainActivity.getInstance().getFilesDir().getPath() + "/licencia.txt");
        file.delete();
        outFile = MainActivity.getInstance().openFileOutput("licencia.txt", MODE_PRIVATE);
        outFile.write(("CLAVEPRO=" + projectKey + "\r\n").getBytes());
    }

    /**
     * Crea el archivo de licencia a partir de los parámetros especificados.
     *
     * @return true si el archivo se ha creado correctamente, false en otro caso.
     */
    public static boolean createLicenseFile(String proyectKey,
                                            String licenseId,
                                            String description,
                                            String matricula) {
        try {
            File file = new File(MainActivity.getInstance().getFilesDir().getPath()
                    + "/licencia.txt");

            file.delete();

            FileOutputStream fos = MainActivity.getInstance().openFileOutput(
                    "licencia.txt", MODE_PRIVATE);

            fos.write(("CLAVEPRO=" + proyectKey + "\r\n").getBytes());
            fos.write(("LICENCIA=" + licenseId + "\r\n").getBytes());
            fos.write(("DESCRIPCION=" + description + "\r\n").getBytes());
            fos.write(("MATRICULA=" + matricula + "\r\n").getBytes());

            MD5 md5 = new MD5();
            md5.update(("CLAVEPRO=" + proyectKey + "\r\n").getBytes());
            md5.update(("LICENCIA=" + licenseId + "\r\n").getBytes());
            md5.update(("DESCRIPCION=" + description + "\r\n").getBytes());
            md5.update(("MATRICULA=" + matricula + "\r\n").getBytes());
            md5.update("intra06dracos".getBytes());

            fos.write(("FIRMA=" + md5.toString() + "\r\n").getBytes());
            fos.close();

            Config.getInstance().setValue("licencia", licenseId);

            return true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return false;
    }


    private void showMessage(String text) {

        try {

            Message msg = new Message();
            Bundle b = new Bundle();

            b.putString("message", text);
            msg.setData(b);

            handler.sendMessage(msg);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    /**
     * Asynctask para realizar las llamas web
     */
    private class ActivacionIntranet extends AsyncTask<String, Void, Boolean> {

        @Override
        protected void onCancelled() {
            super.onCancelled();
        }

        @Override
        protected void onCancelled(Boolean result) {
            super.onCancelled(result);
        }

        @Override
        protected void onPostExecute(Boolean result) {
            super.onPostExecute(result);
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        @Override
        protected void onProgressUpdate(Void... values) {
            super.onProgressUpdate(values);
        }

        /**
         * Esta es la funciin que se ejecuta en segundo plano cuando se crea la
         * Asynctask para realizar las llamadas
         */
        @SuppressWarnings("deprecation")
        @Override
        protected Boolean doInBackground(String... params) {
            boolean res = false;

            try {

                String key = params[0];
                String des = params[1];
                String mat = params[2];

                if (activateSoftware(key, des, mat)) {

                    res = true;

                    setResult(RESULT_OK);

                    SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
                    SharedPreferences.Editor editor = sharedPreferences.edit();
                    editor.putString(KEY_PREF_ZOOM_CLUSTER, "16");
                    editor.putString(KEY_MINZOOM_NOSHOWCLUSTER, "12");
                    editor.apply();

                    finish();

                } else {

                    File file = new File(MainActivity.getInstance()
                            .getFilesDir().getPath()
                            + "/licencia.txt");

                    file.delete();

                    outFile = MainActivity.getInstance().openFileOutput(
                            "licencia.txt", MODE_PRIVATE);

                    outFile.write(("CLAVEPRO=" + key + "\r\n").getBytes());

                    outFile.close();

                    Config.getInstance().setValue("licencia", "");
                }

            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
            }

            return res;
        }

    }

    /**
     * Manejador de mensajes para interactuar con la interfaz de usuario
     */
    static final Handler handler = new Handler() {

        @Override
        public void handleMessage(final Message message) {

            try {

                Toast.makeText(MainActivity.getInstance(),
                        message.getData().getString("message"),
                        Toast.LENGTH_SHORT).show();

            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
            }

        }

    };

}