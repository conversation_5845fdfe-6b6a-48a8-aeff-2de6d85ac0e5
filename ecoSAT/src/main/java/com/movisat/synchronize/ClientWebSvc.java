/**
 * Clase que permite realizar llamadas a un servicio web
 *
 * <AUTHOR>
 */
package com.movisat.synchronize;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.preference.PreferenceManager;
import android.util.Log;

import com.environment.Environment;
import com.movisat.database.DBGrupoAreas;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBPlanchadas;
import com.movisat.database.Incidencia;
import com.movisat.ecosat.CallApiActivity;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.SettingsActivity;
import com.movisat.log.Logg;
import com.movisat.outbox.Packet;
import com.movisat.utilities.Config;
import com.movisat.utilities.Phone;
import com.movisat.utilities.Utils;
import com.movisat.utils.Parser;

import org.apache.http.Header;
import org.apache.http.HttpException;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.concurrent.TimeoutException;
import java.util.zip.GZIPInputStream;

public class ClientWebSvc {
    private static final String TAG = "ClientWebSvc";

    private String imei = "";
    private String firma = "";
    public String token = "";
    private String refreshToken = "";

    private String guid = "";
    private int numPaginas = 0;
    private JSONArray gruposZona;

    // Tiempo miximo de espera para cualquier peticion
    private final int INFINITE = -1;
    private int TIMEOUT = 900;
    private int TIMEOUT_TOKEN = 90;
    private int TIMEOUT_CALL_API = 3;

    // Firma para los MD5
    private final String SECRET_KEY = "Movisat.Sistemas.De.Localizacion.Global.18.09.2006";

    // Usuario y contraseia para las llamadas web-api
    private final String API_USER = "android2";
    private final String API_PASSW = "api2014Movisat";

    // Variable para saber cuando ha finalizado una llamada
    private volatile boolean finished = false;
    //private int errores = 1;

    @SuppressLint("DefaultLocale")
    public ClientWebSvc() {

        try {

            // Recupero el IMEI del mivil y creo la firma
            imei = Phone.getInstance().getIMEI();
            firma = Utils.getM5(imei + SECRET_KEY).toUpperCase();

            // Recupero el iltimo token guardado
            token = Config.getInstance().getValue("ultToken", "");
            refreshToken = Config.getInstance().getValue("ultRefreshToken", "");

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    /**
     * Realiza llamadas al servidor
     */
    public JSONObject sendPacket(String path, JSONObject data, Packet packet)
            throws TimeoutException, InterruptedException, HttpException {
        JSONObject res = null;

        String url = Config.getInstance().getValue("webSvc", "") + path;

        Logg.warning(TAG, "SEND " + url + " Enviando paquete... [" + Parser.toString(packet.getTipo()) + "] " + data.toString());

        // Realizo una primera llamada, si falla renuevo el token y
        // lo intento otra vez
        for (int i = 0; i < 3 && res == null; i++) {

            GetJSON getJSON = (GetJSON) new GetJSON().execute(url, "sendJSON", data.toString());

            try {

                JSONArray auxRes = getJSON.getResult(TIMEOUT);

                if (auxRes != null)
                    res = auxRes.getJSONObject(0);

//                Logg.info(TAG, "[RESPUESTA] [" + url + "] [" + Parser.toString(packet.getTipo()) + "] " + (res == null ? "Null" : res.toString()));
            } catch (Throwable e) {
//                Logg.info(TAG, "[ERROR] [" + url + "] [" + Parser.toString(packet.getTipo()) + "] " + e.getMessage());

                // Si se trata del primer fallo renuevo el token
                if (e instanceof HttpException) {

                    // Si no hay token ni refresh_token pido uno nuevo
                    if (token.equals("") && refreshToken.equals(""))
                        getToken();
                    else
                        refreshToken();
                } else
                    MyLoggerHandler.getInstance().error(e);

            }

        }

        return res;

    }

    public JSONObject sendPacket(String path, JSONArray data)
            throws TimeoutException, InterruptedException, HttpException {
        JSONObject res = null;

        String url = Config.getInstance().getValue("webSvc", "") + path;

        Logg.warning(TAG, "SEND " + url + " Enviando posiciones... " + data.toString());

        // Realizo una primera llamada, si falla renuevo el token y
        // lo intento otra vez
        for (int i = 0; i < 3 && res == null; i++) {

            GetJSON getJSON = (GetJSON) new GetJSON().execute(url, "sendJSON", data.toString());

            try {
                JSONArray auxRes = getJSON.getResult(TIMEOUT);

                if (auxRes != null)
                    res = auxRes.getJSONObject(0);

            } catch (Throwable e) {
                e.printStackTrace();

                // Si se trata del primer fallo renuevo el token
                if (e instanceof HttpException) {

                    // Si no hay token ni refresh_token pido uno nuevo
                    if (token.equals("") && refreshToken.equals(""))
                        getToken();
                    else
                        refreshToken();
                } else
                    MyLoggerHandler.getInstance().error(e);

            }

        }

        return res;
    }

    /**
     * Realiza llamadas al servidor
     */
    public JSONObject callAPI(String pathURL, JSONObject data, final Context context)
            throws TimeoutException, InterruptedException, HttpException {
        JSONObject res = null;

        finished = false;

        // Muestro el mensaje de aviso de conexión al servidor

        new Thread() {
            public void run() {
                try {

                    // Si tarda menos de 1.5 segundos la llamada no saco el aviso
                    Thread.sleep(2500);
                    if (!finished) {
                        Intent callIntent = new Intent(context, CallApiActivity.class);
                        callIntent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                        context.startActivity(callIntent);
                    }

                } catch (Throwable e) {
                }
            }
        }.start();


        String url = Config.getInstance().getValue("webSvc", "");
        if (url.equals(""))
            return null;

        url = url + pathURL;

        // Realizo una primera llamada, si falla renuevo el token y lo intento otra vez
        GetJSON getJSON = null;
        for (int i = 0; i < 3 && res == null; i++) {

            if (data != null)
                getJSON = (GetJSON) new GetJSON().execute(url, "callAPI", data.toString());
            else
                getJSON = (GetJSON) new GetJSON().execute(url, "callAPI");

            try {

                JSONArray auxRes = getJSON.getResult(TIMEOUT_CALL_API);

                if (auxRes != null) {
                    res = auxRes.getJSONObject(0);
                    break;
                }


            } catch (Throwable e) {

                i--;

                // Si se trata del primer fallo renuevo el token
                if (e instanceof HttpException) {
                    getToken();
                } else {
                    MyLoggerHandler.getInstance().error(e);
                }
            }
        }

        // Oculto el mensaje de aviso de conexión al servidor
        finished = true;
        CallApiActivity.close();

        return res;
    }


    /**
     * Realiza llamadas al servidor
     */
    public JSONObject callAPI(String pathURL, JSONObject data, boolean showAlert)
            throws TimeoutException, InterruptedException, HttpException {
        JSONObject res = null;

        try {

            finished = false;

            // Muestro el mensaje de aviso de conexión al servidor
            if (showAlert) {
                new Thread() {
                    public void run() {
                        try {
                            // Si tarda menos de 3 segundos la llamada no saco el aviso
                            Thread.sleep(2000);
                            if (!finished) {
                                /*Intent callIntent = new Intent(MainActivity.getInstance(), CallApiActivity.class);
                                callIntent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                                MainActivity.getInstance().startActivity(callIntent);*/
                            }
                        } catch (Throwable e) {
                        }
                    }
                }.start();
            }

            String url = Config.getInstance().getValue("webSvc", "");
            if (url.equals(""))
                return null;

            url = url + pathURL;

            // Realizo una primera llamada, si falla renuevo el token y lo intento otra vez
            GetJSON getJSON = null;
            for (int i = 0; i < 3 && res == null; i++) {

                if (data != null)
                    getJSON = (GetJSON) new GetJSON().execute(url, "callAPI", data.toString());
                else
                    getJSON = (GetJSON) new GetJSON().execute(url, "callAPI");

                try {


                    JSONArray auxRes = getJSON.getResult(TIMEOUT_CALL_API);

                    if (auxRes != null) {
                        res = auxRes.getJSONObject(0);
                        break;
                    }


                } catch (Throwable e) {

                    //i--;

                    /*

                        if (e instanceof TimeoutException){

                        sinConexion++;
                        if (sinConexion>5){
                            break;
                        }

                    }*/

                    // Si se trata del primer fallo renuevo el token
                    if (e instanceof HttpException) {
                        e.getMessage();
                        e.printStackTrace();
                        getToken();
                    } else {
                        MyLoggerHandler.getInstance().error(e);
                    }
                }
            }

        } catch (Throwable e) {

            e.printStackTrace();
        }

        // Oculto el mensaje de aviso de conexión al servidor
        if (showAlert) {
            finished = true;
            CallApiActivity.close();
        }

        return res;
    }

    /**
     * Pide al servidor los datos de una tabla
     */
    public JSONArray getTable(int table) throws TimeoutException,
            InterruptedException, HttpException {
        JSONArray res = null;
        gruposZona = null;
        JSONArray incidenciasPropietario = null;
        JSONObject jsonRequest = null;

        String url;
        String fecha = Config.getInstance().getValueEmpresa("ultSincro",
                "0000-00-00 00:00:00");

        switch (table) {
            case DBSynchro.GET_DATE_DB:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/bd/fecha";
                break;

            case DBSynchro.GET_FILTRO_PAPELERAS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/parquesyjardines/modelos/papeleras";
                break;

            case DBSynchro.GET_EMPRESAS:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/empresas";

                fecha = Config.getInstance().getValue("lastDateEmpresa", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_AREAS:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/zonas/";

                fecha = Config.getInstance().getValueUsuario("ultSincroAreas",
                        "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_MOTIVOS:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/elementos/motivos/baja";

                fecha = Config.getInstance().getValueUsuario("ultSincroMotivos",
                        "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_GRUPO_AREAS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/zonas/grupos";
                DBGrupoAreas dbGrupoAreas = new DBGrupoAreas();
                Collection<Integer> obj = dbGrupoAreas.getCollectionCodigosGrupos(MainActivity.getInstance().getEmpresa());

                dbGrupoAreas.close();
                if (obj != null)
                    gruposZona = new JSONArray(obj);
                fecha = Config.getInstance().getValueUsuario("ultSincroGrupoAreas",
                        "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_FLOTA:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/fechamod/";

                fecha = Config.getInstance().getValueUsuario("ultSincroFlota",
                        "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_VEHICULOS:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/vehiculos/fechamod/";

                fecha = Config.getInstance().getValueUsuario("ultSincroVehiculos",
                        "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_MUNICIPIOS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/municipios";
                break;

            case DBSynchro.GET_NOMBRE_DISPOSITIVO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/movil/" + imei;
                break;

            case DBSynchro.GET_PROVINCIAS:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/provincias";
                break;

            case DBSynchro.GET_USUARIOS:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/usuarios";

                fecha = Config.getInstance().getValue("ultSincroUsuarios",
                        "0000-00-00 00:00:00");
                //fecha = "0000-00-00 00:00:00";

                break;

            case DBSynchro.GET_TRABAJADORES_TIPO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/trabajadores/categorias";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroTrabajadoresTipo", "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_TRABAJADORES:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/trabajadores";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroTrabajadores", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_ELEMENTOS_TIPO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/elementos/tipos";

                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroElementosTipo", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_ELEMENTOS_MODELO_NIVEL_LLENADO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/elementos/modelos/niveles/llenado";
                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroElementosModeloNivelLlenado",
                        "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_ELEMENTOS_MODELO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/elementos/modelos";
                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroElementosModelos", "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_ELEMENTOS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/elementos";
                fecha = Config.getInstance().getValueEmpresa("ultSincroElementos",
                        "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_INCIDENCIAS_TIPO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias/tipos";
                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroIncidenciasTipo", "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_INCIDENCIAS_MODELO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias/modelos";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroIncidenciasModelo", "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_ULTIMAS_POSICIONES:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/posiciones/ultimas/";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroPosiciones", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_INCIDENCIAS_MOTIVO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias/motivos";
                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroIncidenciasMotivo", "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_ESTADOS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias/estados";

                break;

            case DBSynchro.GET_INCIDENCIAS_ESTADO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias/historico/estados";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroIncidenciasEstado", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_INCIDENCIAS_PROPIETARIOS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias/propietarios/modificados";


                DBIncidencia dbIncidencias = new DBIncidencia();
                ArrayList<Incidencia> incidencias_prop = dbIncidencias.getAllIncidencias(MainActivity.getInstance().getEmpresa(),
                        MainActivity.getInstance().getUsuario(),
                        false);
                dbIncidencias.close();

                if (!incidencias_prop.isEmpty()) {
                    try {
                        JSONArray arrInci = new JSONArray();
                        for (Incidencia inc : incidencias_prop) {
                            if (inc.getIdExterno() > 0)
                                arrInci.put(inc.getIdExterno());
                        }
                        jsonRequest = new JSONObject();
                        jsonRequest.put("Imei", imei);
                        jsonRequest.put("Usuario", MainActivity.getInstance().getUsuario());
                        jsonRequest.put("IdsIncidencias", arrInci);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }


                break;

            case DBSynchro.GET_INCIDENCIAS_HISTORICO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroIncidenciasHistorico", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_MOVILES_USUARIO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/api/usuarios/permisos/moviles";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroMovilesUsuario", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_PLANCHADAS:
                DBPlanchadas dbPlanchadas = new DBPlanchadas();
                url = Config.getInstance().getValue("webSvc", "")
                        + "/api/pesaje/v1/planchadas/" + MainActivity.getInstance().getEmpresa() + "?IdsPlanchadas_CSV="
                        + dbPlanchadas.getCsvIds(MainActivity.getInstance().getEmpresa());
                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroPlanchadas", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_TAGS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/tags";

                fecha = Config.getInstance().getValue(
                        "ultSincroTags", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_INCI_ENTRADAS:

                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/incidencias/entradas/digitales/" + imei;

                fecha = Config.getInstance().getValue(
                        "ultSincroInciEnt", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_PORCENTAJES_LLENADO:

                url = Config.getInstance().getValue("webSvc", "")
                        + "/api/sensores/get/lecturas/llenado";

                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroPorcent", "0000-00-00 00:00:00");

                break;

            case DBSynchro.GET_PROCESADOS:

                url = Config.getInstance().getValue("webSvc", "")
                        + "/moviles/elementos/ultimas/operaciones";

                fecha = Config.getInstance().getValueUsuario(
                        "ultSincroProcesados", "0000-00-00 00:00:00");

                break;


            case DBSynchro.GET_SENSORES_LLENADO:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/api/sensores/niveles/fraccion/" + MainActivity.getInstance().getEmpresa();

                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroSensoresLlenado", "0000-00-00 00:00:00");

                try {
//                    int codigoMovil = parseInt(Config.getInstance().getValue("codigoDispositivo", "0"));

                    // Se obtiene el número de horas máximo para almacenar los sensores y se resta
                    // al tiempo actual para obtener la hora mínima de sincronización
                    //int hours = Integer.parseInt(EcoSATApplication.sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE, "8"));

                    SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
                    int hours = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_LEVEL, "18").trim());
                    Date minOperationDoneTime = new Date(((new Date()).getTime()) - (hours * 3600 * 1000));

                    // Si la hora almacenada es inferior al límite inferior, se utiliza éste último
                    if (Utils.StringToDateTime(fecha).before(minOperationDoneTime)) {
                        fecha = Utils.secondsToDatetimeString(
                                minOperationDoneTime.getTime() / 1000,
                                "yyyy-MM-dd HH:mm:ss");
                    }

                    jsonRequest = new JSONObject();
                    jsonRequest.put("desde", fecha);
                    // Le sumo una hora por si hay desfase de hora con el servidor
                    jsonRequest.put("hasta", Utils.datetimeToString(new Date(System.currentTimeMillis() + 3600000), "yyyy-MM-dd HH:mm:ss"));
                } catch (Throwable e) {
                    e.printStackTrace();
                    return null;
                }

                break;

            //LAVADOSMOD
            case DBSynchro.GET_LAVADOS:
                url = Config.getInstance().getValue("webSvc", "")
                        + "/api/sensores/lavados/" + MainActivity.getInstance().getEmpresa();

                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroLavados", "0000-00-00 00:00:00");

                try {
//                    int codigoMovil = parseInt(Config.getInstance().getValue("codigoDispositivo", "0"));

                    // Se obtiene el número de horas máximo para almacenar los sensores y se resta
                    // al tiempo actual para obtener la hora mínima de sincronización
                    //int hours = Integer.parseInt(EcoSATApplication.sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE, "8"));

                    SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
                    int hours = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_CLEAN, "18").trim());
                    long hours_aux = (long) hours * 3600 * 1000;
                    Date minOperationDoneTime = new Date(((new Date()).getTime()) - hours_aux);

                    // Si la hora almacenada es inferior al límite inferior, se utiliza éste último
                    if (Utils.StringToDateTime(fecha).before(minOperationDoneTime)) {
                        fecha = Utils.secondsToDatetimeString(
                                minOperationDoneTime.getTime() / 1000,
                                "yyyy-MM-dd HH:mm:ss");
                    }

                    jsonRequest = new JSONObject();
                    jsonRequest.put("desde", fecha);
                    // Le sumo una hora por si hay desfase de hora con el servidor
                    jsonRequest.put("hasta", Utils.datetimeToString(new Date(System.currentTimeMillis() + 3600000), "yyyy-MM-dd HH:mm:ss"));

                } catch (Throwable e) {
                    e.printStackTrace();
                    return null;
                }
                break;

            case DBSynchro.GET_TIPO_ZONAS:
                url = Config.getInstance().getValue("webSvc", "") + "/moviles/zonas/elementos/";
                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroTipoZonas", "0000-00-00 00:00:00");
                break;

            case DBSynchro.GET_IDENTIFICACIONES:
                url = Config.getInstance().getValue("webSvc", "")
                      + "/moviles/identificaciones/activas/" + imei;
                fecha = Config.getInstance().getValueEmpresa(
                        "ultSincroIdentificaciones", "0000-00-00 00:00:00");
                break;

            default:
                return null;
        }

        // Realizo una primera llamada, si falla renuevo el token y lo intento otra vez
        for (int i = 0; i < 3 && res == null; i++) {
            try {
                GetJSON getJSON = executeGetJSON(table, url.trim(), jsonRequest, fecha);

                if (getJSON != null)
                    res = getJSON.getResult(TIMEOUT);
            } catch (Throwable e) {
                // Si se trata del primer fallo renuevo el token
                if (e instanceof HttpException) {
                    // Si no hay token ni refresh_token pido uno nuevo
                    if (token.equals("") && refreshToken.equals(""))
                        getToken();
                    else
                        refreshToken();
                } else {
                    MyLoggerHandler.getInstance().error(e);
                }
            }
        }

        return res;

    }

    private GetJSON executeGetJSON(int table, String url, JSONObject jsonRequest, String fecha) {
        switch (table) {
            case DBSynchro.GET_IDENTIFICACIONES:
            case DBSynchro.GET_INCI_ENTRADAS:
                return (GetJSON) new GetJSON().execute(url.trim(), "getJSON_inci");
            case DBSynchro.GET_DATE_DB:
                return (GetJSON) new GetJSON().execute(url.trim(), "getDate");
            case DBSynchro.GET_SENSORES_LLENADO:
            case DBSynchro.GET_LAVADOS:
            case DBSynchro.GET_INCIDENCIAS_PROPIETARIOS:
                if (jsonRequest != null) {
                    return (GetJSON) new GetJSON().execute(url.trim(), "getJSON_alt", jsonRequest.toString());
                }
                break;
            default:
                String empresa = "" + MainActivity.getInstance().getEmpresa();
                String usuario = "" + MainActivity.getInstance().getUsuario();
                return (GetJSON) new GetJSON(gruposZona).execute(url.trim(), "getJSON", imei, fecha, empresa, usuario);
        }
        return null;
    }

    /**
     * Recupera un nuevo token
     */
    public void getToken() throws TimeoutException, InterruptedException,
            HttpException {

        if (token.equals(""))
            userLogout();

        String url = Config.getInstance().getValue("webSvc", "") + "/token";
        try {
            GetJSON getJSON = (GetJSON) new GetJSON().execute(url, "getToken");
            getJSON.getResult(TIMEOUT_TOKEN);
        } catch (Exception ex) {
            Log.e("ClientWebSvc", ex.getMessage() != null ? ex.getMessage() : ex.toString());
        }

    }

    /**
     * Refresca el token
     */
    public void refreshToken() throws TimeoutException, InterruptedException,
            HttpException {

        if (token.equals(""))
            userLogout();

        try {
            String url = Config.getInstance().getValue("webSvc", "") + "/token";

            GetJSON getJSON = (GetJSON) new GetJSON(null).execute(url, "refreshToken");
            getJSON.getResult(TIMEOUT_TOKEN);
        } catch (Exception ex) {
            getToken();
        }
    }

    /**
     * Cierra sesion de usuario
     */
    public void userLogout() throws TimeoutException, InterruptedException,
            HttpException {

        try {
            String url = Config.getInstance().getValue("webSvc", "") + "/api/account/logout";

            GetJSON getJSON = (GetJSON) new GetJSON().execute(url, "logout");
            getJSON.getResult(TIMEOUT);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    /**
     * Recupero el contenido de la respuesta
     */
    public String convertStreamToString(InputStream inputStream) {

        int i = 0;
        StringBuilder res = null;
        final int TAM_BUF = 1024 * 16;
        char c[] = new char[TAM_BUF];
        String line;

        try {

            BufferedReader bReader = new BufferedReader(new InputStreamReader(
                    inputStream));
            res = new StringBuilder();

            if (bReader != null) {
                while ((i = bReader.read(c, 0, TAM_BUF)) > -1)
                    res.append(c, 0, i);
            }

            bReader.close();


        } catch (IOException ex) {
            MyLoggerHandler.getInstance().error(ex);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res != null ? res.toString() : "";

    }

    public String getGUID() {
        JSONArray jsonData;

        String url = Config.getInstance().getValue("webSvc", "") +
              "/moviles/elementos/instantanea";
        String fecha = Config.getInstance().getValueEmpresa("ultSincroElementos",
              "0000-00-00 00:00:00");

        try {

                /*GetJSON getJSON = new GetJSON();
                getJSON.execute(url, "getGUID", imei);
                jsonData = getJSON.getResult(TIMEOUT);*/

            GetJSON getJSON = (GetJSON) new GetJSON().execute(url.trim(), "getGUID", imei, fecha,
                  "" + MainActivity.getInstance().getEmpresa(),
                  "" + MainActivity.getInstance().getUsuario(),
                  "100");

            jsonData = getJSON.getResult(TIMEOUT);

            guid = jsonData.getJSONObject(0).getString("Guid");
            numPaginas = jsonData.getJSONObject(0).getInt("NumeroPaginas");

            Config.getInstance().setValue("guid_elementos", guid);
            Config.getInstance().setValue("num_paginas", Integer.toString(numPaginas));

            // TODO: Eliminar esta lógica cuando el nuevo endpoint esté en todos los servidores
            checkNewElementEndpoint(fecha);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return guid;
    }

    // TODO: REDUCIDOS: Eliminar esta lógica cuando el nuevo endpoint esté en todos los servidores
    private void checkNewElementEndpoint(String fecha) {
        if (Environment.hasNewElementEndpoint) return;
        boolean check;

        try {
            if (fecha.equals("0000-00-00 00:00:00")) check = true;
            else {
                // O si ha pasado más de 8h desde la última sincro
                // Con la intención de que se compruebe una vez al día,
                // para que no esté comprobando constantemente
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date lastSync = sdf.parse(fecha);
                Date now = new Date();
                long diff = now.getTime() - lastSync.getTime();
                long diffDays = diff / (8 * 60 * 60 * 1000);
                check = diffDays > 1;
            }

            if (!check) return;

            String url = Config.getInstance().getValue("webSvc", "") + "/moviles/sincronizar/elementos/reducidos";

            JSONObject jsonRequest = new JSONObject();
            jsonRequest.put("guid", guid);
            jsonRequest.put("pagina", "1");

            HttpPost post = new HttpPost(url);
            post.setHeader("Authorization", "Bearer " + token);
            post.setHeader("Content-Type", "application/json");
            post.setEntity(new StringEntity(jsonRequest.toString()));

            HttpParams parametros = new BasicHttpParams();
            int timeoutConnection = 5000; // 5 segundos
            HttpConnectionParams.setConnectionTimeout(parametros, timeoutConnection);
            int timeoutSocket = 60000; // 60 segundos
            HttpConnectionParams.setSoTimeout(parametros, timeoutSocket);
            DefaultHttpClient httpClient = new DefaultHttpClient(parametros);

            HttpResponse response = httpClient.execute(post);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                Environment.hasNewElementEndpoint = true;
                // Lo guardamos para startEnvironment()
                Config.getInstance().setValueEmpresa("elementosReducidos", "true");
            }
        } catch (Exception e) {
            Logg.error(TAG, e.getMessage());
        }
    }

    public int getNumPaginas() {
        return numPaginas;
    }

    public JSONArray getPagina(int i, String guid, String baseUrl) throws InterruptedException, TimeoutException, HttpException, JSONException {
        JSONArray jsonData = null;
        // TODO: REDUCIDOS: Eliminar esta lógica cuando el nuevo endpoint esté en todos los servidores
        if (!Environment.hasNewElementEndpoint) {

            String url = baseUrl + "/moviles/sincronizar/elementos/" + guid + "/" + i;

//        MyLoggerHandler.getInstance().info(url); // Bloquea la interfaz
            Logg.warning(TAG, "PETICIÓN " + url);

            GetJSON getJSON = new GetJSON();
            getJSON.execute(url.trim(), "getPagina", imei);

            try {
                jsonData = getJSON.getResult(TIMEOUT);
            } catch (Throwable e) {

                if (e instanceof HttpException) {

                    // Si no hay token ni refresh_token pido uno nuevo
                    if (token.equals("") && refreshToken.equals(""))
                        getToken();
                    else
                        refreshToken();
                } else {
                    MyLoggerHandler.getInstance().error(e);
                }
            }
            return jsonData;
        }
        // HASTA AQUÍ

        String url = baseUrl + "/moviles/sincronizar/elementos/reducidos";

        JSONObject jsonRequest = new JSONObject();
        jsonRequest.put("guid", guid);
        jsonRequest.put("pagina", i);

        GetJSON getJSON = new GetJSON();
        getJSON.execute(url, "getJSON_alt", jsonRequest.toString());

        try {
            jsonData = getJSON.getResult(TIMEOUT);
        } catch (Throwable e) {

            if (e instanceof HttpException) {

                // Si no hay token ni refresh_token pido uno nuevo
                if (token.equals("") && refreshToken.equals(""))
                    getToken();
                else
                    refreshToken();
            } else {
                MyLoggerHandler.getInstance().error(e);
            }
        }
        return jsonData;
    }

    /**
     * Asynctask para realizar las llamas al servicio web
     */
    private class GetJSON extends AsyncTask<String, Void, Boolean> {
        private static final int REQUEST_COMPLETED = 1;
        private static final int REQUEST_ERROR = 0;
        private static final int REQUEST_PENDING = -1;

        private volatile int completed = REQUEST_PENDING;
        private JSONArray jsonData = null;
        private JSONArray jsonDataGruposZonas = null;

        public GetJSON(JSONArray jsonDataGruposZonas) {
            this.jsonDataGruposZonas = jsonDataGruposZonas;
        }

        public GetJSON() {
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        @Override
        protected void onProgressUpdate(Void... values) {
            super.onProgressUpdate(values);
        }

        @Override
        protected void onPostExecute(Boolean result) {
            super.onPostExecute(result);
            completed = result ? REQUEST_COMPLETED : REQUEST_ERROR;
        }

        // En versiones de Android API 11 en adelante
        @Override
        protected void onCancelled(Boolean result) {
            super.onCancelled(result);
            completed = REQUEST_ERROR;
        }

        // Espero una respuesta o que pase el tiempo máximo establecido
        public JSONArray getResult(int timeout) throws TimeoutException,
                InterruptedException, HttpException {
            completed = REQUEST_PENDING;
            timeout *= 10;
//            long time = System.currentTimeMillis();
            for (int i = 0; completed == REQUEST_PENDING && (i < timeout || timeout == INFINITE); i++) {
                Thread.sleep(10);
            }
//            Logg.error(TAG, "Tiempo de espera: " + (System.currentTimeMillis() - time) + " ms");
            if (completed == REQUEST_PENDING && timeout != INFINITE){
                Logg.error(TAG, "Throwing TimeoutException");
                throw new TimeoutException("TimeoutException");
            }
            else if (completed == REQUEST_ERROR){
                Logg.error(TAG, "Throwing HttpException");
                throw new HttpException("HttpException");
            }

            return jsonData;
        }

        /**
         * Esta es la función que se ejecuta en segundo plano cuando se crea la
         * Asynctask para realizar las llamadas
         */
        @Override
        protected Boolean doInBackground(String... params) {
            boolean res = false;

            try {
                JSONObject jsonObject = new JSONObject();
                //String url = URLEncoder.encode();

                HttpPost post = new HttpPost(params[0]);
                HttpGet get = null;

                if (params[1].equals("getToken")) {
                    // Preparo la llamada para recibir un nuevo token
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type", "application/x-www-form-urlencoded");
                    post.setEntity(new StringEntity("grant_type=password&username=" + API_USER
                          + "&password=" + API_PASSW + "&client_id="
                          + imei + "&client_secret=" + firma));

                } else if (params[1].equals("refreshToken")) {
                    // Preparo la llamada para refrescar el token
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type",
                          "application/x-www-form-urlencoded");
                    post.setEntity(new StringEntity("grant_type=refresh_token&client_id=" + imei
                          + "&client_secret=" + firma
                          + "&refresh_token=" + refreshToken));

                } else if (params[1].equals("callAPI")) {
                    // Preparo la llamada para recibir datos
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type", "application/json");
                    post.setHeader("Accept-encoding", "gzip");
                    post.setHeader("Authorization", "Bearer " + token);

                    jsonObject.put("imei", params[2]);

                    if (jsonObject != null) {
                        post.setEntity(new StringEntity(jsonObject.toString()));
                    }

                } else if (params[1].equals("getGUID")) {
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type", "application/json");
                    post.setHeader("Accept-encoding", "gzip");
                    post.setHeader("Authorization", "Bearer " + token);

                    jsonObject.put("imei", params[2]);
                    jsonObject.put("ultFechaMod", params[3]);
                    jsonObject.put("idEmpresa", params[4]);
                    jsonObject.put("idUsuario", params[5]);
                    jsonObject.put("paginado", params[6]);

                    post.setEntity(new StringEntity(jsonObject.toString()));

                } else if (params[1].equals("getPagina") || params[1].equals("getJSON_inci") || params[1].equals("getDate")) {
                    get = new HttpGet(params[0]);

                    get.setHeader("User-Agent", "java-Android");
                    get.setHeader("Content-type", "application/json");
                    get.setHeader("Content-encoding", "gzip");
                    get.setHeader("Accept-encoding", "gzip");
                    get.setHeader("Authorization", "Bearer " + token);

                } else if (params[1].equals("getJSON")) {
                    // Preparo la llamada para recibir datos
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type", "application/json");
                    post.setHeader("Accept-encoding", "gzip");
                    post.setHeader("Authorization", "Bearer " + token);

                    jsonObject.put("imei", params[2]);
                    jsonObject.put("ultFechaMod", params[3]);
                    jsonObject.put("idEmpresa", params[4]);
                    jsonObject.put("idUsuario", params[5]);

                    if (jsonDataGruposZonas != null)
                        jsonObject.put("CodigosGrupos", jsonDataGruposZonas);

                    post.setEntity(new StringEntity(jsonObject.toString()));

                } else if (params[1].equals("getJSON_alt")) {
                    // Preparo la llamada para recibir datos
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type", "application/json");
                    post.setHeader("Accept-encoding", "gzip");
                    post.setHeader("Authorization", "Bearer " + token);

                    // El contenido de la petición va en formato JSON en los parámetros
                    jsonObject = new JSONObject(params[2]);

                    post.setEntity(new StringEntity(jsonObject.toString()));

                } else if (params[1].equals("logout")) {
                    // Preparo la llamada para recibir datos
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type", "application/json");
                    post.setHeader("Accept-encoding", "gzip");
                    post.setHeader("Authorization", "Bearer " + token);

                    post.setEntity(new StringEntity(jsonObject.toString()));

                } else if (params[1].equals("sendJSON")) {
                    // Preparo la llamada para recibir datos
                    post.setHeader("User-Agent", "java-Android");
                    post.setHeader("Content-type", "application/json");
                    post.setHeader("Accept-encoding", "gzip");
                    post.setHeader("Authorization", "Bearer " + token);

                    StringEntity entidad = new StringEntity(params[2], HTTP.UTF_8);
                    post.setEntity(entidad);

                }

                // Establezco timeout alto
                HttpParams parametros = new BasicHttpParams();

                int timeoutConnection = 5000; // 5 segundos
                HttpConnectionParams.setConnectionTimeout(parametros, timeoutConnection);

                int timeoutSocket = 60000; // 60 segundos
                HttpConnectionParams.setSoTimeout(parametros, timeoutSocket);

                DefaultHttpClient httpClient = new DefaultHttpClient(parametros);

                long init = System.currentTimeMillis();
                HttpResponse response;
                if (params[1].equals("getPagina") || params[1].equals("getJSON_inci") || params[1].equals("getDate")) {
                    Logg.warning(TAG, "GET  " + params[0]);
                    response = httpClient.execute(get);
                } else {
                    Logg.warning(TAG, "POST " + params[0] + " " + EntityUtils.toString(post.getEntity()));
                    response = httpClient.execute(post);
                }
                long end = System.currentTimeMillis();

                if (params[1].equals("logout")) {
                    // Borro el último token almacenado
                    Config.getInstance().setValue("ultToken", "");
                    Config.getInstance().setValue("ultRefreshToken", "");
                    completed = REQUEST_COMPLETED;
                    return true;
                }

                int result = response.getStatusLine().getStatusCode();
                String responseString = "";

                switch (result) {
                    case 200: // Respuesta correcta
                        if (params[1].equals("getToken") || params[1].equals("refreshToken")) {
                            // Recupero el token devuelto
                            JSONObject jsonToken = new JSONObject(EntityUtils.toString(response.getEntity(), HTTP.UTF_8));

                            // Renuevo el token y el refreshToken
                            token = jsonToken.getString("access_token");
                            refreshToken = jsonToken.getString("refresh_token");

                            // Almaceno el iltimo token recibido
                            Config.getInstance().setValue("ultToken", token);
                            Config.getInstance().setValue("ultRefreshToken", refreshToken);
                        } else if (params[1].equals("getDate")) {
                            String strFecha = EntityUtils.toString(response.getEntity());
                            strFecha = strFecha.replace("\"", "");
                            jsonData = new JSONArray().put(strFecha);
                            responseString = strFecha;
                        } else {
                            InputStream inStream = response.getEntity().getContent();

                            Header contentEncoding = response.getFirstHeader("Content-Encoding");
                            if (contentEncoding != null && contentEncoding.getValue().equalsIgnoreCase("gzip")) {
                                inStream = new GZIPInputStream(inStream);
                            }

                            String strResponse = convertStreamToString(inStream);
                            inStream.close();

                            responseString = strResponse;
                            try {
                                if (strResponse.startsWith("["))
                                    jsonData = new JSONArray(strResponse);
                                else
                                    jsonData = new JSONArray().put(new JSONObject(strResponse));
                            } catch (JSONException e) {
                                Logg.error(TAG, "Error al convertir el JSON: "
                                      + e.getMessage() + "\n[request] " + params[1]
                                      + "\n[strResponse] " + strResponse
                                      + "\n[jsonObject] " + jsonObject
                                );
                            }
                        }

                        res = true;

                        break;

                    case 304:
                        jsonData = new JSONArray().put(new JSONObject()
                              .put("ErrNum", "304")
                              .put("ErrDes", "El elemento forma parte de una ruta"));
                        res = true;
                        responseString = EntityUtils.toString(response.getEntity());
                        break;

                    case 400:
                        if (params[1].equals("getToken") || params[1].equals("refreshToken")) {

                            // El refresh_token ya no es vilido
                            refreshToken = "";
                            token = "";
                            //Quitar el refresh token de la base de datos.
                            // Borro el iltimo token almacenado
                            Config.getInstance().setValue("ultToken", "");
                            Config.getInstance().setValue("ultRefreshToken", "");

                        } else {

                            // No hay datos (para el resto de llamadas)
                            jsonData = new JSONArray().put(new JSONObject()
                                  .put("ErrNum", "400")
                                  .put("ErrDes", "Petición incorrecta"));

                            res = true;
                        }
                        responseString = EntityUtils.toString(response.getEntity());
                        break;

                    case 401: // El token ya no es vilido
                        token = "";
                        responseString = EntityUtils.toString(response.getEntity());
                        break;

                    case 404: // No hay datos
                        jsonData = new JSONArray().put(new JSONObject()
                              .put("ErrNum", "404")
                              .put("ErrDes", "No hay datos"));
                        res = true;
                        responseString = EntityUtils.toString(response.getEntity());
                        break;
                    case 402: // El mivil no es vilido
                        jsonData = new JSONArray().put(new JSONObject()
                              .put("ErrNum", "402")
                              .put("ErrDes", "Móvil no es válido, el IMEI no ha sido dado de alta todavía."));

                        res = true;
                        responseString = EntityUtils.toString(response.getEntity());
                        break;

                    default:
                        responseString = EntityUtils.toString(response.getEntity());
                        Logg.error(TAG, "StatusCode no esperado: " + result + " " + responseString);
                }
                Logg.warning(TAG, result + "  " + params[0] + " (" + (end - init) + "ms) "
                      + responseString.substring(0, Integer.min(480, responseString.length())));
            } catch (Throwable e) {
                Logg.error(TAG, params[0] + "\n" + e);
            }

            // Para salir del bucle de espera de getResult
            completed = res ? REQUEST_COMPLETED : REQUEST_ERROR;

            return res;
        }
    }

}
