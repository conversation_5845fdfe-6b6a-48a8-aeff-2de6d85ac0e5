package com.movisat.synchronize;

import android.content.Intent;
import android.content.SharedPreferences;
import android.database.sqlite.SQLiteStatement;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.preference.PreferenceManager;
import android.util.Base64;
import android.util.Log;
import android.widget.Toast;

import com.environment.Environment;
import com.movisat.apis.LavadosApi;
import com.movisat.apis.RecogidasApi;
import com.movisat.database.Area;
import com.movisat.database.DBArea;
import com.movisat.database.DBElemento;
import com.movisat.database.DBElementoModelo;
import com.movisat.database.DBElementoModeloNivelLlenado;
import com.movisat.database.DBElementoTipo;
import com.movisat.database.DBEmpresa;
import com.movisat.database.DBEstados;
import com.movisat.database.DBFlota;
import com.movisat.database.DBFlotaPosiciones;
import com.movisat.database.DBGrupoAreas;
import com.movisat.database.DBIdentificaciones;
import com.movisat.database.DBIncidencia;
import com.movisat.database.DBIncidenciaEstado;
import com.movisat.database.DBIncidenciaModelo;
import com.movisat.database.DBIncidenciaMotivo;
import com.movisat.database.DBIncidenciaTipo;
import com.movisat.database.DBMotivos;
import com.movisat.database.DBMunicipios;
import com.movisat.database.DBOperationsDone;
import com.movisat.database.DBPlanchadas;
import com.movisat.database.DBProvincias;
import com.movisat.database.DBSensoresNivelesLlenado;
import com.movisat.database.DBTags;
import com.movisat.database.DBTipoZonas;
import com.movisat.database.DBTrabajador;
import com.movisat.database.DBTrabajadorTipo;
import com.movisat.database.DBUsuMoviles;
import com.movisat.database.DBUsuario;
import com.movisat.database.DBVehiculo;
import com.movisat.database.Elemento;
import com.movisat.database.ElementoModelo;
import com.movisat.database.ElementoModeloNivelLlenado;
import com.movisat.database.ElementoTipo;
import com.movisat.database.Empresa;
import com.movisat.database.Estado;
import com.movisat.database.Flota;
import com.movisat.database.FlotaPosiciones;
import com.movisat.database.GrupoAreas;
import com.movisat.database.Identificacion;
import com.movisat.database.Incidencia;
import com.movisat.database.IncidenciaEstado;
import com.movisat.database.IncidenciaModelo;
import com.movisat.database.IncidenciaMotivo;
import com.movisat.database.IncidenciaTipo;
import com.movisat.database.Motivos;
import com.movisat.database.Municipios;
import com.movisat.database.OperationsDone;
import com.movisat.database.OperationsEnum;
import com.movisat.database.Planchada;
import com.movisat.database.Provincias;
import com.movisat.database.SensorNivelLLenado;
import com.movisat.database.Tags;
import com.movisat.database.TipoZonas;
import com.movisat.database.Trabajador;
import com.movisat.database.TrabajadorTipo;
import com.movisat.database.UsuMoviles;
import com.movisat.database.Usuario;
import com.movisat.database.Vehiculo;
import com.movisat.ecosat.LoginActivity;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyBroadCastManager;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.ecosat.NotificationHelper;
import com.movisat.ecosat.R;
import com.movisat.ecosat.SettingsActivity;
import com.movisat.events.onChangeLoading;
import com.movisat.events.onFinishSincroUltimasIdentificaciones;
import com.movisat.events.onFinishSincroUltimasPosiciones;
import com.movisat.helpers.CheckPermisos;
import com.movisat.log.Logg;
import com.movisat.outbox.DBPacket;
import com.movisat.outbox.OutBox;
import com.movisat.utilities.Config;
import com.movisat.utilities.HelperDates;
import com.movisat.utilities.Phone;
import com.movisat.utilities.Utils;
import com.movisat.utils_android.UtilssAndroid;

import org.apache.http.HttpException;
import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeoutException;

public class DBSynchro extends Thread {
    private static final String TAG = "DBSynchro";
    private static DBSynchro instance = null;
    private static final String sync = "DBSynchro_sync";

    /**
     * Manejador de mensajes para interactuar con la interfaz de usuario
     */
    static final Handler handler =
            new Handler() {

                @Override
                public void handleMessage(final Message message) {
                    try {
                        switch (message.what) {
                            case MSG_INI_SYNC:
                                Toast.makeText(
                                                MainActivity.getInstance(),
                                                R.string.sincronizacionIniciada,
                                                Toast.LENGTH_SHORT)
                                        .show();
                                MainActivity.getInstance().setSyncProgressBarVisibility(true);
                                break;

                            case MSG_END_SYNC_OK:
                                Toast.makeText(
                                                MainActivity.getInstance(),
                                                R.string.sincronizacionFinalizada,
                                                Toast.LENGTH_SHORT)
                                        .show();
                                MainActivity.getInstance().setSyncProgressBarVisibility(false);
                                MainActivity.getInstance().cargaElementosBusqueda();
//                              if (!Config.getInstance().getValueEmpresa("ultSincro", "").isEmpty())
//                                  MainActivity.playNotificationSound(null);
                                System.gc();
                                break;

                            case MSG_END_SYNC_ERROR:
                                Toast.makeText(
                                                MainActivity.getInstance(),
                                                R.string.sincronizacionIncompleta,
                                                Toast.LENGTH_SHORT)
                                        .show();
                                MainActivity.getInstance().setSyncProgressBarVisibility(false);
                                break;

                            case MSG_TIMEOUT_ERROR:
                                Toast.makeText(
                                                MainActivity.getInstance(),
                                                R.string.tiempoEsperaSuperado,
                                                Toast.LENGTH_SHORT)
                                        .show();
                                MainActivity.getInstance().setSyncProgressBarVisibility(false);
                                break;

                            case MSG_HTTP_ERROR:
                                Toast.makeText(
                                                MainActivity.getInstance(),
                                                R.string.falloConexionServidor,
                                                Toast.LENGTH_SHORT)
                                        .show();
                                MainActivity.getInstance().setSyncProgressBarVisibility(false);
                                if (LoginActivity.getInstance() != null) {
                                    try {
                                        LoginActivity.getInstance().setSyncProgressBarVisibility(false);
                                    } catch (Exception ex) {
                                        ex.printStackTrace();
                                    }
                                }
                                break;

                            case MSG_INI_SYNC_EMPRESAS:
                                Toast.makeText(
                                                MainActivity.getInstance(), R.string.loadingEmpresas, Toast.LENGTH_SHORT)
                                        .show();

                                EventBus.getDefault().post(new onChangeLoading(true));
                                break;

                            case MSG_END_SYNC_EMPRESAS_OK:
                                Toast.makeText(
                                                MainActivity.getInstance(), R.string.loadedEmpresas, Toast.LENGTH_SHORT)
                                        .show();

                                EventBus.getDefault().post(new onChangeLoading(false));
                                break;

                            case MSG_CHECK_VEHICULO_ASOCIADO:
                                MainActivity.getInstance().checkVehiculoAsociado();
                                break;
                        }
                    } catch (Throwable e) {
                        MyLoggerHandler.getInstance().error(e);
                    }
                }
            };
    private volatile boolean running = false;
    private volatile boolean force_sync = false;
    private volatile boolean synchro = false;

    private volatile boolean isSincronizarFail = false;
    // Intervalo de sincronización cuando todo va bien
    private int TIME_SYNC_OK = 900;

    // Tablas que se sincronizan con el servidor
    public static final int GET_DATE_DB = 0;
    public static final int GET_EMPRESAS = 1;
    public static final int GET_USUARIOS = 2;
    public static final int GET_TRABAJADORES_TIPO = 3;
    public static final int GET_TRABAJADORES = 4;
    public static final int GET_ELEMENTOS_TIPO = 5;
    public static final int GET_ELEMENTOS_MODELO = 6;
    public static final int GET_ELEMENTOS = 7;
    public static final int GET_INCIDENCIAS_TIPO = 8;
    public static final int GET_INCIDENCIAS_MODELO = 9;
    public static final int GET_INCIDENCIAS_MOTIVO = 10;
    public static final int GET_INCIDENCIAS_ESTADO = 11;
    public static final int GET_ESTADOS = 12;
    public static final int GET_INCIDENCIAS_HISTORICO = 13;
    public static final int GET_ULTIMAS_POSICIONES = 14;
    public static final int GET_MOVILES_USUARIO = 15;
    public static final int GET_ELEMENTOS_MODELO_NIVEL_LLENADO = 16;
    public static final int GET_MUNICIPIOS = 17;
    public static final int GET_PROVINCIAS = 18;
    public static final int GET_FLOTA = 19;
    public static final int GET_AREAS = 20;
    public static final int GET_GRUPO_AREAS = 21;
    public static final int GET_FILTRO_PAPELERAS = 22;
    public static final int GET_PLANCHADAS = 23;
    public static final int GET_NOMBRE_DISPOSITIVO = 24;
    public static final int GET_TAGS = 25;
    public static final int GET_VEHICULOS = 26;
    public static final int GET_SENSORES_LLENADO = 27;
    public static final int GET_TIPO_ZONAS = 28;
    public static final int GET_INCIDENCIAS_PROPIETARIOS = 29;
    public static final int GET_LAVADOS = 30;
    public static final int GET_MOTIVOS = 31;
    public static final int GET_INCI_ENTRADAS = 32;
    public static final int GET_PORCENTAJES_LLENADO = 33;
    public static final int GET_PROCESADOS = 34;
    public static final int GET_IDENTIFICACIONES = 35;

    // Envio de información al servidor
    public static final int SET_ELEMENTOS = 1000;

    // Mensajes de notificación
    private static final int MSG_INI_SYNC = 1;
    private static final int MSG_END_SYNC_OK = 2;
    private static final int MSG_END_SYNC_ERROR = 3;
    private static final int MSG_TIMEOUT_ERROR = 4;
    private static final int MSG_HTTP_ERROR = 5;
    private static final int MSG_INI_SYNC_EMPRESAS = 6;
    private static final int MSG_END_SYNC_EMPRESAS_OK = 7;
    private static final int MSG_CHECK_VEHICULO_ASOCIADO = 8;
    private static final int MESES_ELIMINAR_INCIDENCIAS_SIN_ESTADO = 1;
    private long tiempoNotif = 0;
    private static final String formatDate = "yyyy-MM-dd HH:mm:ss";
    SharedPreferences sharedPref;

    // Concurrencia
    // ExecutorService executor = Executors.newSingleThreadExecutor();

    /**
     * Constructor
     */
    private DBSynchro() {

        try {

            wsc = new ClientWebSvc();
            sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public ClientWebSvc wsc = null;

    /**
     * Inicia el proceso de sincronización de la base de datos
     */
    public static boolean init() {
        boolean res = false;

        synchronized (sync) {
            if (instance == null || !instance.running) {
                instance = new DBSynchro();
                instance.start();
                res = true;
            } else
                res = instance.running;
        }

        return res;
    }

    /**
     * Devuelve la instancia
     */
    public static DBSynchro getInstance() {
        if (instance == null) init();
        return instance;
    }

    /**
     * Finaliza el proceso de sincronización de la base de datos
     */
    public static void end() {

        // synchronized (sync) {

        // Finalizo el hilo de sincronización
        if (instance != null) {

            instance.running = false;
            instance.interrupt();
            try {
                new Thread(
                        () -> {
                            try {
                                instance.wsc.userLogout();
                            } catch (Exception e) {
                                MyLoggerHandler.getInstance().error(e);
                            }
                        })
                        .start();
            } catch (Throwable e) {
                MyLoggerHandler.getInstance().error(e);
            }
        }
    }

    public String getDateDbOrSystem() {
        String strDate = Utils.datetimeToString(new Date(System.currentTimeMillis()), "yyyy-MM-dd HH:mm:ss");
        try {
            // 17/05/2023 solo funciona en tecno01
            if (wsc != null) {
                JSONArray jsonData = wsc.getTable(GET_DATE_DB);
                if (jsonData != null) {
                    String res = jsonData.getString(0);
                    Date dateDB = Utils.StringToDateTime(res);
                    if (dateDB != null) {
                        strDate = res;
                    }
                }
            }
        } catch (Exception e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return strDate;
    }

    /**
     * Sincroniza una tabla de la base de datos con el servidor
     */
    public synchronized void synchroData(int table) throws Throwable {

        if (isSincronizarFail || !MainActivity.getInstance().isNetworkAvailable()) {
            isSincronizarFail = true;
            return;
        }

        JSONArray jsonData = null;
        Date fechaModificacion = null;
        Date fechaDefecto = null;
        Date fechaModificacionMasReciente = null;
        SimpleDateFormat format = new SimpleDateFormat(formatDate,
                Locale.getDefault());

        int registros = -1;

        switch (table) {
            case GET_EMPRESAS:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || (jsonData.getJSONObject(0).getInt("ErrNum") != 404
                            && jsonData.getJSONObject(0).getInt("ErrNum") != 402)) {
                        String lastDateEmpresa = HelperDates.getInstance().getDateStringBy(System.currentTimeMillis());

                        // Creo una conexión a la base de datos
                        DBEmpresa db = new DBEmpresa();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            Empresa Empresa = new Empresa(
                                    jsonData.getJSONObject(i).getInt("Codigo"),
                                    jsonData.getJSONObject(i).getString("Nombre"));

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean(
                                    "Borrado"))
                                db.delete(Empresa);
                            else if (!db.update(Empresa))
                                db.insert(Empresa);
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                        Config.getInstance().setValue("lastDateEmpresa", lastDateEmpresa);
                    }
                }

                break;
            case GET_MUNICIPIOS:

                if (wsc == null)
                    break;

                DBMunicipios dbMunicipios = new DBMunicipios();
                registros = dbMunicipios.getCount();

                if (registros == 0) {
                    if ((jsonData = wsc.getTable(table)) != null) {

                        if (jsonData.getJSONObject(0).isNull("ErrNum")
                                || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                            Municipios municipio;
                            for (int i = 0; i < jsonData.length(); i++) {
                                municipio = new Municipios(i,
                                        jsonData.getJSONObject(i).getString("Nombre"),
                                        jsonData.getJSONObject(i).getInt("IdNivel3"));

                                dbMunicipios.insert(municipio);
                            }
                        }
                    }
                }
                dbMunicipios.close();
                break;
            case GET_FILTRO_PAPELERAS:

                if (wsc == null)
                    break;

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        String filtroModelos = jsonData.getJSONObject(0).getString("message");
                        Config.getInstance().setValueUsuario("filtroModeloPapelera", filtroModelos);
                    }
                }
                break;
            case GET_GRUPO_AREAS:

                if (wsc == null)
                    break;


                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                        DBGrupoAreas dbGrupoAreas = new DBGrupoAreas();
                        GrupoAreas grupo;

                        for (int i = 0; i < jsonData.length(); i++) {

                            if (!jsonData.getJSONObject(i).isNull("Borrado")) {
                                if (jsonData.getJSONObject(i).getString("Borrado").toLowerCase().equals("true")) {
                                    grupo = dbGrupoAreas.getBy(jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                            jsonData.getJSONObject(i).getInt("Id"));
                                    dbGrupoAreas.delete(grupo);
                                    continue;
                                }
                            }

                            grupo = dbGrupoAreas.getBy(jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getInt("Id"));
                            int clicks = (grupo == null) ? 0 : grupo.getClicks();
                            grupo = new GrupoAreas(
                                    jsonData.getJSONObject(i).getInt("Id"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Abreviatura"),
                                    jsonData.getJSONObject(i).getString("Nombre"), clicks);

                            fechaModificacion = Utils.StringToDateTime(jsonData
                                    .getJSONObject(i)
                                    .getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!dbGrupoAreas.update(grupo))
                                dbGrupoAreas.insert(grupo);
                        }
                        dbGrupoAreas.close();
                        setFechaMaxima(fechaModificacion, fechaModificacionMasReciente, fechaDefecto, format, "ultSincroGrupoAreas");
                    }
                }
                break;
            case GET_AREAS:

                if (wsc == null)
                    break;

                int tryCount = 0;

                // Le ponemos un número máximo de intentos, ya que se quedaba bloqueado cuando no se
                // tenía acceso a internet. Con esto no se modifica casi el comportamiento original,
                // porque se desconoce el motivo de la existencia del bucle.
                while (tryCount < 3 && (jsonData == null
                        || (jsonData.length() > 0 && jsonData.getJSONObject(0).isNull("ErrNum")))) {
                    tryCount++;

                    jsonData = wsc.getTable(table);

                    if (jsonData != null) {

                        if (jsonData.getJSONObject(0).isNull("ErrNum")
                                || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                            DBArea dbAreas = new DBArea();

                            for (int i = 0; i < jsonData.length(); i++) {

                                JSONObject jsonObject = jsonData.getJSONObject(i);

                                if (jsonObject.getInt("Tipo") == 4) {

                                    Area area = new Area(jsonData.getJSONObject(i));

                                    fechaModificacion = Utils.StringToDateTime(jsonData
                                            .getJSONObject(i)
                                            .getString("FechaModificacion"));

                                    if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                        fechaModificacionMasReciente = fechaModificacion;

                                    if (!jsonData.getJSONObject(i).getBoolean(
                                            "Borrado")) {

                                        if (!dbAreas.update(area))
                                            dbAreas.insert(area);
                                    } else
                                        dbAreas.delete(area);
                                }
                            }

                            dbAreas.close();

                            setFechaMaxima(fechaModificacion, fechaModificacionMasReciente, fechaDefecto, format, "ultSincroAreas");
                        }
                    }
                }
                break;

            case GET_FLOTA:

                if (wsc == null)
                    break;

                String ultSincroFlota = getDateDbOrSystem();
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        DBFlota dbFlota = new DBFlota();
                        DBFlotaPosiciones dbFlotaPos = new DBFlotaPosiciones();
                        Flota equipo;
                        for (int i = 0; i < jsonData.length(); i++) {
                            try {

                                equipo = new Flota(jsonData.getJSONObject(i).getInt("Codigo"),
                                        jsonData.getJSONObject(i).getString("Descripcion"),
                                        jsonData.getJSONObject(i).getInt("Empresa"));

                                if (!dbFlota.update(equipo))
                                    dbFlota.insert(equipo);

                                // busco en la tabla flota_posiciones para ver si hay algún registro con ese móvil
                                // y he de actualizar la descripción del mismo
                                dbFlotaPos.updateMovil(jsonData.getJSONObject(i).getInt("Codigo"),
                                        jsonData.getJSONObject(i).getString("Descripcion"),
                                        jsonData.getJSONObject(i).getInt("Empresa"));

                                fechaModificacion = Utils.StringToDateTime(
                                        jsonData.getJSONObject(i).getString("FechaModificacion"));

                                if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                    fechaModificacionMasReciente = fechaModificacion;

                            } catch (Throwable e) {
                                MyLoggerHandler.getInstance().error(e);
                            }
                        }
                        dbFlota.close();
                        dbFlotaPos.close();

                        if (fechaModificacionMasReciente != null) {
                            if (fechaModificacionMasReciente.after(Utils.StringToDateTime(ultSincroFlota)))
                                Config.getInstance().setValueUsuario("ultSincroFlota", ultSincroFlota);
                            else
                                Config.getInstance().setValueUsuario("ultSincroFlota", format.format(fechaModificacionMasReciente));
                        }

                    }
                }

                break;

            case GET_PROVINCIAS:

                if (wsc == null)
                    break;

                DBProvincias dbProvincias = new DBProvincias();
                registros = dbProvincias.getCount();

                if (registros == 0) {
                    if ((jsonData = wsc.getTable(table)) != null) {

                        if (jsonData.getJSONObject(0).isNull("ErrNum")
                                || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                            Provincias provincia;
                            for (int i = 0; i < jsonData.length(); i++) {
                                provincia = new Provincias(
                                        jsonData.getJSONObject(i).getInt("IdNivel3"),
                                        jsonData.getJSONObject(i).getString("Nombre"));

                                dbProvincias.insert(provincia);
                            }
                        }
                    }
                }
                dbProvincias.close();
                break;

            case GET_MOTIVOS:

                if (wsc == null)
                    break;

                DBMotivos dbMotivos = new DBMotivos();
                registros = dbMotivos.getCount();

                String ultSincroMotivos = getDateDbOrSystem();

                if (registros == 0) {
                    if ((jsonData = wsc.getTable(table)) != null) {

                        if (jsonData.getJSONObject(0).isNull("ErrNum")
                                || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                            Motivos motivo;
                            for (int i = 0; i < jsonData.length(); i++) {
                                motivo = new Motivos(jsonData.getJSONObject(i).getInt("Id"),
                                        jsonData.getJSONObject(i).getString("Abreviatura"),
                                        jsonData.getJSONObject(i).getString("Nombre"),
                                        jsonData.getJSONObject(i).getString("Descripcion"));

                                if (!jsonData.getJSONObject(i).getString("FechaBaja").equals("null") &&
                                        dbMotivos.getByAbreviatura(motivo.getAbreviatura()) != null)
                                    dbMotivos.delete(motivo);

                                if (dbMotivos.getByAbreviatura(motivo.getAbreviatura()) != null)
                                    dbMotivos.update(motivo);
                                else
                                    dbMotivos.insert(motivo);
                            }
                            Config.getInstance().setValue("ultSincroMotivos", ultSincroMotivos);
                        }
                    }
                }
                dbMotivos.close();
                break;

            case GET_NOMBRE_DISPOSITIVO:

                if (wsc == null)
                    break;

                if ((jsonData = wsc.getTable(table)) != null) {
                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                        Config.getInstance().setValue("nombreDispositivo", jsonData.getJSONObject(0).getString("Descripcion"));
                        Config.getInstance().setValue("codigoDispositivo", jsonData.getJSONObject(0).getString("Codigo"));
                    }
                }
                break;

            case GET_USUARIOS:

                if (wsc == null)
                    break;

                String ultSincroUsuarios = getDateDbOrSystem();

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || (jsonData.getJSONObject(0).getInt("ErrNum") != 404
                            && jsonData.getJSONObject(0).getInt("ErrNum") != 402)) {

                        // Creo una conexión a la base de datos
                        DBUsuario db = new DBUsuario();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            JSONObject obj = jsonData.getJSONObject(i);

                            String loginIndra;
                            try {
                                loginIndra = Utils.jsonStringNormalize(obj, "IdIndra");
                            } catch (Exception e) {
                                loginIndra = "";
                            }

                            Usuario Usuario = new Usuario(0,
                                    obj.getInt("Codigo"),
                                    obj.getInt("IdEmpresa"),
                                    obj.getString("Nombre"),
                                    obj.getString("Login"),
                                    obj.getString("Password"),
                                    obj.getInt("Admin"),
                                    //Utils.jsonStringNormalize(obj, "IdIndra")
                                    loginIndra
                            );

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean(
                                    "Borrado"))
                                db.delete(Usuario);
                            else if (!db.update(Usuario))
                                db.insert(Usuario);
                        }

                        Config.getInstance().setValue("ultSincroUsuarios", ultSincroUsuarios);

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_TRABAJADORES_TIPO:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBTrabajadorTipo db = new DBTrabajadorTipo();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            TrabajadorTipo trabajadorTipo = new TrabajadorTipo(0,
                                    jsonData.getJSONObject(i).getInt("Codigo"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Nombre"));

                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(trabajadorTipo);
                            else if (!db.update(trabajadorTipo))
                                db.insert(trabajadorTipo);
                        }

                        if (fechaModificacionMasReciente != null) {
                            Config.getInstance().setValueUsuario(
                                    "ultSincroTrabajadoresTipo",
                                    format.format(fechaModificacionMasReciente));
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_ULTIMAS_POSICIONES:

                if (wsc == null)
                    break;

                String ultSincroPosiciones = getDateDbOrSystem();
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        DBFlotaPosiciones managerPosiciones = new DBFlotaPosiciones();
                        int empresa, movil;
                        boolean estado;
                        String fechaPos, descripcion;
                        double lat, lng;

                        int idEmpresa = 0;
                        // Antes de recorrer borro mi lista de datos
                        if (MainActivity.getInstance() != null)
                            idEmpresa = MainActivity.getInstance()
                                    .getEmpresa();

                        if (idEmpresa == 0)
                            break;

                        // Recorro los datos recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            // id = jsonData.getJSONObject(i).getInt("Id");
                            empresa = jsonData.getJSONObject(i).getInt("IdEmpresa");
                            estado = jsonData.getJSONObject(i).getBoolean("Estado");
                            movil = jsonData.getJSONObject(i).getInt("Movil");
                            fechaPos = jsonData.getJSONObject(i).getString("Fecha");
                            lat = jsonData.getJSONObject(i).getDouble("Lat");
                            lng = jsonData.getJSONObject(i).getDouble("Lng");
                            descripcion = jsonData.getJSONObject(i).getString(
                                    "Descripcion");

                            final FlotaPosiciones posicion = new FlotaPosiciones(
                                    0, empresa, estado, movil, fechaPos, lat, lng,
                                    descripcion);
                            //Quitamos la ultima posicion del movil que vamos a actualizar
                            managerPosiciones.delete(movil);
                            // Guardo la nueva posición
                            managerPosiciones.insert(posicion);

                            fechaModificacion = Utils.StringToDateTime(jsonData
                                    .getJSONObject(i)
                                    .getString("Fecha"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;
                        }
                        managerPosiciones.close();
                        if (fechaModificacionMasReciente != null) {
                            if (fechaModificacionMasReciente.after(Utils.StringToDateTime(ultSincroPosiciones))) {
                                Config.getInstance().setValueUsuario("ultSincroPosiciones", ultSincroPosiciones);
                            } else {
                                setFechaMaxima(fechaModificacion, fechaModificacionMasReciente, fechaDefecto, format, "ultSincroPosiciones");
                            }
                        }
                        EventBus.getDefault().post(new onFinishSincroUltimasPosiciones());
                    }
                }

                break;

            case GET_TRABAJADORES:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBTrabajador db = new DBTrabajador();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            Trabajador trabajador = new Trabajador(0,
                                    jsonData.getJSONObject(i).getInt("Codigo"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Nombre"),
                                    jsonData.getJSONObject(i).getInt("CodigoCategoria"),
                                    jsonData.getJSONObject(i).getString("Identificador"));

                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(trabajador);
                            else if (!db.update(trabajador))
                                db.insert(trabajador);
                        }

                        if (fechaModificacionMasReciente != null) {
                            Config.getInstance().setValueUsuario("ultSincroTrabajadores", format.format(fechaModificacionMasReciente));
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_ELEMENTOS_TIPO:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBElementoTipo db = new DBElementoTipo();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            ElementoTipo elementoTipo = new ElementoTipo(0,
                                    jsonData.getJSONObject(i).getInt("Codigo"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Nombre"));

                            fechaModificacion = Utils.StringToDateTime(jsonData
                                    .getJSONObject(i)
                                    .getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(elementoTipo);
                            else if (!db.update(elementoTipo))
                                db.insert(elementoTipo);
                        }

                        if (fechaModificacionMasReciente != null) {
                            Config.getInstance().setValueEmpresa(
                                    "ultSincroElementosTipo",
                                    format.format(fechaModificacionMasReciente));
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_ELEMENTOS_MODELO_NIVEL_LLENADO:

                if (wsc == null)
                    break;

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                        // Creo una conexión a la base de datos
                        DBElementoModeloNivelLlenado db = new DBElementoModeloNivelLlenado();

                        int id, empresa, codigo_modelo, fracciones, peso_lleno;

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            id = jsonData.getJSONObject(i).getInt("Id");
                            empresa = jsonData.getJSONObject(i).getInt("IdEmpresa");
                            codigo_modelo = jsonData.getJSONObject(i).getInt("CodigoModelo");
                            fracciones = jsonData.getJSONObject(i).getInt("Fracciones");
                            peso_lleno = jsonData.getJSONObject(i).getInt("PesoLleno");

                            ElementoModeloNivelLlenado nivel = new ElementoModeloNivelLlenado(
                                    id, empresa, codigo_modelo, fracciones, peso_lleno);
                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(nivel);
                            else if (!db.update(nivel))
                                db.insert(nivel);
                        }

                        if (fechaModificacionMasReciente != null) {
                            Config.getInstance().setValueUsuario(
                                    "ultSincroElementosModeloNivelLlenado",
                                    format.format(fechaModificacionMasReciente));
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }
                break;

            case GET_ELEMENTOS_MODELO:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBElementoModelo db = new DBElementoModelo();

                        boolean flag = false;

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            ElementoModelo elementoModelo = null;
                            elementoModelo = db.getByID(jsonData.getJSONObject(i).getInt("Codigo"), jsonData.getJSONObject(i).getInt("IdEmpresa"));

                            elementoModelo = new ElementoModelo(
                                    (elementoModelo == null) ? 0 : elementoModelo.getId(),
                                    jsonData.getJSONObject(i).getInt("Codigo"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Nombre"),
                                    jsonData.getJSONObject(i).getInt("CodTipo"),
                                    0,
                                    0,
                                    jsonData.getJSONObject(i).getInt("Capacidad"),
                                    Base64.decode(jsonData.getJSONObject(i)
                                            .getString("IconoElementoHex")
                                            .getBytes(), Base64.DEFAULT));

                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(elementoModelo);
                            else if (!db.update(elementoModelo))
                                db.insert(elementoModelo);
                            flag = true;
                        }
                        // Cierro la conexión con la base de datos
                        db.close();

                        MyBroadCastManager.getInstance().sendBroadCastLoadModels();

                        if (fechaModificacionMasReciente != null) {

                            Config.getInstance().setValueEmpresa(
                                    "ultSincroElementosModelos",
                                    format.format(fechaModificacionMasReciente));
                        }

                        // Si ha habido cambios en los modelos refrescamos cluster.
                        if (flag) {
                            try {
                                MyBroadCastManager.getInstance().sendBroadCastRefreshCluster();
                            } catch (Exception ex) {
                                MyLoggerHandler.getInstance().error(ex);
                            }

                        }
                    }
                }

                break;

            case GET_ESTADOS:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBEstados db = new DBEstados();

                        // Proceso los registros recibidos
                        JSONObject estadoJSON;
                        for (int i = 0; i < jsonData.length(); i++) {

                            estadoJSON = jsonData.getJSONObject(i);
                            Estado estado = new Estado(0,
                                    estadoJSON.getInt("Id"),
                                    estadoJSON.getInt("IdEmpresa"),
                                    estadoJSON.getString("Estado"));

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(estado);
                            else if (!db.update(estado)) {

                                db.insert(estado);
                                //Si lo inserto la primera vez, le digo que sea visible.
                                MyBroadCastManager.getInstance()
                                        .sendBroadCastSetVisibleEstadoIncidencia(
                                                estado.getIdExterno(), true);
                            }

                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_ELEMENTOS:

                if (wsc == null)
                    break;

                Config config = Config.getInstance();
                String ultSincroElementos = config.getValueEmpresa("ultSincroElementos", "");
                boolean isFirstTime = ultSincroElementos.equals("");
                String guidElementos = config.getValue("guid_elementos", "");
                String guid;
                int numPaginas;
                int pagina_actual = 1;

                if (guidElementos.equals("")) {
                    config.setValueEmpresa("ultSincroGuid", getDateDbOrSystem()); // Siempre que se pida el guid, se actualiza la fecha de sincronización
                    guid = wsc.getGUID();
                    numPaginas = wsc.getNumPaginas();
                } else {
                    guid = guidElementos;
                    numPaginas = Integer.parseInt(config.getValue("num_paginas", "0"));
                    pagina_actual = Integer.parseInt(config.getValue("pagina_actual", "1"));
                }

                if (numPaginas == 0) {
                    // Reseteo valores
                    Config.getInstance().setValue("guid_elementos", "");
                    break;
                }

                String webSvc = Config.getInstance().getValue("webSvc", "");

                // Pido la página al servidor
                jsonData = wsc.getPagina(pagina_actual, guid, webSvc);

                boolean oldVersion = Build.VERSION.SDK_INT < Build.VERSION_CODES.N;
                // Para C71 y versiones anteriores a Nougat
                ExecutorService executor = Executors.newSingleThreadExecutor();
                Future<JSONArray> future = null;
                // Para SDK 24 y versiones posteriores
                CompletableFuture<JSONArray> nextJsonDataFuture = null;

                // Creo una conexión a la base de datos
                DBElemento dbElementos = new DBElemento();
                ArrayList<Elemento> elementosSinIdExterno = dbElementos.getSinIdExterno(MainActivity.getInstance().getEmpresa());
                for (int k = pagina_actual; k <= numPaginas; k++) {
                    // Para la siguiente página
                    int finalK = k;
                    if (oldVersion) {
                        future = executor.submit(() -> {
                            try {
                                if (finalK < numPaginas) {
                                    JSONArray nextJsonData = wsc.getPagina(finalK + 1, guid, webSvc);
                                    while (nextJsonData == null) {
                                        Thread.sleep(100);
                                        nextJsonData = wsc.getPagina(finalK + 1, guid, webSvc);
                                    }
                                    return nextJsonData;
                                }
                            } catch (Exception e) {
                                Logg.error(TAG, "Error al obtener la siguiente página " + e.getMessage());
                            }
                            return null;
                        });

                    } else {
                        nextJsonDataFuture = CompletableFuture.supplyAsync(() -> {
                            try {
                                if (finalK < numPaginas) {
                                    JSONArray nextJsonData = wsc.getPagina(finalK + 1, guid, webSvc);
                                    while (nextJsonData == null) {
                                        Thread.sleep(100);
                                        nextJsonData = wsc.getPagina(finalK + 1, guid, webSvc);
                                    }
                                    return nextJsonData;
                                }
                            } catch (Exception e) {
                                Logg.error(TAG, "Error al obtener la siguiente página " + e.getMessage());
                            }
                            return null;
                        });
                    }
                    // Si no hay error en la respuesta
                    if (jsonData.getJSONObject(0).isNull("ErrNum") || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                        jsonData = removeDuplicates(jsonData, "Codigo", "FechaModificacion");
                        int elementosEnPagina = jsonData.length();
                        String errorElemento = "";

                        try {
                            long time = System.currentTimeMillis();
                            dbElementos = new DBElemento();
                            dbElementos.beginTransaction();
                            // Proceso los registros recibidos
                            for (int i = 0; i < elementosEnPagina; i++) {
                                try {
                                    errorElemento = "";

                                    JSONObject jsonObject = jsonData.getJSONObject(i);
                                    int bLunes = 0, bMartes = 0, bMiercoles = 0, bJueves = 0, bViernes = 0, bSabado = 0, bDomingo = 0;
                                    int elementoSimilar = 0, idZona = 0, rotativo = 0, vacia_bajo_demanda = 0;
                                    String nivelCritico = "";

                                    // solo las versiones de camacho, tienen la peculiaridad de estos campos, por eso,
                                    // no los leemos para los demás proyectos para que sea algo más fluído
                                    if (Environment.isSoftCamacho) {
                                        try {
                                            if (jsonObject.has("DiasBloqueo") && !jsonObject.isNull("DiasBloqueo")) {
                                                JSONArray dias_bloqueo = jsonObject.getJSONArray("DiasBloqueo");

                                                // leemos los días de bloqueo
                                                for (int j = 0; j < dias_bloqueo.length(); j++) {
                                                    JSONObject jOb = dias_bloqueo.getJSONObject(j);

                                                    int dia = jOb.getInt("Dia");
                                                    int tipo_bloqueo = jOb.getInt("TipoBloqueo");

                                                    if (dia == 1) {
                                                        bLunes = tipo_bloqueo;
                                                    } else if (dia == 2) {
                                                        bMartes = tipo_bloqueo;
                                                    } else if (dia == 3) {
                                                        bMiercoles = tipo_bloqueo;
                                                    } else if (dia == 4) {
                                                        bJueves = tipo_bloqueo;
                                                    } else if (dia == 5) {
                                                        bViernes = tipo_bloqueo;
                                                    } else if (dia == 6) {
                                                        bSabado = tipo_bloqueo;
                                                    } else if (dia == 7) {
                                                        bDomingo = tipo_bloqueo;
                                                    }
                                                }
                                            }

                                            elementoSimilar = (!jsonObject.has("ElementoSimilar")
                                                    || jsonObject.isNull("ElementoSimilar"))
                                                    ? 0 : jsonObject.getInt("ElementoSimilar");
                                            idZona = (!jsonObject.has("IdZona")
                                                    || jsonObject.isNull("IdZona"))
                                                    ? 0 : jsonObject.getInt("IdZona");
                                            rotativo = (!jsonObject.has("Rotativo")
                                                    || jsonObject.isNull("Rotativo"))
                                                    ? 0 : jsonObject.getInt("Rotativo");

                                            nivelCritico = (!jsonObject.has("NivelCritico") || jsonObject.isNull("NivelCritico"))
                                                    ? "" : jsonObject.getString("NivelCritico");
                                            vacia_bajo_demanda = (!jsonObject.has("VaciaBajoDemanda")
                                                    || jsonObject.isNull("VaciaBajoDemanda"))
                                                    ? 0 : jsonObject.getInt("VaciaBajoDemanda");
                                        } catch (Throwable e) {
                                            errorElemento += "Error al leer los campos del elemento para Camacho. " + jsonObject + ". Error: " + e.getMessage();
                                            // Es posible que si falla esto, tampoco deba continuar con la sincronización.
                                        }
                                    }

                                    Elemento elemento = null;
                                    String descripcion = jsonObject.isNull("Descripcion") ? "" : jsonObject.getString("Descripcion");
                                    int tieneImagen = jsonObject.isNull("TieneImagen") ? 0 : jsonObject.getInt("TieneImagen");
                                    String codFisico = jsonObject.isNull("CodigoFisico") ? "" : jsonObject.getString("CodigoFisico");
                                    String ultimoProcesado = jsonObject.isNull("UltimoProcesado") ? "" : jsonObject.getString("UltimoProcesado");
                                    String ultimoLavado = jsonObject.isNull("UltimoLavado") ? "" : jsonObject.getString("UltimoLavado");
                                    int frecuenciaProcesado = jsonObject.isNull("FrecuenciaProcesado") ? 0 : jsonObject.getInt("FrecuenciaProcesado");
                                    try {
                                        elemento = new Elemento(
                                                0,
                                                jsonObject.getInt("Codigo"),
                                                jsonObject.getInt("IdEmpresa"),
                                                jsonObject.getString("Nombre"),
                                                jsonObject.getInt("CodModelo"),
                                                jsonObject.getString("Matricula"),
                                                jsonObject.getInt("Estado"),
                                                jsonObject.getDouble("Lat"),
                                                jsonObject.getDouble("Lng"),
                                                descripcion,
                                                nivelCritico,
                                                elementoSimilar,
                                                bLunes,
                                                bMartes,
                                                bMiercoles,
                                                bJueves,
                                                bViernes,
                                                bSabado,
                                                bDomingo,
                                                idZona,
                                                rotativo,
                                                vacia_bajo_demanda,
                                                tieneImagen,
                                                null,
                                                codFisico,
                                                ultimoProcesado,
                                                ultimoLavado,
                                                frecuenciaProcesado,
                                                jsonObject.getString("IMEI_WellNess"));
                                    } catch (Exception e) {
                                        errorElemento += "Error al leer los campos de un elemento: " + e.getMessage() + "\nJson: " + jsonObject;
                                    }

                                    if (elemento == null)
                                        throw new Exception("El elemento no se ha podido leer.");

                                    Elemento tmp = isFirstTime ? null : getTemporal(elementosSinIdExterno, elemento);
                                    if (tmp != null) {
                                        elemento.setId(tmp.getId());
                                    }

                                    fechaModificacion = Utils.StringToDateTime(jsonObject.getString("FechaModificacion"));

                                    if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                        fechaModificacionMasReciente = fechaModificacion;

                                    if (!jsonObject.isNull("Borrado") && jsonObject.getBoolean("Borrado")) {
                                        if (dbElementos.exists(elemento)) {
                                            boolean isDeleted = dbElementos.delete(elemento);
                                            if (isDeleted) {
                                                // Lo borro de la lista en memoria y del cluster
                                                MyBroadCastManager.getInstance().sendBroadCastDeleteItemCluster(elemento.getId());
                                            } else {
                                                errorElemento += "No se ha podido borrar un elemento en la base de datos local: \n" + elemento;
                                            }
                                        }
                                    } else {
                                        if (!isFirstTime && dbElementos.update(elemento)) {
                                            MyBroadCastManager.getInstance().sendBroadCastUpdateItemCluster(elemento);
                                        } else {
                                            int id = (int) dbElementos.insert(elemento);
                                            elemento.setId(id);

                                            if (id > 0) {
                                                // Lo añado a la lista en memoria y al cluster
                                                if (isFirstTime)
                                                    MainActivity.getInstance().addElementoBusqueda(elemento);
                                                else
                                                    MyBroadCastManager.getInstance().sendBroadCastInsertItemCluster(elemento);
                                            } else {
                                                errorElemento += "No se ha podido insertar un elemento en la base de datos local: \n" + elemento;
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    errorElemento += "Error en la sincronización de elementos. Página " + k + "- Elemento " + i + " Error: " + e.getMessage();
                                } finally {
                                    if (!errorElemento.isEmpty()) {
                                        throw new Exception(errorElemento);
                                    }
                                }
                            }
                            dbElementos.commitTransaction();
                            dbElementos.endTransaction();

                            // Si se ha recibido algún elemento refresco el mapa
                            if (elementosEnPagina > 0 && !isFirstTime) {
                                MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
                            }
                            config.setValue("pagina_actual", Integer.toString(k + 1));
                            Logg.warning(TAG, "Página " + k + " de " + numPaginas + " sincronizada. " + (System.currentTimeMillis() - time) + " ms");
                        } catch (Exception e) {
                            dbElementos.endTransaction();
                            dbElementos.close();
                            Logg.error(TAG, "GET_ELEMENTOS: Error sincronizando página " + k + ". Error: " + e.getMessage());
                            throw new Exception("Error sincronizando página " + k + ". " + e.getMessage());
                        }
                    }
                    jsonData = oldVersion ? future.get() : nextJsonDataFuture.get();
                }
                dbElementos.close();
                executor.shutdown();

                // Nos quedamos con la más antigua entre fechaModificacionMasReciente y ultSincroGuid
                if (fechaModificacionMasReciente != null) {
                    String ultSincroGuid = config.getValueEmpresa("ultSincroGuid", "");
                    Date ultSincroGuidDate = Utils.StringToDateTime(ultSincroGuid);

                    if (ultSincroGuidDate != null && ultSincroGuidDate.before(fechaModificacionMasReciente))
                        fechaModificacionMasReciente = ultSincroGuidDate;
                    Config.getInstance().setValueEmpresa("ultSincroElementos", Utils.datetimeToString(fechaModificacionMasReciente, "yyyy-MM-dd HH:mm:ss"));
                }

                // Reseteo valores
                Config.getInstance().setValue("num_paginas", Integer.toString(0));
                Config.getInstance().setValue("guid_elementos", "");
                break;

            case GET_INCIDENCIAS_PROPIETARIOS:

                if (wsc == null)
                    break;

                DBIncidencia dbInci = null;

                try {
                    // Pido los datos al servidor
                    if ((jsonData = wsc.getTable(table)) != null) {

                        if (jsonData.getJSONObject(0).isNull("ErrNum")) {

                            dbInci = new DBIncidencia();
                            dbInci.beginTransaction();

                            for (int i = 0; i < jsonData.length(); i++) {

                                // actualizamos los idPropietarios de las incidencias que nos lleguen
                                JSONObject jOb = jsonData.getJSONObject(i);

                                int idIncidencia = jOb.getInt("Id");
                                int idEmpresa = jOb.getInt("IdEmpresa");
                                int tipoPropietario = jOb.getInt("TipoPropietario");
                                int propietario = jOb.getInt("Propietario");

                                Incidencia incidencia = dbInci.getByIdExterno(idIncidencia, idEmpresa);

                                if (incidencia != null) {
                                    incidencia.setTipoPropietario(tipoPropietario);
                                    incidencia.setPropietario(propietario);
                                    dbInci.update(incidencia);
                                }
                            }

                            dbInci.commitTransaction();
                            dbInci.endTransaction();
                            dbInci.close();
                        }
                    }
                } catch (Throwable e) {
                    if (dbInci != null)
                        dbInci.close();
                    MyLoggerHandler.getInstance().error(e);
                }

                break;

            case GET_INCIDENCIAS_HISTORICO:

                if (wsc == null)
                    break;
                String ultSincroIncidenciasHistorico = getDateDbOrSystem();

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBIncidencia db = new DBIncidencia();

                        // Proceso los registros recibidos
                        JSONObject incidenciaJSON;

                        for (int i = 0; i < jsonData.length(); i++) {

                            incidenciaJSON = jsonData.getJSONObject(i);

                            Incidencia incidencia = new Incidencia(
                                0,
                                incidenciaJSON.optInt("Id", 0),
                                incidenciaJSON.optInt("IdEmpresa", 0),
                                incidenciaJSON.optInt("Tipo", 0),
                                incidenciaJSON.optInt("Incidencia", 0),
                                incidenciaJSON.optInt("Motivo", 0),
                                incidenciaJSON.optString("Observaciones", ""),
                                incidenciaJSON.optInt("UltimoEstado", 1),
                                incidenciaJSON.optInt("Elemento", 0),
                                incidenciaJSON.optDouble("Lat", 0),
                                incidenciaJSON.optDouble("Lng", 0),
                                incidenciaJSON.optInt("Usuario", 0),
                                incidenciaJSON.optInt("TipoPropietario", 0),
                                incidenciaJSON.optInt("Propietario", 0),
                                incidenciaJSON.optInt("TotalRegistros", 0),
                                incidenciaJSON.has("Movil") && !incidenciaJSON.isNull("Movil") ? incidenciaJSON.getInt("Movil") : null,
                                incidenciaJSON.optString("IdSincro", null),
                                incidenciaJSON.has("Ruta") && !incidenciaJSON.isNull("Ruta") ? incidenciaJSON.getInt("Ruta") : null,
                                incidenciaJSON.has("RutaH") && !incidenciaJSON.isNull("RutaH") ? incidenciaJSON.getInt("RutaH") : null,
                                incidenciaJSON.optString("FechaBaja", null),
                                incidenciaJSON.has("LatitudInt") && !incidenciaJSON.isNull("LatitudInt") ? incidenciaJSON.getInt("LatitudInt") : null,
                                incidenciaJSON.has("LongitudInt") && !incidenciaJSON.isNull("LongitudInt") ? incidenciaJSON.getInt("LongitudInt") : null,
                                incidenciaJSON.optString("MotivoDesc", null),
                                incidenciaJSON.optString("InfoGeo", null),
                                incidenciaJSON.optString("Fecha", null),
                                incidenciaJSON.has("TipoElem") && !incidenciaJSON.isNull("TipoElem") ? incidenciaJSON.getInt("TipoElem") : null,
                                incidenciaJSON.optString("Matricula", null),
                                incidenciaJSON.optString("Imagen", null),
                                incidenciaJSON.has("IdEquipo") && !incidenciaJSON.isNull("IdEquipo") ? incidenciaJSON.getInt("IdEquipo") : null,
                                incidenciaJSON.optString("EmailCiudadano", null),
                                incidenciaJSON.optInt("Validada", 0),
                                incidenciaJSON.optInt("Informado", 0),
                                incidenciaJSON.has("Acera") && !incidenciaJSON.isNull("Acera") ? incidenciaJSON.getInt("Acera") : null,
                                incidenciaJSON.optString("TextoCambioEstado", null),
                                incidenciaJSON.optBoolean("Borrado", false),
                                incidenciaJSON.optString("FechaModificacion", null),
                                incidenciaJSON.optBoolean("OrdenTrabajo", false),
                                incidenciaJSON.optInt("X", 0),
                                incidenciaJSON.optInt("Y", 0),
                                incidenciaJSON.optInt("X_Mercator", 0),
                                incidenciaJSON.optInt("Y_Mercator", 0),
                                incidenciaJSON.optString("Municipio", null)
                            );

                            fechaModificacion = Utils.StringToDateTime(incidenciaJSON.getString("FechaModificacion"));
                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado") && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(incidencia);
                            else if (!db.update(incidencia)) {
                                db.insert(incidencia);

                                try {
                                    long fActual = System.currentTimeMillis();

                                    // si la versión de android es superior a la 8.0 registro el broadcas receiver
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && (tiempoNotif == 0 || ((fActual - tiempoNotif) > 5000))) {
                                        NotificationHelper registerR = new NotificationHelper(MainActivity.getInstance());
                                        registerR.createNotification("Nueva incidencia", "Tiene nuevas incidencias");
                                        tiempoNotif = System.currentTimeMillis();
                                    } else if (tiempoNotif == 0 || ((fActual - tiempoNotif) > 5000)) {
                                        Intent intent = new Intent();
                                        intent.setAction("com.movisat.INCIDENCIAS");
                                        intent.putExtra("idIncidencia", incidencia.getIdExterno());
                                        MainActivity.getInstance().sendBroadcast(intent);
                                        tiempoNotif = System.currentTimeMillis();
                                    }
                                } catch (Exception e) {
                                    MyLoggerHandler.getInstance().error(e);
                                }
                            }
                        }

                        // Si se ha recibido alguna incidencia refresco el mapa para
                        // que se vean los cambios
                        if (jsonData.length() > 0) {
                            MyBroadCastManager.getInstance().sendBroadCastRefreshIncidencias();
                        }

                        if (fechaModificacionMasReciente != null) {
                            if (Utils.StringToDateTime(ultSincroIncidenciasHistorico).before(fechaModificacionMasReciente)) {
                                Config.getInstance().setValueUsuario("ultSincroIncidenciasHistorico", ultSincroIncidenciasHistorico);
                            } else {
                                Config.getInstance().setValueUsuario("ultSincroIncidenciasHistorico", format.format(fechaModificacionMasReciente));
                            }
                        }
                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }
                break;

            case GET_INCIDENCIAS_ESTADO:

                if (wsc == null)
                    break;

                String ultSincroIncidenciasEstado = getDateDbOrSystem();

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBIncidenciaEstado db = new DBIncidenciaEstado();
                        DBIncidencia dbIncidencia = new DBIncidencia();

                        IncidenciaEstado ultimoEstadoIncidencia;
                        MainActivity main = MainActivity.getInstance();

                        if (main.isFirstSyncIncidencias()) {
                            jsonData = removeDuplicates(jsonData, "IdIncidenciah", "Fecha");
                        }

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {
                            // Obtengo la incidencia interna para asignarle el identificador interno.
                            Incidencia incidencia = dbIncidencia.getByIdExterno(
                                    jsonData.getJSONObject(i).getInt("IdIncidenciah"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"));

                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (incidencia == null) continue;

                            // Si obtiene el estado de la incidencia cuya fecha sea igual a la del
                            // estado recibido, para tomar su id interno y actualizarlo (por si id == 0).
                            ultimoEstadoIncidencia = db.getEstadoIncidenciaByDate(
                                    incidencia.getId(),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Fecha"),
                                    false);

                            IncidenciaEstado incidenciaEstado = new IncidenciaEstado(
                                    ultimoEstadoIncidencia != null ? ultimoEstadoIncidencia.getId() : 0,
                                    jsonData.getJSONObject(i).getInt("Id"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    incidencia.getId(),
                                    jsonData.getJSONObject(i).getInt("IdIncidenciah"),
                                    jsonData.getJSONObject(i).getInt("Estado"),
                                    jsonData.getJSONObject(i).getString("Fecha"),
                                    jsonData.getJSONObject(i).isNull("EsAvisoFalso")
                                            ? 0
                                            : jsonData.getJSONObject(i).getBoolean("EsAvisoFalso") ? 1 : 0,
                                    jsonData.getJSONObject(i).isNull("TextoCambioEstado")
                                            ? ""
                                            : jsonData.getJSONObject(i).getString("TextoCambioEstado")
                            );

                            // TODO: LLegan estados sin incidencia, hay que revisar el servidor
//                            fechaModificacion = Utils.StringToDateTime(
//                                  jsonData.getJSONObject(i).getString("FechaModificacion"));
//
//                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
//                                fechaModificacionMasReciente = fechaModificacion;

                            if (!db.update(incidenciaEstado, incidenciaEstado.getId() == 0))
                                db.insert(incidenciaEstado);
                            incidencia.setUltimoEstado(incidenciaEstado.getIdEstado());
                            incidencia.setFechaUltimoEstado(Utils.stringToDate(incidenciaEstado.getFecha(), "yyyy-MM-dd HH:mm:ss").getTime() / 1000);
                            dbIncidencia.update(incidencia);
                        }

                        // Si es la primera sincronización de incidencias y el usuario es
                        // administrador, se eliminan las incidencias cerradas (mantis 3533)
                        if (main.isFirstSyncIncidencias() && main.getUsuAdmin()) {
                            main.setFirstSyncIncidencias();
                            main.removeLastIncidencias(MESES_ELIMINAR_INCIDENCIAS_SIN_ESTADO);
                        }

                        // Si se ha recibido algun cambio de estado de incidencia
                        // refresco el mapa para que se vean los cambios
                        if (jsonData.length() > 0) {
                            MyBroadCastManager.getInstance().sendBroadCastRefreshIncidencias();
                        }

                        if (fechaModificacionMasReciente != null) {
                            if (Utils.StringToDateTime(ultSincroIncidenciasEstado).before(fechaModificacionMasReciente)) {
                                Config.getInstance().setValueUsuario("ultSincroIncidenciasEstado", ultSincroIncidenciasEstado);
                            } else {
                                Config.getInstance().setValueUsuario("ultSincroIncidenciasEstado", format.format(fechaModificacionMasReciente));
                            }
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                        dbIncidencia.close();
                    }
                }

                break;

            case GET_INCIDENCIAS_TIPO:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBIncidenciaTipo db = new DBIncidenciaTipo();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            IncidenciaTipo inciTipo = new IncidenciaTipo(0,
                                    jsonData.getJSONObject(i).getInt("Id"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Tipo"));

                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(inciTipo);
                            else if (!db.update(inciTipo))
                                db.insert(inciTipo);
                        }

                        if (fechaModificacionMasReciente != null) {
                            Config.getInstance().setValueEmpresa(
                                    "ultSincroIncidenciasTipo",
                                    format.format(fechaModificacionMasReciente));
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_INCIDENCIAS_MODELO:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBIncidenciaModelo db = new DBIncidenciaModelo();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            IncidenciaModelo inciModelo = new IncidenciaModelo(0,
                                    jsonData.getJSONObject(i).getInt("Codigo"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getInt("Tipo"),
                                    jsonData.getJSONObject(i).getString("Nombre"));

                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(inciModelo);
                            else if (!db.update(inciModelo))
                                db.insert(inciModelo);
                        }

                        if (fechaModificacionMasReciente != null) {
                            Config.getInstance().setValueUsuario(
                                    "ultSincroIncidenciasModelo",
                                    format.format(fechaModificacionMasReciente));
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_INCIDENCIAS_MOTIVO:

                if (wsc == null)
                    break;

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBIncidenciaMotivo db = new DBIncidenciaMotivo();

                        // Proceso los registros recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            IncidenciaMotivo inciMotivo = new IncidenciaMotivo(0,
                                    jsonData.getJSONObject(i).getInt("Codigo"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getInt("CodigoIncidencia"),
                                    jsonData.getJSONObject(i).getString("Texto"));

                            //PMARCO. La API ya no envia fecha de modificacion
                            fechaModificacion = Utils.StringToDateTime(
                                    jsonData.getJSONObject(i).getString("FechaModificacion"));

                            if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                fechaModificacionMasReciente = fechaModificacion;

                            if (!jsonData.getJSONObject(i).isNull("Borrado")
                                    && jsonData.getJSONObject(i).getBoolean("Borrado"))
                                db.delete(inciMotivo);
                            else if (!db.update(inciMotivo))
                                db.insert(inciMotivo);
                        }

                        if (fechaModificacionMasReciente != null) {
                            Config.getInstance().setValueEmpresa(
                                    "ultSincroIncidenciasMotivo",
                                    format.format(fechaModificacionMasReciente));
                        }

                        // Cierro la conexión con la base de datos
                        db.close();
                    }
                }

                break;

            case GET_MOVILES_USUARIO:

                if (wsc == null)
                    break;

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBUsuMoviles db = new DBUsuMoviles();
                        if (jsonData.length() > 0)
                            db.deleteAll(jsonData.getJSONObject(0).getInt("IdEmpresa"));
                        // Recorro los datos recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            // empresa = jsonData.getJSONObject(i).getInt("IdEmpresa");
                            UsuMoviles usm = new UsuMoviles(0,
                                    MainActivity.getInstance().getUsuario(),
                                    MainActivity.getInstance().getEmpresa(),
                                    jsonData.getJSONObject(i).getInt("CodigoMovil"));

                            // if (!db.update(usm))
                            db.insert(usm);
                        }

                        db.close();
                    }
                }
                break;

            case GET_PLANCHADAS:

                if (wsc == null)
                    break;

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBPlanchadas db = new DBPlanchadas();
                        if (jsonData.length() > 0)
                            db.deleteAll(jsonData.getJSONObject(0).getInt("IdEmpresa"));
                        // Recorro los datos recibidos
                        for (int i = 0; i < jsonData.length(); i++) {

                            Planchada planchada = new Planchada(
                                    jsonData.getJSONObject(i).getInt("Id"),
                                    jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                    jsonData.getJSONObject(i).getString("Descripcion"),
                                    jsonData.getJSONObject(i).getInt("IdLote"),
                                    jsonData.getJSONObject(i).getString("CodigoPlanchada"));

                            // if (!db.update(usm))
                            db.insert(planchada);
                        }

                        db.close();

                        // Guardo la fecha de la ultima sincronizacion new Date -1 hora
                        Date fechaMod = new Date(System.currentTimeMillis() - 3600000);
                        Config.getInstance().setValueUsuario("ultSincroPlanchadas", format.format(fechaMod));
                    }
                }

                break;

            case GET_TAGS:

                if (wsc == null)
                    break;

                String ultSincroTags = getDateDbOrSystem();
                boolean isFirstSync = Config.getInstance().getValue("ultSincroTags", "").isEmpty();

                // Pido los datos al servidor
                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        // Creo una conexión a la base de datos
                        DBTags db = new DBTags();
                        SQLiteStatement statement = db.prepareInsertStatement();
                        int numTags = jsonData.length();
                        int blockSize = 1000;
                        JSONObject tagObj;
                        long inicio = System.currentTimeMillis();

                        for (int i = 0; i < numTags; i += blockSize) {
                            try {
                                db.beginTransaction();
                                // Proceso los registros recibidos
                                for (int j = i; j < i + blockSize && j < numTags; j++) {
                                    tagObj = jsonData.getJSONObject(j);

                                    int idExterno = tagObj.isNull("Id") ? 0 : tagObj.getInt("Id");
                                    int empresa = tagObj.isNull("IdEmpresa") ? 0 : tagObj.getInt("IdEmpresa");
                                    String matricula = tagObj.isNull("Matricula") ? "" : tagObj.getString("Matricula");
                                    String tag = tagObj.isNull("Tag") ? "" : tagObj.getString("Tag");
                                    int idElem = tagObj.isNull("CodigoElemento") ? 0 : tagObj.getInt("CodigoElemento");

                                    Tags tagReg = new Tags(0, idExterno, empresa, matricula, tag, idElem, 0);

                                    if (isFirstSync || !db.update(tagReg, !isFirstSync))
                                        db.insertWithStatement(statement, tagReg);
                                }
                                db.commitTransaction();
                                db.endTransaction();
                                Log.e(TAG, "Sincronizados " + (i + blockSize) + " tags de " + numTags + " en " + (System.currentTimeMillis() - inicio) + " ms");
                                Thread.sleep(50);
                            } catch (Exception e) {
                                db.endTransaction();
                                Logg.error(TAG, "Error al sincronizar los tags " + e.getMessage());
                            }
                        }
                        // Cierro la conexión con la base de datos
                        statement.close();
                        db.close();

                        Config.getInstance().setValue("ultSincroTags", ultSincroTags);
                    }
                }

                break;

            case GET_VEHICULOS:

                if (wsc == null)
                    break;

                String ultSincroVehiculos = getDateDbOrSystem();

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                        DBVehiculo dbVehiculo = new DBVehiculo();
                        Vehiculo vehiculo;
                        for (int i = 0; i < jsonData.length(); i++) {
                            try {

                                vehiculo = new Vehiculo(
                                        jsonData.getJSONObject(i).getInt("Codigo"),
                                        jsonData.getJSONObject(i).getString("Nombre"),
                                        jsonData.getJSONObject(i).getInt("Empresa"),
                                        jsonData.getJSONObject(i).getInt("Tipo"),
                                        jsonData.getJSONObject(i).getString("Imei"));


                                // Si el vehículo se ha dado de baja, se elimina de la base de datos
                                if (!jsonData.getJSONObject(i).isNull("FechaBaja") &&
                                        !jsonData.getJSONObject(i).getString("FechaBaja").contains("null")) {

                                    dbVehiculo.delete(vehiculo);

                                    // Si el dispositivo está asociado a ese vehículo, se desasocia
                                    Vehiculo vehiculoAsociado = Vehiculo.getVehiculoAsociado();
                                    if (vehiculoAsociado != null && vehiculoAsociado.equals(vehiculo))
                                        Config.getInstance().setValueEmpresa("vehiculoSeleccionado", "");
                                } else if (!dbVehiculo.update(vehiculo))
                                    dbVehiculo.insert(vehiculo);

                            } catch (Throwable e) {
                                Logg.error(TAG, "Error al sincronizar los vehículos " + e.getMessage());
                            }
                        }
                        dbVehiculo.close();

                        Config.getInstance().setValueUsuario("ultSincroVehiculos", ultSincroVehiculos);
                    }
                }

                break;

            case GET_PORCENTAJES_LLENADO:

                if (wsc == null)
                    break;

                String ultSincroPorcent = getDateDbOrSystem();

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                        DBSensoresNivelesLlenado dbSensoresNivelesLlenado = new DBSensoresNivelesLlenado();

                        for (int i = 0; i < jsonData.length(); i++) {
                            try {
                                // TODO: Por qué vienen con fecha 0001-01-01 00:00:00?
                                // Porque se está haciendo el join de ecoelementos con w_measures
                                // por el w_imei/imei en lugar del w_container_id/container
                                // Abro mantis 6461
//                                {
//                                    "CodigoElemento": 9,
//                                      "CodigoMovil": 0,
//                                      "IdEmpresa": 586,
//                                      "Fecha": "0001-01-01 00:00:00",
//                                      "PorcentajeLlenado": 0,
//                                      "Fraccion": 0       //Siempre viene a 0 porque se reutiliza el mismo dto a la hora de enviar una lectura de llenado, la fracción se convierte en la api a porcentaje
//                                },
                                String fecha_str = jsonData.getJSONObject(i).getString("Fecha");
                                Date fecha_date = fecha_str.equals("0001-01-01 00:00:00")
                                        ? new Date(0)
                                        : Utils.stringToDate(fecha_str, "yyyy-MM-dd HH:mm:ss");

                                SensorNivelLLenado porcentaje = new SensorNivelLLenado(
                                        jsonData.getJSONObject(i).getInt("CodigoElemento"),
                                        jsonData.getJSONObject(i).getInt("CodigoMovil"),
                                        jsonData.getJSONObject(i).getInt("IdEmpresa"),
                                        jsonData.getJSONObject(i).getInt("PorcentajeLlenado"),
                                        fecha_date.getTime(), 0);

                                SensorNivelLLenado porcentaje_old = dbSensoresNivelesLlenado.getSensorByElemento(
                                        jsonData.getJSONObject(i).getInt("CodigoElemento"),
                                        jsonData.getJSONObject(i).getInt("IdEmpresa"));

                                if (porcentaje_old == null) {
                                    dbSensoresNivelesLlenado.insert(porcentaje);
                                } else if (porcentaje.getFechaRegistro() > porcentaje_old.getFechaRegistro()) {
                                    dbSensoresNivelesLlenado.update(porcentaje);
                                }

                            } catch (Throwable e) {
                                MyLoggerHandler.getInstance().error(e);
                            }
                        }
                        dbSensoresNivelesLlenado.close();
                        Config.getInstance().setValueEmpresa("ultSincroPorcent", ultSincroPorcent);
                    }
                }

                break;
            case GET_PROCESADOS:

                if (wsc == null)
                    break;

                String ultSincroProcesados = getDateDbOrSystem();

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {
                        DBElemento dbElem = new DBElemento();

                        int NIVEL_LLENADO_FRACCION = 1;
                        int NIVEL_LLENADO_PORCENTAJE = 2;
                        int LAVADO = 3;
                        int PROCESADO_POR_SENSOR = 4;
                        int LECTURA_TAG = 5;
                        int PESAJE = 6;
                        int PROCESADO_ALZADA = 7;
                        int PROCESADO_RECOLECTOR = 8;
                        int LAVADO_LAVACONTENEDOR = 9;
                        int PROCESADO_MANUAL = 10;
                        int PROCESADO_PARADAS = 11;

                        for (int i = 0; i < jsonData.length(); i++) {
                            try {
                                int tipo = jsonData.getJSONObject(i).getInt("TipoProcesado");
                                if (tipo != LECTURA_TAG) {

                                    int idElem = jsonData.getJSONObject(i).getInt("CodigoElemento");
                                    Elemento elem = dbElem.getByIdExterno(idElem, MainActivity.getInstance().getEmpresa());

                                    if (elem != null) {
                                        String fechaProcesado = jsonData.getJSONObject(i).getString("Fecha");

                                        if (tipo == LAVADO || tipo == LAVADO_LAVACONTENEDOR) {
                                            if (elem.setFechaUltLavado(fechaProcesado)) {
                                                dbElem.update(elem);
                                            }
                                        } else {
                                            if (elem.setFechaUltRecogida(fechaProcesado)) {
                                                dbElem.update(elem);
                                            }
                                        }
                                    }

                                }

                            } catch (Throwable e) {
                                MyLoggerHandler.getInstance().error(e);
                            }
                        }
                        dbElem.close();

                        Config.getInstance().setValueUsuario("ultSincroProcesados", ultSincroProcesados);
                    }
                }

                break;

            case GET_SENSORES_LLENADO:

                if (wsc == null)
                    break;

                String ultSincroSensoresLlenado = getDateDbOrSystem();
                JSONObject jsonItem;
                jsonData = wsc.getTable(table);
                if (jsonData != null) {

                    if (jsonData.length() == 0)
                        break;

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        DBOperationsDone dbOperationsDone = new DBOperationsDone();
                        DBElemento dbElemento = new DBElemento();
                        //operationsDone.deleteByType(OperationsEnum.LEVEL.getKey());
                        int empresa = MainActivity.getInstance().getEmpresa();
                        JSONObject jsonFraccion;
                        Elemento elemento;

                        for (int i = 0; i < jsonData.length(); i++) {
                            try {
                                jsonItem = jsonData.getJSONObject(i);
                                // Esto es la fecha del sensor PUEDE SER EN EL FUTURO/PASASDO/PRESENTE
                                String dateString = jsonItem.getString("Fecha");
                                fechaModificacion = Utils.stringToDate(dateString, "yyyy-MM-dd HH:mm:ss");
                                if (fechaModificacion == null)
                                    continue;

                                int idMovil = jsonItem.getInt("Movil");
                                int idSensor = jsonItem.getInt("Sensor");
                                int idElemento = jsonItem.getInt("Elemento");

                                // Se obtiene el elemento asociado al sensor
                                elemento = dbElemento.getByIdExterno(idElemento, empresa);
                                if (elemento == null) continue;

                                // Se comprueba la fecha de baja para borrar la ultima recogida si es la misma
                                String fechaBaja = jsonItem.getString("FechaBaja");
                                if (fechaBaja != null && !fechaBaja.isEmpty() && !fechaBaja.equals("null")) {
                                    dbOperationsDone.deleteByElementAndDate(idSensor == 64 ? 1 : 2, elemento.getId(), fechaModificacion.getTime());
                                    String fechaUltRecogida = elemento.getFechaUltRecogida();
                                    if (fechaUltRecogida != null && !fechaUltRecogida.isEmpty()) {
                                        Date fechaUltRecogidaDate = Utils.stringToDate(fechaUltRecogida, "yyyy-MM-dd HH:mm:ss");
                                        if (fechaUltRecogidaDate != null && fechaUltRecogidaDate.compareTo(fechaModificacion) == 0) {
                                            elemento.removeFechaUltRecogida();
                                            // Buscamos otra anterior
                                            OperationsDone operationsDone = dbOperationsDone.getLastCollectLevelByElem(elemento.getId());
                                            if (operationsDone != null) {
                                                elemento.setFechaUltRecogida(Utils.datetimeToString(new Date(operationsDone.getDate()), "yyyy-MM-dd HH:mm:ss"));
                                            }
                                            dbElemento.update(elemento);
                                        }
                                    }
                                    continue;
                                } else if (elemento.setFechaUltRecogida(dateString)) {
                                    dbElemento.update(elemento);
                                }

                                //Insertamos operación realizada
                                if (idSensor == 64) {
                                    jsonFraccion = jsonItem.getJSONObject("Fraccion");
                                    int fraccion = jsonFraccion.getInt("Fraccion");
                                    dbOperationsDone.insert(
                                            OperationsEnum.LEVEL,
                                            idMovil,
                                            elemento.getId(),
                                            fechaModificacion.getTime(),
                                            elemento.getPosition().latitude,
                                            elemento.getPosition().longitude,
                                            ": Fracción " + fraccion + "\n" + elemento);
                                } else if (idSensor == 80) {
                                    dbOperationsDone.insert(
                                            OperationsEnum.COLLECT,
                                            idMovil,
                                            elemento.getId(),
                                            fechaModificacion.getTime(),
                                            elemento.getPosition().latitude,
                                            elemento.getPosition().longitude,
                                            "\n" + elemento);
                                }

                                // Puede ser en el futuro
                                if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                    fechaModificacionMasReciente = fechaModificacion;

                            } catch (Exception e) {
                                MyLoggerHandler.getInstance().error(e);
                            }
                        }
                        dbElemento.close();
                        dbOperationsDone.close();

                        if (fechaModificacionMasReciente != null) {
                            if (Utils.StringToDateTime(ultSincroSensoresLlenado).before(fechaModificacionMasReciente)) {
                                Config.getInstance().setValueEmpresa("ultSincroSensoresLlenado", ultSincroSensoresLlenado);
                            } else {
                                fechaModificacionMasReciente.setTime(fechaModificacionMasReciente.getTime() - 10000);
                                Config.getInstance().setValueEmpresa("ultSincroSensoresLlenado", format.format(fechaModificacionMasReciente));
                            }
                        }

                        // Se actualiza el mapa
                        MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
                    }
                }

                break;

            //LAVADOSMOD
            case GET_LAVADOS:

                if (wsc == null)
                    break;

                String ultSincroLavados = getDateDbOrSystem();
                JSONObject jsonItem1;

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.length() == 0)
                        break;

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        DBOperationsDone dbOperationsDone = new DBOperationsDone();
                        DBElemento dbElemento = new DBElemento();
                        //operationsDone.deleteByType(OperationsEnum.CLEAN.getKey());

                        for (int i = 0; i < jsonData.length(); i++) {
                            try {
                                jsonItem1 = jsonData.getJSONObject(i);

                                String dateString = jsonItem1.getString("Fecha");
                                fechaModificacion = Utils.StringToDateTime(dateString);
                                if (fechaModificacion == null)
                                    continue;

                                JSONObject jsonLavados = jsonItem1.getJSONObject("Lavado");
                                int idMovil = jsonItem1.getInt("Movil");
                                int idElemento = jsonLavados.getInt("CodigoElemento");
                                int idEmpresa = jsonLavados.getInt("IdEmpresa");
                                //int fraccion = jsonLavados.getInt("Fraccion");

                                // Se obtiene el elemento asociado al sensor
                                Elemento elemento = dbElemento.getByIdExterno(idElemento, idEmpresa);
                                if (elemento == null) continue;
                                if (elemento.setFechaUltLavado(dateString)) {
                                    dbElemento.update(elemento);
                                }

                                //Insertamos operación realizada
                                dbOperationsDone.insert(
                                        OperationsEnum.CLEAN,
                                        idMovil,
                                        elemento.getId(),
                                        fechaModificacion.getTime(),
                                        elemento.getPosition().latitude,
                                        elemento.getPosition().longitude,
                                        "\n" + elemento);

                                if (fechaModificacionMasReciente == null || fechaModificacionMasReciente.before(fechaModificacion))
                                    fechaModificacionMasReciente = fechaModificacion;

                            } catch (Throwable e) {
                                MyLoggerHandler.getInstance().error(e);
                            }
                        }
                        dbElemento.close();
                        dbOperationsDone.close();

                        if (fechaModificacionMasReciente != null) {
                            if (Utils.StringToDateTime(ultSincroLavados).before(fechaModificacionMasReciente)) {
                                Config.getInstance().setValueEmpresa("ultSincroLavados", ultSincroLavados);
                            } else {
                                fechaModificacionMasReciente.setTime(fechaModificacionMasReciente.getTime() - 10000);
                                Config.getInstance().setValueEmpresa("ultSincroLavados", format.format(fechaModificacionMasReciente));
                            }
                        }

                        // Se actualiza el mapa
                        MyBroadCastManager.getInstance().sendBroadCastRefreshMap();
                    }
                }

                break;

            case GET_TIPO_ZONAS:

                if (wsc == null)
                    break;

                Date ultSincroTipoZonas = new Date();

                if ((jsonData = wsc.getTable(table)) != null) {

                    if (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404) {

                        for (int i = 0; i < jsonData.length(); i++) {
                            jsonItem = jsonData.getJSONObject(i);

                            int id = jsonItem.getInt("Id");
                            String nombre = jsonItem.getString("Nombre");
                            String abreviatura = jsonItem.getString("Abreviatura");
                            String fecha_baja = jsonItem.getString("FechaBaja");

                            // Se obtiene el elemento asociado al sensor
                            DBTipoZonas db = new DBTipoZonas();

                            // si no hay ninguna creada, creo una con código 0, que es la que estará marcada por defecto,
                            // cuando no ha ninguna seleccionada
                            if (db.getCount() == 0) {
                                db.insert(new TipoZonas(0, "", "", ""));
                            }

                            TipoZonas tipo_zona = db.getByID(id);

                            // si viene con fecha de baja, lo elimino, y si existe, no hago nada.
                            if (tipo_zona != null) {
                                if (fecha_baja != null && fecha_baja != "") {
                                    db.delete(tipo_zona);
                                }
                                continue;
                            }

                            tipo_zona = new TipoZonas(id, nombre, abreviatura, fecha_baja);

                            //Insertamos operación realizada
                            db.insert(tipo_zona);
                            db.close();
                        }
                    }

                    Config.getInstance().setValueEmpresa("ultSincroTipoZonas", format.format(ultSincroTipoZonas));
                }

                break;

            case GET_INCI_ENTRADAS:
                if (wsc == null)
                    break;

                Date ultSincroInciEnt = new Date();

                if ((jsonData = wsc.getTable(table)) != null) {
                    if (jsonData.length() != 0 && (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404)) {
                        for (int i = 0; i < jsonData.length(); i++) {
                            jsonItem = jsonData.getJSONObject(i);
                            Config.getInstance().setValueEmpresa("Entrada" + jsonItem.getString("Entrada"), jsonItem.getString("Motivo"));
                        }

                    }

                }

                Config.getInstance().setValueEmpresa("ultSincroInciEnt", format.format(ultSincroInciEnt));

                break;

            case GET_IDENTIFICACIONES:
                if (wsc == null)
                    break;

                String ultSincroIdentificaciones = getDateDbOrSystem();

                if ((jsonData = wsc.getTable(table)) != null) {
                    DBIdentificaciones dbIdentificaciones = new DBIdentificaciones();
                    dbIdentificaciones.deleteAll(); // Siempre que haya respuesta limpiamos tabla

                    if (jsonData.length() != 0 && (jsonData.getJSONObject(0).isNull("ErrNum")
                            || jsonData.getJSONObject(0).getInt("ErrNum") != 404)) {

                        // Recorremos moviles
                        for (int i = 0; i < jsonData.length(); i++) {
                            jsonItem = jsonData.getJSONObject(i);
                            int idMovil = jsonItem.getInt("CodigoMovil");
                            String nombreMovil = jsonItem.getString("NombreMovil");
                            JSONArray identificaciones = jsonItem.getJSONArray("Identificaciones");

                            // Recorremos identificaciones
                            for (int j = 0; j < identificaciones.length(); j++) {
                                JSONObject identificacion = identificaciones.getJSONObject(j);
                                String nombreCategoria = identificacion.getString("NombreCategoria");
                                String nombreEmpleado = identificacion.getString("NombreEmpleado");
                                String fechaInicio = identificacion.getString("FechaInicio");
                                dbIdentificaciones.insert(new Identificacion(
                                        idMovil, nombreMovil, nombreCategoria, nombreEmpleado, fechaInicio));
                            }
                        }
                    }
                    dbIdentificaciones.close();

                    Config.getInstance().setValueEmpresa("ultSincroIdentificaciones", ultSincroIdentificaciones);
                    EventBus.getDefault().post(new onFinishSincroUltimasIdentificaciones());

                }

                break;

        }


        if (jsonData != null && jsonData.length() > 0 &&
                !jsonData.getJSONObject(0).isNull("ErrNum")) {
            switch (jsonData.getJSONObject(0).getInt("ErrNum")) {
                case 404:
                    return;
                case 402:

                    if (LoginActivity.getInstance() != null) {
                        // Enviamos aviso
                        Message msg = new Message();
                        LoginActivity.getInstance();
                        msg.what = LoginActivity.MESSAGE_AVISA_USUARIO_NO_VALIDO;
                        LoginActivity.getInstance();
                        LoginActivity.handler.sendMessage(msg);
                        isSincronizarFail = true;
                    }
                    return;
                default:
                    isSincronizarFail = true;
                    break;
            }
        }
    }

//    public void synchroOperationesLevel() {
//        String url = Config.getInstance().getValue("webSvc", "");
//        int codigoDispositivo = MainActivity.getInstance().getCodigoDispositivo();
//
//        SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
//        int hours = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_LEVEL, "18"));
//        Date minOperationDoneTime = new Date(((new Date()).getTime()) - (hours * 3600 * 1000));
//
//        String desde = Utils.datetimeToString(minOperationDoneTime, "yyyy-MM-dd HH:mm:ss");
//        String hasta = Utils.datetimeToString(new Date(), "yyyy-MM-dd HH:mm:ss");
//
//        new RecogidasApi().execute(url, new RecogidasApi.Request(Phone.getInstance().getIMEI(),
//              desde, hasta), wsc.token, value ->
//              new Thread(() -> saveOperationsDone(codigoDispositivo, value)).start());
//    }

    public void syncLevelAndCollectOperations(int hoursFrom, int hoursTo) {
        String webServiceUrl = Config.getInstance().getValue("webSvc", "");

        long millisecondsFrom = hoursFrom * 3600L * 1000L;
        long millisecondsTo = hoursTo * 3600L * 1000L;

        Date fromDate = new Date(System.currentTimeMillis() - millisecondsFrom);
        Date toDate = new Date(System.currentTimeMillis() - millisecondsTo);

        String fromDateString = Utils.datetimeToString(fromDate, "yyyy-MM-dd HH:mm:ss");
        String toDateString = Utils.datetimeToString(toDate, "yyyy-MM-dd HH:mm:ss");

        RecogidasApi.Request request = new RecogidasApi.Request(Phone.getInstance().getIMEI(), fromDateString, toDateString);

        RecogidasApi recogidasApi = new RecogidasApi();
        recogidasApi.execute(webServiceUrl, request, wsc.token, value -> {
            if (value != null) {
                new Thread(() -> saveNivelesRecogidas(value)).start();
            }
        });
    }

    public void syncCleanOperations(int hoursFrom, int hoursTo) {
        String webServiceUrl = Config.getInstance().getValue("webSvc", "");

        long millisecondsFrom = hoursFrom * 3600L * 1000L;
        long millisecondsTo = hoursTo * 3600L * 1000L;

        Date fromDate = new Date(System.currentTimeMillis() - millisecondsFrom);
        Date toDate = new Date(System.currentTimeMillis() - millisecondsTo);

        String fromDateString = Utils.datetimeToString(fromDate, "yyyy-MM-dd HH:mm:ss");
        String toDateString = Utils.datetimeToString(toDate, "yyyy-MM-dd HH:mm:ss");

        LavadosApi.Request request = new LavadosApi.Request(Phone.getInstance().getIMEI(), fromDateString, toDateString);

        LavadosApi lavadosApi = new LavadosApi();
        lavadosApi.execute(webServiceUrl, request, wsc.token, value -> {
            if (value != null) {
                new Thread(() -> saveLavados(value)).start();
            }
        });
    }

    private void saveNivelesRecogidas(JSONArray value) {
        if (value == null) return;
        JSONObject jsonItem;
        String fechaString;
        Date fecha;
        String fechaBaja;

        synchronized (sync) {
            synchro = true;
            MainActivity.getInstance().runOnUiThread(() -> MainActivity.getInstance().setSyncProgressBarVisibility(true));

            DBOperationsDone dbOperationsDone = new DBOperationsDone();
            DBElemento dbElemento = new DBElemento();
            for (int i = 0; i < value.length(); i++) {
                try {
                    jsonItem = value.getJSONObject(i);

                    fechaString = jsonItem.getString("Fecha");
                    fechaBaja = jsonItem.getString("FechaBaja");

                    if (fechaString.isEmpty() || fechaString.equals("null")) {
                        continue;
                    }

                    fecha = Utils.stringToDate(fechaString, "yyyy-MM-dd HH:mm:ss");
                    if (fecha == null) {
                        continue;
                    }
                    
                    if (!fechaBaja.isEmpty() && !fechaBaja.equals("null")) {
                        continue;
                    }

                    // Se obtiene el elemento asociado al sensor
                    int idElemento = jsonItem.getInt("Elemento");
                    int idEmpresa = MainActivity.getInstance().getEmpresa(); // jsonFraccion.getInt("IdEmpresa");
                    Elemento elemento = dbElemento.getByIdExterno(idElemento, idEmpresa);
                    if (elemento == null) continue;
                    if (elemento.setFechaUltRecogida(fechaString)) {
                        dbElemento.update(elemento);
                    }

                    //Insertamos operación realizada
                    int idSensor = jsonItem.getInt("Sensor");
                    int idMovil = jsonItem.getInt("Movil");
                    if (idSensor == 64) {
                        JSONObject jsonFraccion = jsonItem.getJSONObject("Fraccion");
                        int fraccion = jsonFraccion.getInt("Fraccion");
                        dbOperationsDone.insert(
                                OperationsEnum.LEVEL,
                                idMovil,
                                elemento.getId(),
                                fecha.getTime(),
                                elemento.getPosition().latitude,
                                elemento.getPosition().longitude,
                                ": Fracción " + fraccion + "\n" + elemento);
                    } else if (idSensor == 80) {
                        dbOperationsDone.insert(
                                OperationsEnum.COLLECT,
                                idMovil,
                                elemento.getId(),
                                fecha.getTime(),
                                elemento.getPosition().latitude,
                                elemento.getPosition().longitude,
                                "\n" + elemento);
                    }
                } catch (Throwable e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            }
            dbElemento.close();
            dbOperationsDone.close();

            synchro = false;
            MainActivity.getInstance().runOnUiThread(() -> MainActivity.getInstance().setSyncProgressBarVisibility(false));
        }
    }

    private void saveLavados(JSONArray value) {
        if (value == null) return;
        JSONObject jsonItem;
        String fechaString;
        Date fecha;

        synchronized (sync) {
            synchro = true;
            MainActivity.getInstance().runOnUiThread(() -> MainActivity.getInstance().setSyncProgressBarVisibility(true));

            DBOperationsDone dbOperationsDone = new DBOperationsDone();
            DBElemento dbElemento = new DBElemento();
            for (int i = 0; i < value.length(); i++) {
                try {
                    jsonItem = value.getJSONObject(i);

                    JSONObject jsonLavados = jsonItem.getJSONObject("Lavado");

                    fechaString = jsonItem.getString("Fecha");
                    fecha = Utils.stringToDate(fechaString, "yyyy-MM-dd HH:mm:ss");

                    if (fecha == null)
                        continue;

                    int idMovil = jsonItem.getInt("Movil");
                    int idEmpresa = jsonLavados.getInt("IdEmpresa");
                    int idElemento = jsonLavados.getInt("CodigoElemento");

                    // Se obtiene el elemento asociado al sensor
                    Elemento elemento = dbElemento.getByIdExterno(idElemento, idEmpresa);
                    if (elemento == null) continue;
                    if (elemento.setFechaUltLavado(fechaString)) {
                        dbElemento.update(elemento);
                    }

                    //Insertamos operación realizada
                    dbOperationsDone.insert(
                            OperationsEnum.CLEAN,
                            idMovil,
                            elemento.getId(),
                            fecha.getTime(),
                            elemento.getPosition().latitude,
                            elemento.getPosition().longitude,
                            "\n" + elemento);
                } catch (Throwable e) {
                    MyLoggerHandler.getInstance().error(e);
                }
            }
            dbElemento.close();
            dbOperationsDone.close();

            synchro = false;
            MainActivity.getInstance().runOnUiThread(() -> MainActivity.getInstance().setSyncProgressBarVisibility(false));
        }
    }


    private void setFechaMaxima(Date fecha,
                                Date fechaMaxima,
                                Date fechaDefecto,
                                SimpleDateFormat format, String grupo) {
        if (fechaMaxima == null) fechaMaxima = fechaDefecto;

        if (fechaMaxima != null)
            Config.getInstance().setValueUsuario(grupo, format.format(fechaMaxima));
    }

    /**
     * Devuelve el estado de la sincronización
     */
    public boolean getSynchro() {

        return synchro;
    }

    /**
     * Fuerza la sincronización inmediata de la base de datos
     */
    public void forceSync() {

        synchronized (sync) {

            if (running && !synchro)
                force_sync = true;
        }

    }

    /**
     * Hilo principal del proceso de sincronización de la base de datos
     */
    public void run() {

        try {

            running = true;

            while (running || !isInterrupted()) {

                try {

                    if (MainActivity.getInstance() == null)
                        break;

                    // Mientras no se active la aplicación o no se realice la
                    // conexión con la Intranet no se sincroniza nada
                    while ((!MainActivity.getInstance().isAppActive()
                            || !MainActivity.getInstance().getIntranet())) {
                        Thread.sleep(1000);
                    }

                    // Compruebo el número de registros que hay en la
                    // bandeja de salida
                    DBPacket dbp = new DBPacket();
                    int nReg = dbp.getCount();
                    dbp.close();

                    // Mientras existan registros en la bandeja de salida
                    // no se sincroniza nada
                    if (nReg > 0) {
                        do {
                            Log.d(TAG, "Esperando a que se envíen los mensajes de la bandeja de salida");
                            sleep(1000);
                        } while (OutBox.isSending() && running);

//                        for (int i = 0; i < 3 && running; i++)
//                            sleep(1000);

//                        continue;
                    }

                    if (!UtilssAndroid.isInternetAvailable()) continue;

                    synchronized (sync) {

                        // Al comenzar cada sincronización, comprobamos si la anterior ha fallado y
                        // tiene conexión a Internet, para restablecer la variable y que pueda volver
                        // a sincronizar. Antes dejaba de poder sincronizar hasta que la aplicación
                        // se reiniciaba.
                        if (isSincronizarFail && MainActivity.getInstance().isNetworkAvailable())
                            isSincronizarFail = false;

                        // Marco que ha empezado el proceso de
                        // sincronización
                        synchro = true;

                        // Si ya se ha validado un usuario entonces sincronizo
                        // los datos de la empresa seleccionada
                        if (MainActivity.getInstance().getEmpresa() > 0) {

                            // Saco un mensaje de notificación
                            handler.sendEmptyMessage(MSG_INI_SYNC);

                            // Obtengo la fecha y hora de la bd o el sistema
                            String strDate = getDateDbOrSystem();

                            // Pido las categorias de trabajadores
                            //synchroData(GET_TRABAJADORES_TIPO);

                            // Pido los trabajadores
                            //synchroData(GET_TRABAJADORES);

                            // Pido el código del dispositivo
                            synchroData(GET_NOMBRE_DISPOSITIVO);

                            // Pido los vehículos
                            if (CheckPermisos.getInstance().isPermisoValid(MainActivity.MENU_SELECCIONAR_VEHICULO)) {
                                synchroData(GET_VEHICULOS);

                                // Con el código del dispositivo y la lista de vehículos, se
                                // comprueba y muestra cuál es el vehículo asociado
                                handler.sendEmptyMessage(MSG_CHECK_VEHICULO_ASOCIADO);
                            }

                            // Pido los estados creados para las incidencias
                            synchroData(GET_ESTADOS);

                            if (CheckPermisos.getInstance()
                                    .isPermisoValid(MainActivity.MENU_CREAR_ELEMENTOS) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_LAVAR_ELEMENTO) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_LAVAR_ELEMENTO_MAPA) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_MODIFICAR_BORRAR_ELEMENTOS) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_MODIFICAR_ELEMENTOS) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_FILTRAR_MODELOS)) {
                                //Aqui todo lo de sincronizar elementos.
                                // Pido los tipos de elementos
                                synchroData(GET_ELEMENTOS_TIPO);

                                // Pido los modelos de elementos
                                synchroData(GET_ELEMENTOS_MODELO);

                                // Pido los niveles de llenado de modelos de
                                // elementos
                                synchroData(GET_ELEMENTOS_MODELO_NIVEL_LLENADO);

                                // Pido los elementos
                                synchroData(GET_ELEMENTOS);
                                int IdEmpresa = 0;
                                if (MainActivity.getInstance() != null)
                                    IdEmpresa = MainActivity.getInstance().getEmpresa();

                                if (IdEmpresa != 0) {
                                    DBPacket dbp2 = new DBPacket();
                                    int nReg2 = dbp2.getCount();
                                    dbp2.close();

                                    // Solo borramos los elementos si no hay paquetes en la bandeja de salida.
                                    // Había un error de que al crear un elemento con imagen entre la sincronización
                                    // de envío y esta parte del código, se borraba el elemento original y se volvía
                                    // a crear con otro idInterno, quedando algunos paquetes de la bandeja de salida
                                    // bloqueados al no poder encontrar su idInterno original.
                                    if (nReg2 == 0) {
                                        DBElemento db = new DBElemento();
                                        db.deleteIdExternosNulos(IdEmpresa);
                                        db.close();
                                    } 
                                }

                                // Pido los tipos de zonas de elementos
                                synchroData(GET_TIPO_ZONAS);

                            }

                            synchroData(GET_PORCENTAJES_LLENADO);

                            if (CheckPermisos.getInstance()
                                    .isPermisoValid(MainActivity.MENU_CREAR_INCIDENCIAS) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_FILTRAR_INCIDENCIAS) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_MODIFICAR_INCIDENCIAS) ||
                                    CheckPermisos.getInstance()
                                            .isPermisoValid(MainActivity.MENU_VER_INCIDENCIAS)) {
                                // Pido los tipos de incidencias
                                synchroData(GET_INCIDENCIAS_TIPO);

                                // Pido los modelos de incidencias
                                synchroData(GET_INCIDENCIAS_MODELO);

                                // Pido los motivos de incidencias
                                synchroData(GET_INCIDENCIAS_MOTIVO);

                                if (!MainActivity.getInstance().getUsuAdmin())
                                    synchroData(GET_INCIDENCIAS_PROPIETARIOS);

                                // Pido las incidencias del usuario
                                synchroData(GET_INCIDENCIAS_HISTORICO);

                                // Pido los estados en los que se encuentran las incidencias
                                synchroData(GET_INCIDENCIAS_ESTADO);
                            }

                            if (CheckPermisos.getInstance().isPermisoValid(MainActivity.MENU_VER_FLOTA) || CheckPermisos.getInstance().isPermisoValid(MainActivity.MENU_VER_FLOTA_HISTORICO)) {

                                // Pido los móviles que puede ver el usuario
                                synchroData(GET_MOVILES_USUARIO);

                                // Pido las ultimas posiciones de la flota.
                                synchroData(GET_ULTIMAS_POSICIONES);

                                // Pido la flota
                                synchroData(GET_FLOTA);

                                // Pideo las identificaciones
                                synchroData(GET_IDENTIFICACIONES);
                            }

                            // Pido todas las provincias
                            synchroData(GET_PROVINCIAS);

                            // Pido todos los municipios
                            synchroData(GET_MUNICIPIOS);

                            synchroData(GET_PLANCHADAS);

                            synchroData(GET_MOTIVOS);

                            if (CheckPermisos.getInstance().isPermisoValid(MainActivity.MENU_AREAS_PARQUES_JARDINES)
                                    || CheckPermisos.getInstance().isPermisoValid(MainActivity.MENU_PARQUES_JARDINES_MEDIDA)) {
                                // Pido grupos
                                synchroData(GET_GRUPO_AREAS);

                                // Pido areas
                                synchroData(GET_AREAS);

                                //pido filtro papeleras
                                synchroData(GET_FILTRO_PAPELERAS);
                            }

                            //Pido asociacion entradas
                            synchroData(GET_INCI_ENTRADAS);

                            //Pido fechas de procesados
                            synchroData(GET_PROCESADOS);

                            // Pido los sensores de niveles de llenado
                            synchroData(GET_SENSORES_LLENADO);
                            //synchroOperationesLevel();

                            //LAVADOSMOD Pido los lavados
                            synchroData(GET_LAVADOS);

                            // Pido los tags
                            synchroData(GET_TAGS);

                            // Obtener configuración del usuario para operaciones
                            SharedPreferences sharedPref = PreferenceManager.getDefaultSharedPreferences(MainActivity.getInstance());
                            int hoursCheckCollected = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_LEVEL, "18").trim());
                            int hoursCheckCleaned = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_TIME_OPERATION_DONE_CLEAN, "18").trim());

                            // Sincronizar operaciones de recogida y lavados
                            syncLevelAndCollectOperations(hoursCheckCollected, 0);
                            syncCleanOperations(hoursCheckCleaned, 0);

                            // Saco un mensaje de notificación
                            handler.sendEmptyMessage(MSG_END_SYNC_OK);

                            // Guardo la fecha y hora de la última sincronización
                            Config.getInstance().setValueEmpresa("ultSincro", strDate);

                        } else {

                            // Pido todas las empresas
                            synchroData(GET_EMPRESAS);

                            // Pido todos los usuarios de todas las empresas
                            synchroData(GET_USUARIOS);

                            // Saco un mensaje de notificación despues de usuarios para asegurarme que todo va bien
                            if (!isSincronizarFail)
                                handler.sendEmptyMessage(MSG_INI_SYNC_EMPRESAS);

                            // Pido todas las provincias
                            synchroData(GET_PROVINCIAS);

                            // Pido todos los municipios
                            synchroData(GET_MUNICIPIOS);

                            if (!isSincronizarFail)
                                // Saco un mensaje de notificación
                                handler.sendEmptyMessage(MSG_END_SYNC_EMPRESAS_OK);
                        }

                        // Marco que ha terminado el proceso de
                        // sincronización
                        synchro = false;
                    }

                    force_sync = false;

                    // Espero durante el tiempo establecido salvo que se
                    // finalice o se fuerce la sincronización
                    for (int i = 0; i < TIME_SYNC_OK && running && !force_sync; i++) {
                        sleep(1000);
                        // Cada 60 segundos pido las incidencias del usuario
                        if (i != 0 && i % 60 == 0 && MainActivity.getInstance().getUsuario() != 0) {
                            // if (CheckPermisos.getInstance()
                            //       .isPermisoValid(MainActivity.MENU_ASIGNAR_INCIDENCIAS)) {

                            // Pido los estados en los que se encuentran las incidencias
                            synchroData(GET_INCIDENCIAS_ESTADO);

                            // Pido las incidencias del usuario
                            synchroData(GET_INCIDENCIAS_HISTORICO);

                            // Pido los tipos de incidencias
                            synchroData(GET_INCIDENCIAS_TIPO);

                            // Pido los modelos de incidencias
                            synchroData(GET_INCIDENCIAS_MODELO);

                            // Pido los motivos de incidencias
                            synchroData(GET_INCIDENCIAS_MOTIVO);

                            if (!MainActivity.getInstance().getUsuAdmin())
                                synchroData(GET_INCIDENCIAS_PROPIETARIOS);

                            // }
                        }

                        int time_sync = Integer.parseInt(sharedPref.getString(SettingsActivity.KEY_PREF_SYNC_CONN, "15")) * 60;
                        if (time_sync != TIME_SYNC_OK)
                            TIME_SYNC_OK = time_sync;

                    }

                } catch (InterruptedException e) {
                    synchro = false;
                    force_sync = false;
                    handler.sendEmptyMessage(MSG_HTTP_ERROR);
                } catch (Throwable e) {
                    MyLoggerHandler.getInstance().error(e);
                    // Marco que ha terminado el proceso de sincronización
                    synchro = false;
                    force_sync = false;

                    // Si se trata de un error controlado saco un mensaje de notificación
                    if (e instanceof TimeoutException)
                        handler.sendEmptyMessage(MSG_TIMEOUT_ERROR);
                    else if (e instanceof HttpException)
                        handler.sendEmptyMessage(MSG_HTTP_ERROR);

                    // Saco un mensaje de notificación
                    handler.sendEmptyMessage(MSG_END_SYNC_ERROR);

                    // Espero durante el tiempo establecido salvo que se
                    // finalice o se fuerce la sincronización
                    // Intervalo de sincronización cuando algo ha fallado
                    int TIME_SYNC_ERROR = 300;
                    for (int i = 0; i < TIME_SYNC_ERROR && running
                            && !force_sync; i++)
                        sleep(1000);
                }

                force_sync = false;
            }

            running = false;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public static JSONArray removeDuplicates(JSONArray jsonArray, String idKey, String dateKey) {
        try {
            // Crear una lista de elementos únicos
            List<JSONObject> uniqueElements = new ArrayList<>();

            // Ordenar el JSONArray por fecha (más recientes primero)
            List<JSONObject> sortedArray = new ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                sortedArray.add(jsonArray.getJSONObject(i));
            }
            Collections.sort(sortedArray, (o1, o2) -> {
                try {
                    String date1 = o1.getString(dateKey);
                    String date2 = o2.getString(dateKey);
                    return date2.compareTo(date1);
                } catch (JSONException e) {
                    MyLoggerHandler.getInstance().error(e);
                }
                return 0;
            });

            // Recorrer el JSONArray ordenado y guardar los elementos únicos por la clave de identificación
            for (JSONObject jsonObject : sortedArray) {
                Object idValue = jsonObject.get(idKey);

                boolean isDuplicate = false;
                for (JSONObject uniqueElement : uniqueElements) {
                    if (idValue.equals(uniqueElement.get(idKey))) {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate) {
                    uniqueElements.add(jsonObject);
                }
            }

            // Crear un nuevo JSONArray con los elementos únicos
            JSONArray uniqueArray = new JSONArray();
            for (JSONObject element : uniqueElements) {
                uniqueArray.put(element);
            }

            // Devolver el JSONArray con los elementos únicos según el criterio de la clave de identificación y fecha más reciente
            return uniqueArray;

        } catch (JSONException e) {
            MyLoggerHandler.getInstance().error(e);
        }

        // En caso de error, devolver el JSONArray original sin modificaciones
        return jsonArray;
    }

    public Elemento getTemporal(ArrayList<Elemento> elementosSinIdExterno, Elemento
            elementoServidor) {
        if (elementosSinIdExterno.size() == 0) {
            return null;
        }

        // Filtra por latitud y longitud si hay más de uno
        ArrayList<Elemento> elementosFiltradosPorUbicacion = new ArrayList<>();
        for (Elemento e : elementosSinIdExterno) {
            if (e.getPosition().latitude == elementoServidor.getPosition().latitude &&
                    e.getPosition().longitude == elementoServidor.getPosition().longitude) {
                elementosFiltradosPorUbicacion.add(e);
            }
        }

        // Si después de filtrar por ubicación solo queda uno, lo devuelve
        if (elementosFiltradosPorUbicacion.size() == 1) {
            return elementosFiltradosPorUbicacion.get(0);
        }

        // Si todavía hay más de uno, filtra por tag
        for (Elemento e : elementosFiltradosPorUbicacion) {
            if (e.getTag().equals(elementoServidor.getTag())) {
                return e;
            }
        }

        return null;
    }

}
