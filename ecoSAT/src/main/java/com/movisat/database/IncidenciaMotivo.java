package com.movisat.database;

public class IncidenciaMotivo extends Object {
	private int id;
	private int idExterno;
	private int empresa;
	private int modelo;
	private String nombre;

	public IncidenciaMotivo(int id, int idExterno, int empresa, int modelo, String nombre) {

		setId(id);
		setIdExterno(idExterno);
		setEmpresa(empresa);
		setModelo(modelo);
		setNombre(nombre);
	}

	public void setId(int id) {

		this.id = id;
	}

	public void setEmpresa(int empresa) {

		this.empresa = empresa;
	}

	public void setModelo(int tipo) {

		this.modelo = tipo;
	}

	public void setNombre(String nombre) {

		this.nombre = nombre;
	}

	public int getId() {

		return id;
	}

	public int getEmpresa() {

		return empresa;
	}

	public int getModelo() {

		return modelo;
	}

	public String getNombre() {

		return nombre;
	}

	public int getIdExterno() {
		return idExterno;
	}

	public void setIdExterno(int idExterno) {
		this.idExterno = idExterno;
	}

	@Override
	public String toString() {

		return nombre;
	}

}