package com.movisat.database;

import com.movisat.utilities.HelperDates;

/**
 * Created by faroca on 28/06/2017.
 */

public class OperationsDone {

    private int codeMobile;
    private int codeOperation;
    private int idInternoElemento;
    private String description;
    private String value;
    private long date;
    private double lat;
    private double lng;

    public OperationsDone(int codeMobile, int codeOperation, int idInternoElemento,
                          String description, String value, long date, double lat, double lng) {
        this.codeMobile = codeMobile;
        this.codeOperation = codeOperation;
        this.idInternoElemento = idInternoElemento;
        this.description = description;
        this.value = value;
        this.date = date;
        this.lng = lng;
        this.lat = lat;
    }

    public int getCodeMobile() {
        return codeMobile;
    }

    public void setCodeMobile(int codeMobile) {
        this.codeMobile = codeMobile;
    }

    public int getIdInternoElemento() {
        return idInternoElemento;
    }

    public void setIdInternoElemento(int idInternoElemento) {
        this.idInternoElemento = idInternoElemento;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public long getDate() {
        // Se redondea para evitar duplicados en la base de datos
        return (date / 1000) * 1000;
    }

    public void setDate(long date) {
        this.date = date;
    }

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public double getLng() {
        return lng;
    }

    public void setLng(double lng) {
        this.lng = lng;
    }

    public int getCodeOperation() {
        return codeOperation;
    }

    public void setCodeOperation(int codeOperation) {
        this.codeOperation = codeOperation;
    }

    @Override
    public String toString() {
        return this.description + getValue() + "\n" + HelperDates.getInstance().getDateStringBy(date);
    }
}

