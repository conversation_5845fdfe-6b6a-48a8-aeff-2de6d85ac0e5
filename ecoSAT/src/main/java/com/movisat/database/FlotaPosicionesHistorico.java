package com.movisat.database;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.utilities.Utils;

import java.util.Date;

public class FlotaPosicionesHistorico extends  ItemMapa{


    int idRuta, vel;
    double lat, lon, tiempoParada;
    int idMovil, empresa, rumbo, altura;
    private LatLng position;
    String fecha;


    public FlotaPosicionesHistorico(int idRuta, int idMovil, int empresa, double lat, double lon, int vel, int altura, int rumbo, String fecha, double tiempo) {
        this.idRuta = idRuta;
        this.idMovil = idMovil;
        this.empresa = empresa;
        this.lat = lat;
        this.lon = lon;
        this.vel = vel;
        this.altura = altura;
        this.rumbo = rumbo;
        this.fecha = fecha;
        this.tiempoParada = tiempo;
        setPosition(lat, lon);
    }

    @Override
    public String getTipoElemento() {
        return "R";
    }

    @Override
    public int getId() {
        return 0;
    }

    @Override
    public int getIdExterno() {
        return 0;
    }

    @Override
    public void setIdExterno(int idExterno) {

    }


    public String getFecha() {

        return this.fecha;
    }

    public int getVelocidad() {

        return this.vel;
    }

    public double getTiempoParada() {

        return this.tiempoParada;
    }
}
