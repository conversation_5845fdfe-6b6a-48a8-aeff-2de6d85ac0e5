package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.log.Logg;
import com.movisat.utilities.Database;
import com.movisat.utils.Utilss;

import java.util.ArrayList;
import java.util.Calendar;

public class DBElemento {
    private static final String TAG = "DBElemento";
    private static final String TABLE_NAME = "elementos";
    private SQLiteDatabase db = null;
    private final String[] campos =
            (new String[]{
                    "id",
                    "idExterno",
                    "empresa",
                    "nombre",
                    "modelo",
                    "matricula",
                    "estado",
                    "lat",
                    "lon",
                    "descripcion",
                    "nivel_critico",
                    "elemento_similar",
                    "diabloq_lunes",
                    "diabloq_martes",
                    "diabloq_miercoles",
                    "diabloq_jueves",
                    "diabloq_viernes",
                    "diabloq_sabado",
                    "diabloq_domingo",
                    "tipo_zona",
                    "rotativo",
                    "vacia_bajo_demanda",
                    "tieneImagen",
                    "codFisico",
                    "fechaUltRecogida",
                    "fechaUltLavado",
                    "frecuenciaProcesado",
                    "volum_imei"
            });

    private final int COL_ID = 0;
    private final int COL_ID_EXTERNO = 1;
    private final int COL_EMPRESA = 2;
    private final int COL_NOMBRE = 3;
    private final int COL_MODELO = 4;
    private final int COL_MATRICULA = 5;
    private final int COL_ESTADO = 6;
    private final int COL_LATITUD = 7;
    private final int COL_LONGITUD = 8;
    private final int COL_DESCRIPCION = 9;
    private final int COL_NIVEL_CRITICO = 10;
    private final int COL_ELEMENTO_SIMILAR = 11;
    private final int COL_LUNES = 12;
    private final int COL_MARTES = 13;
    private final int COL_MIERCOLES = 14;
    private final int COL_JUEVES = 15;
    private final int COL_VIERNES = 16;
    private final int COL_SABADO = 17;
    private final int COL_DOMINGO = 18;
    private final int COL_TIPO_ZONA = 19;
    private final int COL_ROTATIVO = 20;
    private final int COL_VACIA_BAJO_DEMANDA = 21;
    private final int COL_TIENE_IMAGEN = 22;
    private final int COL_COD_FISICO = 23;
    private final int COL_FECHA_REC = 24;
    private final int COL_FECHA_LAV = 25;
    private final int COL_FRECUENCIA_DE_PROCESADO = 26;
    private final int COL_VOLUM_IMEI = 27;

    public DBElemento() {
        try {
            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public DBElemento(boolean readOnly) {
        try {
            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READONLY);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void close() {
        try {
            if (db != null)
                db.close();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public long insert(Elemento reg) {
        long res = 0;

        if (reg.getIdExterno() < 1) {
            Cursor c = db.rawQuery("SELECT MIN(idExterno) FROM elementos", null);
            if (c.moveToFirst()) {
                int idExterno = c.getInt(0);
                if (idExterno < 1) {
                    reg.setIdExterno(idExterno - 1);
                }
            }
            c.close();
        }

        try {
            ContentValues values = new ContentValues();
            values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_MATRICULA], reg.getMatricula());
            values.put(campos[COL_MODELO], reg.getModelo());
            values.put(campos[COL_ESTADO], reg.getEstado());
            values.put(campos[COL_LATITUD], reg.getPosition().latitude);
            values.put(campos[COL_LONGITUD], reg.getPosition().longitude);
            values.put(campos[COL_DESCRIPCION], reg.getDescripcion());
            values.put(campos[COL_NIVEL_CRITICO], reg.getEstadoCritico());
            values.put(campos[COL_ELEMENTO_SIMILAR], reg.getIdElementoSimilar());
            values.put(campos[COL_LUNES], reg.getDiaBloqueo(1));
            values.put(campos[COL_MARTES], reg.getDiaBloqueo(2));
            values.put(campos[COL_MIERCOLES], reg.getDiaBloqueo(3));
            values.put(campos[COL_JUEVES], reg.getDiaBloqueo(4));
            values.put(campos[COL_VIERNES], reg.getDiaBloqueo(5));
            values.put(campos[COL_SABADO], reg.getDiaBloqueo(6));
            values.put(campos[COL_DOMINGO], reg.getDiaBloqueo(7));
            values.put(campos[COL_ROTATIVO], reg.esRotativo());
            values.put(campos[COL_VACIA_BAJO_DEMANDA], reg.getVaciaBajoDemanda());
            values.put(campos[COL_TIENE_IMAGEN], reg.getTieneImagen());
            values.put(campos[COL_COD_FISICO], reg.getCodFisico());
            values.put(campos[COL_FECHA_REC], reg.getFechaUltRecogida());
            values.put(campos[COL_FECHA_LAV], reg.getFechaUltLavado());
            values.put(campos[COL_FRECUENCIA_DE_PROCESADO], reg.getFrecuenciaProcesado());
            values.put(campos[COL_VOLUM_IMEI], reg.getVolumImei());
            res = db.insert(TABLE_NAME, null, values);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public boolean update(Elemento reg) {
        if (reg == null)
            return false;
        if (reg.getId() <= 0 && reg.getIdExterno() <= 0)
            return false;
        boolean res = false;

        try {
            ContentValues values = new ContentValues();
            values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_MATRICULA], reg.getMatricula());
            values.put(campos[COL_MODELO], reg.getModelo());
            values.put(campos[COL_ESTADO], reg.getEstado());
            values.put(campos[COL_LATITUD], reg.getPosition().latitude);
            values.put(campos[COL_LONGITUD], reg.getPosition().longitude);
            values.put(campos[COL_DESCRIPCION], reg.getDescripcion());
            values.put(campos[COL_NIVEL_CRITICO], reg.getEstadoCritico());
            values.put(campos[COL_ELEMENTO_SIMILAR], reg.getIdElementoSimilar());
            values.put(campos[COL_LUNES], reg.getDiaBloqueo(1));
            values.put(campos[COL_MARTES], reg.getDiaBloqueo(2));
            values.put(campos[COL_MIERCOLES], reg.getDiaBloqueo(3));
            values.put(campos[COL_JUEVES], reg.getDiaBloqueo(4));
            values.put(campos[COL_VIERNES], reg.getDiaBloqueo(5));
            values.put(campos[COL_SABADO], reg.getDiaBloqueo(6));
            values.put(campos[COL_DOMINGO], reg.getDiaBloqueo(7));
            values.put(campos[COL_TIPO_ZONA], reg.getTipoZona());
            values.put(campos[COL_ROTATIVO], reg.esRotativo());
            values.put(campos[COL_VACIA_BAJO_DEMANDA], reg.getVaciaBajoDemanda());
            values.put(campos[COL_TIENE_IMAGEN], reg.getTieneImagen());
            values.put(campos[COL_COD_FISICO], reg.getCodFisico());
            values.put(campos[COL_FECHA_REC], reg.getFechaUltRecogida());
            values.put(campos[COL_FECHA_LAV], reg.getFechaUltLavado());
            values.put(campos[COL_FRECUENCIA_DE_PROCESADO], reg.getFrecuenciaProcesado());
            values.put(campos[COL_VOLUM_IMEI], reg.getVolumImei());

            int rowsAffected;
            if (reg.getId() > 0) {
                rowsAffected = db.update(
                      TABLE_NAME,
                      values,
                      campos[COL_ID]
                            + "="
                            + reg.getId()
                            + " AND "
                            + campos[COL_EMPRESA]
                            + "="
                            + reg.getEmpresa(),
                      null);
            } else {
                rowsAffected = db.update(
                      TABLE_NAME,
                      values,
                      campos[COL_ID_EXTERNO]
                            + "="
                            + reg.getIdExterno()
                            + " AND "
                            + campos[COL_EMPRESA]
                            + "="
                            + reg.getEmpresa(),
                      null);
            }
            res = rowsAffected > 0;
        } catch (Throwable e) {
            Logg.error("[" + getClass() + "] Error SQLITE UPDATE, tabla: ELEMENTOS... \n" + e.getMessage());
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    // Tener cuidado con esta función, puede borrar elementos de paquetes de envío que todavía no han
    // sido procesados y generar errores.
     public boolean deleteIdExternosNulos(int IdEmpresa) {
         boolean res = false;

         try {
             if (db.delete(TABLE_NAME, campos[COL_ID_EXTERNO] + "=0" + " AND "
                     + campos[COL_EMPRESA] + "=" + IdEmpresa, null) > 0)
                 res = true;

         } catch (Throwable e) {
             MyLoggerHandler.getInstance().error(e);
         }
         return res;
     }

    public boolean delete(Elemento reg) {
        boolean res = false;

        try {
            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
                            + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

            if (!res) {
                if (db.delete(
                        TABLE_NAME,
                        campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno()
                                + " AND " + campos[COL_EMPRESA] + "="
                                + reg.getEmpresa(), null) > 0)
                    res = true;
            }

        } catch (Throwable e) {
            Logg.error(TAG, "Error al borrar el elemento: " + e.getMessage());
        }
        return res;
    }

    public boolean exists(Elemento reg) {
        boolean res = false;

        try {
            Cursor cursor = db.query(
                    TABLE_NAME,
                    campos,
                    campos[COL_ID] + "=" + reg.getId(),
                    null, null, null, null);
            if (cursor != null && cursor.getCount() > 0)
                res = true;

            if (!res) {
                cursor = db.query(
                        TABLE_NAME,
                        campos,
                        campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno()
                                + " AND " + campos[COL_EMPRESA] + "="
                                + reg.getEmpresa(),
                        null, null, null, null);
                if (cursor != null && cursor.getCount() > 0)
                    res = true;
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public int getCount(int empresa) {
        int res = 0;
        Cursor cur;

        try {
            cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                    campos[COL_EMPRESA] + "=" + empresa, null, null, null, null);
            if (cur != null) {
                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public void deleteAll(int empresa) {
        try {
            db.delete(TABLE_NAME, "empresa=" + empresa, null);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public Elemento getByIdInterno(int id, int empresa) {
        if (id < 1)
            return null;
        Cursor cur;
        Elemento res = null;

        try {
            // Busco por el código temporal por si se trata de un
            // elemento que todavía no se ha sincronizado
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Elemento(
                            cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA),
                            cur.getString(COL_NOMBRE),
                            cur.getInt(COL_MODELO),
                            cur.getString(COL_MATRICULA),
                            cur.getInt(COL_ESTADO),
                            cur.getDouble(COL_LATITUD),
                            cur.getDouble(COL_LONGITUD),
                            cur.getString(COL_DESCRIPCION),
                            cur.getString(COL_NIVEL_CRITICO),
                            cur.getInt(COL_ELEMENTO_SIMILAR),
                            cur.getInt(COL_LUNES),
                            cur.getInt(COL_MARTES),
                            cur.getInt(COL_MIERCOLES),
                            cur.getInt(COL_JUEVES),
                            cur.getInt(COL_VIERNES),
                            cur.getInt(COL_SABADO),
                            cur.getInt(COL_DOMINGO),
                            cur.getInt(COL_TIPO_ZONA),
                            cur.getInt(COL_ROTATIVO),
                            cur.getInt(COL_VACIA_BAJO_DEMANDA),
                            cur.getInt(COL_TIENE_IMAGEN),
                            null,
                            cur.getString(COL_COD_FISICO),
                            cur.getString(COL_FECHA_REC),
                            cur.getString(COL_FECHA_LAV),
                            cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                            cur.getString(COL_VOLUM_IMEI)
                    );

                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    // Recupero el array de elementos que ha de aparecer en el mapa marcado, si éste está en el día
    // de bloqueo asignado al elemento, y si el modelo de ese elemento es visible.
    public ArrayList<Elemento> getByDiasBloqueo(int dia, int empresa, double lat1, double lon1, double lat2, double lon2) {
        ArrayList<Elemento> elementos = new ArrayList<>();
        Cursor cur;

        String clausula_where = "";
        switch (dia) {
            case Calendar.SUNDAY:
                clausula_where = "AND " + campos[COL_DOMINGO] + " IN (1,2,3) ";
                break;
            case Calendar.MONDAY:
                clausula_where = "AND " + campos[COL_LUNES] + " IN (1,2,3) ";
                break;
            case Calendar.TUESDAY:
                clausula_where = "AND " + campos[COL_MARTES] + " IN (1,2,3) ";
                break;
            case Calendar.WEDNESDAY:
                clausula_where = "AND " + campos[COL_MIERCOLES] + " IN (1,2,3) ";
                break;
            case Calendar.THURSDAY:
                clausula_where = "AND " + campos[COL_JUEVES] + " IN (1,2,3) ";
                break;
            case Calendar.FRIDAY:
                clausula_where = "AND " + campos[COL_VIERNES] + " IN (1,2,3) ";
                break;
            case Calendar.SATURDAY:
                clausula_where = "AND " + campos[COL_SABADO] + " IN (1,2,3) ";
                break;
        }


        // Busco por el código temporal por si se trata de un elemento que todavia no se ha sincronizado
        if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa + " AND (" + campos[COL_LATITUD] + ">=" + lat1 + " AND " +
                        campos[COL_LATITUD] + "<=" + lat2 + " AND " +
                        campos[COL_LONGITUD] + ">=" + lon1 + " AND " +
                        campos[COL_LONGITUD] + " <=" + lon2 + ") " + clausula_where,
                null, null, null, null)) != null) {

            if (cur.moveToFirst())
                do {
                    elementos.add(new Elemento(
                                    cur.getInt(COL_ID),
                                    cur.getInt(COL_ID_EXTERNO),
                                    cur.getInt(COL_EMPRESA),
                                    cur.getString(COL_NOMBRE),
                                    cur.getInt(COL_MODELO),
                                    cur.getString(COL_MATRICULA),
                                    cur.getInt(COL_ESTADO),
                                    cur.getDouble(COL_LATITUD),
                                    cur.getDouble(COL_LONGITUD),
                                    cur.getString(COL_DESCRIPCION),
                                    cur.getString(COL_NIVEL_CRITICO),
                                    cur.getInt(COL_ELEMENTO_SIMILAR),
                                    cur.getInt(COL_LUNES),
                                    cur.getInt(COL_MARTES),
                                    cur.getInt(COL_MIERCOLES),
                                    cur.getInt(COL_JUEVES),
                                    cur.getInt(COL_VIERNES),
                                    cur.getInt(COL_SABADO),
                                    cur.getInt(COL_DOMINGO),
                                    cur.getInt(COL_TIPO_ZONA),
                                    cur.getInt(COL_ROTATIVO),
                                    cur.getInt(COL_VACIA_BAJO_DEMANDA),
                                    cur.getInt(COL_TIENE_IMAGEN),
                                    null,
                                    cur.getString(COL_COD_FISICO),
                                    cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                                    cur.getString(COL_VOLUM_IMEI)
                            )
                    );
                } while (cur.moveToNext());
            cur.close();
        }
        return elementos;
    }

    public Elemento getByIdExterno(int id, int empresa) {
        Cursor cur;
        Elemento res = null;

        try {
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO]
                            + "=" + id + " AND " + campos[COL_EMPRESA] + "=" + empresa,
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Elemento(
                            cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA),
                            cur.getString(COL_NOMBRE),
                            cur.getInt(COL_MODELO),
                            cur.getString(COL_MATRICULA),
                            cur.getInt(COL_ESTADO),
                            cur.getDouble(COL_LATITUD),
                            cur.getDouble(COL_LONGITUD),
                            cur.getString(COL_DESCRIPCION),
                            cur.getString(COL_NIVEL_CRITICO),
                            cur.getInt(COL_ELEMENTO_SIMILAR),
                            cur.getInt(COL_LUNES),
                            cur.getInt(COL_MARTES),
                            cur.getInt(COL_MIERCOLES),
                            cur.getInt(COL_JUEVES),
                            cur.getInt(COL_VIERNES),
                            cur.getInt(COL_SABADO),
                            cur.getInt(COL_DOMINGO),
                            cur.getInt(COL_TIPO_ZONA),
                            cur.getInt(COL_ROTATIVO),
                            cur.getInt(COL_VACIA_BAJO_DEMANDA),
                            cur.getInt(COL_TIENE_IMAGEN),
                            null,
                            cur.getString(COL_COD_FISICO),
                            cur.getString(COL_FECHA_REC),
                            cur.getString(COL_FECHA_LAV),
                            cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                            cur.getString(COL_VOLUM_IMEI)
                    );
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public ArrayList<Elemento> getSinIdExterno(int empresa) {
        ArrayList<Elemento> elementos = new ArrayList<Elemento>();
        Cursor cur;

        try {
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO]
                            + "< 1 AND " + campos[COL_EMPRESA] + "=" + empresa,
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    do {
                        elementos.add(new Elemento(
                              cur.getInt(COL_ID),
                              cur.getInt(COL_ID_EXTERNO),
                              cur.getInt(COL_EMPRESA),
                              cur.getString(COL_NOMBRE),
                              cur.getInt(COL_MODELO),
                              cur.getString(COL_MATRICULA),
                              cur.getInt(COL_ESTADO),
                              cur.getDouble(COL_LATITUD),
                              cur.getDouble(COL_LONGITUD),
                              cur.getString(COL_DESCRIPCION),
                              cur.getString(COL_NIVEL_CRITICO),
                              cur.getInt(COL_ELEMENTO_SIMILAR),
                              cur.getInt(COL_LUNES),
                              cur.getInt(COL_MARTES),
                              cur.getInt(COL_MIERCOLES),
                              cur.getInt(COL_JUEVES),
                              cur.getInt(COL_VIERNES),
                              cur.getInt(COL_SABADO),
                              cur.getInt(COL_DOMINGO),
                              cur.getInt(COL_TIPO_ZONA),
                              cur.getInt(COL_ROTATIVO),
                              cur.getInt(COL_VACIA_BAJO_DEMANDA),
                              cur.getInt(COL_TIENE_IMAGEN),
                              null,
                              cur.getString(COL_COD_FISICO),
                              cur.getString(COL_FECHA_REC),
                              cur.getString(COL_FECHA_LAV),
                              cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                              cur.getString(COL_VOLUM_IMEI)));
                    } while (cur.moveToNext());
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return elementos;
    }

//    public Elemento getTemporal(Elemento elementoServidor) {
//        ArrayList<Elemento> elementosSinIdExterno = getSinIdExterno(elementoServidor.getEmpresa());
//
//        if (elementosSinIdExterno.size() == 0){
//            return null;
//        }
//
//        if (elementosSinIdExterno.size() == 1) {
//            return elementosSinIdExterno.get(0);
//        }
//
//        // Filtra por latitud y longitud si hay más de uno
//        ArrayList<Elemento> elementosFiltradosPorUbicacion = new ArrayList<>();
//        for (Elemento e : elementosSinIdExterno) {
//            if (e.getPosition().latitude == elementoServidor.getPosition().latitude &&
//                  e.getPosition().longitude == elementoServidor.getPosition().longitude) {
//                elementosFiltradosPorUbicacion.add(e);
//            }
//        }
//
//        // Si después de filtrar por ubicación solo queda uno, lo devuelve
//        if (elementosFiltradosPorUbicacion.size() == 1) {
//            return elementosFiltradosPorUbicacion.get(0);
//        }
//
//        // Si todavía hay más de uno, filtra por tag
//        for (Elemento e : elementosFiltradosPorUbicacion) {
//            if (e.getTag().equals(elementoServidor.getTag())) {
//                return e;
//            }
//        }
//
//        return null;
//    }


//    public ArrayList<ItemMapa> getByLatLon(int empresa, double lat1,
//                                           double lon1, double lat2, double lon2, String models) {
//        Cursor cur;
//        ArrayList<ItemMapa> res = new ArrayList<>();
//
//        try {
//            if (!Utilss.isNullOrEmpty(models))
//                models = " AND " + campos[COL_MODELO] + " IN (" + models + ") ";
//
//            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
//                            + empresa + " AND " + campos[COL_LATITUD] + ">=" + lat1
//                            + " AND " + campos[COL_LATITUD] + "<=" + lat2 + " AND "
//                            + campos[COL_LONGITUD] + ">=" + lon1 + " AND "
//                            + campos[COL_LONGITUD] + " <=" + lon2 + models, null, null,
//                    null, campos[COL_LATITUD] + "," + campos[COL_LONGITUD]
//                            + " ASC")) != null) {
//
//                if (cur.moveToFirst()) {
//                    do {
//                        res.add(new Elemento(
//                                cur.getInt(COL_ID),
//                                cur.getInt(COL_ID_EXTERNO),
//                                cur.getInt(COL_EMPRESA),
//                                cur.getString(COL_NOMBRE),
//                                cur.getInt(COL_MODELO),
//                                cur.getString(COL_MATRICULA),
//                                cur.getInt(COL_ESTADO),
//                                cur.getDouble(COL_LATITUD),
//                                cur.getDouble(COL_LONGITUD),
//                                cur.getString(COL_DESCRIPCION),
//                                cur.getString(COL_FECHA_REC),
//                                cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
//                                cur.getString(COL_VOLUM_IMEI)
//                        ));
//
//                    } while (cur.moveToNext());
//                }
//                cur.close();
//            }
//        } catch (Throwable e) {
//            MyLoggerHandler.getInstance().error(e);
//        }
//        return res;
//    }


    public ArrayList<ItemMapa> getByLatLon(int empresa, double lat1, double lon1,
                                           double lat2, double lon2, String models,
                                           int estado_elemento) {
        Cursor cur;
        ArrayList<ItemMapa> res = new ArrayList<>();

        try {
            if (models.equals("todos"))
                models = "";
            else {
                models = " AND " + campos[COL_MODELO] + " IN (" + models + ") ";
            }

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ESTADO] + "=" + estado_elemento
                        + " AND " + campos[COL_EMPRESA] + "=" + empresa
                        + " AND " + campos[COL_LATITUD] + ">=" + lat1
                        + " AND " + campos[COL_LATITUD] + "<=" + lat2
                        + " AND " + campos[COL_LONGITUD] + ">=" + lon1
                        + " AND " + campos[COL_LONGITUD] + "<=" + lon2
                        + models, null, null, null, null)) != null) {

                if (cur.moveToFirst()) {
                    do {
                        res.add(new Elemento(
                                cur.getInt(COL_ID),
                                cur.getInt(COL_ID_EXTERNO),
                                cur.getInt(COL_EMPRESA),
                                cur.getString(COL_NOMBRE),
                                cur.getInt(COL_MODELO),
                                cur.getString(COL_MATRICULA),
                                cur.getInt(COL_ESTADO),
                                cur.getDouble(COL_LATITUD),
                                cur.getDouble(COL_LONGITUD),
                                cur.getString(COL_DESCRIPCION),
                                cur.getString(COL_FECHA_REC),
                                cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                                cur.getString(COL_VOLUM_IMEI)
                        ));
                    } while (cur.moveToNext());
                }
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public Elemento getElementoBy(String name, int empresa) {
        Cursor cur;
        Elemento res = null;

        try {
            name = name.replaceAll("'", "''");

            if ((cur = db.query(TABLE_NAME, campos, "upper("
                            + campos[COL_NOMBRE] + ")='" + name.toUpperCase()
                            + "' AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Elemento(
                            cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA),
                            cur.getString(COL_NOMBRE),
                            cur.getInt(COL_MODELO),
                            cur.getString(COL_MATRICULA),
                            cur.getInt(COL_ESTADO),
                            cur.getDouble(COL_LATITUD),
                            cur.getDouble(COL_LONGITUD),
                            cur.getString(COL_DESCRIPCION),
                            cur.getString(COL_FECHA_REC),
                            cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                            cur.getString(COL_VOLUM_IMEI)
                    );
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public Elemento getElementoByTag(Tags tag) {
        Elemento elemento = null;

        try {
            if (tag != null) {
                if (tag.getIdExternoElemento() > 0)
                    elemento = getByIdExterno(tag.getIdExternoElemento(), MainActivity.getInstance().getEmpresa());
                else if (tag.getIdInternoElemento() > 0)
                    elemento = getByIdInterno(tag.getIdInternoElemento(), MainActivity.getInstance().getEmpresa());
                if (elemento == null && !Utilss.isNullOrEmpty(tag.getMatricula())) {
                    elemento = getElementoByMatricula(tag.getMatricula(), MainActivity.getInstance().getEmpresa());
                }
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return elemento;
    }

    public ArrayList<ItemMapa> getAll(int empresa) {
        Cursor cur;
        ArrayList<ItemMapa> res = null;

//        try {
//            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
//                    + empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {
//
//                if (cur.moveToFirst()) {
//                    res = new ArrayList<>();
//                    do {
//                        res.add(new Elemento(
//                              cur.getInt(COL_ID),
//                              cur.getInt(COL_ID_EXTERNO),
//                              cur.getInt(COL_EMPRESA),
//                              cur.getString(COL_NOMBRE),
//                              cur.getInt(COL_MODELO),
//                              cur.getString(COL_MATRICULA),
//                              cur.getInt(COL_ESTADO),
//                              cur.getDouble(COL_LATITUD),
//                              cur.getDouble(COL_LONGITUD),
//                              cur.getString(COL_DESCRIPCION),
//                              cur.getString(COL_FECHA_REC),
//                              cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
//                              cur.getString(COL_VOLUM_IMEI)
//                        ));
//                    } while (cur.moveToNext());
//                }
//                cur.close();
//            }
//        } catch (Throwable e) {
//            MyLoggerHandler.getInstance().error(e);
//        }
        return res;
    }

    public ArrayList<ItemMapa> getAllByModel(int empresa, String models) {
        Cursor cur;
        ArrayList<ItemMapa> res = null;

        try {
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa
                  + " AND " + campos[COL_MODELO] + " IN(" + models + ")",
                  null, null, null, null)) != null) {

                if (cur.moveToFirst()) {
                    res = new ArrayList<>();
                    do {
                        res.add(new Elemento(cur.getInt(COL_ID),
                                cur.getInt(COL_ID_EXTERNO),
                                cur.getInt(COL_EMPRESA),
                                cur.getString(COL_NOMBRE),
                                cur.getInt(COL_MODELO),
                                cur.getString(COL_MATRICULA),
                                cur.getInt(COL_ESTADO),
                                cur.getDouble(COL_LATITUD),
                                cur.getDouble(COL_LONGITUD),
                                cur.getString(COL_DESCRIPCION),
                                cur.getString(COL_FECHA_REC),
                                cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                                cur.getString(COL_VOLUM_IMEI)
                        ));
                    } while (cur.moveToNext());
                }
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public ArrayList<Elemento> getAllByModelElemento(int empresa, String models) {
        Cursor cur;
        ArrayList<Elemento> res = null;
        String clausula_where = "";

        try {
            if (!models.equalsIgnoreCase("todos")) {
                clausula_where = " AND " + campos[COL_MODELO] + " IN(" + models + ")";
            }

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa + clausula_where, null, null, null, null)) != null) {

                if (cur.moveToFirst()) {
                    res = new ArrayList<>();
                    do {
                        res.add(new Elemento(cur.getInt(COL_ID),
                                cur.getInt(COL_ID_EXTERNO),
                                cur.getInt(COL_EMPRESA),
                                cur.getString(COL_NOMBRE),
                                cur.getInt(COL_MODELO),
                                cur.getString(COL_MATRICULA),
                                cur.getInt(COL_ESTADO),
                                cur.getDouble(COL_LATITUD),
                                cur.getDouble(COL_LONGITUD),
                                cur.getString(COL_DESCRIPCION),
                                cur.getString(COL_FECHA_REC),
                                cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                                cur.getString(COL_VOLUM_IMEI)
                        ));
                    } while (cur.moveToNext());
                }
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    // método encargado de devolver una lista de elementos cuyo nombre contenga el parámetro pasado
    // es usado para la búsqueda
    public ArrayList<Elemento> getAll() {
        Cursor cur;
        ArrayList<Elemento> res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, null, null, null, null, "nombre")) != null) {

                if (cur.moveToFirst()) {
                    res = new ArrayList<>();
                    do {
                        res.add(new Elemento(cur.getInt(COL_ID),
                                cur.getInt(COL_ID_EXTERNO),
                                cur.getInt(COL_EMPRESA),
                                cur.getString(COL_NOMBRE),
                                cur.getInt(COL_MODELO),
                                cur.getString(COL_MATRICULA),
                                cur.getInt(COL_ESTADO),
                                cur.getDouble(COL_LATITUD),
                                cur.getDouble(COL_LONGITUD),
                                cur.getString(COL_DESCRIPCION),
                                cur.getString(COL_FECHA_REC),
                                cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                                cur.getString(COL_VOLUM_IMEI)
                        ));
                    } while (cur.moveToNext());
                }
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public void beginTransaction() {
        try {
            db.beginTransactionNonExclusive();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void endTransaction() {
        try {
            db.endTransaction();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void commitTransaction() {
        try {
            db.setTransactionSuccessful();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public Elemento getElementoByMatricula(String matricula, int empresa) {
        Cursor cur;
        Elemento res = null;

        try {
            if ((cur = db.query(TABLE_NAME, campos, ""
                            + campos[COL_MATRICULA] + "='" + matricula
                            + "' AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Elemento(
                            cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_NOMBRE),
                            cur.getInt(COL_MODELO),
                            cur.getString(COL_MATRICULA),
                            cur.getInt(COL_ESTADO), cur.getDouble(COL_LATITUD),
                            cur.getDouble(COL_LONGITUD),
                            cur.getString(COL_DESCRIPCION),
                            cur.getString(COL_FECHA_REC),
                            cur.getInt(COL_FRECUENCIA_DE_PROCESADO),
                            cur.getString(COL_VOLUM_IMEI)
                    );
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public void removeMatriculaFromOldElemento(String matricula, int idExterno, int empresa) {
        try {
            db.execSQL("UPDATE " + TABLE_NAME + " SET " + campos[COL_MATRICULA] + " = '' WHERE "
                  + campos[COL_MATRICULA] + " = '" + matricula + "' AND " + campos[COL_ID_EXTERNO] + " != " + idExterno
                  + " AND " + campos[COL_EMPRESA] + " = " + empresa);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

}
