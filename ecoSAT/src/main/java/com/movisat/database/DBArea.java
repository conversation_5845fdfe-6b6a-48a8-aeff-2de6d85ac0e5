package com.movisat.database;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBArea {

    private final String TABLE_NAME = "areas";
    private SQLiteDatabase db = null;

    private String[] campos = (new String[]{"codigo", "empresa",
            "abreviatura", "nombre", "superficie", "radio_lado", "centro_lat",
            "centro_lon", "forma", "nombre_grupo", "grupo"});

    private final int COL_CODIGO = 0;
    private final int COL_EMPRESA = 1;
    private final int COL_ABREVIATURA = 2;
    private final int COL_NOMBRE = 3;
    private final int COL_SUPERFICIE = 4;
    private final int COL_RADIO_LADO = 5;
    private final int COL_CENTRO_LAT = 6;
    private final int COL_CENTRO_LON = 7;
    private final int COL_FORMA = 8;
    private final int COL_NOMBRE_GRUPO = 9;
    private final int COL_GRUPO = 10;

    private final String TABLE_NAME_PUNTOS = "areas_puntos";
    private String[] camposPuntos = (new String[]{"area", "empresa",
            "latitud", "longitud", "orden"});

    private final int COL_AREAS_AREA = 0;
    private final int COL_AREAS_EMPRESA = 1;
    private final int COL_AREAS_LAT = 2;
    private final int COL_AREAS_LON = 3;
    private final int COL_AREAS_ORDEN = 4;

    private final String TABLE_NAME_PUNTOS_CONTROL = "areas_puntos_control";
    private String[] camposPuntosControl = (new String[]{"area", "empresa",
            "latitud", "longitud"});
    private final int COL_PUNTO_CONTROL_AREA = 0;
    private final int COL_PUNTO_CONTROL_EMPRESA = 1;
    private final int COL_PUNTO_CONTROL_LAT = 2;
    private final int COL_PUNTO_CONTROL_LON = 3;

    public DBArea() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            e.printStackTrace();
        }

    }

    public long insert(Area reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_CODIGO], reg.getCodigo());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_ABREVIATURA], reg.getAbreviatura());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_SUPERFICIE], reg.getSuperfice());
            values.put(campos[COL_RADIO_LADO], reg.getRadioLado());
            values.put(campos[COL_CENTRO_LAT], reg.getPuntoCentro().latitude);
            values.put(campos[COL_CENTRO_LON], reg.getPuntoCentro().longitude);
            values.put(campos[COL_FORMA], reg.getForma());
            values.put(campos[COL_NOMBRE_GRUPO], reg.getNombreGrupo());
            values.put(campos[COL_GRUPO], reg.getGrupo());

            res = db.insert(TABLE_NAME, null, values);

            if (reg.getListaPuntos() != null && reg.getListaPuntos().size() > 0) {
                insertPuntos(reg);
            }

            if (reg.getListaPuntosControl() != null
                    && reg.getListaPuntosControl().size() > 0) {
                insertPuntosControl(reg);
            }

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    public boolean update(Area reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            // values.put(campos[COL_CODIGO], reg.getCodigo());
            // values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_CODIGO], reg.getCodigo());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_ABREVIATURA], reg.getAbreviatura());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_SUPERFICIE], reg.getSuperfice());
            values.put(campos[COL_RADIO_LADO], reg.getRadioLado());
            values.put(campos[COL_CENTRO_LAT], reg.getPuntoCentro().latitude);
            values.put(campos[COL_CENTRO_LON], reg.getPuntoCentro().longitude);
            values.put(campos[COL_FORMA], reg.getForma());
            values.put(campos[COL_NOMBRE_GRUPO], reg.getNombreGrupo());
            values.put(campos[COL_GRUPO], reg.getGrupo());

            if (db.update(TABLE_NAME, values,
                    campos[COL_CODIGO] + "=" + reg.getCodigo() + " AND " +
                            campos[COL_EMPRESA] + "=" + reg.getEmpresa(), null) > 0)
                res = true;

            insertPuntos(reg);
            insertPuntosControl(reg);

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    public long insertPuntos(Area area) {
        return insertPuntos(area.getCodigo(), area.getEmpresa(),
                area.getListaPuntos());
    }

    public long insertPuntosControl(Area area) {
        return insertPuntosControl(area.getCodigo(), area.getEmpresa(),
                area.getListaPuntosControl());
    }

    public long insertPuntos(int codArea, int empresa, List<LatLng> listaPuntos) {
        long res = 0;
        boolean tra = false;
        ContentValues values;
        try {

            // Borramos los puntos del area
            deletePuntos(codArea, empresa);

            int size = listaPuntos.size();

            db.beginTransaction();
            tra = true;

            for (int i = 0; i < size; i++) {

                // Insertamos los elementos en la ruta
                values = new ContentValues();
                values.put(camposPuntos[COL_AREAS_AREA], codArea);
                values.put(camposPuntos[COL_AREAS_EMPRESA], empresa);
                values.put(camposPuntos[COL_AREAS_LAT],
                        listaPuntos.get(i).latitude);
                values.put(camposPuntos[COL_AREAS_LON],
                        listaPuntos.get(i).longitude);
                values.put(camposPuntos[COL_AREAS_ORDEN], i);

                res = db.insert(TABLE_NAME_PUNTOS, null, values);
            }

            db.setTransactionSuccessful();

        } catch (Throwable e) {

            e.printStackTrace();

        } finally {

            if (tra)
                db.endTransaction();
        }

        return res;
    }

    public long insertPuntosControl(int codArea, int empresa,
                                    List<LatLng> listaPuntos) {
        long res = 0;
        boolean tra = false;
        ContentValues values;
        try {

            // Borramos los puntos del area
            deletePuntosControl(codArea, empresa);

            int size = listaPuntos.size();

            db.beginTransaction();
            tra = true;

            for (int i = 0; i < size; i++) {

                // Insertamos los puntos de control del área
                values = new ContentValues();
                values.put(camposPuntosControl[COL_PUNTO_CONTROL_AREA], codArea);
                values.put(camposPuntosControl[COL_PUNTO_CONTROL_EMPRESA],
                        empresa);
                values.put(camposPuntosControl[COL_PUNTO_CONTROL_LAT],
                        listaPuntos.get(i).latitude);
                values.put(camposPuntosControl[COL_PUNTO_CONTROL_LON],
                        listaPuntos.get(i).longitude);

                try {
                    res = db.insert(TABLE_NAME_PUNTOS_CONTROL, null, values);

                } catch (Exception ex) {
                    //Si intenta insertar y no funciona es porque ya existe.
                }
            }

            db.setTransactionSuccessful();

        } catch (Throwable e) {

            e.printStackTrace();
            MyLoggerHandler.getInstance().error(e);

        } finally {

            if (tra)
                db.endTransaction();
        }

        return res;
    }

    public boolean delete(Area reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME,
                    campos[COL_CODIGO] + "=" + reg.getCodigo(), null) > 0) {

                deletePuntos(reg);
                deletePuntosControl(reg);

                res = true;
            }

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    public boolean deletePuntos(Area area) {
        return deletePuntos(area.getCodigo(), area.getEmpresa());
    }

    public boolean deletePuntosControl(Area area) {
        return deletePuntosControl(area.getCodigo(), area.getEmpresa());
    }

    public boolean deletePuntos(int codArea, int empresa) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME_PUNTOS, camposPuntos[COL_AREAS_AREA] + "="
                    + codArea + " AND " + camposPuntos[COL_AREAS_EMPRESA] + "="
                    + empresa, null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean deletePuntosControl(int codArea, int empresa) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME_PUNTOS_CONTROL,
                    camposPuntosControl[COL_PUNTO_CONTROL_AREA] + "=" + codArea
                            + " AND "
                            + camposPuntosControl[COL_PUNTO_CONTROL_EMPRESA]
                            + "=" + empresa, null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
            e.printStackTrace();
        }

        return res;
    }

    public Area getBy(int codigo, int idEmpresa) {
        Cursor cur;
        Area res = null;
        List<LatLng> listaPuntos = null;
        List<LatLng> listaPuntosControl = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_CODIGO] + "="
                            + codigo + " AND " + campos[COL_EMPRESA] + "=" + idEmpresa,
                    null, null, null, null)) != null) {

                if (cur.moveToFirst()) {

                    listaPuntos = getPuntosBy(codigo);
                    listaPuntosControl = getPuntosControlBy(codigo);
                    res = new Area(cur.getInt(COL_CODIGO),
                            cur.getInt(COL_EMPRESA),
                            cur.getInt(COL_GRUPO),
                            cur.getString(COL_ABREVIATURA),
                            cur.getString(COL_NOMBRE),
                            cur.getInt(COL_SUPERFICIE),
                            cur.getInt(COL_RADIO_LADO), new LatLng(
                            cur.getDouble(COL_CENTRO_LAT),
                            cur.getDouble(COL_CENTRO_LON)),
                            cur.getInt(COL_FORMA), cur.getString(COL_NOMBRE_GRUPO), listaPuntos,
                            listaPuntosControl);

                    cur.close();
                }

            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public List<LatLng> getPuntosBy(int codArea) {
        Cursor cur;
        List<LatLng> listaPuntos = new LinkedList<LatLng>();

        try {

            if ((cur = db.query(TABLE_NAME_PUNTOS, camposPuntos,
                    camposPuntos[COL_AREAS_AREA] + "=" + codArea, null, null,
                    null, camposPuntos[COL_AREAS_ORDEN])) != null) {

                if (cur.moveToFirst()) {

                    do {
                        listaPuntos.add(new LatLng(
                                cur.getDouble(COL_AREAS_LAT), cur
                                .getDouble(COL_AREAS_LON)));

                    } while (cur.moveToNext());

                    cur.close();

                }
                cur.close();

            }

        } catch (Throwable e) {
            e.printStackTrace();
            MyLoggerHandler.getInstance().error(e);
        }


        return listaPuntos;
    }

    public List<LatLng> getPuntosControlBy(int codArea) {
        Cursor cur;
        List<LatLng> listaPuntos = new LinkedList<LatLng>();

        try {

            if ((cur = db
                    .query(TABLE_NAME_PUNTOS_CONTROL, camposPuntosControl,
                            camposPuntosControl[COL_PUNTO_CONTROL_AREA] + "="
                                    + codArea, null, null, null, null)) != null) {

                if (cur.moveToFirst()) {

                    do {
                        listaPuntos.add(new LatLng(cur
                                .getDouble(COL_PUNTO_CONTROL_LAT), cur
                                .getDouble(COL_PUNTO_CONTROL_LON)));

                    } while (cur.moveToNext());

                    cur.close();

                }
                cur.close();

            }

        } catch (Throwable e) {
            e.printStackTrace();
            MyLoggerHandler.getInstance().error(e);
        }

        return listaPuntos;
    }

    public List<String> getNombreGrupos() {
        List<Area> areas = getAll(true);
        List<String> nombres = new ArrayList<>();
        int contador = 0;
        String nombreGrupo = "";
        for (Area item : areas
                ) {
            nombreGrupo = item.getNombreGrupo();
            if (nombreGrupo == null || nombreGrupo.equals(""))
                nombreGrupo = item.getNombre();
            if (!nombres.contains(nombreGrupo)) {
                nombres.add(contador, nombreGrupo);
                contador++;
            }
        }

        return nombres;
    }

    public List<Area> getAreasBy(int IdGrupo) {
        Cursor cur;
        Area res = null;
        List<Area> listaAreas = new LinkedList<Area>();
        List<LatLng> listaPuntos = null;
        List<LatLng> listaPuntosControl = null;

        if ((cur = db.query(TABLE_NAME, campos, campos[COL_GRUPO] + "='" + IdGrupo + "'", null, null, null,
                campos[COL_NOMBRE])) != null) {
            if (cur.moveToFirst()) {
                do {

                    listaPuntos = getPuntosBy(cur.getInt(COL_CODIGO));
                    listaPuntosControl = getPuntosControlBy(cur
                            .getInt(COL_CODIGO));
                    if (listaPuntosControl.size() <= 0)
                        continue;
                    res = new Area(cur.getInt(COL_CODIGO),
                            cur.getInt(COL_EMPRESA),
                            cur.getInt(COL_GRUPO),
                            cur.getString(COL_ABREVIATURA),
                            cur.getString(COL_NOMBRE),
                            cur.getInt(COL_SUPERFICIE),
                            cur.getInt(COL_RADIO_LADO), new LatLng(
                            cur.getDouble(COL_CENTRO_LAT),
                            cur.getDouble(COL_CENTRO_LON)),
                            cur.getInt(COL_FORMA),
                            cur.getString(COL_NOMBRE_GRUPO),
                            listaPuntos,
                            listaPuntosControl);

                    listaAreas.add(res);

                } while (cur.moveToNext());

                cur.close();
            }

        }

        return listaAreas;
    }


    public List<Area> getAll(boolean isShowPuntosControl) {
        Cursor cur;
        Area res = null;
        List<Area> listaAreas = new LinkedList<Area>();
        List<LatLng> listaPuntos = null;
        List<LatLng> listaPuntosControl = null;

        if ((cur = db.query(TABLE_NAME, campos, null, null, null, null,
                campos[COL_NOMBRE])) != null) {

            if (cur.moveToFirst()) {
                do {

                    listaPuntos = getPuntosBy(cur.getInt(COL_CODIGO));
                    listaPuntosControl = getPuntosControlBy(cur
                            .getInt(COL_CODIGO));
                    if (isShowPuntosControl) {
                        if (listaPuntosControl.size() <= 0)
                            continue;
                    }
                    res = new Area(cur.getInt(COL_CODIGO),
                            cur.getInt(COL_EMPRESA),
                            cur.getInt(COL_GRUPO),
                            cur.getString(COL_ABREVIATURA),
                            cur.getString(COL_NOMBRE),
                            cur.getInt(COL_SUPERFICIE),
                            cur.getInt(COL_RADIO_LADO), new LatLng(
                            cur.getDouble(COL_CENTRO_LAT),
                            cur.getDouble(COL_CENTRO_LON)),
                            cur.getInt(COL_FORMA),
                            cur.getString(COL_NOMBRE_GRUPO),
                            listaPuntos,
                            listaPuntosControl);

                    listaAreas.add(res);

                } while (cur.moveToNext());

                cur.close();
            }

        }

        return listaAreas;
    }

}
