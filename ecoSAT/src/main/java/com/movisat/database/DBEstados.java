package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBEstados {
	private static final String TABLE_NAME = "estados";
	private SQLiteDatabase db = null;

	private static String[] campos = (new String[] { "id", "idExterno", "empresa", "estado" });
	private static final int COL_ID = 0;
	private static final int COL_ID_EXTERNO = 1;
	private static final int COL_EMPRESA = 2;
	private static final int COL_ESTADO = 3;

	public DBEstados() {
		try {
			db = Database.getConnection(
					MainActivity.getInstance().getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}
	}

	public void close() {
		try {
			if (db != null)
				db.close();
		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}
	}

	public long insert(Estado reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_ESTADO], reg.getNombre());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(Estado reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_ESTADO], reg.getNombre());

			if (db.update(TABLE_NAME, values,
					campos[COL_ID] + "=" + reg.getId() + " AND "
							+ campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;
			if (!res) {
				if (db.update(TABLE_NAME, values, campos[COL_ID_EXTERNO] + "="
						+ reg.getIdExterno() + " AND " + campos[COL_EMPRESA]
						+ "=" + reg.getEmpresa(), null) > 0)
					res = true;
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(Estado reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
					+ " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;
			if (!res) {
				if (db.delete(
						TABLE_NAME,
						campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno()
								+ " AND " + campos[COL_EMPRESA] + "="
								+ reg.getEmpresa(), null) > 0)
					res = true;
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount(int empresa) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db
					.query(TABLE_NAME, (new String[] { "count(*)" }),
							campos[COL_EMPRESA] + "=" + empresa, null, null,
							null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll(int empresa) {

		try {

			db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public Estado getByID(int id, int empresa) {
		Cursor cur;
		Estado res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO] + "=" + id
					+ " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
					null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new Estado(cur.getInt(COL_ID),
							cur.getInt(COL_ID_EXTERNO),
							cur.getInt(COL_EMPRESA), cur.getString(COL_ESTADO));

				cur.close();

				if (res == null) {
					// buscamos por id externo
					if ((cur = db.query(TABLE_NAME, campos,
							campos[COL_ID] + "=" + id + " AND "
									+ campos[COL_EMPRESA] + "=" + empresa,
							null, null, null, null)) != null) {

						if (cur.moveToFirst())
							res = new Estado(cur.getInt(COL_ID),
									cur.getInt(COL_ID_EXTERNO),
									cur.getInt(COL_EMPRESA),
									cur.getString(COL_ESTADO));

						cur.close();
					}

				}
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public ArrayList<Estado> getAll(int empresa) {
		Cursor cur;
        ArrayList<Estado> res = null;

		try {

			// TODO: Lo quito porquie a Diego no le gusta pese a ser la mejor opción
            //ArrayList<Estado> res = new ArrayList<Estado>();
			//res.add(new Estado(0, 0, empresa, "Nuevas"));

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
					+ empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<Estado>();

					do {

						res.add(new Estado(cur.getInt(COL_ID), cur
								.getInt(COL_ID_EXTERNO), cur
								.getInt(COL_EMPRESA), cur.getString(COL_ESTADO)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public Estado getEstadoByName(String name, int empresa) {
		Cursor cur;
		Estado res = null;

		try {
			String whereClause = "estado COLLATE NOCASE = ? AND empresa = ?";
			String[] whereArgs = new String[]{name, String.valueOf(empresa)};

			cur = db.query(TABLE_NAME, campos, whereClause, whereArgs, null, null, null);
			if (cur != null && cur.moveToFirst()) {
				res = new Estado(
					cur.getInt(COL_ID),
					cur.getInt(COL_ID_EXTERNO),
					cur.getInt(COL_EMPRESA),
					cur.getString(COL_ESTADO));
				cur.close();
			} else {
				MyLoggerHandler.getInstance().info("No se ha encontrado el estado '" + name + "' para la empresa " + empresa);
			}
		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}
		return res;
	}

}
