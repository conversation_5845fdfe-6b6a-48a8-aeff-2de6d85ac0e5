package com.movisat.database;

public class Trabajador extends Object {
	private int id;
	private int idExterno;
	private int empresa;
	private String nombre;
	private int tipo;
	private String identif;

	public Trabajador(int id, int idExterno, int empresa, String nombre, int tipo,
			String identif) {

		setId(id);
		setIdExterno(idExterno);
		setEmpresa(empresa);
		setNombre(nombre);
		setTipo(tipo);
		setIdentif(identif);
	}

	public void setId(int id) {

		this.id = id;
	}

	public void setEmpresa(int empresa) {

		this.empresa = empresa;
	}

	public void setNombre(String nombre) {

		this.nombre = nombre;
	}

	public void setTipo(int tipo) {

		this.tipo = tipo;
	}

	public void setIdentif(String identif) {

		this.identif = identif;
	}

	public int getId() {

		return id;
	}

	public int getEmpresa() {

		return empresa;
	}

	public String getNombre() {

		return nombre;
	}

	public int getTipo() {

		return tipo;
	}

	public String getIdentif() {

		return identif;
	}

	public int getIdExterno() {
		return idExterno;
	}

	public void setIdExterno(int idExterno) {
		this.idExterno = idExterno;
	}

	@Override
	public String toString() {

		return nombre;
	}

}
