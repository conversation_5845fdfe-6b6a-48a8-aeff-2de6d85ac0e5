package com.movisat.database;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.Queue;

import com.movisat.application.EcoSATApplication;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.synchronize.DBSynchro;
import com.movisat.utilities.Database;
import com.movisat.utilities.HelperDates;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

public class DBError {
    private static final String TAG = "DBError";
    private static final String TABLE_NAME = "errores";
    private SQLiteDatabase db = null;
    private static final String[] campos = new String[]{"id", "message", "fecha", "stacktrace"};

    private static final int COL_ID = 0;
    private static final int COL_MESSAGE = 1;
    private static final int COL_STACK = 3;
    private static final int COL_FECHA = 2;

    private static final int MAX_ERRORS = 5000;

    private static Queue<MyError> errorQueue = new LinkedList<>();

    public DBError() {

        try {

            db = Database.getConnection(EcoSATApplication.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Exception e) {
            // En este sitio no se puede llamar porque entrariamos en
            // un bucle infinito de llamadas ya que este objeto se
            // crea desde dentro de MyLoggerHandler.error()
            Log.e(TAG, e.getMessage());
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {

            e.printStackTrace();
        }

    }

    public synchronized long insert(MyError reg) {
        long res = 0;

        try {
            int max = getCount();
            if (max > MAX_ERRORS)
                deleteOld();
            // Para cuando da SQLiteDatabaseLockedException
            if (db == null || !db.isOpen()) {
                // Lo intentamos en la siguiente llamada
                errorQueue.add(reg);
                return -1;
            }

            processQueue();

            ContentValues values = new ContentValues();
            // values.put("id", reg.getId());
            values.put(campos[COL_MESSAGE], reg.getMessage());
            values.put(campos[COL_STACK], reg.getStacktrace());
            values.put(campos[COL_FECHA], reg.getFecha().toString());

            res = db.insert(TABLE_NAME, null, values);

            if (res == -1) {
                errorQueue.add(reg);
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }

        return res;
    }

    public void processQueue() {
        while (!errorQueue.isEmpty()) {
            MyError reg = errorQueue.peek();
            try {
                ContentValues values = new ContentValues();
                values.put(campos[COL_MESSAGE], reg.getMessage());
                values.put(campos[COL_STACK], reg.getStacktrace());
                values.put(campos[COL_FECHA], reg.getFecha().toString());

                long result = db.insert(TABLE_NAME, null, values);
                if (result != -1) {
                    errorQueue.remove();
                } else {
                    Log.e(TAG, "Error al insertar en la base de datos: " + reg.getMessage());
                    break; // Lo intentamos en la siguiente llamada
                }
            } catch (Throwable e) {
                Log.e(TAG, "Error al insertar en la base de datos: " + reg.getMessage() + " - " + e.getMessage());
                break; // Lo intentamos en la siguiente llamada
            }
        }
    }

    public int getCount() {
        int res = 0;
        Cursor cur;

        try {

            if (db!=null) {
                if ((cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                        null, null, null, null, null)) != null) {

                    if (cur.moveToFirst())
                        res = cur.getInt(0);

                    cur.close();
                }
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public String getLastTxt(int lastNumber) {

        ArrayList<MyError> lista=null;
        String errores = "";

        try {

            lista = getLast(lastNumber);

            if (lista != null) {

                for (MyError myError : lista) {
                    errores += "Fecha: " + myError.getFecha() + "\r\nError: " + myError.getMessage() + "\r\nStack: "
                            + myError.getStacktrace() + "\r\n";
                }
                return errores;
            }
        } catch (Throwable e){
            MyLoggerHandler.getInstance().error(e);
        }

        return "";
    }

    public String getLastTxt(long fecha_ini, long fecha_fin) {

        String errores = "";

        try {
            ArrayList<MyError> lista = getLast(fecha_ini, fecha_fin);

            for (MyError myError : lista) {
                errores += "Fecha: " + myError.getFecha() + "\r\nError: " + myError.getMessage() + "\r\nStack: "
                        + myError.getStacktrace() + "\r\n";
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return errores;
    }

    public ArrayList<MyError> getLast(int lastNumber) {
        Cursor cur;
        ArrayList<MyError> res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, null, null, null, null,
                    campos[COL_ID] + " DESC", "" + lastNumber)) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<MyError>();

                    do {

                        res.add(new MyError(cur.getInt(COL_ID), cur
                                .getString(COL_MESSAGE), cur
                                .getString(COL_STACK), cur.getString(COL_FECHA)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<MyError> getLast(long fecha_ini, long fecha_fin) {
        Cursor cur;
        ArrayList<MyError> res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos,
                    campos[COL_FECHA] + ">='" + HelperDates.getInstance().getDateStringBy(fecha_ini)
                            + "' AND " + campos[COL_FECHA] + "<='" + HelperDates.getInstance().getDateStringBy(fecha_fin) + "'"
                    , null, null, null,
                    campos[COL_ID] + " DESC", null)) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<MyError>();

                    do {

                        res.add(new MyError(cur.getInt(COL_ID), cur
                                .getString(COL_MESSAGE), cur
                                .getString(COL_STACK), cur.getString(COL_FECHA)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public ArrayList<MyError> getMesesAnterioresAEsteMes() {
        Cursor cur;
        ArrayList<MyError> res = null;

        try {
            long now = System.currentTimeMillis();

            Date fecha = HelperDates.getInstance().addMonths(new Date(now), -1);

            //long fourWeeksAgo = now - 1000 * 60 * 60 * 24 * 28;
            String sFecha = HelperDates.getInstance().getDateStringBy(fecha.getTime());

            if ((cur = db.query(TABLE_NAME, campos,
                    campos[COL_FECHA] + "<='" + sFecha
                            + "'"
                    , null, null, null,
                    campos[COL_ID] + " DESC", null)) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<MyError>();

                    do {

                        res.add(new MyError(cur.getInt(COL_ID), cur
                                .getString(COL_MESSAGE), cur
                                .getString(COL_STACK), cur.getString(COL_FECHA)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(MyError reg) {
        boolean res = false;

        try {


            if (db!=null && db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId(),
                    null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    /**
     * Borramos las ultimas lineas de log
     */
    public void deleteOld() {
        int preservarLineas = (int) (MAX_ERRORS * 0.9f);
        try {
            String sql = "DELETE FROM errores WHERE id NOT IN ( \n" +
                    "   SELECT id FROM errores  \n" +
                    "   ORDER BY fecha DESC\n" +
                    "   LIMIT " + preservarLineas + " \n" +
                    "   )  ";

            if(db!=null)
                db.execSQL(sql);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

}
