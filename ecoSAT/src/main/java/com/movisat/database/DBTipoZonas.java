package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import java.util.ArrayList;

public class DBTipoZonas {
    private final String TABLE_NAME = "tipo_zonas";
    private SQLiteDatabase db = null;

    private String[] campos = (new String[]{"id", "nombre","abreviatura","fecha_baja"});
    private final int COL_ID = 0;
    private final int COL_NOMBRE = 1;
    private final int COL_ABREVIATURA = 2;
    private final int COL_FECHA_BAJA = 3;

    public DBTipoZonas() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(TipoZonas reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_ABREVIATURA], reg.getAbreviatura());
            values.put(campos[COL_FECHA_BAJA], reg.getFechaBaja());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(TipoZonas reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_ABREVIATURA], reg.getAbreviatura());
            values.put(campos[COL_FECHA_BAJA], reg.getFechaBaja());

            if (db.update(TABLE_NAME, values,
                    campos[COL_ID] + "=" + reg.getId(), null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(TipoZonas reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId(), null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public int getCount() {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                    null, null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public void deleteAll() {

        try {

            db.delete(TABLE_NAME, null, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public TipoZonas getByID(int id) {
        Cursor cur;
        TipoZonas res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id,
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new TipoZonas(cur.getInt(COL_ID),
                            cur.getString(COL_NOMBRE),
                            cur.getString(COL_ABREVIATURA),
                            cur.getString(COL_FECHA_BAJA));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public Cursor getAllCursor() {
        Cursor cur = null;
        ArrayList<TipoZonas> res = null;

        try {

            cur = db.rawQuery("SELECT id AS _id, UPPER(nombre) AS nombre, UPPER(abreviatura) as abreviatura, fecha_baja FROM "
                            + TABLE_NAME,
                    null);
            cur.moveToFirst();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return cur;
    }

    public ArrayList<TipoZonas> getAll() {
        Cursor cur;
        ArrayList<TipoZonas> res = new ArrayList<>();

        try {

            if ((cur = db.query(TABLE_NAME, campos, null, null, null, null,
                    campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<TipoZonas>();

                    do {

                        res.add(new TipoZonas(cur.getInt(COL_ID),
                                cur.getString(COL_NOMBRE),
                                cur.getString(COL_ABREVIATURA),
                                cur.getString(COL_FECHA_BAJA)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public Cursor getAllCursorBy(int id_tipo) {
        Cursor cur = null;

        try {

            cur = db.rawQuery("SELECT id AS _id, UPPER(nombre) AS nombre, UPPER(abreviatura) as abreviatura, fecha_baja FROM "
                            + TABLE_NAME + " WHERE " + campos[COL_ID] + "=" + id_tipo,
                    null);
            cur.moveToFirst();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return cur;
    }

}
