package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import java.util.ArrayList;

public class DBPlanchadas {
    private final String TABLE_NAME = "planchadas";
    private SQLiteDatabase db = null;

    private String[] campos = (new String[] { "id", "empresa",
            "codigo_planchada", "descripcion", "lote_id" });
    private final int COL_ID = 0;
    private final int COL_EMPRESA = 1;
    private final int COL_CODIGO_PLANCHADA = 2;
    private final int COL_DESCRIPCION = 3;
    private final int COL_LOTE_ID = 4;

    public DBPlanchadas() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(Planchada reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_CODIGO_PLANCHADA], reg.getCodigoPlanchada());
            values.put(campos[COL_DESCRIPCION], reg.getDescripcion());
            values.put(campos[COL_LOTE_ID], reg.getLoteId());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(Planchada reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_CODIGO_PLANCHADA], reg.getCodigoPlanchada());
            values.put(campos[COL_DESCRIPCION], reg.getDescripcion());
            values.put(campos[COL_LOTE_ID], reg.getLoteId());

            if (db.update(TABLE_NAME, values,
                    campos[COL_ID] + "=" + reg.getId() + " AND "
                            + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;
            if (!res) {
                if (db.update(TABLE_NAME, values, campos[COL_ID] + "="
                        + reg.getId() + " AND " + campos[COL_EMPRESA]
                        + "=" + reg.getEmpresa()
                        + " AND " + campos[COL_CODIGO_PLANCHADA] + "="  + reg.getCodigoPlanchada()
                        + " AND " + campos[COL_DESCRIPCION] + "=" + reg.getDescripcion()
                        + " AND " + campos[COL_LOTE_ID] + "=" + reg.getLoteId(), null) > 0)
                    res = true;
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(Planchada reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
                            + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;
            if (!res) {
                if (db.delete(
                        TABLE_NAME,
                        campos[COL_ID] + "="
                                + reg.getId() + " AND " + campos[COL_EMPRESA]
                                + "=" + reg.getEmpresa()
                                + " AND " + campos[COL_CODIGO_PLANCHADA] + "="  + reg.getCodigoPlanchada()
                                + " AND " + campos[COL_DESCRIPCION] + "=" + reg.getDescripcion()
                                + " AND " + campos[COL_LOTE_ID] + "=" + reg.getLoteId(), null) > 0)
                    res = true;
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public int getCount(int empresa) {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db
                    .query(TABLE_NAME, (new String[] { "count(*)" }),
                            campos[COL_EMPRESA] + "=" + empresa, null, null,
                            null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public void deleteAll(int empresa) {

        try {

            db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public Planchada getByID(int id, int empresa) {
        Cursor cur;
        Planchada res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Planchada(cur.getInt(COL_ID),
                            cur.getInt(COL_EMPRESA),
                            cur.getString(COL_DESCRIPCION),
                            cur.getInt(COL_LOTE_ID),
                            cur.getString(COL_CODIGO_PLANCHADA));

                cur.close();

                if (res == null) {
                    // buscamos por id externo
                    if ((cur = db.query(TABLE_NAME, campos,
                            campos[COL_ID] + "=" + id + " AND "
                                    + campos[COL_EMPRESA] + "=" + empresa,
                            null, null, null, null)) != null) {

                        if (cur.moveToFirst())
                            res = new Planchada(cur.getInt(COL_ID),
                                    cur.getInt(COL_EMPRESA),
                                    cur.getString(COL_DESCRIPCION),
                                    cur.getInt(COL_LOTE_ID),
                                    cur.getString(COL_CODIGO_PLANCHADA));

                        cur.close();
                    }

                }
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<Planchada> getAll(int empresa) {
        Cursor cur;
        ArrayList<Planchada> res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<Planchada>();

                    do {

                        res.add(new Planchada(cur.getInt(COL_ID),
                                cur.getInt(COL_EMPRESA),
                                cur.getString(COL_DESCRIPCION),
                                cur.getInt(COL_LOTE_ID),
                                cur.getString(COL_CODIGO_PLANCHADA)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public String getCsvIds(int idEmpresa) {
        Cursor cur;
        String res = "";

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + idEmpresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = "";

                    do {
                        res = res.concat(String.valueOf(cur.getInt(COL_ID)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

}
