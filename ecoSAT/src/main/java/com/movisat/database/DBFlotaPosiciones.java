package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;
import com.movisat.utilities.Utils;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteConstraintException;
import android.database.sqlite.SQLiteDatabase;

public class DBFlotaPosiciones {
    private final String TABLE_NAME_FLOTA = "flota";
    private final String TABLE_NAME = "flota_posiciones";
    private final String TABLE_NAME_HISTORICO = "flota_posiciones_historico";
    private SQLiteDatabase db = null;
    private String[] campos = (new String[]{"id", "empresa", "estado",
            "fecha", "movil", "lat", "lon", "descripcion"});

    private final int COL_ID = 0;
    private final int COL_EMPRESA = 1;
    private final int COL_ESTADO = 2;
    private final int COL_FECHA = 3;
    private final int COL_MOVIL = 4;
    private final int COL_LATITUD = 5;
    private final int COL_LONGITUD = 6;
    private final int COL_DESCRIPCION = 7;

    public DBFlotaPosiciones() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }


    public int getMaxRutaHistorico() {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db
                    .query(TABLE_NAME_HISTORICO, (new String[]{"MAX(idRuta)"}),
                            null, null, null,
                            null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }



    public long insert(FlotaPosicionesHistorico reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();
            values.put("idRuta", reg.idRuta);
            values.put("movil", reg.idMovil);
            values.put("empresa", reg.empresa);
            values.put("fecha", reg.fecha);
            values.put("rumbo", reg.rumbo);
            values.put("altura", reg.altura);
            values.put("lat", reg.lat);
            values.put("lon", reg.lon);
            values.put("velocidad", reg.vel);
            values.put("tiempoParada", reg.tiempoParada);

            res = db.replace(TABLE_NAME_HISTORICO, null, values);
        }
        catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public boolean deletePosicionesHistorico() {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME_HISTORICO, null, null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }



    public long insert(FlotaPosiciones reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            // values.put("id", reg.getId());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_ESTADO], reg.getEstadoInt());
            values.put(campos[COL_FECHA], reg.getFecha().toString());
            values.put(campos[COL_MOVIL], reg.getMovil());
            values.put(campos[COL_LATITUD], reg.getLat());
            values.put(campos[COL_LONGITUD], reg.getLng());
            values.put(campos[COL_DESCRIPCION], reg.getDescripcion());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(int id, FlotaPosiciones reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            // values.put("id", reg.getId());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_ESTADO], reg.getEstadoInt());
            values.put(campos[COL_FECHA], reg.getFecha().toString());
            values.put(campos[COL_MOVIL], reg.getMovil());
            values.put(campos[COL_LATITUD], reg.getLat());
            values.put(campos[COL_LONGITUD], reg.getLng());
            values.put(campos[COL_DESCRIPCION], reg.getDescripcion());

            if (db.update(TABLE_NAME, values, campos[COL_ID] + "=" + id
                            + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(int id, int empresa) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + id + " AND "
                    + campos[COL_EMPRESA] + "=" + empresa, null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(int movil) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_MOVIL] + "=" + movil, null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public int getCount(int empresa) {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db
                    .query(TABLE_NAME, (new String[]{"count(*)"}),
                            campos[COL_EMPRESA] + "=" + empresa, null, null,
                            null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public void deleteAll(int empresa) {

        try {

            db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public FlotaPosiciones getByID(int id, int empresa) {
        Cursor cur;
        FlotaPosiciones res = null;
        boolean estado = false;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst()) {
                    estado = (cur.getInt(COL_ESTADO) == 1) ? true : false;
                    res = new FlotaPosiciones(
                            cur.getInt(COL_ID),
                            cur.getInt(COL_EMPRESA), estado,
                            cur.getInt(COL_MOVIL),
                            cur.getString(COL_FECHA),
                            cur.getDouble(COL_LATITUD),
                            cur.getDouble(COL_LONGITUD),
                            cur.getString(COL_DESCRIPCION));
                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<FlotaPosiciones> getAll(int empresa, int usuario) {
        Cursor cur;
        ArrayList<FlotaPosiciones> res = null;
        DBUsuMoviles dbPermisos = new DBUsuMoviles();
        int countPermisos = -1;
        int movil;
        UsuMoviles permiso = null;
        boolean estado = false;
        countPermisos = dbPermisos.getCount(empresa, usuario);

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa, null, null, null, campos[COL_ESTADO]+" DESC, lower("+campos[COL_DESCRIPCION]+ ")" )) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<FlotaPosiciones>();

                    do {
                        estado = (cur.getInt(COL_ESTADO) == 1) ? true : false;

                        if (countPermisos > 0) {
                            // tengo que comprobar si tiene permiso sobre el
                            // movil actual
                            movil = cur.getInt(COL_MOVIL);
                            permiso = dbPermisos.getPermisoBy(empresa, movil);
                        }

                        if (permiso != null || countPermisos == 0) {
                            String descMovil= getDescMovil(cur.getInt(COL_MOVIL));
                            res.add(new FlotaPosiciones(
                                    cur.getInt(COL_ID),
                                    cur.getInt(COL_EMPRESA),
                                    estado,
                                    cur.getInt(COL_MOVIL),
                                    cur.getString(COL_FECHA),
                                    cur.getDouble(COL_LATITUD),
                                    cur.getDouble(COL_LONGITUD),
                                    descMovil.equals("")?cur.getString(COL_DESCRIPCION):descMovil)); // cur.getString(COL_DESCRIPCION)
                        }

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        dbPermisos.close();
        return res;
    }

    // recupera la descripcion de un movil de la flota
    // puesto que esta mal montada la tabla flotaposiciones, se guarda la descripcion del movil,
    // y si cambia esta desde EcoSat/intranet, no se actualiza, pero si lo hace en la tabla flota
    // asi que hago join con el codigo de movil a la tabla flota, que siempre estara actualizada.
    public String getDescMovil(int movil) {
        String nombre_movil="";
        Cursor cur;

        try {

            if ((cur = db
                    .query(TABLE_NAME_FLOTA, (new String[]{"nombre"}),
                            "codigo" + "=" + movil, null, null,
                            null, null)) != null) {

                if (cur.moveToFirst())
                    nombre_movil = cur.getString(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }


        return nombre_movil;
    }

    public FlotaPosiciones getByMovil(int codigo, int empresa) {

        Cursor cur;
        FlotaPosiciones res = null;
        boolean estado = false;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_MOVIL] + "=" + codigo
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst()) {
                    estado = (cur.getInt(COL_ESTADO) == 1) ? true : false;
                    res = new FlotaPosiciones(
                            cur.getInt(COL_ID),
                            cur.getInt(COL_EMPRESA), estado,
                            cur.getInt(COL_MOVIL),
                            cur.getString(COL_FECHA),
                            cur.getDouble(COL_LATITUD),
                            cur.getDouble(COL_LONGITUD),
                            cur.getString(COL_DESCRIPCION));
                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public boolean updateMovil(int codigo, String descripcion, int empresa) {

        boolean res = false;

        try {

            ContentValues values = new ContentValues();
            values.put(campos[COL_DESCRIPCION], descripcion);

            if (db.update(TABLE_NAME, values, campos[COL_MOVIL] + "=" + codigo
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa,
                    null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;

    }


    public ArrayList<ItemMapa> getHistoricoRuta(int empresa, int usuario, int idRuta) {
        Cursor cur;
        ArrayList<ItemMapa> res = null;

        try {


            if ((cur = db.query(TABLE_NAME_HISTORICO,
                    new String[]{"idRuta", "movil", "empresa", "lat", "lon",
                                 "velocidad", "altura", "rumbo", "fecha", "tiempoParada"} , "idRuta" + "="
                    + idRuta, null, null, null, "fecha ASC" )) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<ItemMapa>();

                    do {

                        // int idRuta, int idMovil, int empresa, double lat, double lon, int vel, int altura, int rumbo, Date fecha) {
                            res.add(new FlotaPosicionesHistorico(
                                    cur.getInt(0),
                                    cur.getInt(1),
                                    cur.getInt(2),
                                    cur.getDouble(3),
                                    cur.getDouble(4),
                                    cur.getInt(5),
                                    cur.getInt(6),
                                    cur.getInt(7),
                                    cur.getString(8),
                                    cur.getDouble(9)
                                    ));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }


        return res;
    }

}
