package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBEmpresa {
	private final String TABLE_NAME = "empresas";
	private SQLiteDatabase db = null;
	private String[] campos = (new String[] { "id", "nombre" });

	private final int COL_ID = 0;
	private final int COL_NOMBRE = 1;

	public DBEmpresa() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(Empresa reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID], reg.getId());
			values.put(campos[COL_NOMBRE], reg.getNombre());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(Empresa reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID], reg.getId());
			values.put(campos[COL_NOMBRE], reg.getNombre());

			if (db.update(TABLE_NAME, values,
					campos[COL_ID] + "=" + reg.getId(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(Empresa reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount() {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }),
					null, null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll() {

		try {

			db.delete(TABLE_NAME, null, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public Empresa getByID(int id) {
		Cursor cur;
		Empresa res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id,
					null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new Empresa(cur.getInt(COL_ID),
							cur.getString(COL_NOMBRE));

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public ArrayList<Empresa> getAll() {
		Cursor cur;
		ArrayList<Empresa> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, null, null, null, null,
					campos[COL_NOMBRE] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<Empresa>();

					do {

						res.add(new Empresa(cur.getInt(COL_ID), cur
								.getString(COL_NOMBRE)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
