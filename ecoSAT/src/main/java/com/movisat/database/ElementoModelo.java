package com.movisat.database;

public class ElementoModelo extends Object implements Comparable<ElementoModelo> {
	private int id;
	private int idExterno;
	private int empresa;
	private String nombre;
	int tipo, residuo, recogida, capacidad;
	byte[] icono;

	public ElementoModelo(int id, int idExterno, int empresa, String nombre,
			int tipo, int residuo, int recogida, int capacidad, byte[] icono) {

		setId(id);
		setIdExterno(idExterno);
		setEmpresa(empresa);
		setNombre(nombre);
		setTipo(tipo);
		setResiduo(residuo);
		setRecogida(recogida);
		setIcono(icono);
		setCapacidad(capacidad);
	}

	public void setId(int id) {

		this.id = id;
	}

	public void setEmpresa(int empresa) {

		this.empresa = empresa;
	}

	public void setNombre(String nombre) {

		this.nombre = nombre;
	}

	public void setTipo(int tipo) {

		this.tipo = tipo;
	}

	public void setResiduo(int residuo) {

		this.residuo = residuo;
	}

	public void setRecogida(int recogida) {

		this.recogida = recogida;
	}

	public void setCapacidad(int capacidad) {

		this.capacidad = capacidad;
	}

	public void setIcono(byte[] icono) {

		this.icono = icono;
	}

	public int getId() {

		return id;
	}

	public int getEmpresa() {

		return empresa;
	}

	public String getNombre() {

		return nombre;
	}

	public int getTipo() {

		return tipo;
	}

	public int getResiduo() {

		return residuo;
	}

	public int getRecogida() {

		return recogida;
	}

	public int getCapacidad() {

		return capacidad;
	}

	public byte[] getIcono() {

		return icono;
	}

	public int getIdExterno() {
		return idExterno;
	}

	public void setIdExterno(int idExterno) {
		this.idExterno = idExterno;
	}

	@Override
	public String toString() {

		return nombre;
	}

	@Override
	public int compareTo(ElementoModelo o) {
		return this.nombre.compareTo(o.nombre);
	}
}