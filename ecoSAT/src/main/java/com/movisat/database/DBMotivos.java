package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import java.util.ArrayList;
import java.util.List;

public class DBMotivos {
    private final String TABLE_NAME = "motivos_baja";
    private SQLiteDatabase db = null;
    private String[] campos = (new String[] { "id", "abreviatura", "nombre",
            "descripcion"});
    private final int COL_ID = 0;
    private final int COL_ABREVIATURA = 1;
    private final int COL_NOMBRE = 2;
    private final int COL_DESCRIPCION = 3;

    public DBMotivos() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(Motivos mot) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], mot.getId());
            values.put(campos[COL_ABREVIATURA], mot.getAbreviatura());
            values.put(campos[COL_NOMBRE], mot.getNombre());
            values.put(campos[COL_DESCRIPCION], mot.getDescripcion());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(Motivos mot) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], mot.getId());
            values.put(campos[COL_ABREVIATURA], mot.getAbreviatura());
            values.put(campos[COL_NOMBRE], mot.getNombre());
            values.put(campos[COL_DESCRIPCION], mot.getDescripcion());

            if (db.update(TABLE_NAME, values,
                    campos[COL_ID] + "=" + mot.getId() + " AND "
                            + campos[COL_ABREVIATURA] + "=" + mot.getAbreviatura()
                            + " AND " + campos[COL_NOMBRE] + "=" + mot.getNombre(),
                    null) > 0)
                res = true;


        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(Motivos mot) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + mot.getId()
                    + " AND " + campos[COL_ABREVIATURA] + "=" + mot.getAbreviatura()
                    + " AND " + campos[COL_NOMBRE] + "=" + mot.getNombre(), null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public int getCount() {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }),
                    null, null, null, null,
                    null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public Motivos getByNombre(String nombre) {
        Motivos res = null;

        List<Motivos> motivos = getAll();
        for(Motivos motivo : motivos){
            if(motivo.getNombre().equals(nombre)) return motivo;
        }

        return res;
    }

    public Motivos getByAbreviatura(String abreviatura) {
        Cursor cur;
        Motivos res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ABREVIATURA] + " = '" + abreviatura + "'",
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Motivos(cur.getInt(COL_ID),
                            cur.getString(COL_ABREVIATURA),
                            cur.getString(COL_NOMBRE),
                            cur.getString(COL_DESCRIPCION));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<Motivos> getAll() {
        Cursor cur;
        ArrayList<Motivos> res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, null, null,
                    null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<Motivos>();

                    do {

                        res.add(new Motivos(cur.getInt(COL_ID), cur
                                .getString(COL_ABREVIATURA), cur
                                .getString(COL_NOMBRE), cur
                                .getString(COL_DESCRIPCION)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }
}
