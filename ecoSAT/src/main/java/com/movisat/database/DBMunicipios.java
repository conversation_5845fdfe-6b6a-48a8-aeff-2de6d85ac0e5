package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBMunicipios {
	private final String TABLE_NAME = "municipios";
	private SQLiteDatabase db = null;

	private String[] campos = (new String[] { "id", "municipio", "provincia" });
	private final int COL_ID = 0;
	private final int COL_MUNICIPIO = 1;
	private final int COL_PROVINCIA = 2;

	public DBMunicipios() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(Municipios reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_MUNICIPIO], reg.getMunicipio());
			values.put(campos[COL_PROVINCIA], reg.getProvincia());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(Municipios reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_MUNICIPIO], reg.getMunicipio());
			values.put(campos[COL_PROVINCIA], reg.getProvincia());

			if (db.update(TABLE_NAME, values,
					campos[COL_ID] + "=" + reg.getId(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(Municipios reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount() {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }),
					null, null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll() {

		try {

			db.delete(TABLE_NAME, null, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public Municipios getByID(int id) {
		Cursor cur;
		Municipios res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id,
					null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new Municipios(cur.getInt(COL_ID),
							cur.getString(COL_MUNICIPIO)
							, cur.getInt(COL_PROVINCIA));

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public Cursor getAllCursorBy(int provincia) {
		Cursor cur = null;
		ArrayList<Municipios> res = null;

		try {

			cur = db.rawQuery("SELECT id AS _id, UPPER(municipio) AS municipio FROM " 
							+ TABLE_NAME + " WHERE " + campos[COL_PROVINCIA] + "=" + provincia,
					null);
			cur.moveToFirst();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return cur;
	}

	public ArrayList<Municipios> getAll() {
		Cursor cur;
		ArrayList<Municipios> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, null, null, null, null,
					campos[COL_ID] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<Municipios>();

					do {

						res.add(new Municipios(cur.getInt(COL_ID), cur
								.getString(COL_MUNICIPIO), cur.getInt(COL_PROVINCIA)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
