package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBFlota {
	private final String TABLE_NAME = "flota";
	private SQLiteDatabase db = null;

	private String[] campos = (new String[] { "codigo", "nombre", "empresa" });
	private final int COL_CODIGO = 0;
	private final int COL_NOMBRE = 1;
	private final int COL_EMPRESA = 2;

	public DBFlota() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(Flota reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_CODIGO], reg.getCodigo());
			values.put(campos[COL_NOMBRE], reg.getNombre());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(Flota reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_CODIGO], reg.getCodigo());
			values.put(campos[COL_NOMBRE], reg.getNombre());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());

			if (db.update(TABLE_NAME, values,
					campos[COL_CODIGO] + "=" + reg.getCodigo(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(Flota reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_CODIGO] + "=" + reg.getCodigo(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount() {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }),
					null, null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll() {

		try {

			db.delete(TABLE_NAME, null, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public Flota getBy(int codigo) {
		Cursor cur;
		Flota res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_CODIGO] + "=" + codigo,
					null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new Flota(cur.getInt(COL_CODIGO),
							cur.getString(COL_NOMBRE)
							, cur.getInt(COL_EMPRESA));

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	
	public Cursor getAllCursorBy(int empresa) {
		Cursor cur = null;

		try {
			String query = "SELECT codigo AS _id, UPPER(nombre) AS nombre FROM "
					+ TABLE_NAME + " WHERE " + campos[COL_EMPRESA] + "=" + empresa
					+ " ORDER BY nombre ASC";

			cur = db.rawQuery(query, null);
			cur.moveToFirst();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return cur;
	}
	

	public ArrayList<Flota> getAllBy(int empresa) {
		Cursor cur;
		ArrayList<Flota> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa, null, null, null,
					campos[COL_NOMBRE] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<Flota>();

					do {

						res.add(new Flota(cur.getInt(COL_CODIGO), cur
								.getString(COL_NOMBRE), cur.getInt(COL_EMPRESA)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
