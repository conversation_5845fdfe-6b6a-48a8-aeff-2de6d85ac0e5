package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

public class DBElementoModeloNivelLlenado {
	private final String TABLE_NAME = "elementos_modelos_nllenado";
	private SQLiteDatabase db = null;
	private String[] campos = (new String[] { "id", "empresa", "codigo_modelo",
			"fracciones", "peso_lleno" });

	private final int COL_ID = 0;
	private final int COL_EMPRESA = 1;
	private final int COL_CODIGO_MODELO = 2;
	private final int COL_FRACCIONES = 3;
	private final int COL_PESO_LLENO = 4;

	public DBElementoModeloNivelLlenado() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(ElementoModeloNivelLlenado reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID], reg.getId());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_CODIGO_MODELO], reg.getCodigo_modelo());
			values.put(campos[COL_FRACCIONES], reg.getFracciones());
			values.put(campos[COL_PESO_LLENO], reg.getPeso_lleno());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(ElementoModeloNivelLlenado reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID], reg.getId());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_CODIGO_MODELO], reg.getCodigo_modelo());
			values.put(campos[COL_FRACCIONES], reg.getFracciones());
			values.put(campos[COL_PESO_LLENO], reg.getPeso_lleno());

			if (db.update(
					TABLE_NAME,
					values,
					campos[COL_ID] + "=" + reg.getId() + " AND empresa="
							+ reg.getEmpresa(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(ElementoModeloNivelLlenado reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
					+ " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount(int empresa) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db
					.query(TABLE_NAME, (new String[] { "count(*)" }),
							campos[COL_EMPRESA] + "=" + empresa, null, null,
							null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll(int empresa) {

		try {

			db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public ElementoModeloNivelLlenado getByCodigoModelo(int codigo, int empresa) {
		Cursor cur;
		ElementoModeloNivelLlenado res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_CODIGO_MODELO]
					+ "=" + codigo + " AND " + campos[COL_EMPRESA] + "=" + empresa,
					null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new ElementoModeloNivelLlenado(cur.getInt(COL_ID),
							cur.getInt(COL_EMPRESA),
							cur.getInt(COL_CODIGO_MODELO),
							cur.getInt(COL_FRACCIONES),
							cur.getInt(COL_PESO_LLENO));

				cur.close();

			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
