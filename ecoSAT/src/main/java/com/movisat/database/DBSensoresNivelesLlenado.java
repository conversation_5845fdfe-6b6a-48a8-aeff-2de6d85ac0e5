package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import java.util.ArrayList;

public class DBSensoresNivelesLlenado {
    private final String TABLE_NAME = "sensores_nivel_llenado";
    private SQLiteDatabase db = null;
    private String[] campos = (new String[]{"id", "codigo_elemento",
            "codigo_movil", "empresa", "fecha_registro", "numero_fraccion",
            "procesado"});

    private final int COL_ID = 0;
    private final int COL_CODIGO_ELEMENTO = 1;
    private final int COL_CODIGO_MOVIL = 2;
    private final int COL_EMPRESA = 3;
    private final int COL_FECHA_REGISTRO = 4;
    private final int COL_NUMERO_FRACCION = 5;
    private final int COL_PROCESADO = 6;

    public DBSensoresNivelesLlenado() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(SensorNivelLLenado reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_CODIGO_ELEMENTO], reg.getCodigoElemento());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_CODIGO_MOVIL], reg.getCodigoMovil());
            values.put(campos[COL_NUMERO_FRACCION], reg.getNumeroFraccion());
            values.put(campos[COL_FECHA_REGISTRO], reg.getFechaRegistro());
            values.put(campos[COL_PROCESADO], reg.getProcesado());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(SensorNivelLLenado reg) {
        boolean res = false;

        try {
            if (reg.getId() == 0)
                return res;

            ContentValues values = new ContentValues();

            values.put(campos[COL_CODIGO_ELEMENTO], reg.getCodigoElemento());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_CODIGO_MOVIL], reg.getCodigoMovil());
            values.put(campos[COL_NUMERO_FRACCION], reg.getNumeroFraccion());
            values.put(campos[COL_FECHA_REGISTRO], reg.getFechaRegistro());
            values.put(campos[COL_PROCESADO], reg.getProcesado());

            if (db.update(TABLE_NAME, values,
                    campos[COL_CODIGO_ELEMENTO] + "=" + reg.getCodigoElemento() + " AND "
                            + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean setProcesados(int empresa) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();
            values.put(campos[COL_PROCESADO], 1);

            if (db.update(TABLE_NAME, values,
                    campos[COL_EMPRESA] + "=" + empresa, null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<SensorNivelLLenado> getSensores(int empresa) {

        Cursor cur;
        ArrayList<SensorNivelLLenado> res = new ArrayList<>();
        SensorNivelLLenado sensor = null;
        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa,
                    null, null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<SensorNivelLLenado>();

                    do {
                        sensor = new SensorNivelLLenado(
                                cur.getInt(COL_CODIGO_ELEMENTO),
                                cur.getInt(COL_CODIGO_MOVIL),
                                cur.getInt(COL_EMPRESA),
                                cur.getInt(COL_NUMERO_FRACCION),
                                cur.getLong(COL_FECHA_REGISTRO),
                                0);
                        sensor.setProcesado(cur.getInt(COL_PROCESADO));
                        res.add(sensor);

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<SensorNivelLLenado> getSensoresSinProcesar(int empresa) {

        Cursor cur;
        ArrayList<SensorNivelLLenado> res = null;
        SensorNivelLLenado sensor = null;
        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa + " AND " + campos[COL_PROCESADO] + "="
                    + 0, null, null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<SensorNivelLLenado>();

                    do {
                        sensor = new SensorNivelLLenado(
                                cur.getInt(COL_CODIGO_ELEMENTO),
                                cur.getInt(COL_CODIGO_MOVIL),
                                cur.getInt(COL_EMPRESA),
                                cur.getInt(COL_NUMERO_FRACCION),
                                cur.getLong(COL_FECHA_REGISTRO),
                                0);
                        sensor.setProcesado(cur.getInt(COL_PROCESADO));
                        res.add(sensor);

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public SensorNivelLLenado getSensorByElemento(int codigoElemento, int empresa) {
        Cursor cur;
        SensorNivelLLenado res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_CODIGO_ELEMENTO]
                    + "=" + codigoElemento + " AND " + campos[COL_EMPRESA]
                    + "=" + empresa, null, null, null, null)) != null) {

                if (cur.moveToFirst()) {
                    res = new SensorNivelLLenado(
                          cur.getInt(COL_CODIGO_ELEMENTO),
                          cur.getInt(COL_CODIGO_MOVIL),
                          cur.getInt(COL_EMPRESA),
                          cur.getInt(COL_NUMERO_FRACCION),
                          cur.getLong(COL_FECHA_REGISTRO),
                          0);
                    res.setProcesado(cur.getInt(COL_PROCESADO));
                }

                cur.close();

            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public SensorNivelLLenado getFirstSensorSinProcesarByMovil(int empresa, int codigoMovil) {
        Cursor cur;
        SensorNivelLLenado res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                            + empresa + " AND " + campos[COL_PROCESADO] + "=0 AND " +
                    campos[COL_CODIGO_MOVIL] + "=" + codigoMovil, null,
                    null, null, campos[COL_FECHA_REGISTRO])) != null) {

                if (cur.moveToFirst())
                    res = new SensorNivelLLenado(
                            cur.getInt(COL_CODIGO_ELEMENTO),
                            cur.getInt(COL_CODIGO_MOVIL),
                            cur.getInt(COL_EMPRESA),
                            cur.getInt(COL_NUMERO_FRACCION),
                            cur.getLong(COL_FECHA_REGISTRO),
                            0);
                if (res != null)
                    res.setProcesado(cur.getInt(COL_PROCESADO));

                cur.close();

            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(SensorNivelLLenado reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_CODIGO_ELEMENTO] + "=" + reg.getCodigoElemento()
                            + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }
}
