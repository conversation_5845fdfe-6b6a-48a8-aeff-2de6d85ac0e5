package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;
import com.movisat.utilities.Utils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DBIncidenciaEstado {
   private static final String TABLE_NAME = "incidencias_estados";
   private static final int COL_ID = 0;
   private static final int COL_ID_EXTERNO = 1;
   private static final int COL_EMPRESA = 2;
   private static final int COL_INCIDENCIA = 3;
   private static final int COL_INCIDENCIA_EXTERNO = 4;
   private static final int COL_ESTADO = 5;
   private static final int COL_FECHA = 6;
   private static final int COL_ES_AVISO_FALSO = 7;
   private static final int COL_OBSERVACION = 8;
   //private final String TABLE_NAME_INCIDENCIAS_FOTOS = "incidencias_fotos";
   private SQLiteDatabase db = null;
   private final String[] campos =
         new String[]{
               "id",
               "idExterno",
               "empresa",
               "incidencia",
               "incidenciaExterno",
               "estado",
               "fecha",
               "esAvisoFalso",
               "observacion"
         };

   public DBIncidenciaEstado() {
      try {
         db = Database.getConnection(
               MainActivity.getInstance().getDatabasePath("ecosat.sqlite").getPath(),
               SQLiteDatabase.OPEN_READWRITE);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   public void close() {
      try {
         if (db != null)
            db.close();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   /**
    * Elimina los datos anteriores a una fecha
    *
    * @param fecha
    */
   public void deletePreviousDataToDateBy(Date fecha, int idEmpresa) {
      String dateStr = Utils.datetimeToString(fecha, "yyyy-MM-dd HH:mm:ss");
      Cursor cur;
      DBIncidenciaFoto dbIncidenciaFoto = null;
      DBIncidencia dbIncidencia = null;

      // Obtengo el id del estado cerrada
      Estado estadoCerrada = new DBEstados().getEstadoByName("cerrada", idEmpresa);
      int idEstadoCerrada = estadoCerrada != null ? estadoCerrada.getIdExterno() : 0;

      // Obtengo las incidencias cerradas anteriores a la fecha
      String whereClause = campos[COL_ESTADO] + "=? AND " + campos[COL_FECHA] + "<?";
      String[] whereArgs = new String[] { String.valueOf(idEstadoCerrada), dateStr };
      cur = db.query(TABLE_NAME, campos, whereClause, whereArgs, null, null, campos[COL_ID] + " DESC");

      if (cur != null && cur.moveToFirst()) {
         dbIncidenciaFoto = new DBIncidenciaFoto();
         dbIncidencia = new DBIncidencia();

         do {
            try {
               int idIncidencia = cur.getInt(COL_INCIDENCIA);

               dbIncidenciaFoto.deleteByIdIncidencia(idIncidencia, idEmpresa);
               deleteByIncidenciaInterno(idIncidencia, idEmpresa);
               dbIncidencia.deleteByIdInterno(idIncidencia, idEmpresa);
            } catch (Exception ex) {
               MyLoggerHandler.getInstance().error(ex);
            }
         } while (cur.moveToNext());
         cur.close();
         dbIncidenciaFoto.close();
         dbIncidencia.close();
      }
   }

   public boolean deleteByIncidenciaInterno(int idIncidencia, int empresa) {
      try {
         String whereClause = campos[COL_INCIDENCIA] + "=? AND " + campos[COL_EMPRESA] + "=?";
         String[] whereArgs = new String[] { String.valueOf(idIncidencia), String.valueOf(empresa) };

         return db.delete(TABLE_NAME, whereClause, whereArgs) > 0;
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         return false;
      }
   }

   public long insert(IncidenciaEstado reg) {
      long res = 0;

      try {

         ContentValues values = new ContentValues();

         // values.put("id", reg.getId());
         values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
         values.put(campos[COL_EMPRESA], reg.getEmpresa());
         values.put(campos[COL_INCIDENCIA], reg.getIdIncidencia());
         values.put(campos[COL_INCIDENCIA_EXTERNO],
               reg.getIdIncidenciaExterno());
         values.put(campos[COL_ESTADO], reg.getIdEstado());
         values.put(campos[COL_FECHA], reg.getFecha().toString());
         values.put(campos[COL_ES_AVISO_FALSO], reg.esAvisoFalso());
         values.put(campos[COL_OBSERVACION], reg.getObservacion());

         res = db.insert(TABLE_NAME, null, values);

      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public boolean update(IncidenciaEstado reg, boolean byExterno) {
      try {
         ContentValues values = new ContentValues();
         values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
         values.put(campos[COL_EMPRESA], reg.getEmpresa());
         values.put(campos[COL_INCIDENCIA], reg.getIdIncidencia());
         values.put(campos[COL_INCIDENCIA_EXTERNO], reg.getIdIncidenciaExterno());
         values.put(campos[COL_ESTADO], reg.getIdEstado());
         values.put(campos[COL_FECHA], reg.getFecha());
         values.put(campos[COL_ES_AVISO_FALSO], reg.esAvisoFalso());
         values.put(campos[COL_OBSERVACION], reg.getObservacion());

         String whereById = byExterno
               ? campos[COL_ID_EXTERNO] + "=? AND " + campos[COL_EMPRESA] + "=?"
               : campos[COL_ID] + "=? AND " + campos[COL_EMPRESA] + "=?";

         String[] whereArgsById = {
               String.valueOf(byExterno ? reg.getIdExterno() : reg.getId()),
               String.valueOf(reg.getEmpresa())};

         int rowsAffected = db.update(TABLE_NAME, values, whereById, whereArgsById);

         return rowsAffected > 0;
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         return false;
      }
   }

   public boolean delete(IncidenciaEstado reg) {
      boolean res = false;

      try {

         if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
                     + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
               null) > 0)
            res = true;

      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public int getCount(int empresa) {
      int res = 0;
      Cursor cur;

      try {

         if ((cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
               "empresa=" + empresa, null, null, null, null)) != null) {

            if (cur.moveToFirst())
               res = cur.getInt(0);

            cur.close();
         }

      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public void deleteAll(int empresa) {

      try {

         db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
      }

   }

   public IncidenciaEstado getByIdInterno(int id, int empresa) {
      Cursor cur;
      IncidenciaEstado res = null;

      try {
         if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id
                     + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
               null, null, null)) != null) {

            if (cur.moveToFirst())
               res =
                     new IncidenciaEstado(
                           cur.getInt(COL_ID),
                           cur.getInt(COL_ID_EXTERNO),
                           cur.getInt(COL_EMPRESA),
                           cur.getInt(COL_INCIDENCIA),
                           cur.getInt(COL_INCIDENCIA_EXTERNO),
                           cur.getInt(COL_ESTADO),
                           cur.getString(COL_FECHA),
                           cur.getInt(COL_ES_AVISO_FALSO),
                           cur.getString(COL_OBSERVACION));
            cur.close();
         }
      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public IncidenciaEstado getByIdExterno(int id, int empresa) {
      Cursor cur;
      IncidenciaEstado res = null;

      try {
         if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO] + "=" + id
                     + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
               null, null, null)) != null) {

            if (cur.moveToFirst())
               res = new IncidenciaEstado(
                     cur.getInt(COL_ID),
                     cur.getInt(COL_ID_EXTERNO),
                     cur.getInt(COL_EMPRESA),
                     cur.getInt(COL_INCIDENCIA),
                     cur.getInt(COL_INCIDENCIA_EXTERNO),
                     cur.getInt(COL_ESTADO),
                     cur.getString(COL_FECHA),
                     cur.getInt(COL_ES_AVISO_FALSO),
                     cur.getString(COL_OBSERVACION));

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public IncidenciaEstado getLastEstadoIncidencia(int incidenciaId, int empresa, boolean findByIdExterno) {
      if (incidenciaId == 0) return null;
      Cursor cur = null;
      IncidenciaEstado res = null;
      String selection;

      try {
         if (findByIdExterno) {
            selection = campos[COL_INCIDENCIA_EXTERNO] + "=? AND " + campos[COL_EMPRESA] + "=?";
         } else {
            selection = campos[COL_INCIDENCIA] + "=? AND " + campos[COL_EMPRESA] + "=?";
         }
         cur = db.query(TABLE_NAME, campos, selection, new String[]{String.valueOf(incidenciaId), String.valueOf(empresa)},
               null, null, campos[COL_FECHA] + " DESC");

         if (cur != null && cur.moveToFirst()) {
            res = new IncidenciaEstado(
                  cur.getInt(COL_ID),
                  cur.getInt(COL_ID_EXTERNO),
                  cur.getInt(COL_EMPRESA),
                  cur.getInt(COL_INCIDENCIA),
                  cur.getInt(COL_INCIDENCIA_EXTERNO),
                  cur.getInt(COL_ESTADO),
                  cur.getString(COL_FECHA),
                  cur.getInt(COL_ES_AVISO_FALSO),
                  cur.getString(COL_OBSERVACION)
            );
         }
      } catch (Exception e) {
         MyLoggerHandler.getInstance().error(e);
      } finally {
         if (cur != null) {
            cur.close();
         }
      }

      return res;
   }

   /**
    * Obtiene el estado de incidencia cuyo id y fecha coincide con los especificados.
    */
   public IncidenciaEstado getEstadoIncidenciaByDate(int incidenciaId, int empresa, String fecha, boolean findByIdExterno) {
      IncidenciaEstado res = null;
      Cursor cur = null;

      try {
         String selectionColumn = findByIdExterno ? campos[COL_INCIDENCIA_EXTERNO] : campos[COL_INCIDENCIA];

         String selection = selectionColumn + "=" + incidenciaId
               + " AND " + campos[COL_EMPRESA] + "=" + empresa
               + " AND " + campos[COL_FECHA] + "='" + fecha + "'";

         cur = db.query(TABLE_NAME, campos, selection, null, null, null, null);

         if (cur != null && cur.moveToFirst()) {
            res = new IncidenciaEstado(
                  cur.getInt(COL_ID),
                  cur.getInt(COL_ID_EXTERNO),
                  cur.getInt(COL_EMPRESA),
                  cur.getInt(COL_INCIDENCIA),
                  cur.getInt(COL_INCIDENCIA_EXTERNO),
                  cur.getInt(COL_ESTADO),
                  cur.getString(COL_FECHA),
                  cur.getInt(COL_ES_AVISO_FALSO),
                  cur.getString(COL_OBSERVACION));
         }

      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      } finally {
         if (cur != null) {
            cur.close();
         }
      }

      return res;
   }

   public boolean setIdIncidenciaExterno(int idIncidenciaEstado,
                                         int incidenciaExterno, int empresa) {
      boolean res = false;

      try {

         ContentValues values = new ContentValues();
         //

         values.put(campos[COL_INCIDENCIA_EXTERNO], incidenciaExterno);

         if (db.update(TABLE_NAME, values, campos[COL_ID] + "="
               + idIncidenciaEstado + " AND " + campos[COL_EMPRESA] + "="
               + empresa, null) > 0)
            res = true;

      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
      }

      return res;

   }

   public ArrayList<IncidenciaEstado> getAll(int empresa) {
      Cursor cur;
      ArrayList<IncidenciaEstado> res = null;

      try {

         if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
               + empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

            if (cur.moveToFirst()) {

               res = new ArrayList<IncidenciaEstado>();

               do {
                  res.add(
                        new IncidenciaEstado(
                              cur.getInt(COL_ID),
                              cur.getInt(COL_ID_EXTERNO),
                              cur.getInt(COL_EMPRESA),
                              cur.getInt(COL_INCIDENCIA),
                              cur.getInt(COL_INCIDENCIA_EXTERNO),
                              cur.getInt(COL_ESTADO),
                              cur.getString(COL_FECHA),
                              cur.getInt(COL_ES_AVISO_FALSO),
                              cur.getString(COL_OBSERVACION)));
               } while (cur.moveToNext());

            }

            cur.close();
         }

      } catch (Throwable e) {

         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   /**
    * Elimina los registros de estado de las incidencias especificadas.
    *
    * @param incidencias Lista de incidencias.
    * @return Número de registros eliminados.
    */
   public int removeAllByIncidencia(List<ItemMapa> incidencias) {
      int res = 0;
      List<Integer> id_list = new ArrayList<>();

      try {
         if (incidencias == null || incidencias.size() == 0) return 0;

         // Se almacenan los id de las incidencias en una lista
         for (ItemMapa incidencia : incidencias) {
            id_list.add(incidencia.getId());
         }

         // Se obtiene un string con los ids de las incidencias separados por comas
         String id_list_str = TextUtils.join(",", id_list);

         res = db.delete(TABLE_NAME, campos[COL_INCIDENCIA] + " IN(" + id_list_str + ")", null);

      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

}
