package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

import com.movisat.application.EcoSATApplication;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

public class DBLecturas {
    private static final String TABLE_NAME = "lecturas";
    private SQLiteDatabase db = null;
    private static final String[] campos = new String[]{"id", "fecha", "tag", "raw", "tipo"};
    private static final int COL_ID = 0;
    private static final int COL_FECHA = 1;
    private static final int COL_TAG = 2;
    private static final int COL_RAW = 3;
    private static final int COL_TIPO = 4;

    public DBLecturas() {
        try {
            db = Database.getConnection(
                  EcoSATApplication.getInstance().getDatabasePath("ecosat.sqlite").getPath(),
                  SQLiteDatabase.OPEN_READWRITE);
        } catch (Exception e) {
            // En este sitio no se puede llamar porque entrariamos en
            // un bucle infinito de llamadas ya que este objeto se
            // crea desde dentro de MyLoggerHandler.error()
            Log.e("ERROR:", e.getMessage());
        }
    }

    public void close() {
        try {
            if (db != null)
                db.close();
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public long insert(Lectura reg) {
        long res = 0;

        try {
//            int max = getCount();
//            if (max > 2000)
//                deleteOld();

            ContentValues values = new ContentValues();
            values.put(campos[COL_FECHA], reg.getFecha());
            values.put(campos[COL_TAG], reg.getTag());
            values.put(campos[COL_RAW], reg.getRaw());
            values.put(campos[COL_TIPO], reg.getTipo());

            res = db.insert(TABLE_NAME, null, values);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return res;
    }

    public boolean delete(Lectura reg) {
        try {
            if (db != null && db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId(), null) > 0)
                return true;
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return false;
    }

    public int getCount() {
        int res = 0;

        try {
            if (db != null) {
                Cursor cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                      null, null, null, null, null);
                if (cur != null) {
                    if (cur.moveToFirst())
                        res = cur.getInt(0);
                    cur.close();
                }
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    /**
     * Borramos todos excepto las ultimas 1800 lineas de log
     */
    public void deleteOld() {
        try {
            String sql = "DELETE FROM errores WHERE id NOT IN ( \n" +
                  "SELECT id FROM errores  \n" +
                  "ORDER BY fecha DESC\n" +
                  "LIMIT 1800  \n" +
                  ")";

            if (db != null)
                db.execSQL(sql);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }
}
