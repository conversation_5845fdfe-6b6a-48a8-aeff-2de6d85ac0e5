package com.movisat.database;

public class SensorNivelLLenado {

	private int id;
	private int codigoElemento;
	private int codigoMovil;
	private int empresa;
	private int numeroFraccion;
	private long fechaRegistro;
	private int procesado;
	private int vaciado_parcial;
	
	public SensorNivelLLenado(int codigoElemento, int codigoMovil, int empresa, int numeroFraccion, long fechaRegistro, int vaciado_parcial) {
		setCodigoElemento(codigoElemento);
		setCodigoMovil(codigoMovil);
		setEmpresa(empresa);
		setFechaRegistro(fechaRegistro);
		setNumeroFraccion(numeroFraccion);
		setVaciadoParcial(vaciado_parcial);
	}

	public int getCodigoElemento() {
		return codigoElemento;
	}

	public void setCodigoElemento(int codigoElemento) {
		this.codigoElemento = codigoElemento;
	}

	public int getCodigoMovil() {
		return codigoMovil;
	}

	public void setCodigoMovil(int codigoMovil) {
		this.codigoMovil = codigoMovil;
	}

	public long getFechaRegistro() {
		return fechaRegistro;
	}

	public void setFechaRegistro(long fechaRegistro) {
		this.fechaRegistro = fechaRegistro;
	}

	public int getNumeroFraccion() {
		return numeroFraccion;
	}

	public void setNumeroFraccion(int numeroFraccion) {
		this.numeroFraccion = numeroFraccion;
	}

	public int getEmpresa() {
		return empresa;
	}

	public void setEmpresa(int empresa) {
		this.empresa = empresa;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getProcesado() {
		return procesado;
	}

	public void setProcesado(int procesado) {
		this.procesado = procesado;
	}

	public void setVaciadoParcial(int vaciado) {
		this.vaciado_parcial= vaciado;
	}

	public int getVaciadoParcial() {
		return vaciado_parcial;
	}

}
