package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBIncidenciaMotivo {
	private final String TABLE_NAME = "incidencias_motivo";
	private SQLiteDatabase db = null;
	private String[] campos = (new String[] { "id", "idExterno", "empresa",
			"modelo", "nombre" });

	private final int COL_ID = 0;
	private final int COL_ID_EXTERNO = 1;
	private final int COL_EMPRESA = 2;
	private final int COL_MODELO = 3;
	private final int COL_NOMBRE = 4;

	public DBIncidenciaMotivo() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(IncidenciaMotivo reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_MODELO], reg.getModelo());
			values.put(campos[COL_NOMBRE], reg.getNombre());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(IncidenciaMotivo reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_MODELO], reg.getModelo());
			values.put(campos[COL_NOMBRE], reg.getNombre());

			if (db.update(
					TABLE_NAME,
					values,
					campos[COL_ID] + "=" + reg.getId() + " AND "
							+ campos[COL_EMPRESA] + "=" + reg.getEmpresa()
							+ " AND " + campos[COL_MODELO] + "="
							+ reg.getModelo(), null) > 0)
				res = true;
			if (!res) {
				if (db.update(TABLE_NAME, values, campos[COL_ID_EXTERNO] + "="
						+ reg.getIdExterno() + " AND " + campos[COL_EMPRESA]
						+ "=" + reg.getEmpresa() + " AND " + campos[COL_MODELO]
						+ "=" + reg.getModelo(), null) > 0)
					res = true;
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(IncidenciaMotivo reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
					+ " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa()
					+ " AND " + campos[COL_MODELO] + "=" + reg.getModelo(),
					null) > 0)
				res = true;
			if (!res) {
				if (db.delete(
						TABLE_NAME,
						campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno()
								+ " AND " + campos[COL_EMPRESA] + "="
								+ reg.getEmpresa() + " AND "
								+ campos[COL_MODELO] + "=" + reg.getModelo(),
						null) > 0)
					res = true;
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount(int empresa, int modelo) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }),
					campos[COL_EMPRESA] + "=" + empresa + " AND "
							+ campos[COL_MODELO] + "=" + modelo, null, null,
					null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll(int empresa, int modelo) {

		try {

			db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa + " AND "
					+ campos[COL_MODELO] + "=" + modelo, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public IncidenciaMotivo getByID(int id, int empresa, int modelo) {
		Cursor cur;
		IncidenciaMotivo res = null;

		try {

			if ((cur = db
					.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO] + "=" + id
							+ " AND " + campos[COL_EMPRESA] + "=" + empresa
							+ " AND " + campos[COL_MODELO] + "=" + modelo,
							null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new IncidenciaMotivo(cur.getInt(COL_ID),
							cur.getInt(COL_ID_EXTERNO),
							cur.getInt(COL_EMPRESA), cur.getInt(COL_MODELO),
							cur.getString(COL_NOMBRE));

				cur.close();
				
				if (res==null) {
					//buscamos por id externo

					if ((cur = db
							.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id
									+ " AND " + campos[COL_EMPRESA] + "=" + empresa
									+ " AND " + campos[COL_MODELO] + "=" + modelo,
									null, null, null, null)) != null) {

						if (cur.moveToFirst())
							res = new IncidenciaMotivo(cur.getInt(COL_ID),
									cur.getInt(COL_ID_EXTERNO),
									cur.getInt(COL_EMPRESA), cur.getInt(COL_MODELO),
									cur.getString(COL_NOMBRE));

						cur.close();
					}
				}
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public IncidenciaMotivo getByID(int id, int empresa) {
		Cursor cur;
		IncidenciaMotivo res = null;

		try {

			if ((cur = db
					.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO] + "=" + id
									+ " AND " + campos[COL_EMPRESA] + "=" + empresa,
							null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new IncidenciaMotivo(cur.getInt(COL_ID),
							cur.getInt(COL_ID_EXTERNO),
							cur.getInt(COL_EMPRESA), cur.getInt(COL_MODELO),
							cur.getString(COL_NOMBRE));

				cur.close();

			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public ArrayList<IncidenciaMotivo> getAll(int empresa, int modelo) {
		Cursor cur;
		ArrayList<IncidenciaMotivo> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
					+ empresa + " AND " + campos[COL_MODELO] + "=" + modelo,
					null, null, null, campos[COL_ID] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<IncidenciaMotivo>();

					do {

						res.add(new IncidenciaMotivo(cur.getInt(COL_ID), cur
								.getInt(COL_ID_EXTERNO), cur
								.getInt(COL_EMPRESA), cur.getInt(COL_MODELO),
								cur.getString(COL_NOMBRE)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
