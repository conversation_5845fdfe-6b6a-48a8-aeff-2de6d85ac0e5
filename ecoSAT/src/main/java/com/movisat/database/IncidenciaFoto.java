package com.movisat.database;

import java.io.Serializable;

public class IncidenciaFoto implements Serializable {

    private int id;
    private int idExterno;
    private int incidenciaInterno;
    private int incidenciaExterno;
    private int empresa;
    private int usuario;
    private long fecha;
    private String imageBase64;

    public IncidenciaFoto(int id, int idExterno, int incidenciaInterno,
                          int incidenciaExterno, int empresa, int usuario, String imageBase64) {

        setId(id);
        setIdExterno(idExterno);
        setIncidenciaInterno(incidenciaInterno);
        setIncidenciaExterno(incidenciaExterno);
        setEmpresa(empresa);
        setUsuario(usuario);
        setImageBase64(imageBase64);
    }

    public void setIncidenciaInterno(int incidencia) {
        this.incidenciaInterno = incidencia;
    }

    public void setIncidenciaExterno(int incidenciaExterno) {
        this.incidenciaExterno = incidenciaExterno;
    }

    public void setId(int id) {

        this.id = id;
    }

    public void setIdExterno(int idExterno) {

        this.idExterno = idExterno;
    }

    public void setEmpresa(int empresa) {

        this.empresa = empresa;
    }

    public int getId() {

        return id;
    }

    public int getIdExterno() {

        return idExterno;
    }

    public int getEmpresa() {

        return empresa;
    }

    public long getFecha() {

        return fecha;
    }

    public int getIncidenciaInterno() {
        return incidenciaInterno;
    }

    public int getIncidenciaExterno() {
        return incidenciaExterno;
    }

    public String getImageBase64() {
        return imageBase64;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    public void setFecha(long millisecond) {
        this.fecha = millisecond;
    }

    @Override
    public String toString() {
        return "";
    }

    public int getUsuario() {
        return usuario;
    }

    public void setUsuario(int usuario) {
        this.usuario = usuario;
    }

    public boolean hasImageBase64() {
        return imageBase64 != null && !imageBase64.isEmpty();
    }
}