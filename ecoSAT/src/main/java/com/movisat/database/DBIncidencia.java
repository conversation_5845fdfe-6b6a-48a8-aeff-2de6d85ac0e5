package com.movisat.database;

import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Config;
import com.movisat.utilities.Database;

import java.util.ArrayList;
import java.util.Arrays;

public class DBIncidencia {
   private static final String TABLE_NAME = "incidencias";
   private SQLiteDatabase db = null;
   private final String[] campos = (new String[]{"id", "idExterno", "empresa", "tipo", "modelo", "motivo", "observ",
           "estado", "elemento", "lat", "lon", "usuario", "tipoPropietario", "propietario", "fechaModificacion",
           "ultimoEstado", "totalRegistros", "movil", "idSincro", "ruta", "rutaH", "fechaBaja", "latitudInt",
           "longitudInt", "motivoDesc", "infoGeo", "fecha", "tipoElem", "matricula", "imagen", "idEquipo",
           "emailCiudadano", "validada", "informado", "acera", "textoCambioEstado", "borrado", "ordenTrabajo",
           "x", "y", "xMercator", "yMercator", "municipio"});
   private static final int COL_ID = 0;
   private static final int COL_ID_EXTERNO = 1;
   private static final int COL_EMPRESA = 2;
   private static final int COL_TIPO = 3;
   private static final int COL_MODELO = 4;
   private static final int COL_MOTIVO = 5;
   private static final int COL_OBSERVACIONES = 6;
   private static final int COL_ULTIMO_ESTADO = 7;
   private static final int COL_ELEMENTO = 8;
   private static final int COL_LATITUD = 9;
   private static final int COL_LONGITUD = 10;
   private static final int COL_USUARIO = 11;
   private static final int COL_TIPO_PROPIETARIO = 12;
   private static final int COL_PROPIETARIO = 13;
   private static final int COL_FECHA_ESTADO = 14;
   private static final int COL_ULTIMO_ESTADO_NEW = 15;
   private static final int COL_TOTAL_REGISTROS = 16;
   private static final int COL_MOVIL = 17;
   private static final int COL_ID_SINCRO = 18;
   private static final int COL_RUTA = 19;
   private static final int COL_RUTA_H = 20;
   private static final int COL_FECHA_BAJA = 21;
   private static final int COL_LATITUD_INT = 22;
   private static final int COL_LONGITUD_INT = 23;
   private static final int COL_MOTIVO_DESC = 24;
   private static final int COL_INFO_GEO = 25;
   private static final int COL_FECHA = 26;
   private static final int COL_TIPO_ELEM = 27;
   private static final int COL_MATRICULA = 28;
   private static final int COL_IMAGEN = 29;
   private static final int COL_ID_EQUIPO = 30;
   private static final int COL_EMAIL_CIUDADANO = 31;
   private static final int COL_VALIDADA = 32;
   private static final int COL_INFORMADO = 33;
   private static final int COL_ACERA = 34;
   private static final int COL_TEXTO_CAMBIO_ESTADO = 35;
   private static final int COL_BORRADO = 36;
   private static final int COL_ORDEN_TRABAJO = 37;
   private static final int COL_X = 38;
   private static final int COL_Y = 39;
   private static final int COL_X_MERCATOR = 40;
   private static final int COL_Y_MERCATOR = 41;
   private static final int COL_MUNICIPIO = 42;

   public DBIncidencia() {
      try {
         db = Database.getConnection(MainActivity.getInstance().getDatabasePath("ecosat.sqlite").getPath(),
               SQLiteDatabase.OPEN_READWRITE);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }
   
   public DBIncidencia(boolean readOnly) {
      try {
         db = Database.getConnection(MainActivity.getInstance().getDatabasePath("ecosat.sqlite").getPath(),
               SQLiteDatabase.OPEN_READWRITE);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   public void close() {
      try {
         if (db != null)
            db.close();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   /**
    * Elimina las incidencias con código de empresa especificado que no tengan ningún estado o
    * estén cerradas.
    *
    * @return Número de incidencias sin estado eliminadas.
    */
   public int removeIncidenciasSinEstado(int empresa) {
      int removeCount = 0;

      try {
         ArrayList<ItemMapa> incidencias = getAll(empresa);
         if (incidencias != null) {
            DBIncidenciaEstado dbIncidenciaEstado = new DBIncidenciaEstado();
            DBIncidenciaFoto dbIncidenciaFotos = new DBIncidenciaFoto();
            for (ItemMapa incidencia : incidencias) {
               IncidenciaEstado estado = dbIncidenciaEstado.getLastEstadoIncidencia(incidencia.getId(), empresa, false);
               if (estado == null) {
                  delete(incidencia, empresa);
                  dbIncidenciaFotos.deleteByIncidenciaIdExterno(incidencia.getIdExterno(), empresa);
                  removeCount++;
               }
            }
            dbIncidenciaEstado.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return removeCount;
   }

   /**
    * Elimina las incidencias con código de empresa y de usuario especificados que no tengan
    * ningún estado.
    */
   /* public void removeIncidenciasSinEstado(int empresa, int usuario) {
        ArrayList<ItemMapa> incidencias = getAll(empresa, usuario);
        if (incidencias != null) {
            DBIncidenciaEstado dbIncidenciaEstado = new DBIncidenciaEstado();
            for (ItemMapa incidencia : incidencias) {
                IncidenciaEstado estado = dbIncidenciaEstado.getLastEstadoIncidencia(incidencia.getId(), empresa,
                false);
                if (estado == null) {
                    delete(incidencia, empresa);
                }
            }
            dbIncidenciaEstado.close();
        }
    }*/

   /**
    * @param reg
    * @return id de la incidencia insertada
    */
   public long insert(Incidencia reg) {
      if (reg.getId() != 0)
         return -1;

      long res = -1;
      try {
         ContentValues values = new ContentValues();
         values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
         values.put(campos[COL_EMPRESA], reg.getEmpresa());
         values.put(campos[COL_TIPO], reg.getTipo());
         values.put(campos[COL_MODELO], reg.getModelo());
         values.put(campos[COL_USUARIO], reg.getUsuario());
         values.put(campos[COL_MOTIVO], reg.getMotivo());
         values.put(campos[COL_ULTIMO_ESTADO], reg.getUltimoEstado());
         values.put(campos[COL_ELEMENTO], reg.getElemento());
         values.put(campos[COL_LATITUD], reg.getPosition().latitude);
         values.put(campos[COL_LONGITUD], reg.getPosition().longitude);
         values.put(campos[COL_OBSERVACIONES], reg.getObserv());
         values.put(campos[COL_TIPO_PROPIETARIO], reg.getTipoPropietario());
         values.put(campos[COL_PROPIETARIO], reg.getPropietario());
         values.put(campos[COL_FECHA_ESTADO], reg.getFechaUltimoEstado());
         values.put(campos[COL_ULTIMO_ESTADO_NEW], reg.getUltimoEstado());
         values.put(campos[COL_TOTAL_REGISTROS], reg.getTotalRegistros());
         values.put(campos[COL_MOVIL], reg.getMovil());
         values.put(campos[COL_ID_SINCRO], reg.getIdSincro());
         values.put(campos[COL_RUTA], reg.getRuta());
         values.put(campos[COL_RUTA_H], reg.getRutaH());
         values.put(campos[COL_FECHA_BAJA], reg.getFechaBaja());
         values.put(campos[COL_LATITUD_INT], reg.getLatitudInt());
         values.put(campos[COL_LONGITUD_INT], reg.getLongitudInt());
         values.put(campos[COL_MOTIVO_DESC], reg.getMotivoDesc());
         values.put(campos[COL_INFO_GEO], reg.getInfoGeo());
         values.put(campos[COL_FECHA], reg.getFecha());
         values.put(campos[COL_TIPO_ELEM], reg.getTipoElem());
         values.put(campos[COL_MATRICULA], reg.getMatricula());
         values.put(campos[COL_IMAGEN], reg.getImagen());
         values.put(campos[COL_ID_EQUIPO], reg.getIdEquipo());
         values.put(campos[COL_EMAIL_CIUDADANO], reg.getEmailCiudadano());
         values.put(campos[COL_VALIDADA], reg.getValidada());
         values.put(campos[COL_INFORMADO], reg.getInformado());
         values.put(campos[COL_ACERA], reg.getAcera());
         values.put(campos[COL_TEXTO_CAMBIO_ESTADO], reg.getTextoCambioEstado());
         values.put(campos[COL_BORRADO], reg.isBorrado() ? 1 : 0);
         values.put(campos[COL_ORDEN_TRABAJO], reg.isOrdenTrabajo() ? 1 : 0);
         values.put(campos[COL_X], reg.getX());
         values.put(campos[COL_Y], reg.getY());
         values.put(campos[COL_X_MERCATOR], reg.getXMercator());
         values.put(campos[COL_Y_MERCATOR], reg.getYMercator());
         values.put(campos[COL_MUNICIPIO], reg.getMunicipio());

         res = db.insert(TABLE_NAME, null, values);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   /**
    * @param reg
    * @return id de la incidencia insertada
    */
   public long insertOrUpdate(Incidencia reg) {
      long res = -1;

      try {
         ContentValues values = new ContentValues();
         values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
         values.put(campos[COL_EMPRESA], reg.getEmpresa());
         values.put(campos[COL_TIPO], reg.getTipo());
         values.put(campos[COL_MODELO], reg.getModelo());
         values.put(campos[COL_USUARIO], reg.getUsuario());
         values.put(campos[COL_MOTIVO], reg.getMotivo());
         values.put(campos[COL_ULTIMO_ESTADO], reg.getUltimoEstado());
         values.put(campos[COL_ELEMENTO], reg.getElemento());
         values.put(campos[COL_LATITUD], reg.getPosition().latitude);
         values.put(campos[COL_LONGITUD], reg.getPosition().longitude);
         values.put(campos[COL_OBSERVACIONES], reg.getObserv());
         values.put(campos[COL_TIPO_PROPIETARIO], reg.getTipoPropietario());
         values.put(campos[COL_PROPIETARIO], reg.getPropietario());
         values.put(campos[COL_FECHA_ESTADO], reg.getFechaUltimoEstado());
         values.put(campos[COL_ULTIMO_ESTADO_NEW], reg.getUltimoEstado());
         values.put(campos[COL_TOTAL_REGISTROS], reg.getTotalRegistros());
         values.put(campos[COL_MOVIL], reg.getMovil());
         values.put(campos[COL_ID_SINCRO], reg.getIdSincro());
         values.put(campos[COL_RUTA], reg.getRuta());
         values.put(campos[COL_RUTA_H], reg.getRutaH());
         values.put(campos[COL_FECHA_BAJA], reg.getFechaBaja());
         values.put(campos[COL_LATITUD_INT], reg.getLatitudInt());
         values.put(campos[COL_LONGITUD_INT], reg.getLongitudInt());
         values.put(campos[COL_MOTIVO_DESC], reg.getMotivoDesc());
         values.put(campos[COL_INFO_GEO], reg.getInfoGeo());
         values.put(campos[COL_FECHA], reg.getFecha());
         values.put(campos[COL_TIPO_ELEM], reg.getTipoElem());
         values.put(campos[COL_MATRICULA], reg.getMatricula());
         values.put(campos[COL_IMAGEN], reg.getImagen());
         values.put(campos[COL_ID_EQUIPO], reg.getIdEquipo());
         values.put(campos[COL_EMAIL_CIUDADANO], reg.getEmailCiudadano());
         values.put(campos[COL_VALIDADA], reg.getValidada());
         values.put(campos[COL_INFORMADO], reg.getInformado());
         values.put(campos[COL_ACERA], reg.getAcera());
         values.put(campos[COL_TEXTO_CAMBIO_ESTADO], reg.getTextoCambioEstado());
         values.put(campos[COL_BORRADO], reg.isBorrado() ? 1 : 0);
         values.put(campos[COL_ORDEN_TRABAJO], reg.isOrdenTrabajo() ? 1 : 0);
         values.put(campos[COL_X], reg.getX());
         values.put(campos[COL_Y], reg.getY());
         values.put(campos[COL_X_MERCATOR], reg.getXMercator());
         values.put(campos[COL_Y_MERCATOR], reg.getYMercator());
         values.put(campos[COL_MUNICIPIO], reg.getMunicipio());

         int idExterno = reg.getIdExterno();
         if (idExterno != 0) {
            String selectQuery = "SELECT " + campos[COL_FECHA_ESTADO] + " FROM " + TABLE_NAME
                  + " WHERE " + campos[COL_ID_EXTERNO] + " = ?";
            Cursor cursor = db.rawQuery(selectQuery, new String[]{String.valueOf(reg.getIdExterno())});
         }

         res = db.insert(TABLE_NAME, null, values);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public boolean update(Incidencia reg) {
      if (reg.getId() == 0 && reg.getIdExterno() == 0) {
         return false;
      }

      boolean res = false;
      try {
         ContentValues values = new ContentValues();

         values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
         values.put(campos[COL_EMPRESA], reg.getEmpresa());
         values.put(campos[COL_TIPO], reg.getTipo());
         values.put(campos[COL_MODELO], reg.getModelo());
         values.put(campos[COL_USUARIO], reg.getUsuario());
         values.put(campos[COL_MOTIVO], reg.getMotivo());
         values.put(campos[COL_ULTIMO_ESTADO], reg.getUltimoEstado());
         values.put(campos[COL_ELEMENTO], reg.getElemento());
         values.put(campos[COL_LATITUD], reg.getPosition().latitude);
         values.put(campos[COL_LONGITUD], reg.getPosition().longitude);
         values.put(campos[COL_OBSERVACIONES], reg.getObserv());
         values.put(campos[COL_TIPO_PROPIETARIO], reg.getTipoPropietario());
         values.put(campos[COL_PROPIETARIO], reg.getPropietario());
         values.put(campos[COL_FECHA_ESTADO], reg.getFechaUltimoEstado());
         values.put(campos[COL_ULTIMO_ESTADO_NEW], reg.getUltimoEstado());
         values.put(campos[COL_TOTAL_REGISTROS], reg.getTotalRegistros());
         values.put(campos[COL_MOVIL], reg.getMovil());
         values.put(campos[COL_ID_SINCRO], reg.getIdSincro());
         values.put(campos[COL_RUTA], reg.getRuta());
         values.put(campos[COL_RUTA_H], reg.getRutaH());
         values.put(campos[COL_FECHA_BAJA], reg.getFechaBaja());
         values.put(campos[COL_LATITUD_INT], reg.getLatitudInt());
         values.put(campos[COL_LONGITUD_INT], reg.getLongitudInt());
         values.put(campos[COL_MOTIVO_DESC], reg.getMotivoDesc());
         values.put(campos[COL_INFO_GEO], reg.getInfoGeo());
         values.put(campos[COL_FECHA], reg.getFecha());
         values.put(campos[COL_TIPO_ELEM], reg.getTipoElem());
         values.put(campos[COL_MATRICULA], reg.getMatricula());
         values.put(campos[COL_IMAGEN], reg.getImagen());
         values.put(campos[COL_ID_EQUIPO], reg.getIdEquipo());
         values.put(campos[COL_EMAIL_CIUDADANO], reg.getEmailCiudadano());
         values.put(campos[COL_VALIDADA], reg.getValidada());
         values.put(campos[COL_INFORMADO], reg.getInformado());
         values.put(campos[COL_ACERA], reg.getAcera());
         values.put(campos[COL_TEXTO_CAMBIO_ESTADO], reg.getTextoCambioEstado());
         values.put(campos[COL_BORRADO], reg.isBorrado() ? 1 : 0);
         values.put(campos[COL_ORDEN_TRABAJO], reg.isOrdenTrabajo() ? 1 : 0);
         values.put(campos[COL_X], reg.getX());
         values.put(campos[COL_Y], reg.getY());
         values.put(campos[COL_X_MERCATOR], reg.getXMercator());
         values.put(campos[COL_Y_MERCATOR], reg.getYMercator());
         values.put(campos[COL_MUNICIPIO], reg.getMunicipio());

         if (reg.getId() != 0)
            res = db.update(TABLE_NAME, values,
                  campos[COL_ID] + "=" + reg.getId() + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(), null) > 0;

         if (!res && reg.getIdExterno() != 0) {
            res = db.update(TABLE_NAME, values,
                  campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno() + " AND " + campos[COL_EMPRESA] + "=" +
                        reg.getEmpresa(), null) > 0;
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public boolean delete(ItemMapa reg, int empresa) {
      boolean res = false;

      try {
         if (db.delete(TABLE_NAME,
               campos[COL_ID] + "=" + reg.getId() + " AND " + campos[COL_EMPRESA] + "=" + empresa, null) > 0)
            res = true;

         if (!res) {
            if (db.delete(TABLE_NAME,
                  campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno() + " AND " + campos[COL_EMPRESA] + "=" +
                        empresa, null) > 0)
               res = true;
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public boolean delete(Incidencia reg) {
      boolean res = false;

      try {
         if (db.delete(TABLE_NAME,
               campos[COL_ID] + "=" + reg.getId() + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(), null) > 0)
            res = true;

         else if (db.delete(TABLE_NAME,
               campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno() + " AND " + campos[COL_EMPRESA] + "=" +
                     reg.getEmpresa(), null) > 0)
            res = true;

         // elimino las fotos de la incidencia
         DBIncidenciaFoto dbIncidenciaFotos = new DBIncidenciaFoto();
         dbIncidenciaFotos.deleteByIncidenciaIdExterno(reg.getIdExterno(), reg.getEmpresa());
         dbIncidenciaFotos.close();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public boolean deleteByIdInterno(int idInterno, int empresa) {
      try {
         String whereClause = campos[COL_ID] + "=? AND " + campos[COL_EMPRESA] + "=?";
         String[] whereArgs = new String[]{String.valueOf(idInterno), String.valueOf(empresa)};

         return db.delete(TABLE_NAME, whereClause, whereArgs) > 0;
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
         return false;
      }
   }

   public int getCount(int empresa) {
      int res = 0;
      Cursor cur;

      try {
         if ((cur = db.query(TABLE_NAME, (new String[]{"count(*)"}), "empresa=" + empresa, null, null, null, null)) !=
               null) {

            if (cur.moveToFirst())
               res = cur.getInt(0);

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

    /*public void deleteAll(int empresa) {

        try {

            db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }*/

   public Incidencia getByID(int id, int empresa) {
      Cursor cur;
      Incidencia res = null;

      try {
         if ((cur = db.query(TABLE_NAME, campos,
               campos[COL_ID_EXTERNO] + "=" + id + " AND " + campos[COL_EMPRESA] + "=" +
                     empresa, null, null, null, null)) != null) {

            if (cur.moveToFirst())
               res = incidenciaFromCursor(cur);

            cur.close();

            if (res == null) {
               // Busco por el cidigo temporal por si se trata de un
               // elemento que todavia no se ha sincronizado
               if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id + " AND " + campos[COL_EMPRESA] + "=" +
                     empresa, null, null, null, null)) != null) {

                  if (cur.moveToFirst())
                     res = incidenciaFromCursor(cur);

                  cur.close();
               }
            }
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public Incidencia getByIdInterno(int id, int empresa) {
      Cursor cur;
      Incidencia res = null;

      try {
         if ((cur = db.query(TABLE_NAME, campos,
               campos[COL_ID] + "=" + id + " AND " + campos[COL_EMPRESA] + "=" + empresa, null, null, null, null)) !=
               null) {

            if (cur.moveToFirst())
               res = incidenciaFromCursor(cur);

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public ItemMapa getItemMapaByIdInterno(int id) {
      ItemMapa res = null;

      try {
         Cursor cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id,
               null, null, null, null);
         if (cur != null) {
            if (cur.moveToFirst())
               res = incidenciaFromCursor(cur);

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public Incidencia getByIdExterno(int id, int empresa) {
      if (id == 0)
         return null;
      Cursor cur;
      Incidencia res = null;

      try {
         if ((cur = db.query(TABLE_NAME, campos,
               campos[COL_ID_EXTERNO] + "=" + id + " AND " + campos[COL_EMPRESA] + "=" +
                     empresa, null, null, null, null)) != null) {

            if (cur.moveToFirst())
               res = incidenciaFromCursor(cur);

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public ArrayList<Incidencia> getByElemId(int id, int empresa, String estados, String tipos) {
      Cursor cur;
      ArrayList<Incidencia> res = null;

      try {
         if (estados == null) estados = "";
         if (estados.equals("todos")) {
            estados = "";
         } else if (estados.equals("ninguno")) {
            estados = " AND 1=0 ";  // No mostrar ningún resultado
         } else if (!estados.equals("")) {
            estados = " AND " + campos[COL_ULTIMO_ESTADO] + " IN (0," + estados + ") ";
         }
         
         if (tipos == null) tipos = "";
         if (tipos.equals("todos")) {
            tipos = "";
         } else if (tipos.equals("ninguno")) {
            tipos = " AND 1=0 ";  // No mostrar ningún resultado
         } else if (!tipos.equals("")) {
            tipos = " AND " + campos[COL_TIPO] + " IN (" + tipos + ") ";
         }

         if ((cur = db.query(TABLE_NAME, campos,
               campos[COL_ELEMENTO] + "=" + id + " AND " + campos[COL_EMPRESA] + "=" + empresa +
                     estados + tipos, null, null, null, null)) != null) {

            if (cur.moveToFirst()) {
               res = new ArrayList<Incidencia>();

               do {
                  res.add(incidenciaFromCursor(cur));
               } while (cur.moveToNext());
            }

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   /**
    * Obtiene todas las incidencias del histórico por el código de empresa.
    */
   public synchronized ArrayList<ItemMapa> getAll(int empresa) {
      Cursor cur;
      ArrayList<ItemMapa> res = null;

      try {
         if ((cur = db.query(TABLE_NAME, campos,
               campos[COL_EMPRESA] + "=" + empresa, null, null, null, "id asc", "1000")) != null) {
            if (cur.moveToFirst()) {
               res = new ArrayList<ItemMapa>();
               do {
                  res.add(incidenciaFromCursor(cur));
               } while (cur.moveToNext());
            }
            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public ArrayList<ItemMapa> getAll(int empresa, int usuario) {
      Cursor cur;
      ArrayList<ItemMapa> res = null;

      String estadosVisibles = Config.getInstance().getValueUsuario("inciVisibles", "");
      String whereEstado = (!estadosVisibles.isEmpty() && !estadosVisibles.equals("todos")) ?
            " AND " + campos[COL_ULTIMO_ESTADO] + " IN (0," + estadosVisibles + ")" : "";

      try {
         if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa +
               (!MainActivity.getInstance().getUsuAdmin() ?
                     " AND (" + campos[COL_USUARIO] + "=" + usuario + " OR (" + campos[COL_PROPIETARIO] + "=" +
                           usuario + " AND " + campos[COL_TIPO_PROPIETARIO] + "=1))" : "") +
               whereEstado, null, null, null, campos[COL_ID] + " DESC")) != null) {

            if (cur.moveToFirst()) {

               res = new ArrayList<ItemMapa>();

               do {
                  res.add(incidenciaFromCursor(cur));
               } while (cur.moveToNext());
            }

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   // recupero todas las incidencias si soy administrador ó
   // recupero solo mis incidencias o en las que sea propietario
   public ArrayList<Incidencia> getAllIncidencias(int empresa, int usuario, boolean filtro_estados) {
      Cursor cur;
      ArrayList<Incidencia> res = new ArrayList<>();
      String whereEstado = "";
      String whereUsuario = "";

      if (filtro_estados) {
         String estadosVisibles = Config.getInstance().getValueUsuario("inciVisibles", "");
         if (estadosVisibles.equals("ninguno"))
            return res;
         if (!estadosVisibles.isEmpty() && !estadosVisibles.equals("todos"))
            whereEstado = " AND " + campos[COL_ULTIMO_ESTADO] + " IN (0," + estadosVisibles + ")";
      }

      if (!MainActivity.getInstance().getUsuAdmin()) {
         whereUsuario =
               " AND (" + campos[COL_USUARIO] + "=" + usuario + " OR (" + campos[COL_PROPIETARIO] + "=" + usuario +
                     " AND " + campos[COL_TIPO_PROPIETARIO] + "=1))";
      }

      try {
         cur = db.query(TABLE_NAME, campos,
               campos[COL_EMPRESA] + "=" + empresa + whereUsuario + whereEstado, null, null, null, null);

         if (cur != null) {
            while (cur.moveToNext()) {
               res.add(incidenciaFromCursor(cur));
            }
            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
      return res;
   }

   @SuppressLint("Range")
   public ArrayList<Incidencia> getAllIncidenciasWithNombrePropietario(int empresa, int usuario, boolean filtro) {
      Cursor cur;
      ArrayList<Incidencia> res = new ArrayList<>();
      String whereEstado = "";
      String whereUsuario = "";
      String whereTipos = "";

      // Filtro estados
      if (filtro) {
         String estadosVisibles = Config.getInstance().getValueUsuario("inciVisibles", "");
         if (estadosVisibles.equals("ninguno"))
            return res;
         if (!estadosVisibles.isEmpty() && !estadosVisibles.equals("todos"))
            whereEstado = " AND " + campos[COL_ULTIMO_ESTADO] + " IN (0," + estadosVisibles + ")";
      }

      // Filtro tipos
       if (filtro) {
           String tiposVisibles = Config.getInstance().getValueUsuario("tiposInciVisibles", "todos");
           if (tiposVisibles.equals("ninguno"))
              return res;
           if (!tiposVisibles.isEmpty() && !tiposVisibles.equals("todos"))
              whereTipos = " AND " + campos[COL_TIPO] + " IN (" + tiposVisibles + ")";
       }

      if (!MainActivity.getInstance().getUsuAdmin()) {
         whereUsuario =
               " AND (" + campos[COL_USUARIO] + "=" + usuario + " OR (" + campos[COL_PROPIETARIO] + "=" + usuario +
                     " AND " + campos[COL_TIPO_PROPIETARIO] + "=1))";
      }

      String subquery = "(SELECT nombre FROM usuarios WHERE usuarios.idExterno = incidencias.propietario)";
      String[] selectFields = {"incidencias.*", subquery + " AS nombrePropietario"};

      try {
          cur = db.query(TABLE_NAME, selectFields,
                "incidencias." + campos[COL_EMPRESA] + "=" + empresa + whereUsuario + whereEstado + whereTipos,
                null, null, null, null);

         if (cur != null) {
            while (cur.moveToNext()) {
               Incidencia inci = new Incidencia(
                     cur.getInt(cur.getColumnIndex( campos[COL_ID])),
                     cur.getInt(cur.getColumnIndex( campos[COL_ID_EXTERNO])),
                     cur.getInt(cur.getColumnIndex( campos[COL_EMPRESA])),
                     cur.getInt(cur.getColumnIndex( campos[COL_TIPO])),
                     cur.getInt(cur.getColumnIndex( campos[COL_MODELO])),
                     cur.getInt(cur.getColumnIndex( campos[COL_MOTIVO])),
                     cur.getString(cur.getColumnIndex( campos[COL_OBSERVACIONES])),
                     cur.getInt(cur.getColumnIndex( campos[COL_ULTIMO_ESTADO])),
                     cur.getInt(cur.getColumnIndex( campos[COL_ELEMENTO])),
                     cur.getDouble(cur.getColumnIndex( campos[COL_LATITUD])),
                     cur.getDouble(cur.getColumnIndex( campos[COL_LONGITUD])),
                     cur.getInt(cur.getColumnIndex( campos[COL_USUARIO])),
                     cur.getInt(cur.getColumnIndex( campos[COL_TIPO_PROPIETARIO])),
                     cur.getInt(cur.getColumnIndex( campos[COL_PROPIETARIO]))
               );
               inci.setFechaUltimoEstado(cur.getLong(cur.getColumnIndex(campos[COL_FECHA_ESTADO])));
               inci.setTotalRegistros(cur.getInt(cur.getColumnIndex(campos[COL_TOTAL_REGISTROS])));
               inci.setMovil(cur.isNull(cur.getColumnIndex(campos[COL_MOVIL])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_MOVIL])));
               inci.setIdSincro(cur.getString(cur.getColumnIndex(campos[COL_ID_SINCRO])));
               inci.setRuta(cur.isNull(cur.getColumnIndex(campos[COL_RUTA])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_RUTA])));
               inci.setRutaH(cur.isNull(cur.getColumnIndex(campos[COL_RUTA_H])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_RUTA_H])));
               inci.setFechaBaja(cur.getString(cur.getColumnIndex(campos[COL_FECHA_BAJA])));
               inci.setLatitudInt(cur.isNull(cur.getColumnIndex(campos[COL_LATITUD_INT])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_LATITUD_INT])));
               inci.setLongitudInt(cur.isNull(cur.getColumnIndex(campos[COL_LONGITUD_INT])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_LONGITUD_INT])));
               inci.setMotivoDesc(cur.getString(cur.getColumnIndex(campos[COL_MOTIVO_DESC])));
               inci.setInfoGeo(cur.getString(cur.getColumnIndex(campos[COL_INFO_GEO])));
               inci.setFecha(cur.getString(cur.getColumnIndex(campos[COL_FECHA])));
               inci.setTipoElem(cur.isNull(cur.getColumnIndex(campos[COL_TIPO_ELEM])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_TIPO_ELEM])));
               inci.setMatricula(cur.getString(cur.getColumnIndex(campos[COL_MATRICULA])));
               inci.setImagen(cur.getString(cur.getColumnIndex(campos[COL_IMAGEN])));
               inci.setIdEquipo(cur.isNull(cur.getColumnIndex(campos[COL_ID_EQUIPO])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_ID_EQUIPO])));
               inci.setEmailCiudadano(cur.getString(cur.getColumnIndex(campos[COL_EMAIL_CIUDADANO])));
               inci.setValidada(cur.getInt(cur.getColumnIndex(campos[COL_VALIDADA])));
               inci.setInformado(cur.getInt(cur.getColumnIndex(campos[COL_INFORMADO])));
               inci.setAcera(cur.isNull(cur.getColumnIndex(campos[COL_ACERA])) ? null : cur.getInt(cur.getColumnIndex(campos[COL_ACERA])));
               inci.setTextoCambioEstado(cur.getString(cur.getColumnIndex(campos[COL_TEXTO_CAMBIO_ESTADO])));
               inci.setBorrado(cur.getInt(cur.getColumnIndex(campos[COL_BORRADO])) == 1);
               inci.setFechaModificacion(cur.getString(cur.getColumnIndex(campos[COL_FECHA_ESTADO])));
               inci.setOrdenTrabajo(cur.getInt(cur.getColumnIndex(campos[COL_ORDEN_TRABAJO])) == 1);
               inci.setX(cur.getInt(cur.getColumnIndex(campos[COL_X])));
               inci.setY(cur.getInt(cur.getColumnIndex(campos[COL_Y])));
               inci.setXMercator(cur.getInt(cur.getColumnIndex(campos[COL_X_MERCATOR])));
               inci.setYMercator(cur.getInt(cur.getColumnIndex(campos[COL_Y_MERCATOR])));
               inci.setMunicipio(cur.getString(cur.getColumnIndex(campos[COL_MUNICIPIO])));
               inci.setNombrePropietario(cur.getString(cur.getColumnIndex("nombrePropietario")));
               res.add(inci);
            }
            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
      return res;
   }

   public String getNombrePropietario(Incidencia incidencia) {
      int propietarioId = incidencia.getPropietario();

      String nombrePropietario = "";
      Cursor cursor = null;

      try {
         String query = "SELECT nombre FROM usuarios WHERE idExterno = ?";
         cursor = db.rawQuery(query, new String[]{String.valueOf(propietarioId)});

         if (cursor != null && cursor.moveToFirst()) {
            nombrePropietario = cursor.getString(0);
         }
      } catch (Exception e) {
         // Manejar posibles excepciones
         e.printStackTrace();
      } finally {
         // Cerrar el cursor si es necesario
         if (cursor != null && !cursor.isClosed()) {
            cursor.close();
         }
      }

      return nombrePropietario;
   }

   public void setNombrePropietario(Incidencia incidencia) {
      incidencia.setNombrePropietario(getNombrePropietario(incidencia));
   }

   public ArrayList<ItemMapa> getAllByEstado(int empresa, int usuario, String estados) {
      Cursor cur;
      ArrayList<ItemMapa> res = null;

      try {
         // Las incidencias nuevas se crean con estado 0, por eso
         // añado en el Where el estado 0 siempre
         if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa +
                     (!MainActivity.getInstance().getUsuAdmin() ?
                           " AND (" + campos[COL_USUARIO] + "=" + usuario + " OR (" + campos[COL_PROPIETARIO] + "=" +
                                 usuario + " AND " + campos[COL_TIPO_PROPIETARIO] + "=1))" : "") + " AND " +
                     campos[COL_ULTIMO_ESTADO] + " IN(0," + estados + ")"

               , null, null, null, campos[COL_ID] + " DESC")) != null) {

            if (cur.moveToFirst()) {
               res = new ArrayList<ItemMapa>();
               do {
                  res.add(incidenciaFromCursor(cur));
               } while (cur.moveToNext());
            }

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   public ArrayList<ItemMapa> getByLatLon(int empresa, int usuario, double latitude, double longitude,
         double latitude2, double longitude2, String estados, String tipos) {
      Cursor cur;
      ArrayList<ItemMapa> res = new ArrayList<>();

      try {
         if (estados.equals("todos")) {
            estados = "";
         } else {
            estados = " AND " + campos[COL_ULTIMO_ESTADO] + " IN (0," + estados + ") ";
         }
         
         if (tipos.equals("todos")) {
            tipos = "";
         } else if (tipos.equals("ninguno")) {
            tipos = " AND 1=0 ";  // No mostrar ningún resultado
         } else {
            tipos = " AND " + campos[COL_TIPO] + " IN (" + tipos + ") ";
         }

         if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa +
               (!MainActivity.getInstance().getUsuAdmin() ?
                     " AND (" + campos[COL_USUARIO] + "=" + usuario + " OR (" + campos[COL_PROPIETARIO] + "=" +
                           usuario + " AND " + campos[COL_TIPO_PROPIETARIO] + "=1))" : "") + " AND " +
               campos[COL_LATITUD] + ">=" + latitude + " AND " + campos[COL_LATITUD] + "<=" + latitude2 + " AND " +
               campos[COL_LONGITUD] + ">=" + longitude + " AND " + campos[COL_LONGITUD] + " <=" + longitude2 +
               estados + tipos, null, null, null, null)) != null) {

            if (cur.moveToFirst()) {
               do {
                        /*int id, int idExterno, int empresa, int tipo, int modelo,
                        int motivo, String observ, int estado, int elemento, double lat,
						double lon, int usuario*/
                  res.add(incidenciaFromCursor(cur));
               } while (cur.moveToNext());
            }

            cur.close();
         }
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   /**
    * Elimina las incidencias con los estados especificados (no elimina los registros de estado).
    *
    * @param estados Cadena de enteros separados por coma, siendo cada entero un identificador de estado.
    * @return Número de incidencias eliminadas.
    */
   public int removeAllByEstado(int empresa, int usuario, String estados) {
      int res = 0;

      try {
         res = db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa +
               (!MainActivity.getInstance().getUsuAdmin() ? " AND " + campos[COL_USUARIO] + "=" + usuario : "") +
               " AND " + campos[COL_ULTIMO_ESTADO] + " IN(" + estados + ")", null);
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }

      return res;
   }

   private Incidencia incidenciaFromCursor(Cursor cur) {
      return new Incidencia(
            cur.getInt(COL_ID),
            cur.getInt(COL_ID_EXTERNO),
            cur.getInt(COL_EMPRESA),
            cur.getInt(COL_TIPO),
            cur.getInt(COL_MODELO),
            cur.getInt(COL_MOTIVO),
            cur.getString(COL_OBSERVACIONES),
            cur.getInt(COL_ULTIMO_ESTADO),
            cur.getInt(COL_ELEMENTO),
            cur.getDouble(COL_LATITUD),
            cur.getDouble(COL_LONGITUD),
            cur.getInt(COL_USUARIO),
            cur.getInt(COL_TIPO_PROPIETARIO),
            cur.getInt(COL_PROPIETARIO),
            cur.getInt(COL_TOTAL_REGISTROS),
            cur.isNull(COL_MOVIL) ? null : cur.getInt(COL_MOVIL),
            cur.getString(COL_ID_SINCRO),
            cur.isNull(COL_RUTA) ? null : cur.getInt(COL_RUTA),
            cur.isNull(COL_RUTA_H) ? null : cur.getInt(COL_RUTA_H),
            cur.getString(COL_FECHA_BAJA),
            cur.isNull(COL_LATITUD_INT) ? null : cur.getInt(COL_LATITUD_INT),
            cur.isNull(COL_LONGITUD_INT) ? null : cur.getInt(COL_LONGITUD_INT),
            cur.getString(COL_MOTIVO_DESC),
            cur.getString(COL_INFO_GEO),
            cur.getString(COL_FECHA),
            cur.isNull(COL_TIPO_ELEM) ? null : cur.getInt(COL_TIPO_ELEM),
            cur.getString(COL_MATRICULA),
            cur.getString(COL_IMAGEN),
            cur.isNull(COL_ID_EQUIPO) ? null : cur.getInt(COL_ID_EQUIPO),
            cur.getString(COL_EMAIL_CIUDADANO),
            cur.getInt(COL_VALIDADA),
            cur.getInt(COL_INFORMADO),
            cur.isNull(COL_ACERA) ? null : cur.getInt(COL_ACERA),
            cur.getString(COL_TEXTO_CAMBIO_ESTADO),
            cur.getInt(COL_BORRADO) == 1,
            cur.getString(COL_FECHA_ESTADO),
            cur.getInt(COL_ORDEN_TRABAJO) == 1,
            cur.getInt(COL_X),
            cur.getInt(COL_Y),
            cur.getInt(COL_X_MERCATOR),
            cur.getInt(COL_Y_MERCATOR),
            cur.getString(COL_MUNICIPIO)
      );
   }

   public void beginTransaction() {
      try {
         db.beginTransactionNonExclusive();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   public void endTransaction() {
      try {
         db.endTransaction();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }

   public void commitTransaction() {
      try {
         db.setTransactionSuccessful();
      } catch (Throwable e) {
         MyLoggerHandler.getInstance().error(e);
      }
   }
}
