package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteStatement;

import com.environment.Environment;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.log.Logg;
import com.movisat.tags.ITag;
import com.movisat.utilities.Database;

import java.util.ArrayList;
import java.util.List;

public class DBTags {
    private final String TABLE_NAME = "tags";
    private SQLiteDatabase db = null;
    private String[] campos = (new String[]{"id", "idExterno", "empresa",
            "tag", "matricula", "idExternoElemento", "idInternoElemento"});

    private final int COL_ID = 0;
    private final int COL_ID_EXTERNO = 1;
    private final int COL_EMPRESA = 2;
    private final int COL_TAG = 3;
    private final int COL_MATRICULA = 4;
    private final int COL_ID_EXTERNO_ELEMENTO = 5;
    private final int COL_ID_INTERNO_ELEMENTO = 6;

    public DBTags() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(Tags reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_MATRICULA], reg.getMatricula());
            values.put(campos[COL_ID_EXTERNO_ELEMENTO], reg.getIdExternoElemento());
            values.put(campos[COL_ID_INTERNO_ELEMENTO], reg.getIdInternoElemento());
            values.put(campos[COL_TAG], reg.getTag());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public SQLiteStatement prepareInsertStatement() {
        String sql = "INSERT INTO " + TABLE_NAME + " (" +
              campos[COL_ID_EXTERNO] + ", " +
              campos[COL_EMPRESA] + ", " +
              campos[COL_MATRICULA] + ", " +
              campos[COL_ID_EXTERNO_ELEMENTO] + ", " +
              campos[COL_ID_INTERNO_ELEMENTO] + ", " +
              campos[COL_TAG] +
              ") VALUES (?, ?, ?, ?, ?, ?)";

        SQLiteStatement statement = db.compileStatement(sql);

        return statement;
    }

    public long insertWithStatement(SQLiteStatement statement, Tags reg) {
        statement.clearBindings();

        statement.bindLong(1, reg.getIdExterno());
        statement.bindLong(2, reg.getEmpresa());
        statement.bindString(3, reg.getMatricula());
        statement.bindLong(4, reg.getIdExternoElemento());
        statement.bindLong(5, reg.getIdInternoElemento());
        statement.bindString(6, reg.getTag());

        return statement.executeInsert();
    }

    public void beginTransaction() {
        try {
            db.beginTransactionNonExclusive();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void endTransaction() {
        try {
            db.endTransaction();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void commitTransaction() {
        try {
            db.setTransactionSuccessful();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public boolean update(Tags reg) {
        return update(reg, false);
    }

    public boolean update(Tags reg, boolean byTag) {
        boolean res = false;
        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_MATRICULA], reg.getMatricula());
            values.put(campos[COL_ID_EXTERNO_ELEMENTO], reg.getIdExternoElemento());
            values.put(campos[COL_ID_INTERNO_ELEMENTO], reg.getIdInternoElemento());
            values.put(campos[COL_TAG], reg.getTag());

            if (db.update(TABLE_NAME, values,
                  campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno() + " AND "
                        + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                  null) > 0)
                return true;
            if (reg.getId() > 0) {
                if (db.update(TABLE_NAME, values, campos[COL_ID] + "="
                      + reg.getId() + " AND " + campos[COL_EMPRESA]
                      + "=" + reg.getEmpresa(), null) > 0)
                    return true;
            }
            if (byTag) {
                if (db.update(TABLE_NAME, values, campos[COL_TAG] + "='"
                      + reg.getTag() + "' AND " + campos[COL_EMPRESA]
                      + "=" + reg.getEmpresa(), null) > 0)
                    return true;
            }

        } catch (Throwable e) {
            Logg.error("[" + getClass() + "] Error SQLITE UPDATE, tabla: TAGS... \n" + e.getMessage());
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(Tags reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno()
                            + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

            if (!res) {
                if (db.delete(
                        TABLE_NAME,
                        campos[COL_ID] + "=" + reg.getId()
                                + " AND " + campos[COL_EMPRESA] + "="
                                + reg.getEmpresa(), null) > 0)
                    res = true;
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public List<Tags> getAll() {
        Cursor cur = null;
        List<Tags> alls = new ArrayList<>();
        try {
            cur = db.query(
                    TABLE_NAME, null, null, null, null, null, null);

            if (cur != null)
                if (cur.moveToFirst()) {
                    do {
                        alls.add(
                                new Tags(cur.getInt(COL_ID),
                                        cur.getInt(COL_ID_EXTERNO),
                                        cur.getInt(COL_EMPRESA), cur.getString(COL_MATRICULA),
                                        cur.getString(COL_TAG),
                                        cur.getInt(COL_ID_EXTERNO_ELEMENTO),
                                        cur.getInt(COL_ID_INTERNO_ELEMENTO))
                        );
                    } while (cur.moveToNext());
                }
        } catch (Exception e) {
            Logg.error("Tags getAll: " + e.getMessage());
        } finally {
            if (cur != null)
                cur.close();
            // db.close();
        }

        return alls;
    }

    public int getCount(int empresa) {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db
                    .query(TABLE_NAME, (new String[]{"count(*)"}),
                            campos[COL_EMPRESA] + "=" + empresa, null, null,
                            null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public void deleteAll(int empresa) {

        try {

            db.delete(TABLE_NAME, "empresa=" + empresa, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public Tags getByElementIdInternalOrExternal(int externalId, int internalId, int company) {
        if (externalId != 0) {
            Tags tag = getByElemento(externalId, company);
            if (tag != null) return tag;
        }

        return getByIdInternoElemento(internalId, company);
    }

    public Tags getByElemento(int idElementoExterno, int empresa) {
        Cursor cur;
        Tags res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO_ELEMENTO] + "=" + idElementoExterno
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Tags(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_MATRICULA),
                            cur.getString(COL_TAG),
                            cur.getInt(COL_ID_EXTERNO_ELEMENTO),
                            cur.getInt(COL_ID_INTERNO_ELEMENTO));

                cur.close();

            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public Tags getByIdInternoElemento(int iInternoExterno, int empresa) {
        Cursor cur;
        Tags res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_INTERNO_ELEMENTO] + "=" + iInternoExterno
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Tags(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_MATRICULA),
                            cur.getString(COL_TAG),
                            cur.getInt(COL_ID_EXTERNO_ELEMENTO),
                            cur.getInt(COL_ID_INTERNO_ELEMENTO));

                cur.close();

            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public Tags getByID(int id, int empresa) {
        Cursor cur;
        Tags res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO] + "=" + id
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Tags(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_MATRICULA),
                            cur.getString(COL_TAG),
                            cur.getInt(COL_ID_EXTERNO_ELEMENTO),
                            cur.getInt(COL_ID_INTERNO_ELEMENTO));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    private Tags getByTag(String tag, int empresa) {
        Cursor cur;
        Tags res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_TAG] + "='" + tag + "'"
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Tags(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_MATRICULA),
                            cur.getString(COL_TAG),
                            cur.getInt(COL_ID_EXTERNO_ELEMENTO),
                            cur.getInt(COL_ID_INTERNO_ELEMENTO));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public Tags getByTag(ITag tag, int empresa) {
        if (tag == null) return null;
        if (Environment.isTagUHFLongAndShort && tag.isUHF()) {
            Tags lng = getByTag(tag.getLong(), empresa);
            if (lng != null) return lng;

            Tags shrt = getByTag(tag.getShort(), empresa);
            if (shrt != null) tag.isShortInDatabase = true;

            return shrt;
        }
        return (getByTag(tag.get(), empresa));
    }

    public Tags getByMatricula(String matricula, int empresa) {
        Cursor cur;
        Tags res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_MATRICULA] + "='" + matricula + "'"
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Tags(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_MATRICULA),
                            cur.getString(COL_TAG),
                            cur.getInt(COL_ID_EXTERNO_ELEMENTO),
                            cur.getInt(COL_ID_INTERNO_ELEMENTO));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

}
