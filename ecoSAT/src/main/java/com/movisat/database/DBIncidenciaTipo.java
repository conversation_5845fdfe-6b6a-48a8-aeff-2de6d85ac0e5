package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBIncidenciaTipo {
	private final String TABLE_NAME = "incidencias_tipo";
	private SQLiteDatabase db = null;
	private String[] campos = (new String[] { "id", "idExterno", "empresa",
			"nombre" });

	private final int COL_ID = 0;
	private final int COL_ID_EXTERNO = 1;
	private final int COL_EMPRESA = 2;
	private final int COL_NOMBRE = 3;

	public DBIncidenciaTipo() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(IncidenciaTipo reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_NOMBRE], reg.getNombre());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(IncidenciaTipo reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_NOMBRE], reg.getNombre());

			if (db.update(TABLE_NAME, values,
					campos[COL_ID] + "=" + reg.getId() + " AND "
							+ campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;

			if (!res) {
				if (db.update(TABLE_NAME, values, campos[COL_ID_EXTERNO] + "="
						+ reg.getIdExterno() + " AND " + campos[COL_EMPRESA]
						+ "=" + reg.getEmpresa(), null) > 0)
					res = true;
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(IncidenciaTipo reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
					+ " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;
			if (!res) {
				if (db.delete(
						TABLE_NAME,
						campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno()
								+ " AND " + campos[COL_EMPRESA] + "="
								+ reg.getEmpresa(), null) > 0)
					res = true;
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount(int empresa) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db
					.query(TABLE_NAME, (new String[] { "count(*)" }),
							campos[COL_EMPRESA] + "=" + empresa, null, null,
							null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll(int empresa) {

		try {

			db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public IncidenciaTipo getByID(int id, int empresa) {
		Cursor cur;
		IncidenciaTipo res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO] + "=" + id
					+ " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
					null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new IncidenciaTipo(cur.getInt(COL_ID),
							cur.getInt(COL_ID_EXTERNO),
							cur.getInt(COL_EMPRESA), cur.getString(COL_NOMBRE));

				cur.close();
				
				if (res==null) {
					if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id
							+ " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
							null, null, null)) != null) {

						if (cur.moveToFirst())
							res = new IncidenciaTipo(cur.getInt(COL_ID),
									cur.getInt(COL_ID_EXTERNO),
									cur.getInt(COL_EMPRESA), cur.getString(COL_NOMBRE));

						cur.close();
					}
				}
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public ArrayList<IncidenciaTipo> getAll(int empresa) {
		Cursor cur;
		ArrayList<IncidenciaTipo> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
					+ empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<IncidenciaTipo>();

					do {

						res.add(new IncidenciaTipo(cur.getInt(COL_ID), cur
								.getInt(COL_ID_EXTERNO), cur
								.getInt(COL_EMPRESA), cur.getString(COL_NOMBRE)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
