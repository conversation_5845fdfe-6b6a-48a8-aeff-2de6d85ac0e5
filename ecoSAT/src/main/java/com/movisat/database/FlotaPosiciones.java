package com.movisat.database;

import java.util.ArrayList;
import java.util.List;

public class FlotaPosiciones extends ItemMapa {
	
	private int id;
	private int empresa;
	private boolean estado;
	private int movil;
	private String fecha;
	private double lat;
	private double lng;
	private String descripcion;
	private List<Identificacion> identificaciones = new ArrayList<>();
	

	public FlotaPosiciones(int id, int empresa, boolean estado, int movil,
			String fecha, double Lat, double Lng, String descripcion) {

		setId(id);
		setEmpresa(empresa);
		setEstado(estado);
		setMovil(movil);
		setFecha(fecha);
		setLat(Lat);
		setLng(Lng);
		setDescripcion(descripcion);

	}



	public void setEmpresa(int empresa) {

		this.empresa = empresa;
	}

	public void setEstado(boolean estado) {

		this.estado = estado;
	}

	

	public int getEmpresa() {

		return empresa;
	}

	public boolean getEstado() {

		return estado;
	}
	
	public int getEstadoInt() {

		if (estado)
		return 1;
		else return 0;
	}

	public int getMovil() {
		return movil;
	}

	public void setMovil(int movil) {
		this.movil = movil;
	}

	public String getFecha() {
		return fecha;
	}

	public void setFecha(String fecha) {
		this.fecha = fecha;
	}

	public double getLat() {
		return lat;
	}

	public void setLat(double lat) {
		this.lat = lat;
	}

	public double getLng() {
		return lng;
	}

	public void setLng(double lng) {
		this.lng = lng;
	}

	public String getDescripcion() {
		return descripcion;
	}

	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public List<Identificacion> getIdentificaciones() {
		return identificaciones;
	}

	public void setIdentificaciones(List<Identificacion> identificaciones) {
		this.identificaciones = identificaciones;
	}

	@Override
	public String getTipoElemento() {
		return "F";
	}

	@Override
	public int getIdExterno() {
		return id;
	}

	@Override
	public void setIdExterno(int idExterno) {}

	@Override
	public String toString() {

		return fecha + " - " + descripcion;
	}

}