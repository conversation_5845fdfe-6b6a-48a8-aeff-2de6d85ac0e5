package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBUsuMoviles {
	private final String TABLE_NAME = "usu_moviles";
	private SQLiteDatabase db = null;

	private String[] campos = (new String[] { "id", "empresa", "usuario",
			"movil" });
	private final int COL_ID = 0;
	private final int COL_EMPRESA = 1;
	private final int COL_USUARIO = 2;
	private final int COL_MOVIL = 3;

	public DBUsuMoviles() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(UsuMoviles reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_USUARIO], reg.getUsuario());
			values.put(campos[COL_MOVIL], reg.getMovil());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(UsuMoviles reg) {
		boolean res = false;

		try {

			if (db.delete(
					TABLE_NAME,
					campos[COL_EMPRESA] + "=" + reg.getEmpresa() + " AND "
							+ campos[COL_USUARIO] + "=" + reg.getUsuario()
							+ " AND " + campos[COL_MOVIL] + "="
							+ reg.getMovil(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount(int empresa, int usuario, int movil) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }),
					campos[COL_EMPRESA] + "=" + empresa + " AND "
							+ campos[COL_USUARIO] + "=" + usuario + " AND "
							+ campos[COL_MOVIL] + "=" + movil, null, null,
					null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount(int empresa, int usuario) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }),
					campos[COL_EMPRESA] + "=" + empresa + " AND "
							+ campos[COL_USUARIO] + "=" + usuario, null, null,
					null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll(int empresa) {

		try {

			db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public UsuMoviles getPermisoBy(int empresa, int codigoMovil) {
		Cursor cur;
		UsuMoviles res = null;
		int id, usuario, movil;
		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_MOVIL] + "="
					+ codigoMovil + " AND " + campos[COL_EMPRESA] + "="
					+ empresa, null, null, null, null)) != null) {

				if (cur.moveToFirst()) {
					id = cur.getInt(COL_ID);
					usuario = cur.getInt(COL_USUARIO);
					movil = cur.getInt(COL_MOVIL);
					res = new UsuMoviles(id, usuario, empresa, movil);
				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public ArrayList<UsuMoviles> getAll(int empresa) {
		Cursor cur;
		ArrayList<UsuMoviles> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
					+ empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<UsuMoviles>();

					do {

						res.add(new UsuMoviles(cur.getInt(COL_ID), cur
								.getInt(COL_EMPRESA), cur.getInt(COL_USUARIO),
								cur.getInt(COL_MOVIL)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
