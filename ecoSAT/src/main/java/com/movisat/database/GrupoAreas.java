package com.movisat.database;

import com.google.android.gms.maps.model.LatLng;

/**
 * Created by faroca on 17/08/2015.
 */
public class GrupoAreas implements Comparable<GrupoAreas> {

    private int id;
    private int empresa;
    private String abreviatura;
    private String nombre;
    private int clicks;
    private int distance;

    public GrupoAreas(int id, int empresa, String abreviatura, String nombre, int clicks) {
        setId(id);
        setEmpresa(empresa);
        setAbreviatura(abreviatura);
        setNombre(nombre);
        setClicks(clicks);
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getAbreviatura() {
        return abreviatura;
    }

    public void setAbreviatura(String abreviatura) {
        this.abreviatura = abreviatura;
    }


    public int getClicks() {
        return clicks;
    }

    public void setClicks(int clicks) {
        this.clicks = clicks;
    }


    public int getDistance() {
        return distance;
    }

    public void setDistance(int distance) {
        this.distance = distance;
    }

    @Override
    public int compareTo(GrupoAreas another) {
        return this.distance - another.distance;
    }
}
