package com.movisat.database;

import java.util.ArrayList;

import java.io.File;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBIncidenciaFoto {
	private final String TABLE_NAME = "incidencias_fotos";
	private SQLiteDatabase db = null;
	private String[] campos = (new String[] { "id", "idExterno", "incidencia",
			"incidenciaExterno", "empresa", "usuario", "foto_url" });

	private final int COL_ID = 0;
	private final int COL_ID_EXTERNO = 1;
	private final int COL_INCIDENCIA_INTERNO = 2;
	private final int COL_INCIDENCIA_EXTERNO = 3;
	private final int COL_EMPRESA = 4;
	private final int COL_USUARIO = 5;
	private final int COL_FOTO_URL = 6;

	public DBIncidenciaFoto() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(IncidenciaFoto reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_INCIDENCIA_INTERNO], reg.getIncidenciaInterno());
			values.put(campos[COL_INCIDENCIA_EXTERNO],
					reg.getIncidenciaExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_USUARIO], reg.getUsuario());
			values.put(campos[COL_FOTO_URL], reg.getImageBase64());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(IncidenciaFoto reg) {
		boolean res = false;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
			values.put(campos[COL_INCIDENCIA_INTERNO], reg.getIncidenciaInterno());
			values.put(campos[COL_INCIDENCIA_EXTERNO],
					reg.getIncidenciaExterno());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_USUARIO], reg.getUsuario());
			values.put(campos[COL_FOTO_URL], reg.getImageBase64());

			if (db.update(TABLE_NAME, values,
					campos[COL_ID] + "=" + reg.getId() + " AND "
							+ campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;
			else if (db.update(TABLE_NAME, values, campos[COL_ID_EXTERNO] + "="
					+ reg.getIdExterno() + " AND " + campos[COL_ID_EXTERNO]
					+ "=" + reg.getEmpresa(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(IncidenciaFoto reg) {
		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
					+ " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;
			else if (db.delete(TABLE_NAME,
					campos[COL_ID_EXTERNO] + "=" + reg.getIdExterno() + " AND "
							+ campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;


			if (res){

				File file = new File(reg.getImageBase64());
				boolean deleted = file.delete();
				if (deleted)
					MyLoggerHandler.getInstance().info("Eliminamos la imagen de la incidencia: "+reg.getIncidenciaExterno());
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount(int empresa) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db
					.query(TABLE_NAME, (new String[] { "count(*)" }),
							campos[COL_EMPRESA] + "=" + empresa, null, null,
							null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}
	
	public int getCountIncidencias(int idIncidencia) {
		int res = 0;
		Cursor cur;

		try {

			if ((cur = db
					.query(TABLE_NAME, (new String[] { "count(*)" }),
							campos[COL_INCIDENCIA_INTERNO] + "=" + idIncidencia, null, null,
							null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll(int empresa) {

		try {

			db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public IncidenciaFoto getById(int id, int empresa) {
		Cursor cur;
		IncidenciaFoto res = null;

		try {
			// Busco por el cidigo temporal por si se trata de un
			// elemento que todavia no se ha sincronizado
			if ((cur = db.query(TABLE_NAME, campos,campos[COL_ID] + "=" + id
							+ " AND " + campos[COL_EMPRESA] + "=" + empresa,null,
					null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new IncidenciaFoto(cur.getInt(COL_ID),
							cur.getInt(COL_ID_EXTERNO),
							cur.getInt(COL_INCIDENCIA_INTERNO),
							cur.getInt(COL_INCIDENCIA_EXTERNO),
							cur.getInt(COL_EMPRESA),
							cur.getInt(COL_USUARIO),
							cur.getString(COL_FOTO_URL));

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			res = null;
		}

		return res;
	}

	public boolean deleteByIdIncidencia(int id, int empresa) {

		boolean res = false;

		// eliminar la imagen del dispositivo asociada a la incidencia, si existe
		try {
			ArrayList<IncidenciaFoto> lista_fotos = getAll(empresa, id);
			if (lista_fotos!=null)
				for (IncidenciaFoto foto : lista_fotos) {

					File file = new File(foto.getImageBase64());
					boolean deleted = file.delete();
					if (deleted)
						MyLoggerHandler.getInstance().info("Eliminamos la imagen de la incidencia: " + foto.getIncidenciaExterno());

				}
		} catch (Throwable e){
			MyLoggerHandler.getInstance().error(e);
		}

		try {
			if (db.delete(TABLE_NAME, campos[COL_INCIDENCIA_INTERNO] + "=" + id
							+ " AND " + campos[COL_EMPRESA] + "=" + empresa,
					null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			res = false;
		}

		return res;
	}


	public boolean deleteByIncidenciaIdExterno(int id, int empresa) {

		boolean res = false;

		try {

			if (db.delete(TABLE_NAME, campos[COL_ID_EXTERNO] + "=" + id
							+ " AND " + campos[COL_EMPRESA] + "=" + empresa,
					null) > 0)
			res = true;

			// eliminar la imagen asociada a la incidencia, si existe
			try {
				ArrayList<IncidenciaFoto> lista_fotos = getAll(empresa, id);
				if (lista_fotos!=null)
					for (IncidenciaFoto foto : lista_fotos) {

						File file = new File(foto.getImageBase64());
						boolean deleted = file.delete();
						if (deleted)
							MyLoggerHandler.getInstance().info("Eliminamos la imagen de la incidencia: " + foto.getIncidenciaExterno());

					}
			} catch (Throwable e){
				MyLoggerHandler.getInstance().error(e);
			}


		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			res = false;
		}

		return res;
	}
	
	public ArrayList<IncidenciaFoto> getAll(int empresa, int idIncidencia) {
		Cursor cur;
		ArrayList<IncidenciaFoto> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa + " AND " 
								+ campos[COL_INCIDENCIA_INTERNO] + "=" + idIncidencia,
								null, null, null, null)) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<IncidenciaFoto>();

					do {

						res.add(new IncidenciaFoto(cur.getInt(COL_ID),
													cur.getInt(COL_ID_EXTERNO),
													cur.getInt(COL_INCIDENCIA_INTERNO),
													cur.getInt(COL_INCIDENCIA_EXTERNO),
													cur.getInt(COL_EMPRESA),
													cur.getInt(COL_USUARIO),
													cur.getString(COL_FOTO_URL)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
			res = null;
		}

		return res;
	}

	public int getMax() {

		int res = 0;
		Cursor cur;

		try {

			if ((cur = db
					.query(TABLE_NAME, (new String[]{"MAX(id)"}),
							null, null, null,
							null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res+1;

	}
}
