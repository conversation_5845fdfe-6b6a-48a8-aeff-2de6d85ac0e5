package com.movisat.database;


import java.io.Serializable;

public class Tags implements Serializable {
	private int id;
	private int idExterno;
	private int empresa;
	private int idExternoElemento;
	private int idInternoElemento;
	private String tag;
	private String matricula;

	public Tags(int id, int idExterno, int empresa, String matricula,
			String tag, int idExternoElemento, int idInternoElemento) {

		setId(id);
		setIdExterno(idExterno);
		setEmpresa(empresa);
		setTag(tag);
		setMatricula(matricula);
		setIdExternoElemento(idExternoElemento);
		setIdInternoElemento(idInternoElemento);
	}

	public void setId(int id) {

		this.id = id;
	}

	public void setEmpresa(int empresa) {

		this.empresa = empresa;
	}

	public void setTag(String tag) {

		this.tag = tag;
	}

	public void setMatricula(String matricula) {

		this.matricula = matricula;
	}

	public void setIdExternoElemento(int elemento) {

		this.idExternoElemento = elemento;
	}

	public void setIdInternoElemento(int elemento) {

		this.idInternoElemento = elemento;
	}

	public int getId() {

		return id;
	}

	public int getEmpresa() {

		return empresa;
	}

	public String getTag() {

		return tag;
	}

	public String getMatricula() {

		return matricula;
	}

	public int getIdExternoElemento() {

		return idExternoElemento;
	}

	public int getIdInternoElemento() {

		return idInternoElemento;
	}

	public int getIdExterno() {

		return idExterno;
	}

	public void setIdExterno(int idExterno) {
		this.idExterno = idExterno;
	}
	
	@Override
	public String toString() {
		return tag;
	}

}
