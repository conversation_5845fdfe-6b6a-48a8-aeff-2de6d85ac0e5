package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBProvincias {
    private final String TABLE_NAME = "provincias";
    private SQLiteDatabase db = null;

    private String[] campos = (new String[]{"id", "provincia"});
    private final int COL_ID = 0;
    private final int COL_PROVINCIA = 1;

    public DBProvincias() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(Provincias reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_PROVINCIA], reg.getProvincia());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(Provincias reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_PROVINCIA], reg.getProvincia());

            if (db.update(TABLE_NAME, values,
                    campos[COL_ID] + "=" + reg.getId(), null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(Provincias reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId(), null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public int getCount() {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                    null, null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public void deleteAll() {

        try {

            db.delete(TABLE_NAME, null, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public Provincias getByID(int id) {
        Cursor cur;
        Provincias res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID] + "=" + id,
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Provincias(cur.getInt(COL_ID),
                            cur.getString(COL_PROVINCIA));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public Cursor getAllCursor() {
        Cursor cur = null;
        ArrayList<Provincias> res = null;

        try {

            cur = db.rawQuery("SELECT id AS _id, UPPER(provincia) AS provincia FROM "
                            + TABLE_NAME,
                    null);
            cur.moveToFirst();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return cur;
    }

    public ArrayList<Provincias> getAll() {
        Cursor cur;
        ArrayList<Provincias> res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, null, null, null, null,
                    campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<Provincias>();

                    do {

                        res.add(new Provincias(cur.getInt(COL_ID), cur
                                .getString(COL_PROVINCIA)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

}
