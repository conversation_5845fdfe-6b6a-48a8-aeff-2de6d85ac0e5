package com.movisat.database;

import java.io.ObjectOutputStream.PutField;
import java.util.LinkedList;
import java.util.List;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import android.location.Location;

//import com.google.android.gms.internal.zzdb;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;

public class Area {

    private static final int CIRCULO = 0;
    private static final int CUADRADO = 1;
    private static final int POLIGONO = 2;

    private int codigo;
    private int empresa;
    private String abreviatura;
    private String nombre;
    private int superfice;
    private int radioLado;
    LatLng puntoCentro;
    private int forma;
    private String nombreGrupo;
    private int grupo;

    List<LatLng> listaPuntos = null;
    List<LatLng> listaPuntosControl = null;

    public Area(int codigo, int empresa, int grupo, String abreviatura, String nombre,
                int superfice, int radioLado, LatLng puntoCentro, int forma, String nombreGrupo,
                List<LatLng> listaPuntos, List<LatLng> listaPuntosControl) {

        setCodigo(codigo);
        setEmpresa(empresa);
        setAbreviatura(abreviatura);
        setNombre(nombre);
        setRadioLado(radioLado);
        setSuperfice(superfice);
        setPuntoCentro(puntoCentro);
        setForma(forma);
        setListaPuntos(listaPuntos);
        setListaPuntosControl(listaPuntosControl);
        setNombreGrupo(nombreGrupo);
        setGrupo(grupo);
    }

    public Area(JSONObject json) throws JSONException {
        LatLng pos;
        JSONArray jsonPuntos;
        JSONArray jsonPuntosControl;

        // Cogemos el campo puntos

        List<LatLng> listaPoints = new LinkedList<LatLng>();
        List<LatLng> listaPointControl = new LinkedList<LatLng>();

        try {

            jsonPuntos = json.getJSONArray("Puntos");

        } catch (Exception e) {

            jsonPuntos = null;
        }

        if (jsonPuntos != null) {

            // Creamos una lista de puntos
            for (int j = 0; j < jsonPuntos.length(); j++) {

                pos = new LatLng(jsonPuntos.getJSONObject(j).getDouble("Lat"),
                        jsonPuntos.getJSONObject(j).getDouble("Lng"));

                listaPoints.add(pos);
            }
        }

        try {

            jsonPuntosControl = json.getJSONArray("PuntosControl");

        } catch (Exception e) {

            jsonPuntosControl = null;
        }

        if (jsonPuntosControl != null) {

            // Creamos una lista de puntos
            for (int j = 0; j < jsonPuntosControl.length(); j++) {

                pos = new LatLng(jsonPuntosControl.getJSONObject(j).getDouble(
                        "Lat"), jsonPuntosControl.getJSONObject(j).getDouble(
                        "Lng"));

                listaPointControl.add(pos);
            }
        }

        setCodigo(json.getInt("Codigo"));
        setEmpresa(json.getInt("Empresa"));
        setAbreviatura(json.getString("Abreviatura"));
        setNombre(json.getString("Nombre"));
        setRadioLado(json.getInt("RadioOLado"));
        setSuperfice(json.getInt("Superficie"));
        setPuntoCentro(new LatLng(json.getDouble("LatCentro"),
                json.getDouble("LngCentro")));
        setForma(json.getInt("_Forma"));
        setNombreGrupo(json.getString("NombreGrupo"));
        if (!json.isNull("Grupo")) {
            setGrupo(json.getInt("Grupo"));
        }
        setListaPuntos(listaPoints);
        setListaPuntosControl(listaPointControl);
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public String getAbreviatura() {
        return abreviatura;
    }

    public void setAbreviatura(String abreviatura) {
        this.abreviatura = abreviatura;
    }

    public String getNombre() {
        return nombre;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public int getRadioLado() {
        return radioLado;
    }

    public void setRadioLado(int radioLado) {
        this.radioLado = radioLado;
    }

    public int getSuperfice() {
        return superfice;
    }

    public void setSuperfice(int superfice) {
        this.superfice = superfice;
    }

    public LatLng getPuntoCentro() {
        return puntoCentro;
    }

    public void setPuntoCentro(LatLng puntoCentro) {
        this.puntoCentro = puntoCentro;
    }

    public int getForma() {
        return forma;
    }

    public void setForma(int forma) {
        this.forma = forma;
    }

    public List<LatLng> getListaPuntos() {
        return listaPuntos;
    }

    public void setListaPuntos(List<LatLng> listaPuntos) {
        this.listaPuntos = listaPuntos;
    }

    public List<LatLng> getListaPuntosControl() {
        return listaPuntosControl;
    }

    public void setListaPuntosControl(List<LatLng> listaPuntosControl) {
        this.listaPuntosControl = listaPuntosControl;
    }

    public boolean isInside(LatLng punto) {

        LatLngBounds.Builder builder = new LatLngBounds.Builder();
        if (listaPuntos != null) {
            for (LatLng latLng : listaPuntos) {
                builder.include(latLng);
            }
            LatLngBounds bounds = builder.build();

            if (!bounds.contains(punto))
                return false;
            else {

                // Tengo que comprobar si esta dentro del poligono.
                return Contains(punto);
            }
        } else {
            return isInsideCircle(punto);
        }
    }

    /**
     * Me dice si el punto que le paso esta dentro de los puntos de control
     *
     * @param punto
     * @param metros
     * @return
     */
    public boolean isCercaPuntoControl(LatLng punto, int metros) {
        if (getListaPuntosControl().size() <= 0)
            return false;
        float distancia = Float.MAX_VALUE;
        float[] results = new float[2];
        for (LatLng latLng : listaPuntosControl) {
            Location.distanceBetween(punto.latitude, punto.longitude,
                    latLng.latitude, latLng.longitude, results);
            distancia = results[0];
            if (distance(punto, latLng) <= metros) {
                return true;
            }
        }
        return false;
    }

    public boolean isInsideCircle(LatLng punto) {
        float[] distance = new float[2];
        LatLng puntoCentro = getPuntoCentro();
        Location.distanceBetween(punto.latitude, punto.longitude,
                puntoCentro.latitude, puntoCentro.longitude, distance);

        return distance[0] <= getRadioLado();
    }


    public int distanceMinPuntosControl(LatLng p1) {
        List<LatLng> puntosControl = this.getListaPuntosControl();
        if (puntosControl.size() == 0) return 0;
        int distanceMin = Integer.MAX_VALUE;
        int distanceInt = -1;

        double distance;
        double earthRadius = 3958.75;
        int meterConversion = 1609;
        double latDiff;
        double lngDiff;
        double a;
        double c;


        float[] results = new float[2];

        for (LatLng p2 :
                puntosControl) {
//            latDiff = Math.toRadians(p2.latitude - p1.latitude);
//            lngDiff = Math.toRadians(p2.longitude - p1.longitude);
//            a = Math.sin(latDiff / 2) * Math.sin(latDiff / 2)
//                    + Math.cos(Math.toRadians(p1.latitude))
//                    * Math.cos(Math.toRadians(p2.latitude)) * Math.sin(lngDiff / 2)
//                    * Math.sin(lngDiff / 2);
//            c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
//            distance = earthRadius * c;
//            distanceInt = (int) new Float(distance * meterConversion).floatValue();
            Location.distanceBetween(p1.latitude, p1.longitude,
                    p2.latitude, p2.longitude, results);
            if (results == null) continue;
            distanceInt = (int) results[0];
            if (distanceInt < distanceMin) {
                distanceMin = distanceInt;
            }
        }

        return distanceMin;
    }

    public float distance(LatLng p1, LatLng p2) {
        double earthRadius = 3958.75;
        double latDiff = Math.toRadians(p2.latitude - p1.latitude);
        double lngDiff = Math.toRadians(p2.longitude - p1.longitude);
        double a = Math.sin(latDiff / 2) * Math.sin(latDiff / 2)
                + Math.cos(Math.toRadians(p1.latitude))
                * Math.cos(Math.toRadians(p2.latitude)) * Math.sin(lngDiff / 2)
                * Math.sin(lngDiff / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = earthRadius * c;

        int meterConversion = 1609;

        return new Float(distance * meterConversion).floatValue();
    }

    public boolean Contains(LatLng location) {
        if (location == null)
            return false;

        LatLng lastPoint = listaPuntos.get(listaPuntos.size() - 1);
        boolean isInside = false;
        double x = location.longitude;

        for (LatLng point : listaPuntos) {
            double x1 = lastPoint.longitude;
            double x2 = point.longitude;
            double dx = x2 - x1;

            if (Math.abs(dx) > 180.0) {
                // we have, most likely, just jumped the dateline (could do
                // further validation to this effect if needed). normalise the
                // numbers.
                if (x > 0) {
                    while (x1 < 0)
                        x1 += 360;
                    while (x2 < 0)
                        x2 += 360;
                } else {
                    while (x1 > 0)
                        x1 -= 360;
                    while (x2 > 0)
                        x2 -= 360;
                }
                dx = x2 - x1;
            }

            if ((x1 <= x && x2 > x) || (x1 >= x && x2 < x)) {
                double grad = (point.latitude - lastPoint.latitude) / dx;
                double intersectAtLat = lastPoint.latitude + ((x - x1) * grad);

                if (intersectAtLat > location.latitude)
                    isInside = !isInside;
            }
            lastPoint = point;
        }

        return isInside;
    }

    public String getNombreGrupo() {
        return nombreGrupo;
    }

    public void setNombreGrupo(String nombreGrupo) {
        this.nombreGrupo = nombreGrupo;
    }

    public int getGrupo() {
        return grupo;
    }

    public void setGrupo(int grupo) {
        this.grupo = grupo;
    }
}
