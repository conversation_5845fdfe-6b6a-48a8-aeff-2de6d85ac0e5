package com.movisat.database;

import com.movisat.utils.Utilss;

public class Usuario extends Object {
    private int id;
    private int idExterno;
    private int empresa;
    private String nombre;
    private String login;
    private String passw;
    private int admin;
    private String idIndra;

    public Usuario(int id, int idExterno, int empresa, String nombre, String login,
                   String passw, int admin, String idIndra) {

        setId(id);
        setIdExterno(idExterno);
        setEmpresa(empresa);
        setNombre(nombre);
        setLogin(login);
        setPassw(passw);
        setAdmin(admin);
        setIdIndra(idIndra);
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setAdmin(int admin) {
        this.admin = admin;
    }

    public void setEmpresa(int empresa) {

        this.empresa = empresa;
    }

    public void setNombre(String nombre) {
        this.nombre = nombre;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public void setPassw(String passw) {
        this.passw = passw;
    }

    public int getId() {
        return id;
    }

    public int getEmpresa() {
        return empresa;
    }

    public String getNombre() {
        return nombre;
    }

    public String getLogin() {
        return login;
    }

    public String getPassw() {
        return passw;
    }

    public String getPasswDecode() {
        byte[] b = passw.getBytes();

        // Decodifico la contrase?a
        for (int i = 0; i < passw.length(); i++)
            b[i] += (i - passw.length());

        return new String(b);
    }

    public int getAdmin() {
        return admin;
    }

    public int getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(int idExterno) {
        this.idExterno = idExterno;
    }

    @Override
    public String toString() {
        return nombre;
    }

    public String getIdIndra() {
        return idIndra;
    }

    public void setIdIndra(String idIndra) {
        this.idIndra = idIndra;
    }

    public boolean isIndra() {
        return !Utilss.isNullOrEmptyLiteral(getIdIndra());
    }
}