package com.movisat.database;

import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Config;
import com.movisat.utilities.Utils;

import java.util.List;

public class Vehiculo {

    public static final int TIPO_MOVIL = 1;
    public static final int TIPO_NO_MOVIL = 2;

	private int codigo;     // No puede ser clave primaria
	private String nombre;
	private int empresa;
	private int tipo;
	private String imei;

	public Vehiculo(int codigo, String nombre, int empresa, int tipoVehiculo, String imei) {

		setCodigo(codigo);
		setNombre(nombre);
		setEmpresa(empresa);
		setTipo(tipoVehiculo);
		setImei(imei);
	}

	public int getCodigo() {
		return codigo;
	}

	public void setCodigo(int codigo) {
		this.codigo = codigo;
	}

	public String getNombre() {
		return nombre;
	}

	public void setNombre(String nombre) {
		this.nombre = nombre;
	}

	public int getEmpresa() {
		return empresa;
	}

	public void setEmpresa(int empresa) {
		this.empresa = empresa;
	}

    public int getTipo() {
        return tipo;
    }

    public void setTipo(int tipo) {
        this.tipo = tipo;
    }

	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	public String toString() {
		return nombre;
	}

	/**
	 * Compara la instancia actual con el vehículo especificado e indica si son el mismo.
	 */
	public boolean equals(Vehiculo vehiculo) {
		return vehiculo != null && codigo == vehiculo.getCodigo() &&
				empresa == vehiculo.getEmpresa() && tipo == vehiculo.getTipo();
	}


	/**
	 * Obtiene el vehículo asociado que está guardado en la configuración (BBDD) del dispositivo.
	 * @return Vehículo asociado al dispositivo, null si no hay vehículo asociado u ocurre un error.
	 */
	public static Vehiculo getVehiculoAsociado() {

		Vehiculo vehiculo = null;

		try {
			String vehiculoConfig = Config.getInstance().getValueEmpresa("vehiculoSeleccionado", "");

			if (vehiculoConfig.length() != 0) {
				List<Integer> values = Utils.stringToIntegerList(vehiculoConfig, ";");

				if (values.size() == 3) {
					DBVehiculo dbVehiculo = new DBVehiculo();
					vehiculo = dbVehiculo.getBy(values.get(0), values.get(1), values.get(2));
					dbVehiculo.close();
				}
			}

		} catch (Exception e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}

		return vehiculo;
	}


	/**
	 * Guarda en la base de datos el vehículo con el que está asociado el dispositivo.
	 * @param v Vehículo que se ha asociado al dispositivo, o null si no se quiere asociación.
	 */
	public static void setVehiculoAsociado(Vehiculo v) {
		try {
			String vehiculoConfig = "";
			if (v != null)
				vehiculoConfig = v.getCodigo() + ";" + v.getEmpresa() + ";" + v.getTipo();
			Config.getInstance().setValueEmpresa("vehiculoSeleccionado", vehiculoConfig);

		} catch (Exception e) {
			MyLoggerHandler.getInstance().error(e);
			e.printStackTrace();
		}
	}
}