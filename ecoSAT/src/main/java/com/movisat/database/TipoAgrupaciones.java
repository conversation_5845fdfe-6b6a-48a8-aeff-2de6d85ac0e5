package com.movisat.database;

import java.sql.Time;
import java.sql.Timestamp;

public class TipoAgrupaciones {
    private int id;
    private String nombre, abreviatura;
    private String fecha_baja;

    public TipoAgrupaciones(int id, String nombre, String abreviatura, String fecha_baja  ) {

        setId(id);
        setNombre(nombre);
        setAbreviatura(abreviatura);
        setFechaBaja(fecha_baja);
    }

    public void setId(int id) {

        this.id = id;
    }

    public void setNombre(String nombre) {

        this.nombre = nombre;
    }

    public void setFechaBaja(String fecha) {

        this.fecha_baja = fecha;
    }

    public void setAbreviatura(String abrev) {

        this.abreviatura = abrev;
    }

    public String getFechaBaja() {

        return fecha_baja;
    }

    public int getId() {

        return id;
    }

    public String getNombre() {

        return nombre;
    }

    public String getAbreviatura() {

        return abreviatura;
    }

    @Override
    public String toString() {

        return nombre;
    }


}