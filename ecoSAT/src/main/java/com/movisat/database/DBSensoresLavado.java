package com.movisat.database;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.sqlite.SQLiteDatabase;

public class DBSensoresLavado {
	private final String TABLE_NAME = "sensores_lavado";
	private SQLiteDatabase db = null;
	private String[] campos = (new String[] { "id", "codigo_elemento",
			"codigo_movil", "empresa", "fecha_registro" });

	private final int COL_ID = 0;
	private final int COL_CODIGO_ELEMENTO = 1;
	private final int COL_CODIGO_MOVIL = 2;
	private final int COL_EMPRESA = 3;
	private final int COL_FECHA_REGISTRO = 4;

	public DBSensoresLavado() {

		try {

			db = Database.getConnection(MainActivity.getInstance()
					.getDatabasePath("ecosat.sqlite").getPath(),
					SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {

			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(SensorLavado reg) {
		long res = 0;

		try {

			ContentValues values = new ContentValues();

			values.put(campos[COL_CODIGO_ELEMENTO], reg.getCodigoElemento());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_CODIGO_MOVIL], reg.getCodigoMovil());
			values.put(campos[COL_FECHA_REGISTRO], reg.getFechaRegistro());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(SensorLavado reg) {
		boolean res = false;

		try {
			if (reg.getId() == 0)
				return res;
			
			ContentValues values = new ContentValues();

			values.put(campos[COL_CODIGO_ELEMENTO], reg.getCodigoElemento());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
			values.put(campos[COL_CODIGO_MOVIL], reg.getCodigoMovil());
			values.put(campos[COL_FECHA_REGISTRO], reg.getFechaRegistro());

			if (db.update(TABLE_NAME, values,
					campos[COL_ID] + "=" + reg.getId() + " AND "
							+ campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
					null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}
}
