package com.movisat.database;

import com.movisat.use_case.ElementCalculateProcessingFrequencyState;
import com.movisat.utilities.Utils;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;

public class Elemento extends ItemMapa implements Serializable {

    /**
     *
     */
    public static final int ESTADO_INACTIVO = 0;
    public static final int ESTADO_ACTIVO = 1;
    public static final int ESTADO_TALLER = 3;
    public static final int ESTADO_LAVADO = 4;
    public static final int ESTADO_BAJA = 5;

    private static final long serialVersionUID = 1L;
    private int id;
    private int idExterno;
    private int empresa;
    private String nombre;
    private String matricula;
    private int modelo, estado, tipo_zona = 0;
    private int rotativo = 0;
    private int vacia_bajo_demanda = 0;
    private String descripcion;
    private String tag;
    private String nivel_critico = "";
    private String tipoElementoMapa = "E";
    private int dia_bloqueo_lunes = 0;
    private int dia_bloqueo_martes = 0;
    private int dia_bloqueo_miercoles = 0;
    private int dia_bloqueo_jueves = 0;
    private int dia_bloqueo_viernes = 0;
    private int dia_bloqueo_sabado = 0;
    private int dia_bloqueo_domingo = 0;
    private byte[] foto = null;
    private int tiene_imagen = 0;
    private String fecha_creacion = "";
    private String codFisico = "";
    private String fechaUltRecogida = "";
    private String fechaUltLavado = "";
    private String volumImei = "";
    private int frecuenciaProcesado = 0;

    private int elemento_similar = 0;
    private ArrayList<Integer> dias_bloqueo = new ArrayList<Integer>();

    // DINIGO - 08/02/2021 - Mantis 0004987: Cuando se introducen fotos en Elementos
    // Al añadir imágenes de elementos, el usuario debe poder elegir si borrar las imágenes existentes en el servidor.
    public boolean tieneQueBorrarImagenesAnterioresEnServidor = false;

    // DINIGO - 08/02/2021 - FIN

    public Elemento(int id, int idExterno, int empresa, String nombre,
                    int modelo, String matricula, int estado, double lat, double lon,
                    String descripcion, String fechaUltRecogida, int frecuenciaProcesado, String volumImei) {

        setId(id);
        setIdExterno(idExterno);
        setEmpresa(empresa);
        setNombre(nombre);
        setModelo(modelo);

        if (!matricula.contains("null"))
            setMatricula(matricula);
        else
            setMatricula("");
        setEstado(estado);
        setTag("");
        setDescripcion(descripcion);
        setFechaUltRecogida(fechaUltRecogida);
        setFrecuenciaProcesado(frecuenciaProcesado);
        setVolumImei(volumImei);
        // Metodos necesarios para los cluster
        setPosition(lat, lon);
    }

    public Elemento(int id, int idExterno, int empresa, String nombre,
                    int modelo, String matricula, int estado, double lat,
                    double lon, String descripcion, String nivel_cri,
                    int elemen_simil, int lunes, int martes, int miercoles,
                    int jueves, int viernes, int sabado, int domingo,
                    int tipo_zona, int rotativo, int vacia_bajo_demanda,
                    int tieneImagen, Date fecha, String codFisico, int frecuenciaProcesado, String volumImei) {

        setTieneImagen(tieneImagen == 1);
        setId(id);
        setIdExterno(idExterno);
        setEmpresa(empresa);
        setNombre(nombre);
        setModelo(modelo);
        setFechaCreacion(fecha); // solo se usa para enviarlo al servidor

        if (!matricula.contains("null"))
            setMatricula(matricula);
        else
            setMatricula("");
        setEstado(estado);
        setTag("");
        setDescripcion(descripcion);
        setDiaBloqueo(1, lunes);
        setDiaBloqueo(2, martes);
        setDiaBloqueo(3, miercoles);
        setDiaBloqueo(4, jueves);
        setDiaBloqueo(5, viernes);
        setDiaBloqueo(6, sabado);
        setDiaBloqueo(7, domingo);
        setIdElementoSimilar(elemen_simil);
        setEstadoCritico(nivel_cri);
        setTipoZona(tipo_zona);
        setRotativo(rotativo);
        setVaciaBajoDemanda(vacia_bajo_demanda);
        setCodFisico(codFisico);
        setFrecuenciaProcesado(frecuenciaProcesado);
        setVolumImei(volumImei);

        // Mitodos necesarios para los cluster
        setPosition(lat, lon);
    }

    public Elemento(int id, int idExterno, int empresa, String nombre,
                    int modelo, String matricula, int estado, double lat,
                    double lon, String descripcion, String nivel_cri,
                    int elemen_simil, int lunes, int martes, int miercoles,
                    int jueves, int viernes, int sabado, int domingo,
                    int tipo_zona, int rotativo, int vacia_bajo_demanda, int tieneImagen,
                    Date fecha, String codFisico, String fechaUltRecogida, String fechaUltLavado,
                    int frecuenciaProcesado, String volumImei) {

        setTieneImagen(tieneImagen == 1);
        setId(id);
        setIdExterno(idExterno);
        setEmpresa(empresa);
        setNombre(nombre);
        setModelo(modelo);
        setFechaCreacion(fecha); // solo se usa para enviarlo al servidor

        if (!matricula.contains("null"))
            setMatricula(matricula);
        else
            setMatricula("");
        setEstado(estado);
        setTag("");
        setDescripcion(descripcion);
        setDiaBloqueo(1, lunes);
        setDiaBloqueo(2, martes);
        setDiaBloqueo(3, miercoles);
        setDiaBloqueo(4, jueves);
        setDiaBloqueo(5, viernes);
        setDiaBloqueo(6, sabado);
        setDiaBloqueo(7, domingo);
        setIdElementoSimilar(elemen_simil);
        setEstadoCritico(nivel_cri);
        setTipoZona(tipo_zona);
        setRotativo(rotativo);
        setVaciaBajoDemanda(vacia_bajo_demanda);
        setCodFisico(codFisico);
        setFechaUltRecogida(fechaUltRecogida);
        setFechaUltLavado(fechaUltLavado);
        setFrecuenciaProcesado(frecuenciaProcesado);
        setVolumImei(volumImei);

        // Mitodos necesarios para los cluster
        setPosition(lat, lon);
    }

    public void setFechaCreacion(Date fecha) {
        if (fecha != null)
            this.fecha_creacion = Utils.datetimeToString(fecha, "yyyy-MM-dd HH:mm:ss");

    }

    public String getFechaCreacion() {
        return this.fecha_creacion;
    }


    public void setEstadoCritico(String nivel_critico) {
        this.nivel_critico = nivel_critico;
    }

    public void setIdElementoSimilar(int similar) {
        this.elemento_similar = similar;
    }

    public int getIdElementoSimilar() {
        return elemento_similar;
    }

    public int getTipoZona() {
        return this.tipo_zona;
    }

    public int esRotativo() {
        return this.rotativo;
    }

    public int getVaciaBajoDemanda() {
        return this.vacia_bajo_demanda;
    }

    public String getEstadoCritico() {
        return this.nivel_critico;
    }

    public void setTipoZona(int tipo_zona) {
        this.tipo_zona = tipo_zona;
    }

    public void setRotativo(int rotativo) {
        this.rotativo = rotativo;
    }

    public void setVaciaBajoDemanda(int demanda) {
        this.vacia_bajo_demanda = demanda;
    }

    private void setVolumImei(String volumImei) {
        this.volumImei = volumImei;
    }


    public void setDiaBloqueo(int dia, int valor) {
        if (dia == 1) {
            this.dia_bloqueo_lunes = valor;
        } else if (dia == 2) {
            this.dia_bloqueo_martes = valor;
        } else if (dia == 3) {
            this.dia_bloqueo_miercoles = valor;
        } else if (dia == 4) {
            this.dia_bloqueo_jueves = valor;
        } else if (dia == 5) {
            this.dia_bloqueo_viernes = valor;
        } else if (dia == 6) {
            this.dia_bloqueo_sabado = valor;
        } else if (dia == 7) {
            this.dia_bloqueo_domingo = valor;
        }
    }

    public int getDiaBloqueo(int dia) {
        if (dia == 1) {
            return dia_bloqueo_lunes;
        } else if (dia == 2) {
            return dia_bloqueo_martes;
        } else if (dia == 3) {
            return dia_bloqueo_miercoles;
        } else if (dia == 4) {
            return dia_bloqueo_jueves;
        } else if (dia == 5) {
            return dia_bloqueo_viernes;
        } else if (dia == 6) {
            return dia_bloqueo_sabado;
        } else if (dia == 7) {
            return dia_bloqueo_domingo;
        } else {
            return 0;
        }
    }

    public ArrayList<Integer> getDiasBloqueo() {

        dias_bloqueo.clear();
        if (dia_bloqueo_lunes == 1)
            dias_bloqueo.add(dia_bloqueo_lunes);
        else if (dia_bloqueo_martes == 1)
            dias_bloqueo.add(dia_bloqueo_martes);
        else if (dia_bloqueo_miercoles == 1)
            dias_bloqueo.add(dia_bloqueo_miercoles);
        else if (dia_bloqueo_jueves == 1)
            dias_bloqueo.add(dia_bloqueo_jueves);
        else if (dia_bloqueo_viernes == 1)
            dias_bloqueo.add(dia_bloqueo_viernes);
        else if (dia_bloqueo_sabado == 1)
            dias_bloqueo.add(dia_bloqueo_sabado);
        else if (dia_bloqueo_domingo == 1)
            dias_bloqueo.add(dia_bloqueo_domingo);

        return dias_bloqueo;
    }

    public void setId(int id) {

        this.id = id;
    }

    public void setEmpresa(int empresa) {

        this.empresa = empresa;
    }

    public void setNombre(String nombre) {

        this.nombre = nombre;
    }

    public void setModelo(int modelo) {

        this.modelo = modelo;
    }

    public void setEstado(int estado) {

        this.estado = estado;
    }

    public int getId() {

        return id;
    }

    @Override
    public int getIdExterno() {

        return idExterno;
    }

    public int getEmpresa() {

        return empresa;
    }

    public String getNombre() {

        return nombre;
    }

    public int getModelo() {

        return modelo;
    }

    public int getEstado() {

        return estado;
    }

    public String getMatricula() {

        return matricula;
    }

    public void setMatricula(String matricula) {

        this.matricula = matricula;
    }

    @Override
    public String toString() {

        String n = "";
        if (nombre.trim().length() > 0)
            n += nombre.trim();
        if (matricula.trim().length() > 0)
            n += "\nMat: " + matricula.trim();
        else
            n += idExterno > 0 ? "\nId: " + idExterno : "";

        if (descripcion != null && descripcion.trim().length() > 0)
            n += "\n" + descripcion.trim();

        if (tag.trim().length() > 0)
            n += "\nTag: " + tag.trim();

        return n;
    }

    public String toString(boolean show) {

        String n = "";
        if (nombre.trim().length() > 0)
            n += nombre.trim();
        if (matricula.trim().length() > 0)
            n += "\nMat: " + matricula.trim();
        else
            n += idExterno > 0 ? "\nId: " + idExterno : "";

        if (descripcion != null && descripcion.trim().length() > 0)
            n += "\n" + descripcion.trim();

        if (tag.trim().length() > 0)
            n += "\nTag: " + tag.trim();

        if (show) {
            n += "\nUlt. recogida: " + (!fechaUltRecogida.equals("") ? fechaUltRecogida : "No registrado");
            n += "\nUlt. lavado: " + (!fechaUltLavado.equals("") ? fechaUltLavado : "No registrado");
        }

        return n;
    }

    @Override
    public void setIdExterno(int idExterno) {
        this.idExterno = idExterno;
    }

    @Override
    public String getTipoElemento() {
        return tipoElementoMapa;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        if (descripcion == null || descripcion.equals("null"))
            this.descripcion = "";
        else
            this.descripcion = descripcion;
    }

    public boolean hasLinkedVolumetric(){
        return volumImei != null && !volumImei.equals("null") && !volumImei.trim().isEmpty();
    }

    public boolean isInsideArea(int codigoArea, int idEmpresa) {

        DBArea dbArea = new DBArea();
        Area area = dbArea.getBy(codigoArea, idEmpresa);
        dbArea.close();

        if (area == null) return false;

        return (area.isInside(this.getPosition()));

    }

    public void setTipoElementoMapa(String tipoElemento) {
        this.tipoElementoMapa = tipoElemento;
    }


    public void setFoto(byte[] foto) {
        this.foto = foto;
    }

    public byte[] getFoto() {
        return this.foto;
    }


    public void setTieneImagen(boolean b) {
        this.tiene_imagen = b ? 1 : 0;
    }

    public boolean getTieneImagen() {
        return this.tiene_imagen == 1;
    }

    public String getCodFisico() {
        return codFisico;
    }

    public FrecuenciaProcesadoState getFrecuenciaProcesadoState() {
        return ElementCalculateProcessingFrequencyState.execute(
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                this.fechaUltRecogida,
                this.frecuenciaProcesado);
    }

    public void setCodFisico(String codFisico) {
        if (codFisico != null && !codFisico.equals("null"))
            this.codFisico = codFisico;
    }

    public String getFechaUltRecogida() {
        return fechaUltRecogida;
    }

    public boolean setFechaUltRecogida(String fechaUltRecogida) {
        if (fechaUltRecogida != null && !fechaUltRecogida.equals("")) {
            if (this.fechaUltRecogida == null || this.fechaUltRecogida.equals("")
                  || Utils.StringToDateTime(fechaUltRecogida).after(Utils.StringToDateTime(this.fechaUltRecogida))) {
                this.fechaUltRecogida = fechaUltRecogida;
                return true;
            }
        }
        return false;
    }

    public void removeFechaUltRecogida() {
        this.fechaUltRecogida = "";
    }

    public String getFechaUltLavado() {
        return fechaUltLavado;
    }

    public boolean setFechaUltLavado(String fechaUltLavado) {
        if (fechaUltLavado != null && !fechaUltLavado.equals("")) {
            if (this.fechaUltLavado == null || this.fechaUltLavado.equals("")
                  || Utils.StringToDateTime(fechaUltLavado).after(Utils.StringToDateTime(this.fechaUltLavado))) {
                this.fechaUltLavado = fechaUltLavado;
                return true;
            }
        }
        return false;
    }

    public int getFrecuenciaProcesado() {
        return frecuenciaProcesado;
    }

    public void setFrecuenciaProcesado(int frecuenciaProcesado) {
        this.frecuenciaProcesado = frecuenciaProcesado;
    }

    public String getVolumImei() {
        return volumImei;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Elemento elemento = (Elemento) o;
        return id == elemento.id && idExterno == elemento.idExterno;
    }

}