package com.movisat.database;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.Calendar;
import java.util.Date;

public class IncidentFilterCriteria implements Parcelable {
    public enum DateFilter {
        ALL,
        TODAY,
        YESTERDAY,
        LAST_7_DAYS,
        CUSTOM
    }

    public enum SortOrder {
        NEWEST_FIRST,
        OLDEST_FIRST
    }

    private DateFilter dateFilter = DateFilter.ALL;
    private Date fromDate;
    private Date toDate;
    private SortOrder sortOrder = SortOrder.NEWEST_FIRST;
    private int statusFilter = -1; // -1 means all statuses
    private int incidentTypeFilter = -1; // -1 means all types
    private int ownerFilter = -1; // -1 means all owners
    private String ownerName = ""; // Empty means all owners
    private boolean withElementOnly = false;

    public IncidentFilterCriteria() {
        // Default constructor
    }

    protected IncidentFilterCriteria(Parcel in) {
        dateFilter = DateFilter.values()[in.readInt()];
        long fromDateLong = in.readLong();
        fromDate = fromDateLong == -1 ? null : new Date(fromDateLong);
        long toDateLong = in.readLong();
        toDate = toDateLong == -1 ? null : new Date(toDateLong);
        sortOrder = SortOrder.values()[in.readInt()];
        statusFilter = in.readInt();
        incidentTypeFilter = in.readInt();
        ownerFilter = in.readInt();
        ownerName = in.readString();
        withElementOnly = in.readByte() != 0;
    }

    public static final Creator<IncidentFilterCriteria> CREATOR = new Creator<IncidentFilterCriteria>() {
        @Override
        public IncidentFilterCriteria createFromParcel(Parcel in) {
            return new IncidentFilterCriteria(in);
        }

        @Override
        public IncidentFilterCriteria[] newArray(int size) {
            return new IncidentFilterCriteria[size];
        }
    };

    // Getters and setters
    public DateFilter getDateFilter() {
        return dateFilter;
    }

    public void setDateFilter(DateFilter dateFilter) {
        this.dateFilter = dateFilter;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public SortOrder getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(SortOrder sortOrder) {
        this.sortOrder = sortOrder;
    }

    public int getStatusFilter() {
        return statusFilter;
    }

    public void setStatusFilter(int statusFilter) {
        this.statusFilter = statusFilter;
    }

    public int getIncidentTypeFilter() {
        return incidentTypeFilter;
    }

    public void setIncidentTypeFilter(int incidentTypeFilter) {
        this.incidentTypeFilter = incidentTypeFilter;
    }

    public int getOwnerFilter() {
        return ownerFilter;
    }

    public void setOwnerFilter(int ownerFilter) {
        this.ownerFilter = ownerFilter;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName == null ? "" : ownerName;
    }

    public boolean isWithElementOnly() {
        return withElementOnly;
    }

    public void setWithElementOnly(boolean withElementOnly) {
        this.withElementOnly = withElementOnly;
    }

    public void reset() {
        dateFilter = DateFilter.ALL;
        fromDate = null;
        toDate = null;
        sortOrder = SortOrder.NEWEST_FIRST;
        statusFilter = -1;
        incidentTypeFilter = -1;
        ownerFilter = -1;
        ownerName = "";
        withElementOnly = false;
    }

    public boolean hasActiveFilters() {
        return dateFilter != DateFilter.ALL ||
               statusFilter != -1 ||
               incidentTypeFilter != -1 ||
               ownerFilter != -1 ||
               (ownerName != null && !ownerName.isEmpty()) ||
               withElementOnly;
    }

    public Date[] getEffectiveDateRange() {
        Calendar calendar = Calendar.getInstance();
        Date[] range = new Date[2]; // [from, to]

        switch (dateFilter) {
            case TODAY:
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                range[0] = calendar.getTime();
                
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                range[1] = calendar.getTime();
                break;

            case YESTERDAY:
                calendar.add(Calendar.DAY_OF_MONTH, -1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                range[0] = calendar.getTime();
                
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                range[1] = calendar.getTime();
                break;

            case LAST_7_DAYS:
                calendar.add(Calendar.DAY_OF_MONTH, -7);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                range[0] = calendar.getTime();
                
                calendar = Calendar.getInstance();
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                range[1] = calendar.getTime();
                break;

            case CUSTOM:
                range[0] = fromDate;
                range[1] = toDate;
                break;

            case ALL:
            default:
                range[0] = null;
                range[1] = null;
                break;
        }

        return range;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(dateFilter.ordinal());
        dest.writeLong(fromDate == null ? -1 : fromDate.getTime());
        dest.writeLong(toDate == null ? -1 : toDate.getTime());
        dest.writeInt(sortOrder.ordinal());
        dest.writeInt(statusFilter);
        dest.writeInt(incidentTypeFilter);
        dest.writeInt(ownerFilter);
        dest.writeString(ownerName);
        dest.writeByte((byte) (withElementOnly ? 1 : 0));
    }
}