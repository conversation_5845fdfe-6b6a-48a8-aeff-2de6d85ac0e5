package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import java.util.ArrayList;

public class DBVehiculo {
	private final String TABLE_NAME = "vehiculos";
	private SQLiteDatabase db = null;

	private String[] campos = (new String[] { "codigo", "nombre", "empresa", "tipo", "imei" });
	private final int COL_CODIGO = 0;
	private final int COL_NOMBRE = 1;
	private final int COL_EMPRESA = 2;
    private final int COL_TIPO = 3;
    private final int COL_IMEI = 4;


	public DBVehiculo() {

		try {
			db = Database.getConnection(MainActivity.getInstance().getDatabasePath(
					"ecosat.sqlite").getPath(), SQLiteDatabase.OPEN_READWRITE);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public void close() {

		try {
			if (db != null)
				db.close();

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public long insert(Vehiculo reg) {
		long res = 0;

		try {
			ContentValues values = new ContentValues();

			values.put(campos[COL_CODIGO], reg.getCodigo());
			values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_TIPO], reg.getTipo());
            values.put(campos[COL_IMEI], reg.getImei());

			res = db.insert(TABLE_NAME, null, values);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean update(Vehiculo reg) {
		boolean res = false;

		try {
			ContentValues values = new ContentValues();

			values.put(campos[COL_CODIGO], reg.getCodigo());
			values.put(campos[COL_NOMBRE], reg.getNombre());
			values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_TIPO], reg.getTipo());
            values.put(campos[COL_IMEI], reg.getImei());

			if (db.update(TABLE_NAME, values,
                    campos[COL_CODIGO] + "=" + reg.getCodigo() + " AND "
                            + campos[COL_EMPRESA] + "=" + reg.getEmpresa() + " AND "
                            + campos[COL_TIPO] + "=" + reg.getTipo(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public boolean delete(Vehiculo reg) {
		boolean res = false;

		try {
		    // Los vehículos se identifican por su código, empresa y tipo
			if (db.delete(TABLE_NAME,
                    campos[COL_CODIGO] + "=" + reg.getCodigo() + " AND "
                            + campos[COL_EMPRESA] + "=" + reg.getEmpresa() + " AND "
                            + campos[COL_TIPO] + "=" + reg.getTipo(), null) > 0)
				res = true;

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public int getCount() {
		int res = 0;
		Cursor cur;

		try {
			if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }), null,
                    null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = cur.getInt(0);

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public void deleteAll() {

		try {
			db.delete(TABLE_NAME, null, null);

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

	}

	public Vehiculo getBy(int codigo, int empresa, int tipo) {
		Cursor cur;
		Vehiculo res = null;

		try {
            // Los vehículos se identifican por su código, empresa y tipo
			if ((cur = db.query(TABLE_NAME, campos,
                    campos[COL_CODIGO] + "=" + codigo + " AND "
                            + campos[COL_EMPRESA] + "=" + empresa + " AND "
                            + campos[COL_TIPO] + "=" + tipo,
					null, null, null, null)) != null) {

				if (cur.moveToFirst())
					res = new Vehiculo(cur.getInt(COL_CODIGO), cur.getString(COL_NOMBRE),
                            cur.getInt(COL_EMPRESA), cur.getInt(COL_TIPO), cur.getString(COL_IMEI));

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

	public ArrayList<Vehiculo> getAllBy(int empresa) {
		Cursor cur;
		ArrayList<Vehiculo> res = null;

		try {

			if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "=" + empresa,
                    null, null, null,
					campos[COL_NOMBRE] + " ASC")) != null) {

				if (cur.moveToFirst()) {

					res = new ArrayList<Vehiculo>();

					do {

						res.add(new Vehiculo(cur.getInt(COL_CODIGO), cur.getString(COL_NOMBRE),
                                cur.getInt(COL_EMPRESA), cur.getInt(COL_TIPO), cur.getString(COL_IMEI)));

					} while (cur.moveToNext());

				}

				cur.close();
			}

		} catch (Throwable e) {
			MyLoggerHandler.getInstance().error(e);
		}

		return res;
	}

}
