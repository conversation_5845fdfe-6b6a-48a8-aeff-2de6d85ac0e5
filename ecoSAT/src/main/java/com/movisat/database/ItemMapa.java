package com.movisat.database;

import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.maps.android.clustering.ClusterItem;

public abstract class  ItemMapa  implements ClusterItem, Cloneable {

	private LatLng position;
	private Marker marker;
	private boolean fromRfID = false;
	
	@Override
	public LatLng getPosition() {

		return position;
	}
	
	protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

	@Override
	public void setMarker(Marker marker) {

		this.marker = marker;
		
	}

	@Override
	public Marker getMarker() {

		return this.marker;
	}
	
	public void setFromRfID(boolean valor) {
		fromRfID = valor;
	}

	public boolean getFromRfID() {
		return fromRfID;
	}

	public void setPosition(double lat, double lon) {

		this.position = new LatLng(lat, lon);
	}
	
	public abstract String getTipoElemento ();
	
	public abstract  int getId();
		
	public abstract  int getIdExterno();
	
	public abstract  void setIdExterno(int idExterno);

}
