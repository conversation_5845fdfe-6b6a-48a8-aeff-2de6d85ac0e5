package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.location.Location;

import com.google.android.gms.maps.model.LatLng;
import com.movisat.ecosat.GruposActivity;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Created by faroca on 17/08/2015.
 */
public class DBGrupoAreas {

    private final String TABLE_NAME = "areas_grupos";
    private SQLiteDatabase db = null;
    private String[] campos = (new String[]{"id", "empresa",
            "abreviatura", "nombre", "clicks"});

    private final int COL_ID = 0;
    private final int COL_EMPRESA = 1;
    private final int COL_ABREVIATURA = 2;
    private final int COL_NOMBRE = 3;
    private final int COL_CLICKS = 4;

    public DBGrupoAreas() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }


    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(GrupoAreas reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_ABREVIATURA], reg.getAbreviatura());
            values.put(campos[COL_CLICKS], reg.getClicks());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(GrupoAreas reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID], reg.getId());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_ABREVIATURA], reg.getAbreviatura());
            values.put(campos[COL_CLICKS], reg.getClicks());

            if (db.update(TABLE_NAME, values,
                    campos[COL_ID] + "=" + reg.getId() + " AND "
                            + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(GrupoAreas reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
                            + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;


        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public int getCount(int empresa) {
        int res = 0;
        Cursor cur;

        try {
            cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                    campos[COL_EMPRESA] + "=" + empresa, null, null, null, null);
            if (cur != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public Collection<Integer> getCollectionCodigosGrupos(int empresa) {
        ArrayList<GrupoAreas> areas = getAll(empresa);
        Collection<Integer> newCollection = null;
        if (areas != null) {
            newCollection = new ArrayList<Integer>();
            for (GrupoAreas area :
                    areas) {
                newCollection.add(area.getId());
            }
        }
        return newCollection;
    }

    public GrupoAreas getBy(int empresa, int id) {
        Cursor cur;
        GrupoAreas res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa + " AND id=" + id, null, null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    do {

                        res = new GrupoAreas(
                                cur.getInt(COL_ID),
                                cur.getInt(COL_EMPRESA),
                                cur.getString(COL_ABREVIATURA),
                                cur.getString(COL_NOMBRE),
                                cur.getInt(COL_CLICKS));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<GrupoAreas> getLast50MoreClicks(int empresa) {
        Cursor cur;
        ArrayList<GrupoAreas> res = null;

        DBArea dbAreas = new DBArea();
        List<Area> areas;
        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa + " AND clicks > 0", null, null, null, campos[COL_CLICKS] + " DESC", "50")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<GrupoAreas>();

                    do {

                        areas = dbAreas.getAreasBy(cur.getInt(COL_ID));

                        if (areas.size() > 0) {
                            res.add(new GrupoAreas(
                                    cur.getInt(COL_ID),
                                    cur.getInt(COL_EMPRESA),
                                    cur.getString(COL_ABREVIATURA),
                                    cur.getString(COL_NOMBRE),
                                    cur.getInt(COL_CLICKS)

                            ));
                        }
                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        dbAreas.close();
        return res;
    }


    public int calculateDistance(GrupoAreas grupo, LatLng position) {
        DBArea dbArea = new DBArea();

        int distanceMin = Integer.MAX_VALUE;
        int distance = 0;
        List<Area> areas = dbArea.getAreasBy(grupo.getId());
        for (Area area :
                areas) {
            distance =
                    area.distanceMinPuntosControl(position);
            if (distance < distanceMin)
                distanceMin = distance;
        }
        dbArea.close();
        return distanceMin;
    }

    public void AddClick(int empresa, int id) {
        GrupoAreas grupo = getBy(empresa, id);
        if (grupo != null) {
            grupo.setClicks(grupo.getClicks() + 1);
        }
        update(grupo);
    }

    public ArrayList<GrupoAreas> getAll(int empresa, String nombre) {
        Cursor cur;
        ArrayList<GrupoAreas> res = null;

        DBArea dbAreas = new DBArea();
        List<Area> areas;
        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa + " AND " + campos[COL_NOMBRE] + " LIKE '%" + nombre + "%'", null, null, null, campos[COL_NOMBRE] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<GrupoAreas>();

                    do {

                        areas = dbAreas.getAreasBy(cur.getInt(COL_ID));

                        if (areas.size() > 0) {
                            res.add(new GrupoAreas(
                                    cur.getInt(COL_ID),
                                    cur.getInt(COL_EMPRESA),
                                    cur.getString(COL_ABREVIATURA),
                                    cur.getString(COL_NOMBRE),
                                    cur.getInt(COL_CLICKS)

                            ));
                        }
                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        dbAreas.close();
        return res;
    }

    public ArrayList<GrupoAreas> getAll(int empresa) {
        Cursor cur;
        ArrayList<GrupoAreas> res = null;

        DBArea dbAreas = new DBArea();
        List<Area> areas;
        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<GrupoAreas>();

                    do {

                        areas = dbAreas.getAreasBy(cur.getInt(COL_ID));

                        if (areas.size() > 0) {
                            res.add(new GrupoAreas(
                                    cur.getInt(COL_ID),
                                    cur.getInt(COL_EMPRESA),
                                    cur.getString(COL_ABREVIATURA),
                                    cur.getString(COL_NOMBRE),
                                    cur.getInt(COL_CLICKS)

                            ));
                        }
                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        dbAreas.close();
        return res;
    }
}
