package com.movisat.database;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import java.util.ArrayList;

public class DBIdentificaciones {
    private static final String TABLE_NAME = "identificaciones";
    private SQLiteDatabase db = null;
    private final String[] campos = (new String[] {
            "codigoMovil",
            "nombreMovil",
            "nombreCategoria",
            "nombreEmpleado",
            "fechaInicio" });
    private final int COL_CODIGO_MOVIL = 0;
    private final int COL_NOMBRE_MOVIL = 1;
    private final int COL_NOMBRE_CATEGORIA = 2;
    private final int COL_NOMBRE_EMPLEADO = 3;
    private final int COL_FECHA_INICIO = 4;

    public DBIdentificaciones() {
        try {
            db = Database.getConnection(MainActivity.getInstance()
                    .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public void close() {
        try {
            if (db != null)
                db.close();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }

    public long insert(Identificacion reg) {
        long res = 0;
        try {
            ContentValues values = new ContentValues();
            values.put(campos[COL_CODIGO_MOVIL], reg.getCodigoMovil());
            values.put(campos[COL_NOMBRE_MOVIL], reg.getNombreMovil());
            values.put(campos[COL_NOMBRE_CATEGORIA], reg.getNombreCategoria());
            values.put(campos[COL_NOMBRE_EMPLEADO], reg.getNombreEmpleado());
            values.put(campos[COL_FECHA_INICIO], reg.getFechaInicio());
            res = db.insert(TABLE_NAME, null, values);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public boolean update(Identificacion reg) {
        boolean res = false;
        try {
            ContentValues values = new ContentValues();
            values.put(campos[COL_CODIGO_MOVIL], reg.getCodigoMovil());
            values.put(campos[COL_NOMBRE_MOVIL], reg.getNombreMovil());
            values.put(campos[COL_NOMBRE_CATEGORIA], reg.getNombreCategoria());
            values.put(campos[COL_NOMBRE_EMPLEADO], reg.getNombreEmpleado());
            values.put(campos[COL_FECHA_INICIO], reg.getFechaInicio());
            res = db.update(TABLE_NAME, values, campos[COL_CODIGO_MOVIL] + " = ?",
                    new String[] { String.valueOf(reg.getCodigoMovil()) }) > 0;
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public boolean delete(Identificacion reg) {
        boolean res = false;
        try {
            res = db.delete(TABLE_NAME, campos[COL_CODIGO_MOVIL] + " = ?",
                    new String[] { String.valueOf(reg.getCodigoMovil()) }) > 0;
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public Identificacion get(int codigoMovil) {
        Identificacion reg = null;
        try {
            Cursor cursor = db.query(TABLE_NAME, campos, campos[COL_CODIGO_MOVIL] + " = ?",
                    new String[] { String.valueOf(codigoMovil) }, null, null, null);
            if (cursor.moveToFirst()) {
                reg = new Identificacion(cursor.getInt(COL_CODIGO_MOVIL),
                        cursor.getString(COL_NOMBRE_MOVIL),
                        cursor.getString(COL_NOMBRE_CATEGORIA),
                        cursor.getString(COL_NOMBRE_EMPLEADO),
                        cursor.getString(COL_FECHA_INICIO));
            }
            cursor.close();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return reg;
    }

    public ArrayList<Identificacion> getAll() {
        ArrayList<Identificacion> list = new ArrayList<Identificacion>();
        try {
            Cursor cursor = db.query(TABLE_NAME, campos, null, null, null, null, null);
            if (cursor.moveToFirst()) {
                do {
                    list.add(new Identificacion(cursor.getInt(COL_CODIGO_MOVIL),
                            cursor.getString(COL_NOMBRE_MOVIL),
                            cursor.getString(COL_NOMBRE_CATEGORIA),
                            cursor.getString(COL_NOMBRE_EMPLEADO),
                            cursor.getString(COL_FECHA_INICIO)));
                } while (cursor.moveToNext());
            }
            cursor.close();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return list;
    }

    public ArrayList<Identificacion> getByMovil(int codigoMovil) {
        ArrayList<Identificacion> list = new ArrayList<Identificacion>();
        try {
            Cursor cursor = db.query(TABLE_NAME, campos, campos[COL_CODIGO_MOVIL] + " = ?",
                    new String[] { String.valueOf(codigoMovil) }, null, null, null);
            if (cursor.moveToFirst()) {
                do {
                    list.add(new Identificacion(cursor.getInt(COL_CODIGO_MOVIL),
                            cursor.getString(COL_NOMBRE_MOVIL),
                            cursor.getString(COL_NOMBRE_CATEGORIA),
                            cursor.getString(COL_NOMBRE_EMPLEADO),
                            cursor.getString(COL_FECHA_INICIO)));
                } while (cursor.moveToNext());
            }
            cursor.close();
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return list;
    }

    public int getCount() {
        int res = 0;
        Cursor cur;
        try {
            if ((cur = db.query(TABLE_NAME, (new String[] { "count(*)" }), null, null, null, null, null)) != null) {
                if (cur.moveToFirst())
                    res = cur.getInt(0);
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return res;
    }

    public Cursor getCursor() {
        Cursor cur = null;
        try {
            cur = db.query(TABLE_NAME, campos, null, null, null, null, null);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return cur;
    }

    public void deleteAll() {
        try {
            db.delete(TABLE_NAME, null, null);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }
}
