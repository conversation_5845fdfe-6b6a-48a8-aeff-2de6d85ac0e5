package com.movisat.database;

import java.util.ArrayList;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

public class DBUsuario {
    private final String TABLE_NAME = "usuarios";
    private SQLiteDatabase db = null;
    private String[] campos = (new String[]{"id", "idExterno", "empresa",
            "nombre", "login", "passw", "admin", "idIndra"});

    private final int COL_ID = 0;
    private final int COL_ID_EXTERNO = 1;
    private final int COL_EMPRESA = 2;
    private final int COL_NOMBRE = 3;
    private final int COL_LOGIN = 4;
    private final int COL_PASSWORD = 5;
    private final int COL_ADMIN = 6;
    private final int COL_ID_INDRA = 7;

    public DBUsuario() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public long insert(Usuario reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_LOGIN], reg.getLogin());
            values.put(campos[COL_PASSWORD], reg.getPassw());
            values.put(campos[COL_ADMIN], reg.getAdmin());
            values.put(campos[COL_ID_INDRA], reg.getIdIndra());

            res = db.insert(TABLE_NAME, null, values);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(Usuario reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_ID_EXTERNO], reg.getIdExterno());
            values.put(campos[COL_EMPRESA], reg.getEmpresa());
            values.put(campos[COL_NOMBRE], reg.getNombre());
            values.put(campos[COL_LOGIN], reg.getLogin());
            values.put(campos[COL_PASSWORD], reg.getPassw());
            values.put(campos[COL_ADMIN], reg.getAdmin());
            values.put(campos[COL_ID_INDRA], reg.getIdIndra());

            if (db.update(TABLE_NAME, values,
                    campos[COL_ID] + "=" + reg.getId() + " AND "
                            + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

            if (!res) {
                if (db.update(TABLE_NAME, values, campos[COL_ID_EXTERNO] + "="
                        + reg.getIdExterno() + " AND " + campos[COL_EMPRESA]
                        + "=" + reg.getEmpresa(), null) > 0)
                    res = true;
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean delete(Usuario reg) {
        boolean res = false;

        try {

            if (db.delete(TABLE_NAME, campos[COL_ID] + "=" + reg.getId()
                            + " AND " + campos[COL_EMPRESA] + "=" + reg.getEmpresa(),
                    null) > 0)
                res = true;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public int getCount(int empresa) {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db
                    .query(TABLE_NAME, (new String[]{"count(*)"}),
                            campos[COL_EMPRESA] + "=" + empresa, null, null,
                            null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public void deleteAll(int empresa) {

        try {

            db.delete(TABLE_NAME, campos[COL_EMPRESA] + "=" + empresa, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public Usuario getByID(int id, int empresa) {
        Cursor cur;
        Usuario res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_EXTERNO] + "=" + id
                            + " AND " + campos[COL_EMPRESA] + "=" + empresa, null,
                    null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Usuario(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_NOMBRE),
                            cur.getString(COL_LOGIN),
                            cur.getString(COL_PASSWORD),
                            cur.getInt(COL_ADMIN),
                            cur.getString(COL_ID_INDRA));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    public Usuario getIndra(String login) {
        Cursor cur;
        Usuario res = null;

        try {

            // Ignoramos el case sensitive porque EcoSat modifica el login del usuario y lo establece a mayúsculas cuando el usuario se modifica.
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_LOGIN] + "='"
                            + login + "' COLLATE NOCASE AND (" + campos[COL_PASSWORD] + " IS NULL OR " + campos[COL_PASSWORD] + " = '')",
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Usuario(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_NOMBRE),
                            cur.getString(COL_LOGIN),
                            cur.getString(COL_PASSWORD),
                            cur.getInt(COL_ADMIN),
                            cur.getString(COL_ID_INDRA));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public Usuario getByLogin(String login, int empresa) {
        Cursor cur;
        Usuario res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_LOGIN] + "='"
                            + login + "' AND " + campos[COL_EMPRESA] + "=" + empresa,
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = new Usuario(cur.getInt(COL_ID),
                            cur.getInt(COL_ID_EXTERNO),
                            cur.getInt(COL_EMPRESA), cur.getString(COL_NOMBRE),
                            cur.getString(COL_LOGIN),
                            cur.getString(COL_PASSWORD),
                            cur.getInt(COL_ADMIN),
                            cur.getString(COL_ID_INDRA));

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<Usuario> getAll(int empresa) {
        Cursor cur;
        ArrayList<Usuario> res = null;

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_EMPRESA] + "="
                    + empresa, null, null, null, campos[COL_ID] + " ASC")) != null) {

                if (cur.moveToFirst()) {

                    res = new ArrayList<Usuario>();

                    do {

                        res.add(new Usuario(cur.getInt(COL_ID), cur
                                .getInt(COL_ID_EXTERNO), cur
                                .getInt(COL_EMPRESA),
                                cur.getString(COL_NOMBRE), cur
                                .getString(COL_LOGIN), cur
                                .getString(COL_PASSWORD),
                                cur.getInt(COL_ADMIN),
                                cur.getString(COL_ID_INDRA)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }


    /**
     * Devuelve todos los registros de usuario con el login especificado. Se devolverán tantos
     * registros como empresas haya asociadas a ese usuario.
     */
    public ArrayList<Usuario> getByLogin(String login) {
        Cursor cur;
        ArrayList<Usuario> res = new ArrayList<>();

        try {

            if ((cur = db.query(TABLE_NAME, campos, campos[COL_LOGIN] + "='" + login + "'",
                    null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    do {
                        res.add(new Usuario(cur.getInt(COL_ID),
                                cur.getInt(COL_ID_EXTERNO),
                                cur.getInt(COL_EMPRESA),
                                cur.getString(COL_NOMBRE),
                                cur.getString(COL_LOGIN),
                                cur.getString(COL_PASSWORD),
                                cur.getInt(COL_ADMIN),
                                cur.getString(COL_ID_INDRA)));
                    } while (cur.moveToNext());

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

}
