package com.movisat.database;

public class IncidenciaEstado {
    private int id;
    private int idExterno;
    private int empresa;
    private int idIncidencia;
    private int idIncidenciaExterno;
    private int idEstado;
    private int esAvisoFalso;
    private String observacion;
    private String fecha;

    public IncidenciaEstado(int id, int idExterno, int empresa, int idIncidencia, int idIncidenciaExterno,
                            int idEstado, String fecha, int esAvisoFalso, String observacion) {
        setId(id);
        setEmpresa(empresa);
        setIdIncidencia(idIncidencia);
        setIdIncidenciaExterno(idIncidenciaExterno);
        setIdEstado(idEstado);
        setFecha(fecha);
        setIdExterno(idExterno);
        setEsAvisoFalso(esAvisoFalso);
        setObservacion(observacion);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getIdExterno() {
        return idExterno;
    }

    public void setIdExterno(int idExterno) {
        this.idExterno = idExterno;
    }

    public int getEmpresa() {
        return empresa;
    }

    public void setEmpresa(int empresa) {
        this.empresa = empresa;
    }

    public int getIdIncidencia() {
        return idIncidencia;
    }

    public void setIdIncidencia(int idIncidencia) {
        this.idIncidencia = idIncidencia;
    }

    public int getIdIncidenciaExterno() {
        return idIncidenciaExterno;
    }

    public void setIdIncidenciaExterno(int idIncidenciaExterno) {
        this.idIncidenciaExterno = idIncidenciaExterno;
    }

    public int getIdEstado() {
        return idEstado;
    }

    public void setIdEstado(int idEstado) {
        this.idEstado = idEstado;
    }

    public int esAvisoFalso() {
        return esAvisoFalso;
    }

    public void setEsAvisoFalso(int esAvisoFalso) {
        this.esAvisoFalso = esAvisoFalso;
    }

    public String getObservacion() {
        return observacion;
    }

    public void setObservacion(String observacion) {
        this.observacion = observacion;
    }

    public String getFecha() {
        return fecha;
    }

    public void setFecha(String fecha) {
        this.fecha = fecha;
    }

    @Override
    public String toString() {
        return fecha;
    }

}