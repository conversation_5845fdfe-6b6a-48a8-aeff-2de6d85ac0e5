package com.movisat.database;

import android.util.Log;

import androidx.annotation.NonNull;

import com.environment.Environment;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Utils;

import java.io.Serializable;

public class Incidencia extends ItemMapa implements Serializable, Comparable<Incidencia> {
   private int id;
   private int idExterno;
   private int empresa;
   private int tipo, modelo, motivo, ultimoEstado;
   private long fechaUltimoEstado; // Fecha del último estado
   private int elemento;
   private String observ;
   private int usuario, tipoPropietario, propietario;
   private String nombrePropietario;
   private Marker marker;
   private LatLng position;
   private String toStringGenerated;
   private int totalRegistros;
   private Integer movil;
   private String idSincro;
   private Integer ruta;
   private Integer rutaH;
   private String fechaBaja;
   private Integer latitudInt;
   private Integer longitudInt;
   private String motivoDesc;
   private String infoGeo;
   private String fecha;
   private Integer tipoElem;
   private String matricula;
   private String imagen;
   private Integer idEquipo;
   private String emailCiudadano;
   private int validada;
   private int informado;
   private Integer acera;
   private String textoCambioEstado;
   private boolean borrado;
   private String fechaModificacion;
   private boolean ordenTrabajo;
   private int x;
   private int y;
   private int xMercator;
   private int yMercator;
   private String municipio;

   public Incidencia(int id, int idExterno, int empresa, int tipo, int modelo,
         int motivo, String observ, int ultimoEstado, int elemento, double lat,
         double lon, int usuario, int tipoPropietario, int propietario) {
      setId(id);
      setIdExterno(idExterno);
      setEmpresa(empresa);
      setTipo(tipo);
      setModelo(modelo);
      setMotivo(motivo);
      setObserv(observ);
      setUltimoEstado(ultimoEstado);
      setElemento(elemento);
      setUsuario(usuario);
      setPosition(lat, lon);
      setMarker(null);
      setTipoPropietario(tipoPropietario);
      setPropietario(propietario);
   }

   public Incidencia(int id, int idExterno, int empresa, int tipo, int modelo,
         int motivo, String observ, int ultimoEstado, long fechaUltimoEstado, int elemento,
         double lat, double lon, int usuario, int tipoPropietario, int propietario) {
      this(id, idExterno, empresa, tipo, modelo,
            motivo, observ, ultimoEstado, elemento, lat,
            lon, usuario, tipoPropietario, propietario);
      setFechaUltimoEstado(fechaUltimoEstado);
   }

   public Incidencia(int id, int idExterno, int empresa, int tipo, int modelo,
         int motivo, String observ, int ultimoEstado, long fechaUltimoEstado, int elemento,
         double lat, double lon, int usuario, int tipoPropietario, int propietario, String nombrePropietario) {
      this(id, idExterno, empresa, tipo, modelo,
            motivo, observ, ultimoEstado, elemento, lat,
            lon, usuario, tipoPropietario, propietario);
      setFechaUltimoEstado(fechaUltimoEstado);
      setNombrePropietario(nombrePropietario);
   }

   public Incidencia(int id, int idExterno, int empresa, int tipo, int modelo,
         int motivo, String observ, int ultimoEstado, int elemento, double lat,
         double lon, int usuario, int tipoPropietario, int propietario,
         int totalRegistros, Integer movil, String idSincro, Integer ruta, Integer rutaH, String fechaBaja,
         Integer latitudInt, Integer longitudInt, String motivoDesc, String infoGeo, String fecha,
         Integer tipoElem, String matricula, String imagen, Integer idEquipo, String emailCiudadano,
         int validada, int informado, Integer acera, String textoCambioEstado, boolean borrado,
         String fechaModificacion, boolean ordenTrabajo,
         int x, int y, int xMercator, int yMercator, String municipio) {
      this(id, idExterno, empresa, tipo, modelo, motivo, observ, ultimoEstado, elemento, lat, lon, usuario, tipoPropietario, propietario);
      this.totalRegistros = totalRegistros;
      this.movil = movil;
      this.idSincro = idSincro;
      this.ruta = ruta;
      this.rutaH = rutaH;
      this.fechaBaja = fechaBaja;
      this.latitudInt = latitudInt;
      this.longitudInt = longitudInt;
      this.motivoDesc = motivoDesc;
      this.infoGeo = infoGeo;
      this.fecha = fecha;
      this.tipoElem = tipoElem;
      this.matricula = matricula;
      this.imagen = imagen;
      this.idEquipo = idEquipo;
      this.emailCiudadano = emailCiudadano;
      this.validada = validada;
      this.informado = informado;
      this.acera = acera;
      this.textoCambioEstado = textoCambioEstado;
      this.borrado = borrado;
      this.fechaModificacion = fechaModificacion;
      this.ordenTrabajo = ordenTrabajo;
      this.x = x;
      this.y = y;
      this.xMercator = xMercator;
      this.yMercator = yMercator;
      this.municipio = municipio;
   }

   public void setId(int id) {
      this.id = id;
   }

   public void setEmpresa(int empresa) {
      this.empresa = empresa;
   }

   public void setTipo(int tipo) {
      this.tipo = tipo;
   }

   public void setModelo(int modelo) {
      this.modelo = modelo;
   }

   public void setMotivo(int motivo) {
      this.motivo = motivo;
   }

   public void setUltimoEstado(int ultimoEstado) {
      if (this.ultimoEstado != ultimoEstado) {
         this.ultimoEstado = ultimoEstado;
         toStringGenerated = null; // Invalido el valor previamente generado
      }
   }

   public void setElemento(int elemento) {
      this.elemento = elemento;
   }

   public void setObserv(String observ) {
      this.observ = observ;
   }

   public void setPosition(double lat, double lon) {
      position = new LatLng(lat, lon);
   }

   public void setMarker(Marker marker) {
      this.marker = marker;
   }

   public int getId() {
      return id;
   }

   public int getEmpresa() {
      return empresa;
   }

   public int getTipo() {
      return tipo;
   }

   public int getModelo() {
      return modelo;
   }

   public int getMotivo() {
      return motivo;
   }

   public String getObserv() {
      return observ;
   }

   public int getUltimoEstado() {
      if (ultimoEstado == 0) {
         IncidenciaEstado incidenciaEstado = getUltimoEstadoIncidencia();

         if (incidenciaEstado != null) {
            ultimoEstado = incidenciaEstado.getIdEstado();
            // Aprovechamos para actualizar la fecha
            fechaUltimoEstado = Utils.stringToDate(incidenciaEstado.getFecha(), "yyyy-MM-dd HH:mm:ss").getTime() / 1000;
         }
      }
      return ultimoEstado;
   }

   public int getElemento() {
      return elemento;
   }

   public LatLng getPosition() {
      return position;
   }

   public Marker getMarker() {
      return marker;
   }

   @Override
   @NonNull
   public String toString() {
      if (toStringGenerated != null && !toStringGenerated.isEmpty()) {
         return toStringGenerated;
      }
      try {
         DBIncidenciaMotivo managerMotivos = new DBIncidenciaMotivo();
         IncidenciaMotivo mot = managerMotivos.getByID(motivo, empresa, modelo);
         managerMotivos.close();

         String nombreElemento = "";
         if (elemento > 0) {
            DBElemento dbElemento = new DBElemento();
            Elemento elem = dbElemento.getByIdExterno(elemento, empresa);
            if (elem != null)
               nombreElemento = elem.getNombre();
            dbElemento.close();
         }

         DBEstados managerEstados = new DBEstados();
         Estado estado = managerEstados.getByID(getUltimoEstado(), empresa);
         managerEstados.close();

         String nameState = "SIN ESTADO";
         if (estado != null)
            nameState = estado.getNombre();

         String date = Utils.secondsToDatetimeString(getFechaUltimoEstado(), "yyyy-MM-dd HH:mm:ss");
         toStringGenerated = getIncidenceDisplayText(date, mot, nombreElemento, nameState);

         return toStringGenerated;
      } catch (Throwable e) {
         Log.e("Incidencia", "Error al cargar la incidencia: " + e.getMessage());
         MyLoggerHandler.getInstance().error(e);
      }

      return "";
   }

   @NonNull
   private String getIncidenceDisplayText(String date, IncidenciaMotivo mot, String nombreElemento, String nameState) {
      // Mantis 0005878: Añadir en la versión de Indra tipo y subtipo a la ventana de detalles de incidencia
      String tipoIndra = "";
      String motive = "";

      if (Environment.isSoftIndra) {
         DBIncidenciaModelo modelosDB = new DBIncidenciaModelo();
         IncidenciaModelo modelo = modelosDB.getByID(
               this.getModelo(), MainActivity.getInstance().getEmpresa(), this.getTipo());
         modelosDB.close();

         String auxName = "* Desconocido *";

         if (modelo != null && modelo.getNombre() != null && !modelo.getNombre().isEmpty())
            auxName = modelo.getNombre();
         tipoIndra = String.format("Tipo: %s %n", auxName);

         motive = "Subtipo: " + (mot != null && !mot.getNombre().isEmpty() ? mot.getNombre() : "* Desconocido *");
      } else {
         motive = (mot != null && !mot.getNombre().isEmpty() ? mot.getNombre() : "* Motivo desconocido *");
      }

      String dateInfo = date.isEmpty() ? "" : "\n" + date;
      String element = nombreElemento.isEmpty() ? "" : "\n" + nombreElemento;
      String state = nameState.isEmpty() ? "\n(Nueva)" : "\n(" + nameState + ")";
      if (empresa == 582) {
         motive = String.format("%d - %s", idExterno, motive);
      }
      return tipoIndra + motive + dateInfo + element + state;//17151 a 17147
      // FIN - 28/02/2020 - DINIGO - Mantis #0004532
   }

   public int getUsuario() {
      return usuario;
   }

   public void setUsuario(int usuario) {
      this.usuario = usuario;
   }

   public int getIdExterno() {
      return idExterno;
   }

   public void setIdExterno(int idExterno) {
      this.idExterno = idExterno;
   }

   @Override
   public String getTipoElemento() {
      return "I";
   }

   @Override
   public int compareTo(Incidencia another) {
      return (int) (another.getFechaUltimoEstado() - this.getFechaUltimoEstado());
   }

   public long getFechaUltimoEstado() {
      if (fechaUltimoEstado == 0)
         setFecha();
      return fechaUltimoEstado;
   }

   public void setFechaUltimoEstado(long fechaUltimoEstado) {
      this.fechaUltimoEstado = fechaUltimoEstado;
   }

   public void setFecha() {
      IncidenciaEstado incidenciaEstado = getUltimoEstadoIncidencia();

      if (incidenciaEstado != null && this.id != 0) {
         fechaUltimoEstado = Utils.stringToDate(incidenciaEstado.getFecha(), "yyyy-MM-dd HH:mm:ss").getTime() / 1000;
         // Aprovechamos para guardar el ultimo estado
         ultimoEstado = incidenciaEstado.getIdEstado();
      }
   }

   public void setTipoPropietario(int tipoPropietario) {
      this.tipoPropietario = tipoPropietario;
   }

   public void setPropietario(int propietario) {
      this.propietario = propietario;
   }

   public void setNombrePropietario(String nombrePropietario) {
      this.nombrePropietario = nombrePropietario == null || nombrePropietario.isEmpty() ? "Sin asignar" : nombrePropietario;
   }

   public int getTipoPropietario() {
      return tipoPropietario;
   }

   public int getPropietario() {
      return propietario;
   }

   public String getNombrePropietario() {
      return nombrePropietario;
   }

   public IncidenciaEstado getUltimoEstadoIncidencia() {
      DBIncidenciaEstado dbIncidenciaEstado = new DBIncidenciaEstado();
      IncidenciaEstado ultimoIncidenciaEstado = dbIncidenciaEstado.getLastEstadoIncidencia(id, empresa, false);
      if (ultimoIncidenciaEstado == null)
         ultimoIncidenciaEstado = dbIncidenciaEstado.getLastEstadoIncidencia(idExterno, empresa, true);
      dbIncidenciaEstado.close();

      return ultimoIncidenciaEstado;
   }

   private long getCurrentTimeInSeconds() {
      return System.currentTimeMillis() / 1000;
   }

   public int getTotalRegistros() { return totalRegistros; }
   public void setTotalRegistros(int totalRegistros) { this.totalRegistros = totalRegistros; }
   public Integer getMovil() { return movil; }
   public void setMovil(Integer movil) { this.movil = movil; }
   public String getIdSincro() { return idSincro; }
   public void setIdSincro(String idSincro) { this.idSincro = idSincro; }
   public Integer getRuta() { return ruta; }
   public void setRuta(Integer ruta) { this.ruta = ruta; }
   public Integer getRutaH() { return rutaH; }
   public void setRutaH(Integer rutaH) { this.rutaH = rutaH; }
   public String getFechaBaja() { return fechaBaja; }
   public void setFechaBaja(String fechaBaja) { this.fechaBaja = fechaBaja; }
   public Integer getLatitudInt() { return latitudInt; }
   public void setLatitudInt(Integer latitudInt) { this.latitudInt = latitudInt; }
   public Integer getLongitudInt() { return longitudInt; }
   public void setLongitudInt(Integer longitudInt) { this.longitudInt = longitudInt; }
   public String getMotivoDesc() { return motivoDesc; }
   public void setMotivoDesc(String motivoDesc) { this.motivoDesc = motivoDesc; }
   public String getInfoGeo() { return infoGeo; }
   public void setInfoGeo(String infoGeo) { this.infoGeo = infoGeo; }
   public String getFecha() { return fecha; }
   public void setFecha(String fecha) { this.fecha = fecha; }
   public Integer getTipoElem() { return tipoElem; }
   public void setTipoElem(Integer tipoElem) { this.tipoElem = tipoElem; }
   public String getMatricula() { return matricula; }
   public void setMatricula(String matricula) { this.matricula = matricula; }
   public String getImagen() { return imagen; }
   public void setImagen(String imagen) { this.imagen = imagen; }
   public Integer getIdEquipo() { return idEquipo; }
   public void setIdEquipo(Integer idEquipo) { this.idEquipo = idEquipo; }
   public String getEmailCiudadano() { return emailCiudadano; }
   public void setEmailCiudadano(String emailCiudadano) { this.emailCiudadano = emailCiudadano; }
   public int getValidada() { return validada; }
   public void setValidada(int validada) { this.validada = validada; }
   public int getInformado() { return informado; }
   public void setInformado(int informado) { this.informado = informado; }
   public Integer getAcera() { return acera; }
   public void setAcera(Integer acera) { this.acera = acera; }
   public String getTextoCambioEstado() { return textoCambioEstado; }
   public void setTextoCambioEstado(String textoCambioEstado) { this.textoCambioEstado = textoCambioEstado; }
   public boolean isBorrado() { return borrado; }
   public void setBorrado(boolean borrado) { this.borrado = borrado; }
   public String getFechaModificacion() { return fechaModificacion; }
   public void setFechaModificacion(String fechaModificacion) { this.fechaModificacion = fechaModificacion; }
   public boolean isOrdenTrabajo() { return ordenTrabajo; }
   public void setOrdenTrabajo(boolean ordenTrabajo) { this.ordenTrabajo = ordenTrabajo; }
   public int getX() { return x; }
   public void setX(int x) { this.x = x; }
   public int getY() { return y; }
   public void setY(int y) { this.y = y; }
   public int getXMercator() { return xMercator; }
   public void setXMercator(int xMercator) { this.xMercator = xMercator; }
   public int getYMercator() { return yMercator; }
   public void setYMercator(int yMercator) { this.yMercator = yMercator; }
   public String getMunicipio() { return municipio; }
   public void setMunicipio(String municipio) { this.municipio = municipio; }

   public String getFormattedIncidentInfo() {
      StringBuilder info = new StringBuilder();
      
      try {
         DBEstados managerEstados = new DBEstados();
         Estado estado = managerEstados.getByID(this.getUltimoEstado(), this.getEmpresa());
         managerEstados.close();

         boolean hasEstadoFecha = false;
         if (estado != null && estado.getNombre() != null && !estado.getNombre().isEmpty() && !estado.getNombre().equalsIgnoreCase("null")) {
            info.append("Estado: ").append(estado.getNombre()).append("\n");
            hasEstadoFecha = true;
         }

         if (this.getFechaUltimoEstado() > 0) {
            String date = Utils.secondsToDatetimeString(this.getFechaUltimoEstado(), "dd/MM/yyyy HH:mm:ss");
            info.append("Fecha: ").append(date).append("\n");
            hasEstadoFecha = true;
         }

         if (hasEstadoFecha) {
            info.append("\n");
         }

         if (this.getMatricula() != null && !this.getMatricula().isEmpty() && !this.getMatricula().equalsIgnoreCase("null")) {
            info.append("Matrícula: ").append(getMatricula()).append("\n");
            info.append("\n");
         }

         DBIncidenciaTipo tiposDB = new DBIncidenciaTipo();
         IncidenciaTipo tipo = tiposDB.getByID(this.getTipo(), this.getEmpresa());
         tiposDB.close();

         DBIncidenciaModelo modelosDB = new DBIncidenciaModelo();
         IncidenciaModelo modelo = modelosDB.getByID(this.getModelo(), this.getEmpresa(), this.getTipo());
         modelosDB.close();

         DBIncidenciaMotivo motivosDB = new DBIncidenciaMotivo();
         IncidenciaMotivo motivo = motivosDB.getByID(this.getMotivo(), this.getEmpresa(), this.getModelo());
         motivosDB.close();

         boolean hasTipoModeloMotivoObservaciones = false;
         if (tipo != null && tipo.getNombre() != null && !tipo.getNombre().isEmpty() && !tipo.getNombre().equalsIgnoreCase("null")) {
            info.append("Tipo: ").append(tipo.getNombre()).append("\n");
            hasTipoModeloMotivoObservaciones = true;
         }

         if (modelo != null && modelo.getNombre() != null && !modelo.getNombre().isEmpty() && !modelo.getNombre().equalsIgnoreCase("null")) {
            info.append("Modelo: ").append(modelo.getNombre()).append("\n");
            hasTipoModeloMotivoObservaciones = true;
         }
         
         if (motivo != null && motivo.getNombre() != null && !motivo.getNombre().isEmpty() && !motivo.getNombre().equalsIgnoreCase("null")) {
            info.append("Motivo: ").append(motivo.getNombre()).append("\n");
            hasTipoModeloMotivoObservaciones = true;
         }

         if (this.getObserv() != null && !this.getObserv().isEmpty() && !this.getObserv().equalsIgnoreCase("null")) {
            info.append("Observaciones: ").append(this.getObserv()).append("\n");
            hasTipoModeloMotivoObservaciones = true;
         }

         if (hasTipoModeloMotivoObservaciones) {
            info.append("\n");
         }

         boolean hasElementoInfo = false;
         if (this.getElemento() > 0) {
            DBElemento dbElemento = new DBElemento();
            Elemento elem = dbElemento.getByIdExterno(this.getElemento(), this.getEmpresa());
            dbElemento.close();

            if (elem != null) {
               if (elem.getNombre() != null && !elem.getNombre().isEmpty() && !elem.getNombre().equalsIgnoreCase("null")) {
                  info.append("Elemento: ").append(elem.getNombre()).append("\n");
                  hasElementoInfo = true;
               }

               if (elem.getMatricula() != null && !elem.getMatricula().isEmpty() && !elem.getMatricula().equalsIgnoreCase("null")) {
                  info.append("Matrícula: ").append(elem.getMatricula()).append("\n");
                  hasElementoInfo = true;
               }

               if (elem.getModelo() > 0) {
                  DBElementoModelo dbElementoModelo = new DBElementoModelo();
                  ElementoModelo elementoModelo = dbElementoModelo.getByID(elem.getModelo(), this.getEmpresa());
                  dbElementoModelo.close();

                  if (elementoModelo != null && elementoModelo.getNombre() != null && !elementoModelo.getNombre().isEmpty() && !elementoModelo.getNombre().equalsIgnoreCase("null")) {
                     info.append("Modelo: ").append(elementoModelo.getNombre()).append("\n");
                     hasElementoInfo = true;
                  }
               }
            }
         }

         if (hasElementoInfo) {
            info.append("\n");
         }

         boolean hasDireccionMunicipio = false;
         if (this.getInfoGeo() != null && !this.getInfoGeo().isEmpty() && !this.getInfoGeo().equalsIgnoreCase("null")) {
            info.append("Dirección: ").append(this.getInfoGeo()).append("\n");
            hasDireccionMunicipio = true;
         }
         if (this.getMunicipio() != null && !this.getMunicipio().isEmpty() && !this.getMunicipio().equalsIgnoreCase("null")) {
            info.append("Municipio: ").append(this.getMunicipio()).append("\n");
            hasDireccionMunicipio = true;
         }

         if (hasDireccionMunicipio) {
            info.append("\n");
         }

         MainActivity mainActivity = MainActivity.getInstance();
         if (mainActivity == null || mainActivity.isValidMenu(MainActivity.MENU_ASIGNAR_INCIDENCIAS, "")) {
            if (this.getNombrePropietario() != null && !this.getNombrePropietario().isEmpty()) {
               info.append("Propietario: ").append(this.getNombrePropietario()).append("\n");
            }
         }
         
      } catch (Exception e) {
         Log.e("Incidencia", "Error al formatear información de incidencia: " + e.getMessage());
         MyLoggerHandler.getInstance().error(e);
         return "Error al cargar información de la incidencia";
      }

      String result = info.toString();
      if (result.endsWith("\n")) {
         result = result.substring(0, result.length() - 1);
      }
      
      return result;
   }
}