package com.movisat.database;

import java.util.Date;

public class InfoSustituir {
	private Elemento mElementoRetirado;
	private Elemento mElementoDepositado;
	private Date mFecha;
	private int mMotivo;

	public int getMotivo() {
		return mMotivo;
	}

	public void setMotivo(int mMotivo) {
		this.mMotivo = mMotivo;
	}

	public Elemento getElementoRetirado() {
		return mElementoRetirado;
	}

	public void setElementoRetirado(Elemento elementoRetirado) {
		this.mElementoRetirado = elementoRetirado;
	}

	public Elemento getElementoDepositado() {
		return mElementoDepositado;
	}

	public void setElementoDepositado(Elemento elementoDepositado) {
		this.mElementoDepositado = elementoDepositado;
	}

	public Date getFecha() {
		return mFecha;
	}

	public void setFecha(Date fecha) {
		mFecha = fecha;
	}

	public InfoSustituir(Elemento elementoRetirado, Elemento elementoDepositado, Date fecha) {
		setElementoRetirado(elementoRetirado);
		setElementoDepositado(elementoDepositado);
		setFecha(fecha);
		setMotivo(0);
	}

	public InfoSustituir(Elemento elementoRetirado, Elemento elementoDepositado, Date fecha, int motivo) {
		setElementoRetirado(elementoRetirado);
		setElementoDepositado(elementoDepositado);
		setFecha(fecha);
		setMotivo(motivo);
	}

}
