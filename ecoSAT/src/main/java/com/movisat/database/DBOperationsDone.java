package com.movisat.database;

import android.content.ContentValues;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.movisat.ecosat.MainActivity;
import com.movisat.ecosat.MyLoggerHandler;
import com.movisat.utilities.Database;

import java.util.ArrayList;

/**
 * Created by faroca on 28/06/2017.
 */

public class DBOperationsDone {

    private final String TABLE_NAME = "operations_done";
    private SQLiteDatabase db = null;

    private String[] campos = (new String[]{"id", "code_mobile","code_operation", "date",
            "description", "value", "lat", "lng", "idInternoElemento"});
    private final int COL_CODE_MOBILE = 1;
    private final int COL_CODE_OPERATION = 2;
    private final int COL_DATE = 3;
    private final int COL_DESCRIPTION = 4;
    private final int COL_VALUE = 5;
    private final int COL_LAT = 6;
    private final int COL_LNG = 7;
    private final int COL_ID_INTERNO_ELEMENTO = 8;

    public DBOperationsDone() {

        try {

            db = Database.getConnection(MainActivity.getInstance()
                            .getDatabasePath("ecosat.sqlite").getPath(),
                    SQLiteDatabase.OPEN_READWRITE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void close() {

        try {

            if (db != null)
                db.close();

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void insert (OperationsEnum type, int codeMobile, int idInternoElemento, long date, double lat, double lng, String value) {
        OperationsDone operationsDone = new OperationsDone(codeMobile, type.getKey(),
                idInternoElemento, type.getDescription(), value, date, lat, lng);
        insert(operationsDone);
    }

    public long insert(OperationsDone reg) {
        long res = 0;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_CODE_MOBILE], reg.getCodeMobile());
            values.put(campos[COL_CODE_OPERATION], reg.getCodeOperation());
            values.put(campos[COL_ID_INTERNO_ELEMENTO], reg.getIdInternoElemento());
            values.put(campos[COL_DATE], reg.getDate());
            values.put(campos[COL_DESCRIPTION], reg.getDescription());
            values.put(campos[COL_VALUE], reg.getValue());
            values.put(campos[COL_LAT], reg.getLat());
            values.put(campos[COL_LNG], reg.getLng());

            res = db.insertWithOnConflict(TABLE_NAME, null, values, SQLiteDatabase.CONFLICT_IGNORE);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public boolean update(OperationsDone reg) {
        boolean res = false;

        try {

            ContentValues values = new ContentValues();

            values.put(campos[COL_CODE_MOBILE], reg.getCodeMobile());
            values.put(campos[COL_CODE_OPERATION], reg.getCodeOperation());
            values.put(campos[COL_ID_INTERNO_ELEMENTO], reg.getIdInternoElemento());
            values.put(campos[COL_DATE], reg.getDate());
            values.put(campos[COL_DESCRIPTION], reg.getDescription());
            values.put(campos[COL_VALUE], reg.getValue());
            values.put(campos[COL_LAT], reg.getLat());
            values.put(campos[COL_LNG], reg.getLng());

            res = db.update(TABLE_NAME, values, campos[COL_ID_INTERNO_ELEMENTO] + "="
                    + reg.getIdInternoElemento() , null) > 0;

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public ArrayList<Integer> getElementIdsWithCleanFromDateAndLatLon(long fromDate, double lat1, double lon1, double lat2, double lon2) {
        ArrayList<Integer> elementIds = new ArrayList<>();
        Cursor cur;
        String clausula = "";

        try {
            int codigoDispositivo = MainActivity.getInstance().getCodigoDispositivo();
            boolean admin = MainActivity.getInstance().getUsuAdmin();

            if (!admin)
                clausula = campos[COL_CODE_MOBILE] + "=" + codigoDispositivo + " AND ";

            if ((cur = db.query(TABLE_NAME, new String[]{campos[COL_ID_INTERNO_ELEMENTO]},
                  clausula + campos[COL_DATE] + ">=" + fromDate + " AND "
                        + campos[COL_CODE_OPERATION] + "=" + OperationsEnum.CLEAN.getKey() + " AND "
                        + campos[COL_LAT] + ">=" + lat1 + " AND " + campos[COL_LAT] + "<=" + lat2 + " AND "
                        + campos[COL_LNG] + ">=" + lon1 + " AND " + campos[COL_LNG] + " <=" + lon2,
                  null, null, null, campos[COL_DATE] + " DESC")) != null) {

                if (cur.moveToFirst()) {
                    do {
                        elementIds.add(cur.getInt(0));
                    } while (cur.moveToNext());
                }
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return elementIds;
    }

    public ArrayList<Integer> getElementIdsWithCollectOrLevelFromDateAndLatLon(long fromDate, double lat1, double lon1, double lat2, double lon2) {
        ArrayList<Integer> elementIds = new ArrayList<>();
        Cursor cur;
        String clausula = "";

        try {
            int codigoDispositivo = MainActivity.getInstance().getCodigoDispositivo();
            boolean admin = MainActivity.getInstance().getUsuAdmin();

            if (!admin)
                clausula = campos[COL_CODE_MOBILE] + "=" + codigoDispositivo + " AND ";

            if ((cur = db.query(TABLE_NAME, new String[]{campos[COL_ID_INTERNO_ELEMENTO]},
                  clausula + campos[COL_DATE] + ">=" + fromDate + " AND "
                        + campos[COL_CODE_OPERATION] + " in (" + OperationsEnum.COLLECT.getKey() + "," + OperationsEnum.LEVEL.getKey() + ") AND "
                        + campos[COL_LAT] + ">=" + lat1 + " AND " + campos[COL_LAT] + "<=" + lat2 + " AND "
                        + campos[COL_LNG] + ">=" + lon1 + " AND " + campos[COL_LNG] + " <=" + lon2,
                  null, null, null, campos[COL_DATE] + " DESC")) != null) {

                if (cur.moveToFirst()) {
                    do {
                        elementIds.add(cur.getInt(0));
                    } while (cur.moveToNext());
                }
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return elementIds;
    }

    public ArrayList<OperationsDone> getFromDateAndLatLon(long fromDate, double lat1, double lon1, double lat2, double lon2) {
        ArrayList<OperationsDone> operations = new ArrayList<>();
        Cursor cur;
        String clausula="";

        try {

            int codigoDispositivo = MainActivity.getInstance().getCodigoDispositivo();
            boolean admin = MainActivity.getInstance().getUsuAdmin();

            if (!admin)
                clausula = campos[COL_CODE_MOBILE]+"="+codigoDispositivo+" AND ";


            if ((cur = db.query(TABLE_NAME, campos, clausula+ campos[COL_DATE] + ">="
                    + fromDate+" AND " + campos[COL_LAT] + ">=" + lat1
                            + " AND " + campos[COL_LAT] + "<=" + lat2 + " AND "
                            + campos[COL_LNG] + ">=" + lon1 + " AND "
                            + campos[COL_LNG] + " <=" + lon2
                    , null, null, null, campos[COL_DATE] + " DESC")) != null) {

                if (cur.moveToFirst()) {
                    do {

                        operations.add(new OperationsDone(
                                cur.getInt(COL_CODE_MOBILE),
                                cur.getInt(COL_CODE_OPERATION),
                                cur.getInt(COL_ID_INTERNO_ELEMENTO),
                                cur.getString(COL_DESCRIPTION),
                                cur.getString(COL_VALUE),
                                cur.getLong(COL_DATE),
                                cur.getDouble(COL_LAT),
                                cur.getDouble(COL_LNG)));

                    } while (cur.moveToNext());

                }

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return operations;
    }

    public ArrayList<OperationsDone> getFromDateLatLonAndOperation(long fromDate, double lat1, double lon1, double lat2, double lon2, int codeOperation) {
        ArrayList<OperationsDone> operations = new ArrayList<>();
        Cursor cur;
        String clausula = "";

        try {
            int codigoDispositivo = MainActivity.getInstance().getCodigoDispositivo();
            boolean admin = MainActivity.getInstance().getUsuAdmin();

            if (!admin)
                clausula = campos[COL_CODE_MOBILE] + "=" + codigoDispositivo + " AND ";

            if ((cur = db.query(TABLE_NAME, campos, clausula + campos[COL_DATE] + ">="
                  + fromDate + " AND " + campos[COL_LAT] + ">=" + lat1
                  + " AND " + campos[COL_LAT] + "<=" + lat2 + " AND "
                  + campos[COL_LNG] + ">=" + lon1 + " AND "
                  + campos[COL_LNG] + " <=" + lon2 + " AND "
                  + campos[COL_CODE_OPERATION] + "=" + codeOperation, null, null, null, campos[COL_DATE] + " DESC")) != null) {

                if (cur.moveToFirst()) {
                    do {
                        operations.add(new OperationsDone(
                              cur.getInt(COL_CODE_MOBILE),
                              cur.getInt(COL_CODE_OPERATION),
                              cur.getInt(COL_ID_INTERNO_ELEMENTO),
                              cur.getString(COL_DESCRIPTION),
                              cur.getString(COL_VALUE),
                              cur.getLong(COL_DATE),
                              cur.getDouble(COL_LAT),
                              cur.getDouble(COL_LNG)));

                    } while (cur.moveToNext());
                }

                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return operations;
    }

    public OperationsDone getLastCollectLevelByElem(int idElem){
        OperationsDone operation = null;
        Cursor cur;

        try{
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_INTERNO_ELEMENTO] + "="
                    + idElem + " AND " + campos[COL_CODE_OPERATION] + ">=" + OperationsEnum.LEVEL.getKey(),
                    null, null, null, campos[COL_DATE] + " DESC")) != null) {
                if(cur.moveToFirst()){
                    operation = new OperationsDone(
                            cur.getInt(COL_CODE_MOBILE),
                            cur.getInt(COL_CODE_OPERATION),
                            cur.getInt(COL_ID_INTERNO_ELEMENTO),
                            cur.getString(COL_DESCRIPTION),
                            cur.getString(COL_VALUE),
                            cur.getLong(COL_DATE),
                            cur.getDouble(COL_LAT),
                            cur.getDouble(COL_LNG));
                }
            }
        }catch(Exception e){
            MyLoggerHandler.getInstance().error(e);
        }

        return operation;
    }

    public OperationsDone getLastCleanByElem(int idElem){
        OperationsDone operation = null;
        Cursor cur;

        try{
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_ID_INTERNO_ELEMENTO] + "="
                            + idElem + " AND " + campos[COL_CODE_OPERATION] + "=" + OperationsEnum.CLEAN.getKey(),
                    null, null, null, campos[COL_DATE] + " DESC")) != null) {
                if(cur.moveToFirst()){
                    operation = new OperationsDone(
                            cur.getInt(COL_CODE_MOBILE),
                            cur.getInt(COL_CODE_OPERATION),
                            cur.getInt(COL_ID_INTERNO_ELEMENTO),
                            cur.getString(COL_DESCRIPTION),
                            cur.getString(COL_VALUE),
                            cur.getLong(COL_DATE),
                            cur.getDouble(COL_LAT),
                            cur.getDouble(COL_LNG));
                }
            }
        }catch(Exception e){
            MyLoggerHandler.getInstance().error(e);
        }

        return operation;
    }

    public ArrayList<OperationsDone> getFromDate(long fromDate) {
        ArrayList<OperationsDone> operations = new ArrayList<>();
        Cursor cur;

        try {
            if ((cur = db.query(TABLE_NAME, campos, campos[COL_DATE] + ">="
                    + fromDate, null, null, null, campos[COL_DATE] + " DESC")) != null) {
                if (cur.moveToFirst()) {
                    do {
                        int codMobile= cur.getInt(COL_CODE_MOBILE);
                        if (MainActivity.getInstance().getUsuAdmin() || codMobile == MainActivity.getInstance().getCodigoDispositivo())
                            operations.add(new OperationsDone(
                                    codMobile,
                                    cur.getInt(COL_CODE_OPERATION),
                                    cur.getInt(COL_ID_INTERNO_ELEMENTO),
                                    cur.getString(COL_DESCRIPTION),
                                    cur.getString(COL_VALUE),
                                    cur.getLong(COL_DATE),
                                    cur.getDouble(COL_LAT),
                                    cur.getDouble(COL_LNG)));
                    } while (cur.moveToNext());
                }
                cur.close();
            }
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
        return operations;
    }

    public int getCount() {
        int res = 0;
        Cursor cur;

        try {

            if ((cur = db.query(TABLE_NAME, (new String[]{"count(*)"}),
                    null, null, null, null, null)) != null) {

                if (cur.moveToFirst())
                    res = cur.getInt(0);

                cur.close();
            }

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

        return res;
    }

    public void deleteAll() {

        try {

            db.delete(TABLE_NAME, null, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void deleteByType(int type) {

        try {

            db.delete(TABLE_NAME, campos[COL_CODE_OPERATION] + "=" + type, null);

        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }

    }

    public void deleteByElementAndDate(int type, int elementId, long time) {
        try {
            db.delete(TABLE_NAME, campos[COL_CODE_OPERATION] + "=" + type + " AND "
                  + campos[COL_ID_INTERNO_ELEMENTO] + "=" + elementId + " AND "
                  + campos[COL_DATE] + "=" + time, null);
        } catch (Throwable e) {
            MyLoggerHandler.getInstance().error(e);
        }
    }
}
