package com.movisat.database;

import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public final class OperationsEnum {
    public static final OperationsEnum CLEAN = new OperationsEnum(0, "Clean", "Limpieza de elemento");
    public static final OperationsEnum LEVEL = new OperationsEnum(1, "Level", "Nivel de llenado");
    public static final OperationsEnum COLLECT = new OperationsEnum(2, "Collect", "Recogida de elemento");

    private final int key;
    private final String name;
    private final String description;

    private OperationsEnum(int key, String name, String description) {
        this.key = key;
        this.name = name;
        this.description = description;
    }

    public String toString() {
        return this.name;
    }

    public int getKey() {
        return this.key;
    }

    public static OperationsEnum valueOf(String name) {
        Iterator iter = VALUES.iterator();
        while (iter.hasNext()) {
            OperationsEnum operation = (OperationsEnum) iter.next();
            if (name.equals(operation.toString())) {
                return operation;
            }
        }
        //this method is unusual in that IllegalArgumentException is
        //possibly thrown not at its beginning, but at its end.
        throw new IllegalArgumentException(
                "Cannot parse into an element of Suit : '" + name + "'"
        );
    }

    public static OperationsEnum valueOf(int key) {
        Iterator iter = VALUES.iterator();
        while (iter.hasNext()) {
            OperationsEnum operation = (OperationsEnum) iter.next();
            if (key == operation.key) {
                return operation;
            }
        }
        //this method is unusual in that IllegalArgumentException is
        //possibly thrown not at its beginning, but at its end.
        throw new IllegalArgumentException(
                "Cannot parse into an element of Operation : '" + key + "'"
        );
    }


    /**
     * These two lines are all that's necessary to export a List of VALUES.
     */
    private static final OperationsEnum[] fValues = {CLEAN, LEVEL, COLLECT};
    //VALUES needs to be located here, otherwise illegal forward reference
    public static final List VALUES = Collections.unmodifiableList(Arrays.asList(fValues));

    public String getDescription() {
        return description;
    }
}
