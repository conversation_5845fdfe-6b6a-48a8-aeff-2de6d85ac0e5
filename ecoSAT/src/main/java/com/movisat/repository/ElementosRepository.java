package com.movisat.repository;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;

import com.movisat.apis.ElementosApi;
import com.movisat.log.Logg;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.utilities.Config;
import com.movisat.utils.ICallBack;
import com.movisat.utils_android.UtilssAndroid;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ElementosRepository {

    public void getImagenesPuntoUbicacion(final int empresa,
                                          final int codigo,
                                          final String desde,
                                          final String hasta,
                                          final ICallBack<List<Bitmap>> callback) {
        final Handler mainHandler = new Handler(Looper.getMainLooper());

        Runnable deliverEmpty = () -> mainHandler.post(() -> callback.execute(Collections.emptyList()));

        new Thread(() -> {
            try {
                String baseUrl = Config.getInstance().getValue("webSvc", "");
                if (baseUrl == null || baseUrl.length() == 0) {
                    deliverEmpty.run();
                    return;
                }

                ClientWebSvc wsc = new ClientWebSvc();
                if (wsc.token == null || wsc.token.equals("")) {
                    try {
                        wsc.getToken();
                    } catch (Throwable e) {
                        Logg.error("ElementosRepository", "Error obteniendo token: " + e.getMessage());
                    }
                }

                final String token = wsc.token;

                new ElementosApi().getImagenesPuntoUbicacion(baseUrl, codigo, empresa, token, desde, hasta, jsonArray -> {
                    new Thread(() -> {
                        List<Bitmap> result = new ArrayList<>();
                        try {
                            if (jsonArray != null) {
                                for (int i = jsonArray.length() - 1; i >= 0; i--) {
                                    try {
                                        String base64 = jsonArray.getJSONObject(i).optString("Imagen", "");
                                        if (base64 == null || base64.length() == 0) continue;
                                        Bitmap bmp = UtilssAndroid.base64ToBitmap(base64);
                                        if (bmp != null) result.add(bmp);
                                    } catch (Throwable t) {
                                        Logg.error("ElementosRepository", "Error decodificando imagen: " + t.getMessage());
                                    }
                                }
                            }
                        } catch (Throwable e) {
                            Logg.error("ElementosRepository", "Error procesando imágenes de elemento: " + e.getMessage());
                        }

                        mainHandler.post(() -> callback.execute(result));
                    }).start();
                });
            } catch (Throwable e) {
                Logg.error("ElementosRepository", "Error solicitando imágenes de elemento: " + e.getMessage());
                deliverEmpty.run();
            }
        }).start();
    }
}
