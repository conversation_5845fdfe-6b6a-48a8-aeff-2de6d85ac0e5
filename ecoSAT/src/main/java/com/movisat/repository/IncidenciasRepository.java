package com.movisat.repository;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;

import com.movisat.apis.IncidenciasApi;
import com.movisat.log.Logg;
import com.movisat.synchronize.ClientWebSvc;
import com.movisat.utils.ICallBack;
import com.movisat.utilities.Config;
import com.movisat.utils_android.UtilssAndroid;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class IncidenciasRepository {

    public void getHistoricoImagenes(final int empresa,
                                     final Integer idIncidencia,
                                     final Integer idIncidenciaExterno,
                                     final ICallBack<List<Bitmap>> callback) {
        final Handler mainHandler = new Handler(Looper.getMainLooper());

        Runnable deliverEmpty = () -> mainHandler.post(() -> callback.execute(Collections.emptyList()));

        new Thread(() -> {
            try {
                String baseUrl = Config.getInstance().getValue("webSvc", "");
                if (baseUrl == null || baseUrl.length() == 0) {
                    deliverEmpty.run();
                    return;
                }

                ClientWebSvc wsc = new ClientWebSvc();
                if (wsc.token == null || wsc.token.equals("")) {
                    try {
                        wsc.getToken();
                    } catch (Throwable e) {
                        Logg.error("IncidenciasRepository", "Error obteniendo token: " + e.getMessage());
                    }
                }

                final String token = wsc.token;
                IncidenciasApi.Request req = new IncidenciasApi.Request(
                        empresa,
                        idIncidencia,
                        (idIncidenciaExterno != null && idIncidenciaExterno > 0) ? idIncidenciaExterno : null,
                        null,
                        null,
                        null
                );

                new IncidenciasApi().getHistoricoImagenes(baseUrl, req, token, jsonArray -> {
                    new Thread(() -> {
                        List<Bitmap> result = new ArrayList<>();
                        try {
                            if (jsonArray != null) {
                                for (int i = jsonArray.length() - 1; i >= 0; i--) {
                                    try {
                                        String base64 = jsonArray.getJSONObject(i).optString("Imagen", "");
                                        if (base64 == null || base64.length() == 0) continue;
                                        Bitmap bmp = UtilssAndroid.base64ToBitmap(base64);
                                        if (bmp != null) result.add(bmp);
                                    } catch (Throwable t) {
                                        Logg.error("IncidenciasRepository", "Error decodificando imagen: " + t.getMessage());
                                    }
                                }
                            }
                        } catch (Throwable e) {
                            Logg.error("IncidenciasRepository", "Error procesando histórico de imágenes: " + e.getMessage());
                        }

                        mainHandler.post(() -> callback.execute(result));
                    }).start();
                });
            } catch (Throwable e) {
                Logg.error("IncidenciasRepository", "Error solicitando histórico de imágenes: " + e.getMessage());
                deliverEmpty.run();
            }
        }).start();
    }
}
