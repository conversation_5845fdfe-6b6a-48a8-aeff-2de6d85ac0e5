<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_checked="true"
        android:drawable="@color/theme_principal" />
    <item android:state_checked="false"
        android:drawable="@color/gris" />

    <item android:state_checked="true"
        android:state_window_focused="false"
        android:drawable="@color/theme_principal" />
    <item android:state_checked="false"
        android:state_window_focused="false"
        android:drawable="@color/gris" />

    <item android:state_checked="true"
        android:state_pressed="true"
        android:drawable="@color/theme_principal_hover" />
    <item android:state_checked="false"
        android:state_pressed="true"
        android:drawable="@color/theme_principal_hover" />

    <item android:state_checked="true"
        android:state_focused="true"
        android:drawable="@color/theme_principal" />
    <item android:state_checked="false"
        android:state_focused="true"
        android:drawable="@color/gris" />

</selector>