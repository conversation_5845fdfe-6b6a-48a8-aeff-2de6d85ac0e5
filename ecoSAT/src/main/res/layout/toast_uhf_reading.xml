<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_reading_tag_toast"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="@dimen/toast_uhf_reading_padding"
    android:background="@color/toast_uhf_reading_background">

    <ImageView
        android:id="@+id/img_reading_tag_toast"
        android:layout_width="@dimen/toast_uhf_reading_image_size"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/toast_uhf_reading_image_text_spacing"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/txtToast"
        android:adjustViewBounds="true"
        tools:srcCompat="@drawable/reading_tag"
        app:tint="@color/toast_uhf_reading_textcolor" />

    <TextView
        android:id="@+id/txtToast"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/img_reading_tag_toast"
        app:layout_constraintEnd_toEndOf="parent"
        android:gravity="center"
        android:textColor="@color/toast_uhf_reading_textcolor"
        android:textSize="@dimen/toast_uhf_reading_textsize"
        android:textStyle="bold"
        android:text="@string/toast_tag_reading_message"
        tools:text="@string/toast_tag_reading_message"/>

</androidx.constraintlayout.widget.ConstraintLayout>