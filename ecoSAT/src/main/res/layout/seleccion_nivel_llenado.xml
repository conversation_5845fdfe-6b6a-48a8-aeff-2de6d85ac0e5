<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/seleccion_nivel_llenado"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/textViewTitulo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/activity_horizontal_margin"
        android:textAppearance="?android:attr/textAppearanceLarge"
        app:layout_constraintTop_toTopOf="parent"/>

    <Spinner
        android:id="@+id/cbFlota"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        app:layout_constraintTop_toBottomOf="@id/textViewTitulo" />

    <EditText
        android:id="@+id/editDesde"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:editable="false"
        app:layout_constraintTop_toBottomOf="@id/cbFlota"/>

    <ListView
        android:id="@+id/lvniveles"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_below="@+id/textViewTitulo"
        app:layout_constraintTop_toBottomOf="@id/editDesde"
        app:layout_constraintBottom_toTopOf="@id/layoutAceptarCancelar"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutAceptarCancelar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/lvniveles"
        app:layout_constraintBottom_toBottomOf="parent">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnAceptar"/>

        <Button
            android:id="@+id/btnAceptar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco"
            app:layout_constraintStart_toEndOf="@id/btnVolver"
            app:layout_constraintEnd_toEndOf="parent"
            android:enabled="false"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>