<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/seleccion_usuario_incidencia"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/textViewTitulo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/activity_horizontal_margin"
        android:textAppearance="?android:attr/textAppearanceLarge"
        app:layout_constraintTop_toTopOf="parent" />

    <ListView
        android:id="@+id/lvUsuarios"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/layoutAceptarCancelar"
        app:layout_constraintTop_toBottomOf="@id/textViewTitulo" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutAceptarCancelar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/lvniveles">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco"
            app:layout_constraintEnd_toStartOf="@id/btnAceptar"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/btnAceptar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:enabled="false"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnVolver" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>