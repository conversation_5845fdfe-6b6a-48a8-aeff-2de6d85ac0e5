<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/LinearLayout1"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_key"
    android:orientation="vertical"
    tools:context=".LoginActivity">

    <ScrollView
        android:id="@+id/scrollView1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/activity_vertical_margin"
        android:layout_marginLeft="@dimen/activity_horizontal_margin"
        android:layout_marginRight="@dimen/activity_horizontal_margin"
        android:layout_marginTop="@dimen/activity_vertical_margin"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/blanco"
            android:padding="@dimen/activity_horizontal_margin"
            android:orientation="vertical">

            <EditText
                android:id="@+id/edLogin"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:hint="@string/login"
                android:imeOptions="flagNoExtractUi"
                android:inputType="textCapCharacters|textNoSuggestions"
                android:textColor="@color/black_color"
                android:textColorHint="@color/black_color"
                android:textColorLink="@android:color/holo_green_dark"
                android:editable="true"
                android:autoText="true" />
            
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="50dp">

                <EditText
                    android:id="@+id/edPassw"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:hint="@string/passw"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="textPassword|textCapCharacters|textNoSuggestions"
                    android:textColor="@color/black_color"
                    android:textColorHint="@color/black_color"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:editable="true" />

                <ImageButton
                    android:id="@+id/btnShowPassword"
                    android:layout_width="32dp"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    android:background="#00000000"
                    android:paddingRight="5dp"
                    android:scaleType="fitCenter"
                    app:srcCompat="@drawable/btn_password" />
                
            </RelativeLayout>

            <Spinner
                android:id="@+id/cbEmpresa"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:visibility="gone"
                tools:visibility="visible"/>

            <CheckBox
                android:id="@+id/chbRecordar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="4dp"
                android:text="@string/recordar"
                android:textColor="@color/black_color"
                android:textColorHint="@color/black_color"
                android:textColorLink="@android:color/holo_green_dark" />

            <Button
                android:id="@+id/btEntrar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_key"
                android:minWidth="150dp"
                android:text="@string/entrar"
                android:textColor="@android:color/primary_text_dark"
                android:textStyle="bold" />
        </LinearLayout>

    </ScrollView>

    <fr.castorflex.android.smoothprogressbar.SmoothProgressBar
        android:id="@+id/progressBarSync"
        android:layout_width="match_parent"
        android:layout_height="@dimen/sync_progressbar_height"
        android:indeterminate="true"
        app:spb_color="@color/sync_progressbar_color"
        app:spb_stroke_width="@dimen/sync_progressbar_stroke_width"
        app:spb_stroke_separator_length="@dimen/sync_progressbar_stroke_separator_length"
        app:spb_interpolator="spb_interpolator_acceleratedecelerate"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>