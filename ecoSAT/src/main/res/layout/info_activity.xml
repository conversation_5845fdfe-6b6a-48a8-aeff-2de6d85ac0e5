<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/LinearLayoutInfo"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/theme_principal"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    tools:context=".InfoActivity">

    <com.gc.materialdesign.views.ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/blanco"
            android:orientation="vertical"
            android:paddingLeft="16sp"
            android:paddingTop="16sp"
            android:paddingRight="16sp"
            android:paddingBottom="16sp">

            <TextView
                android:id="@+id/textView000"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/theme_principal"
                android:gravity="center_horizontal"
                android:padding="5sp"
                android:text="@string/title_info"
                android:textColor="@color/blanco"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/TextView01"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tipoSoftware"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/tipoSoftware"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Large Text"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/TextView02"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/imei"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/imei"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Large Text"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textView000"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/url"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/url"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Large Text"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textView2"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:text="@string/fecha_sincronizacion"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/ultsincro"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Large Text"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textViewSat"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:text="@string/num_satelites"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/NumSatelites"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/cinco_pixeles"
                android:text="Large Text"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textViewVersion"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:text="@string/num_version"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/numVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/cinco_pixeles"
                android:text="Large Text"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textViewDescripcion"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:text="@string/descripcion"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/tvDescripcion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/cinco_pixeles"
                android:text="Large Text"
                android:textAppearance="?android:attr/textAppearanceSmall" />


            <TextView
                android:id="@+id/textViewNumElem"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:text="@string/elementos_sincronizados"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/tvNumElem"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/cinco_pixeles"
                android:textAppearance="?android:attr/textAppearanceSmall" />


            <TextView
                android:id="@+id/textElementosAEnviar"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:text="@string/elementos_a_enviar"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/tvNumMensajes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/cinco_pixeles"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textPulsera"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:text="Pulsera vinculada"
                android:visibility="gone"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:id="@+id/tvMacPulsera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/cinco_pixeles"
                android:visibility="gone"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textViewLectoresDisponibles"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/theme_principal"
                android:gravity="center_horizontal"
                android:padding="5sp"
                android:text="@string/lectores_disponible"
                android:textColor="@color/blanco"
                android:textStyle="bold" />

            <CheckedTextView
                android:id="@+id/checkedTextLectorNFC"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/spacing_2"
                android:paddingBottom="@dimen/spacing_2"
                android:text="@string/nfc"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <CheckedTextView
                android:id="@+id/checkedTextLectorUHFC71"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/spacing_2"
                android:paddingBottom="@dimen/spacing_2"
                android:text="@string/uhf_v1"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <CheckedTextView
                android:id="@+id/checkedTextLectorUHFU9000"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:layout_gravity="center_vertical"
                android:paddingTop="@dimen/spacing_2"
                android:paddingBottom="@dimen/spacing_2"
                android:text="@string/uhf_v2"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <CheckedTextView
                android:id="@+id/checkedTextLectorLFChainway"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/spacing_2"
                android:paddingBottom="@dimen/spacing_2"
                android:text="@string/lf"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/volver"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="1sp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_key"
                    android:text="@string/volver"
                    android:textColor="@android:color/primary_text_dark"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textStyle="bold" />

                <Button
                    android:id="@+id/recargar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="1sp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_key"
                    android:text="@string/recargar"
                    android:textColor="@android:color/primary_text_dark"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textStyle="bold" />
            </LinearLayout>

        </LinearLayout>
    </com.gc.materialdesign.views.ScrollView>
</LinearLayout>