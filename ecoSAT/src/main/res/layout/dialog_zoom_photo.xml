<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#AA000000">

    <com.movisat.utilities.TouchImageView
        android:id="@+id/zoom_image_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:scaleType="matrix"
        android:contentDescription="Foto ampliada" />

    <TextView
        android:id="@+id/btn_close"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="top|end"
        android:layout_margin="16dp"
        android:background="@android:color/black"
        android:text="×"
        android:textColor="@android:color/white"
        android:textSize="24sp"
        android:gravity="center"
        android:contentDescription="Cerrar vista de foto"
        android:clickable="true"
        android:focusable="true" />

</FrameLayout>