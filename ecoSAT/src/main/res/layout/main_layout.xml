<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:materialdesign="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--
         As the main content view, the view below consumes the entire
         space available using match_parent in both dimensions.
    -->

        >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/content_frame"
            android:layout_width="match_parent"
            android:layout_height="match_parent" >

        </RelativeLayout>



        <fr.castorflex.android.smoothprogressbar.SmoothProgressBar
            android:id="@+id/progressBarSync"
            android:layout_width="match_parent"
            android:layout_height="@dimen/sync_progressbar_height"
            android:indeterminate="true"
            app:spb_color="@color/sync_progressbar_color"
            app:spb_stroke_width="@dimen/sync_progressbar_stroke_width"
            app:spb_stroke_separator_length="@dimen/sync_progressbar_stroke_separator_length"
            app:spb_interpolator="spb_interpolator_acceleratedecelerate"
            tools:visibility="visible"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--
         android:layout_gravity="start" tells DrawerLayout to treat
         this as a sliding drawer on the left side for left-to-right
         languages and on the right side for right-to-left languages.
         The drawer is given a fixed width in dp and extends the full height of
         the container. A solid background is used for contrast
         with the content view.
    -->

    <ListView
        android:id="@+id/left_drawer"
        android:layout_width="240dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/black_color"
        android:choiceMode="singleChoice"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="1dp" />

    <ExpandableListView
        android:id="@+id/right_drawer"
        android:layout_width="240dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:background="@color/black_color"
        android:choiceMode="singleChoice"
        android:divider="@android:color/holo_green_dark"
        android:dividerHeight="1dp"
        android:childDivider="@android:color/darker_gray"
        android:drawSelectorOnTop="true" />



</androidx.drawerlayout.widget.DrawerLayout>