<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <LinearLayout
        android:id="@+id/layoutVolverAceptar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:orientation="horizontal"
        android:weightSum="1.0">

        <Button
            android:id="@+id/btnAceptar"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/crear"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:layout_above="@id/layoutVolverAceptar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layoutEstado"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_margin="20dp"
                android:padding="10dp"
                android:background="@color/gris">

                <ImageView
                    android:id="@+id/imgEstado"
                    android:layout_width="@dimen/sustituir_elem_status_img_size"
                    android:layout_height="@dimen/sustituir_elem_status_img_size"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_depositar_black_60dp"
                    android:tint="@color/white_color"/>

                <TextView
                    android:id="@+id/textEstado"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/sustituir_elem_status_img_size"
                    android:ems="40"
                    android:layout_marginStart="@dimen/sustituir_elem_status_margin"
                    android:text="@string/choseDateRange"
                    android:textColor="@color/white_color"
                    android:textAlignment="center"
                    android:gravity="center"
                    android:autoSizeTextType="uniform"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:orientation="vertical"
                android:layout_marginBottom="30dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">

                    <TextView
                        android:id="@+id/textAddMatricula"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/Desde"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textSize="20sp" />

                    <EditText
                        android:id="@+id/editFechaDesde"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:ems="12"
                        android:text=""
                        android:textStyle="bold"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:background="@drawable/rectangle"
                        android:textSize="18sp"
                        android:editable="false">
                        <requestFocus/>
                    </EditText>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutHasta"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_horizontal"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/textAddTag"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/Hasta"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textSize="20sp" />

                    <EditText
                        android:id="@+id/editFechaHasta"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:ems="12"
                        android:textStyle="bold"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:background="@drawable/rectangle"
                        android:textSize="18sp"
                        android:editable="false"/>

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutParada"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_horizontal"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/textTiempoParada"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/tiempoParada"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textSize="20sp"
                        android:inputType="number"/>

                    <EditText
                        android:id="@+id/editTiempoParada"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/rectangle"
                        android:ems="12"
                        android:gravity="center"
                        android:inputType="time"
                        android:text="5"
                        android:textAlignment="center"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                </LinearLayout>


                <LinearLayout
                    android:id="@+id/layoutBoton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:visibility="visible"/>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</RelativeLayout>

