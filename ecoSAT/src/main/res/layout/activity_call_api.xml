<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/LinearLayout1"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_key"
    android:orientation="vertical"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    tools:context=".LoginActivity">

    <TextView
        android:id="@+id/textView"
        android:layout_width="match_parent"
        android:layout_height="122dp"
        android:freezesText="true"
        android:gravity="center_vertical|center_horizontal"
        android:text="@string/connect_to_server"
        android:textColor="#ffffff"
        android:textSize="24sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/blanco"
        android:orientation="vertical"
        android:padding="1dp">

        <Button
            android:id="@+id/btCancelar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:autoText="false"
            android:background="@drawable/bg_key"
            android:elegantTextHeight="false"
            android:elevation="0dp"
            android:gravity="center_vertical|center_horizontal"
            android:minWidth="150dp"
            android:text="@string/cancelar"
            android:textColor="@android:color/primary_text_dark"
            android:textStyle="bold" />
    </LinearLayout>

</LinearLayout>