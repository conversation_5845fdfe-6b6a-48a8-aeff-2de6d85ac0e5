<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <LinearLayout
        android:id="@+id/layoutTitulo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ProgressBar
            android:id="@+id/progressCargarMunicipios"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:background="@color/theme_principal" />

        <TextView
            android:id="@+id/lblTitulo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/theme_principal"
            android:padding="@dimen/activity_vertical_margin"
            android:text="@string/cargando_municipios"
            android:textColor="@color/blanco"
            android:layout_alignParentTop="true"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/layoutTitulo"
        android:layout_marginTop="5dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTituloSwitch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/activity_vertical_margin"
            android:text="Activar Municipios" />

        <Switch
            android:id="@+id/switchMunicipios"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_below="@+id/tvTituloSwitch"
            />


        <Spinner
            android:id="@+id/sProvincias"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"

            android:ems="10"
            android:imeOptions="flagNoExtractUi" />

        <Spinner
            android:id="@+id/sMunicipios"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true"
            android:ems="10"
            android:imeOptions="flagNoExtractUi" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="30dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/sustituir_elem_img_size"
                        android:layout_height="@dimen/sustituir_elem_img_size"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_retirar_black_60dp" />

                    <TextView
                        android:id="@+id/textRetirado"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:ellipsize="end"
                        android:lines="1"
                        android:paddingLeft="@dimen/spacing_1"
                        android:text="@string/retirado"
                        android:textAlignment="center"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="@color/gris"
                        android:textColorLink="@android:color/holo_green_dark"
                        android:textSize="@dimen/sustituir_elem_title_textsize"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textAddMatricula"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:text="IDCENSO"
                    android:textAlignment="center"
                    android:textSize="20sp" />

                <EditText
                    android:id="@+id/textCensoRetirado"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginEnd="@dimen/spacing_2"
                    android:layout_marginStart="@dimen/spacing_2"
                    android:ems="10"
                    android:enabled="false"
                    android:gravity="center"
                    android:inputType="number"
                    android:textAlignment="center"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/sustituir_elem_img_size"
                        android:layout_height="@dimen/sustituir_elem_img_size"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ic_depositar_black_60dp" />

                    <TextView
                        android:id="@+id/textDepositado"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:ellipsize="end"
                        android:lines="1"
                        android:paddingLeft="@dimen/spacing_1"
                        android:text="@string/depositado"
                        android:textAlignment="center"
                        android:textAppearance="?android:attr/textAppearanceMedium"
                        android:textColor="@color/gris"
                        android:textColorLink="@android:color/holo_green_dark"
                        android:textSize="@dimen/sustituir_elem_title_textsize"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:id="@+id/textAddMatricula"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:gravity="center"
                    android:text="ID CENSO"
                    android:textAlignment="center"
                    android:textSize="20sp" />

                <EditText
                    android:id="@+id/textCensoDepositado"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginEnd="@dimen/spacing_2"
                    android:layout_marginStart="@dimen/spacing_2"
                    android:ems="10"
                    android:gravity="center"
                    android:inputType="number"
                    android:textAlignment="center"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/layoutVolverAceptar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:orientation="horizontal"
        android:weightSum="1.0">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnAceptar"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco" />

    </LinearLayout>

</RelativeLayout>

