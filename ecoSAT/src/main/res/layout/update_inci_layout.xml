<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:columnCount="1">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/layoutUpdateEstadoIncidenciaAceptar">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/textInformacionIncidenciaLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:background="@color/theme_principal"
                android:padding="10sp"
                android:text="@string/infomacion_incidencia"
                android:textColor="@color/blanco"
                android:textColorLink="@android:color/holo_green_dark"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/textInformacionIncidencia"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/textInformacionIncidenciaLabel"
                android:layout_alignParentLeft="true"
                android:layout_margin="14sp"
                android:text=""
                android:textColorLink="@android:color/holo_green_dark"
                android:textSize="18sp" />

            <RelativeLayout
                android:id="@+id/editableContentContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/textInformacionIncidencia">

            <TextView
                android:id="@+id/titulo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentRight="true"
                android:background="@color/default_color"
                android:padding="@dimen/activity_vertical_margin"
                android:text="@string/changeEstado"
                android:textAlignment="center"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/pressed_color"
                android:textColorLink="@android:color/holo_green_dark"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/lblNumeroFotos"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/cbEstado"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="16sp"
                android:text="@string/lblNumeroFotos"
                android:textAppearance="?android:attr/textAppearanceSmall" />

            <TextView
                android:id="@+id/textUpdateEstado"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/titulo"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="10sp"
                android:text="@string/label_estado"
                android:textColorLink="@android:color/holo_green_dark"
                android:textSize="22sp" />

            <Spinner
                android:id="@+id/cbEstado"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/textUpdateEstado"
                android:layout_alignParentLeft="true"
                android:paddingLeft="10sp"
                android:textSize="12sp" />

            <CheckBox
                android:id="@+id/checkBoxEsAvisoFalso"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/cbEstado"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="10sp"
                android:layout_marginTop="30dp"
                android:buttonTint="@android:color/holo_green_dark"
                android:minHeight="48dp"
                android:padding="10sp"
                android:text="@string/falsaIncidencia"
                android:textSize="22sp"
                android:visibility="gone" />


            <View
                android:layout_width="match_parent"
                android:id="@+id/spaceCheckBoxEsAvisoFalso"
                android:layout_height="30dp"
                android:layout_below="@+id/checkBoxEsAvisoFalso"></View>

            <TextView
                android:id="@+id/textObservacionesEstadoLabel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/spaceCheckBoxEsAvisoFalso"
                android:layout_alignParentLeft="true"
                android:background="@color/theme_principal"
                android:padding="10sp"
                android:text="@string/descripcion_estado"
                android:textColor="@color/blanco"
                android:textColorLink="@android:color/holo_green_dark"
                android:textSize="15sp" />

            <EditText
                android:id="@+id/etObserv"
                android:layout_width="fill_parent"
                android:layout_height="60dp"
                android:layout_below="@+id/textObservacionesEstadoLabel"
                android:layout_marginHorizontal="10sp"
                android:layout_marginBottom="30dp"
                android:autofillHints=""
                android:ems="10"
                android:imeOptions="flagNoExtractUi"
                android:inputType="text"
                android:textColorLink="@android:color/holo_green_dark" />

            <RelativeLayout
                android:id="@+id/relativelayout1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/relativelayout_hist"
                android:layout_alignParentLeft="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:background="@color/blanco"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textoImagenes"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_alignParentTop="true"
                            android:layout_marginLeft="10sp"
                            android:layout_marginTop="5dp"
                            android:text="@string/imagenes_almacenadas"
                            android:textSize="22sp" />

                        <TextView
                            android:id="@+id/textoImagenesNumero"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/textoImagenes"
                            android:layout_alignBottom="@+id/textoImagenes"
                            android:layout_marginLeft="8dp"
                            android:layout_toRightOf="@+id/textoImagenes"
                            android:text="0"
                            android:textAppearance="?android:attr/textAppearanceSmall"
                            android:textSize="18sp" />

                    </RelativeLayout>

                    <View
                        android:layout_width="fill_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="5dp" />

                    <HorizontalScrollView
                        android:id="@+id/horizontalScrollView1"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="5dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/layout_scroll"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal" />
                    </HorizontalScrollView>
                </LinearLayout>
            </RelativeLayout>

            <!-- Historial Imagenes (servidor) -->
            <RelativeLayout
                android:id="@+id/relativelayout_hist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/etObserv"
                android:layout_marginTop="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:background="@color/blanco"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/lbl_historial_foto"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_alignParentTop="true"
                            android:layout_marginLeft="10sp"
                            android:layout_marginTop="5dp"
                            android:text="@string/imagenes_historico"
                            android:textSize="22sp" />

                        <ProgressBar
                            android:id="@+id/progress_hist_img"
                            style="?android:attr/progressBarStyleSmall"
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_toRightOf="@id/lbl_historial_foto"
                            android:layout_alignTop="@id/lbl_historial_foto"
                            android:layout_alignBottom="@id/lbl_historial_foto"
                            android:layout_marginLeft="8dp"
                            android:indeterminate="true"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/txt_hist_img_numero"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/lbl_historial_foto"
                            android:layout_alignBottom="@+id/lbl_historial_foto"
                            android:layout_marginLeft="8dp"
                            android:layout_toRightOf="@+id/lbl_historial_foto"
                            android:text="0"
                            android:textAppearance="?android:attr/textAppearanceSmall"
                            android:textSize="18sp" />

                    </RelativeLayout>

                    <View
                        android:layout_width="fill_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="5dp" />

                    <HorizontalScrollView
                        android:id="@+id/horizontalScrollViewHist"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="5dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/layout_scroll_hist"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal" />
                    </HorizontalScrollView>
                </LinearLayout>
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/imagesContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/relativelayout1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:background="@color/blanco"
                    android:orientation="vertical"
                    android:paddingLeft="10sp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/textoImagenesMod"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_alignParentTop="true"
                            android:layout_marginTop="5dp"
                            android:text="@string/imagenes_nuevas"
                            android:textSize="22sp" />

                        <TextView
                            android:id="@+id/textoImagenesModNumero"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/textoImagenesMod"
                            android:layout_alignBottom="@+id/textoImagenesMod"
                            android:layout_marginLeft="8sp"
                            android:layout_toRightOf="@+id/textoImagenesMod"
                            android:text="0"
                            android:textAppearance="?android:attr/textAppearanceSmall"
                            android:textSize="18sp" />
                    </RelativeLayout>

                    <View
                        android:layout_width="fill_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="5dp" />

                    <HorizontalScrollView
                        android:id="@+id/horizontalScrollView2"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="5dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/layout_scroll2"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"></LinearLayout>
                    </HorizontalScrollView>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="5dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:id="@+id/selectall"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginRight="10dp"
                            android:adjustViewBounds="false"
                            android:src="@drawable/selectall" />

                        <ImageView
                            android:id="@+id/desselectall"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@drawable/deselectall" />

                        <ImageView
                            android:id="@+id/papelera"
                            android:layout_width="47dp"
                            android:layout_height="55dp"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@drawable/ic_delete" />

                        <ImageView
                            android:id="@+id/gallery"
                            android:layout_width="46dp"
                            android:layout_height="55dp"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@drawable/ic_gallery" />
                    </LinearLayout>

                    <View
                        android:layout_width="fill_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="5dp" />

                    <HorizontalScrollView
                        android:id="@+id/horizontalScrollView2"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="5dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/layout_scroll2"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"></LinearLayout>
                    </HorizontalScrollView>
                </LinearLayout>
            </RelativeLayout>

            </RelativeLayout>

        </RelativeLayout>
    </ScrollView>

    <RelativeLayout
        android:id="@+id/layoutUpdateEstadoIncidenciaAceptar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_principal"
        android:gravity="bottom">

        <Button
            android:id="@+id/btnAddFoto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@+id/btnAceptarAdd"
            android:layout_alignBottom="@+id/btnAceptarAdd"
            android:layout_alignParentLeft="true"
            android:background="@drawable/bg_key"
            android:drawableLeft="@drawable/ic_action_photo"
            android:minWidth="150dp"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:text="@string/foto"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

        <Button
            android:id="@+id/btnAceptarAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_key"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:drawableRight="@drawable/ic_action_ok"
            android:minWidth="150dp"
            android:text="@string/crear"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />
    </RelativeLayout>

</RelativeLayout>
