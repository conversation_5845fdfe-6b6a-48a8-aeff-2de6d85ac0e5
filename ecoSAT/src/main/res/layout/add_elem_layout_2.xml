<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_marginBottom="30dp">

            <TextView
                android:id="@+id/titulo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_alignParentRight="true"
                android:layout_alignParentTop="true"
                android:padding="@dimen/activity_vertical_margin"
                android:text="@string/CreacionElemento"
                android:textAlignment="center"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textColor="@color/pressed_color"
                android:textColorLink="@android:color/holo_green_dark"
                android:textStyle="normal" />

            <TextView
                android:id="@+id/textAddModelo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="@dimen/activity_horizontal_margin"
                android:text="@string/selecciona_modelo"
                android:textColorLink="@android:color/holo_green_dark"
                android:textSize="20sp" />

            <Spinner
                android:id="@+id/cbModelos"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_below="@+id/textAddModelo"
                android:paddingLeft="@dimen/activity_horizontal_margin" />

            <TextView
                android:id="@+id/textAddDescripcion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="@dimen/activity_horizontal_margin"
                android:layout_marginTop="20dp"
                android:text="@string/introduzca_descripcion"
                android:textColorLink="@android:color/holo_green_dark"
                android:textSize="20sp" />

            <EditText
                android:id="@+id/editDescripcion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:textStyle="bold"
                android:textSize="20sp"
                android:inputType="textCapSentences" >

                <requestFocus/>
            </EditText>


        </LinearLayout>


    </ScrollView>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:orientation="horizontal"
        android:weightSum="1.0">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnAceptar"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco" />

    </LinearLayout>
</RelativeLayout>

