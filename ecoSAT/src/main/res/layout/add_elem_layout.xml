<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:scrollbars="vertical">-->

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/layoutAceptarCancelar"
        android:layout_alignParentStart="false"
        android:layout_alignParentTop="true"
        android:scrollbars="vertical">

        <RelativeLayout
            android:id="@+id/svPanel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <LinearLayout
                android:id="@+id/llModelo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="5dp"
                android:background="#D4EED7">

                <TextView
                    android:id="@+id/textAddModelo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="@string/selecciona_modelo"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <Spinner
                    android:id="@+id/cbModelos"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="false"
                    android:layout_alignParentTop="false"
                    android:padding="2sp" />
            </LinearLayout>


            <RelativeLayout
                android:id="@+id/llNombre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llModelo"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A">

                <TextView
                    android:id="@+id/lblNombre"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:text="@string/Nombre"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <Spinner
                    android:id="@+id/eProvincias"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/lblNombre"
                    android:layout_marginLeft="6sp"
                    android:layout_marginBottom="8sp"
                    android:imeOptions="flagNoExtractUi" />

                <Spinner
                    android:id="@+id/eMunicipio"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/eProvincias"
                    android:layout_marginLeft="6sp"
                    android:imeOptions="flagNoExtractUi" />

                <EditText
                    android:id="@+id/ePrefijoNombre"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_below="@id/eMunicipio"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="6sp"
                    android:enabled="false"
                    android:hint="Municipio"
                    android:inputType="textPersonName"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="13sp" />

                <!--android:inputType="textPersonName|number"-->
                <EditText
                    android:id="@+id/ebNombre"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/eMunicipio"
                    android:layout_alignParentBottom="true"
                    android:layout_marginLeft="6sp"
                    android:layout_toEndOf="@id/ePrefijoNombre"
                    android:digits="0123456789"
                    android:hint="Introduce el nº de elemento"
                    android:inputType="number"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="13sp" />

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/llMatricula"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llNombre"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textAddMatricula"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="@string/introduzca_matricula"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/ebMatricula"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textAddMatricula"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:ems="10"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="textCapCharacters"
                    android:textColorLink="@android:color/holo_green_dark" />

                <TextView
                    android:id="@+id/textCodFisico"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="Cod. Físico"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/ebCodFisico"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textAddMatricula"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:ems="10"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="textCapCharacters"
                    android:textColorLink="@android:color/holo_green_dark" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llMatriculaTag"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llMatricula"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textAddTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="@string/tag"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/ebTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textAddMatricula"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:ems="10"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="number"
                    android:textColorLink="@android:color/holo_green_dark" />

                <Button
                    android:id="@+id/leerTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/ebMatricula"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:ems="10"
                    android:text="Leer tag"
                    android:textColorLink="@android:color/holo_green_dark" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llDescripcion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llMatriculaTag"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textAddDescripcion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="@string/introduzca_descripcion"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/ebDescripcion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textAddDescripcion"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentRight="true"
                    android:layout_weight="1"
                    android:ems="10"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="text"
                    android:textColorLink="@android:color/holo_green_dark">

                </EditText>

            </LinearLayout>


            <!-- Historial Imagenes (servidor) -->
            <RelativeLayout
                android:id="@+id/relativelayout_hist_elem"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llDescripcion"
                android:layout_marginTop="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:background="@color/blanco"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/lbl_historial_foto_elem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_alignParentTop="true"
                            android:layout_marginLeft="10sp"
                            android:layout_marginTop="5dp"
                            android:text="@string/imagenes_historico"
                            android:textSize="22sp" />

                        <ProgressBar
                            android:id="@+id/progress_hist_img_elem"
                            style="?android:attr/progressBarStyleSmall"
                            android:layout_width="18dp"
                            android:layout_height="18dp"
                            android:layout_toRightOf="@id/lbl_historial_foto_elem"
                            android:layout_alignTop="@id/lbl_historial_foto_elem"
                            android:layout_alignBottom="@id/lbl_historial_foto_elem"
                            android:layout_marginLeft="8dp"
                            android:indeterminate="true"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/txt_hist_img_numero_elem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/lbl_historial_foto_elem"
                            android:layout_alignBottom="@+id/lbl_historial_foto_elem"
                            android:layout_marginLeft="8dp"
                            android:layout_toRightOf="@+id/lbl_historial_foto_elem"
                            android:text="0"
                            android:textAppearance="?android:attr/textAppearanceSmall"
                            android:textSize="18sp" />

                    </RelativeLayout>

                    <View
                        android:layout_width="fill_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="5dp" />

                    <HorizontalScrollView
                        android:id="@+id/horizontalScrollViewHistElem"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="5dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/layout_scroll_hist_elem"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal" />
                    </HorizontalScrollView>
                </LinearLayout>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/llImagenes"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/relativelayout_hist_elem"
                android:layout_alignParentLeft="false"
                android:layout_alignParentTop="false"
                android:background="@color/background_box_add_element"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rlImagenElemento"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="5sp"
                    android:layout_marginEnd="5sp">

                    <TextView
                        android:id="@+id/textoImagenes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_alignParentTop="true"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:text="Imagen Elemento"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="5dp">

                        <ImageView
                            android:id="@+id/btExisteImagen"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="true"
                            android:scaleType="centerInside"
                            android:src="@drawable/ic_menu_report_image" />

                        <ImageView
                            android:id="@+id/btImagenElem"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@android:drawable/ic_menu_camera" />

                        <ImageView
                            android:id="@+id/btPapeleraElem"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@android:drawable/ic_menu_delete" />

                        <ImageView
                            android:id="@+id/btGalleryElem"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@drawable/gallery" />

                    </LinearLayout>
                </RelativeLayout>

                <View
                    android:layout_width="fill_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp" />

                <HorizontalScrollView
                    android:id="@+id/scrollImagenes"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="2dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="2dp"
                    android:layout_marginBottom="5dp"
                    android:scrollbars="none">

                    <LinearLayout
                        android:id="@+id/layout_scroll_imagenes"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"></LinearLayout>
                </HorizontalScrollView>


                <CheckBox
                    android:id="@+id/borrar_imagen_servidor"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15px"
                    android:checked="false"
                    android:onClick="borrarImagenesAnterioresServidorSeleccionado"
                    android:text="@string/borrar_imagenes_previas"
                    android:textSize="18sp" />

                <CheckBox
                    android:id="@+id/alta_taller"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15px"
                    android:checked="false"
                    android:onClick="altaTallerSeleccionado"
                    android:text="@string/alta_taller"
                    android:textSize="18sp" />

                <LinearLayout
                    android:id="@+id/infoPosLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginBottom="24dp"
                    android:background="@drawable/rounded_box_white"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="24dp"
                        android:layout_marginStart="16dp"
                        android:background="@drawable/location_pin" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/latitude_label"
                                android:textColor="@color/black_color"
                                android:textSize="16sp" />

                            <ImageView
                                android:layout_width="0dp"
                                android:layout_height="1dp"
                                android:layout_weight="1" >
                            </ImageView>

                            <TextView
                                android:id="@+id/textLatitude"
                                android:layout_marginStart="16dp"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black_color"
                                android:textSize="16sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/longitude_label"
                                android:textColor="@color/black_color"
                                android:textSize="16sp" />

                            <ImageView
                                android:layout_width="0dp"
                                android:layout_height="1dp"
                                android:layout_weight="1" >
                            </ImageView>

                            <TextView
                                android:layout_marginStart="16dp"
                                android:id="@+id/textLongitude"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black_color"
                                android:textSize="16sp" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </RelativeLayout>


    </ScrollView>

    <LinearLayout
        android:id="@+id/layoutAceptarCancelar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="false"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnAceptarAdd"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="false"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco" />

    </LinearLayout>

</RelativeLayout>
