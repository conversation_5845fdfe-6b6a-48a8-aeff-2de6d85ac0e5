<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linearLayoutModelIncidencias"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:descendantFocusability="blocksDescendants"
    android:orientation="horizontal"
    android:padding="5dp"
    android:weightSum="1">

    <Button
        android:id="@+id/btCambioEstadoInci"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="8dp"
        android:adjustViewBounds="true"
        android:background="@drawable/button_edit_inci"
        android:gravity="center"
        android:scaleType="centerInside"
        android:layout_centerVertical="true"/>

    <TextView
        android:id="@+id/textItem"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@+id/btCambioEstadoInci"
        android:layout_centerVertical="true"
        android:textColor="@color/black_color"
        android:textSize="18sp" />

    <!-- android:drawableLeft="@drawable/ic_launcher" -->

</RelativeLayout>