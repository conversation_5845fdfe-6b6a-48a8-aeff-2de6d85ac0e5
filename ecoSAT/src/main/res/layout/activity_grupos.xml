<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.movisat.ecosat.GruposActivity">

    <EditText
        android:id="@+id/BuscadorAreas"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:ems="10" >

        <requestFocus />
    </EditText>

    <ListView
        android:id="@+id/lvGruposAreas"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/BuscadorAreas"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:layout_above="@+id/btNinguno">

    </ListView>

    <Button
        android:id="@+id/btNinguno"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_key"
        android:drawableRight="@drawable/ic_action_check_nothing"
        android:minWidth="150dp"
        android:text="@string/bt_ninguno"
        android:gravity="right|center"
        android:textColor="@color/default_color"
        android:textColorLink="@android:color/holo_green_dark"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true" />

    <Button
        android:id="@+id/btTodos"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/bg_key"
        android:minWidth="150dp"
        android:text="@string/bt_todos"
        android:gravity="center|center_vertical"
        android:textColor="@color/default_color"
        android:textColorLink="@android:color/holo_green_dark"
        android:layout_toLeftOf="@+id/btNinguno"
        android:layout_toRightOf="@+id/btVolver" />

    <Button
        android:id="@+id/btVolver"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_key"
        android:drawableLeft="@drawable/ic_action_back"
        android:minWidth="150dp"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:text="@string/volver"
        android:textColor="@color/default_color"
        android:textColorLink="@android:color/holo_green_dark"
        android:gravity="left|center_vertical"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true" />
</RelativeLayout>
