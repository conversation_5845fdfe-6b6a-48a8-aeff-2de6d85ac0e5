<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/LinearLayoutInfo"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".FlotaActivity" >

    <LinearLayout
        android:id="@+id/rlBotonera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_principal" >

        <Button
            android:id="@+id/btCentrar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentLeft="true"
            android:background="@drawable/bg_key"
            android:drawableLeft="@drawable/ic_action_center"
            android:minWidth="150sp"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:text="@string/bt_centrar"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark"
            android:layout_weight="1" />


        <Button
            android:id="@+id/btHistorico"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_key"
            android:drawableLeft="@drawable/ic_drawer"
            android:minWidth="150sp"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:text="Histórico"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark"
            android:layout_weight="1" />

        <Button
            android:id="@+id/btSalir"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:background="@drawable/bg_key"
            android:drawableRight="@drawable/ic_action_back"
            android:minWidth="150sp"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:text="@string/volver"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark"
            android:layout_weight="1" />
    </LinearLayout>

    <ListView
        android:id="@+id/lvFlota"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/rlBotonera"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/headerLayout"
        android:footerDividersEnabled="true" >
    </ListView>

    <LinearLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/activity_vertical_margin"
        android:layout_alignParentTop="true"
        android:background="@color/default_color">

        <TextView
            android:id="@+id/titulo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:background="@color/default_color"
            android:text="@string/myFlota"
            android:textAlignment="center"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:textColor="@color/pressed_color"
            android:textColorLink="@android:color/holo_green_dark"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/txtUltimasIdentificaciones"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginEnd="@dimen/activity_vertical_margin"
            android:text=""
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:textColor="@color/pressed_color"
            android:textColorLink="@android:color/holo_green_dark"
            android:textStyle="normal"
            android:textSize="10sp"
            android:gravity="right|center"/>

        <Button
            android:id="@+id/btActualizar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="24dp"
            android:text="Sincronizar"
            android:textAlignment="center"
            android:layout_gravity="center_vertical"
            android:textAllCaps="false"
            android:textColor="@color/default_color"
            android:background="@drawable/bg_key"
            android:textColorLink="@android:color/holo_green_dark" />
    </LinearLayout>

    <View
        android:id="@+id/viewSeparatorUp"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_alignBottom="@+id/headerLayout"
        android:layout_alignParentLeft="true"
        android:background="@color/theme_principal" />

</RelativeLayout>