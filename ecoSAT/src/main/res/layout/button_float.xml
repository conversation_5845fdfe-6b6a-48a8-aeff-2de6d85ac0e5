<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:materialdesign="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <com.gc.materialdesign.views.ButtonFloat
        android:id="@+id/buttonFloat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_marginRight="16dp"
        android:background="#1E88E5"
        materialdesign:animate="true"
        materialdesign:iconDrawable="@drawable/ic_action_ok" />

</RelativeLayout>