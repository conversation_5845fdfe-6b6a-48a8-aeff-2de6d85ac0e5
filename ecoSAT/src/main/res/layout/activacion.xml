<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/LinearLayoutInfo"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/theme_principal"
    android:orientation="vertical"
    android:padding="@dimen/activity_horizontal_margin"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin">

    <ScrollView
        android:id="@+id/scrollView1"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/contenedor_activacion"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/blanco"
            android:orientation="vertical"
            android:padding="@dimen/activity_horizontal_margin">

            <TextView
                android:id="@+id/titulo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:focusable="false"
                android:text="@string/activacion"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textStyle="normal" />

            <requestFocus />

            <EditText
                android:id="@+id/numeroElemento"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:ems="10"
                android:imeOptions="flagNoExtractUi"
                android:inputType="textCapCharacters|textNoSuggestions"
                android:textColorLink="@android:color/holo_green_dark" />

            <TextView
                android:id="@+id/TextView03"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="17dp"
                android:focusable="false"
                android:text="@string/desc_movil"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:textStyle="normal" />

            <EditText
                android:id="@+id/etDescripcion"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:ems="10"
                android:imeOptions="flagNoExtractUi"
                android:inputType="textCapCharacters|textNoSuggestions"
                android:maxLength="50"
                android:textColorLink="@android:color/holo_green_dark" />

            <Button
                android:id="@+id/btnProcesa"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_key"
                android:text="@string/bt_activar"
                android:textColor="@color/default_color"
                android:textColorLink="@android:color/holo_green_dark" />

        </LinearLayout>
    </ScrollView>
</LinearLayout>