<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:columnCount="1">

    <ScrollView
        android:id="@+id/scrollView2"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/layoutUpdateEstadoIncidenciaAceptar">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/blanco"
                android:orientation="vertical"
                android:padding="@dimen/activity_horizontal_margin">

                <TextView
                    android:id="@+id/txtTipo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/theme_principal"
                    android:padding="@dimen/activity_horizontal_margin"
                    android:text="@string/selec_tipo_inci"
                    android:textAppearance="?android:attr/textAppearanceSmallInverse"
                    android:textColor="@color/blanco"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="15sp" />

                <Spinner
                    android:id="@+id/cbTipoInci"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_alignParentLeft="true" />

                <TextView
                    android:id="@+id/TextView01"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/theme_principal"
                    android:padding="@dimen/activity_horizontal_margin"
                    android:text="@string/selec_modelo_inci"
                    android:textColor="@color/blanco"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="15sp" />

                <Spinner
                    android:id="@+id/cbModeloInci"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_alignParentLeft="true" />


                <TextView
                    android:id="@+id/TextView02"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/theme_principal"
                    android:padding="@dimen/activity_horizontal_margin"
                    android:text="@string/selec_motivo_inci"
                    android:textColor="@color/blanco"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="15sp" />

                <Spinner
                    android:id="@+id/cbMotInci"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_alignParentLeft="true" />


                <TextView
                    android:id="@+id/textObservaciones"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:background="@color/theme_principal"
                    android:padding="@dimen/activity_horizontal_margin"
                    android:text="@string/observ_inci"
                    android:textColor="@color/blanco"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="15sp" />

                <EditText
                    android:id="@+id/ebObserv"
                    android:layout_width="fill_parent"
                    android:layout_height="fill_parent"
                    android:autofillHints=""
                    android:ems="10"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="text"
                    android:padding="@dimen/activity_horizontal_margin"
                    android:textColorLink="@android:color/holo_green_dark" />

                <RelativeLayout
                    android:id="@+id/layoutFoto"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="30dp">

                    <TextView
                        android:id="@+id/textoImagenes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_alignParentTop="true"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:text="@string/imagenes"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/textoImagenesNumero"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignBaseline="@+id/textoImagenes"
                        android:layout_alignBottom="@+id/textoImagenes"
                        android:layout_marginLeft="8dp"
                        android:layout_toRightOf="@+id/textoImagenes"
                        android:text="0"
                        android:textAppearance="?android:attr/textAppearanceSmall"
                        android:textSize="14sp" />


                    <HorizontalScrollView
                        android:id="@+id/horizontalScrollView1"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_marginTop="40dp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="5dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/layout_scroll"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal" />
                    </HorizontalScrollView>
                </RelativeLayout>

                <View
                    android:layout_width="fill_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="5dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:id="@+id/selectall"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginRight="10dp"
                        android:adjustViewBounds="false"
                        android:src="@drawable/selectall" />

                    <ImageView
                        android:id="@+id/desselectall"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginRight="5dp"
                        android:adjustViewBounds="false"
                        android:src="@drawable/deselectall" />

                    <ImageView
                        android:id="@+id/papelera"
                        android:layout_width="47dp"
                        android:layout_height="47dp"
                        android:layout_marginRight="5dp"
                        android:adjustViewBounds="false"
                        android:src="@drawable/ic_delete" />

                    <ImageView
                        android:id="@+id/gallery"
                        android:layout_width="46dp"
                        android:layout_height="50dp"
                        android:layout_marginRight="5dp"
                        android:adjustViewBounds="false"
                        android:src="@drawable/ic_gallery" />

                </LinearLayout>


            </LinearLayout>

            <View
                android:id="@+id/viewSeparator"
                android:layout_width="fill_parent"
                android:layout_height="1dp"
                android:layout_alignParentLeft="true"
                android:background="@color/theme_principal" />

        </RelativeLayout>
    </ScrollView>

    <RelativeLayout
        android:id="@+id/layoutUpdateEstadoIncidenciaAceptar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignEnd="@id/layoutFoto"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_principal"
        android:gravity="bottom">

        <Button
            android:id="@+id/btnAddFoto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@+id/btnAceptarAdd"
            android:layout_alignBottom="@+id/btnAceptarAdd"
            android:layout_alignParentLeft="true"
            android:background="@drawable/bg_key"
            android:drawableLeft="@drawable/ic_action_photo"
            android:minWidth="150dp"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:text="@string/foto"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

        <Button
            android:id="@+id/btnAceptarAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_key"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:drawableRight="@drawable/ic_action_ok"
            android:minWidth="150dp"
            android:text="@string/crear"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

    </RelativeLayout>
</RelativeLayout>