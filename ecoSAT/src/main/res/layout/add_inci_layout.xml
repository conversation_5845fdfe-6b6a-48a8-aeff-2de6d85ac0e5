<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:columnCount="1">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_alignEnd="@layout/add_inci_layout"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_principal"
        android:gravity="bottom">

        <View
            android:id="@+id/viewSeparator"
            android:layout_width="fill_parent"
            android:layout_height="1dp"
            android:layout_alignParentLeft="true"
            android:background="@color/theme_principal" />

        <Button
            android:id="@+id/btnAddFoto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBaseline="@+id/btnAceptarAdd"
            android:layout_alignBottom="@+id/btnAceptarAdd"
            android:layout_alignParentLeft="true"
            android:background="@drawable/bg_key"
            android:drawableLeft="@drawable/ic_action_photo"
            android:minWidth="150dip"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:text="@string/foto"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

        <Button
            android:id="@+id/btnAceptarAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:background="@drawable/bg_key"
            android:drawableRight="@drawable/ic_action_ok"
            android:minWidth="150dip"
            android:text="@string/crear"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />
    </RelativeLayout>

    <TextView
        android:id="@+id/titulo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:background="@color/default_color"
        android:padding="@dimen/activity_vertical_margin"
        android:text="@string/CreacionIncidencia"
        android:textAlignment="center"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="@color/pressed_color"
        android:textColorLink="@android:color/holo_green_dark"
        android:textStyle="normal" />

    <Spinner
        android:id="@+id/cbTipoInci"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/TextView00"
        android:layout_alignParentLeft="true"
        android:paddingLeft="@dimen/activity_horizontal_margin" />

    <TextView
        android:id="@+id/TextView01"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/cbTipoInci"
        android:layout_alignParentLeft="true"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:text="@string/selec_modelo_inci"
        android:textColor="#000"
        android:textColorLink="@android:color/holo_green_dark"
        android:textSize="15sp" />

    <Spinner
        android:id="@+id/cbModeloInci"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/TextView01"
        android:layout_alignParentLeft="true"
        android:paddingLeft="@dimen/activity_horizontal_margin" />

    <TextView
        android:id="@+id/TextView02"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/cbModeloInci"
        android:layout_alignParentLeft="true"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:text="@string/selec_motivo_inci"
        android:textColor="#000"
        android:textColorLink="@android:color/holo_green_dark"
        android:textSize="15sp" />

    <Spinner
        android:id="@+id/cbMotInci"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/TextView02"
        android:layout_alignParentLeft="true"
        android:paddingLeft="@dimen/activity_horizontal_margin" />

    <TextView
        android:id="@+id/textAddMatricula"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/cbMotInci"
        android:layout_alignParentLeft="true"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:text="@string/observ_inci"
        android:textColor="#000"
        android:textColorLink="@android:color/holo_green_dark"
        android:textSize="15sp" />

    <EditText
        android:id="@+id/ebObserv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/textAddMatricula"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:ems="10"
        android:imeOptions="flagNoExtractUi"
        android:inputType="text"
        android:textColorLink="@android:color/holo_green_dark">

        <requestFocus />
    </EditText>

    <TextView
        android:id="@+id/TextView00"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/titulo"
        android:layout_alignParentLeft="true"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:text="@string/selec_tipo_inci"
        android:textAppearance="?android:attr/textAppearanceSmallInverse"
        android:textColor="#000"
        android:textColorLink="@android:color/holo_green_dark"
        android:textSize="15sp" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/relativeLayout1"
        android:layout_below="@+id/ebObserv"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:background="@color/blanco"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/textoImagenes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="5dp"
                    android:text="@string/imagenes"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/textoImagenesNumero"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignBaseline="@+id/textoImagenes"
                    android:layout_alignBottom="@+id/textoImagenes"
                    android:layout_marginLeft="8dp"
                    android:layout_toRightOf="@+id/textoImagenes"
                    android:text="0"
                    android:textAppearance="?android:attr/textAppearanceSmall"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="50px"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"
                    android:gravity="center_vertical">

                    <ImageView
                        android:id="@+id/selectall"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="7dp"
                        android:adjustViewBounds="false"
                        android:src="@drawable/selectall" />

                    <ImageView
                        android:id="@+id/desselectall"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="2dp"
                        android:adjustViewBounds="false"
                        android:src="@drawable/deselectall" />

                    <ImageView
                        android:id="@+id/papelera"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="false"
                        android:src="@drawable/ic_delete" />

                    <ImageView
                        android:id="@+id/gallery"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:adjustViewBounds="false"
                        android:src="@drawable/ic_gallery" />

                </LinearLayout>
            </RelativeLayout>

            <View
                android:layout_width="fill_parent"
                android:layout_height="0.5dp"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="5dp" />

            <HorizontalScrollView
                android:id="@+id/horizontalScrollView1"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="2dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="2dp"
                android:layout_marginBottom="5dp"
                android:scrollbars="none">

                <LinearLayout
                    android:id="@+id/layout_scroll"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"></LinearLayout>
            </HorizontalScrollView>
        </LinearLayout>
    </RelativeLayout>

</RelativeLayout>