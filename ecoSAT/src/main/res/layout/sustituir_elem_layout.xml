<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <LinearLayout
        android:id="@+id/layoutVolverAceptar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:orientation="horizontal"
        android:weightSum="1.0">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnAceptar"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_above="@id/layoutVolverAceptar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layoutEstadoSustitucion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_margin="20dp"
                android:padding="10dp"
                android:background="@color/gris">

                <ImageView
                    android:id="@+id/imgEstadoSustitucion"
                    android:layout_width="@dimen/sustituir_elem_status_img_size"
                    android:layout_height="@dimen/sustituir_elem_status_img_size"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_retirar_black_60dp"
                    android:tint="@color/white_color"/>

                <TextView
                    android:id="@+id/textEstadoSustitucion"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/sustituir_elem_status_img_size"
                    android:ems="40"
                    android:layout_marginStart="@dimen/sustituir_elem_status_margin"
                    android:text="@string/escanear_elemento_retirado"
                    android:textColor="@color/white_color"
                    android:textAlignment="center"
                    android:gravity="center"
                    android:autoSizeTextType="uniform"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="274dp"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="30dp"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/sustituir_elem_img_size"
                            android:layout_height="@dimen/sustituir_elem_img_size"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_retirar_black_60dp" />

                        <TextView
                            android:id="@+id/textRetirado"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:ellipsize="end"
                            android:lines="1"
                            android:paddingLeft="@dimen/spacing_1"
                            android:text="@string/retirado"
                            android:textAlignment="center"
                            android:textAppearance="?android:attr/textAppearanceMedium"
                            android:textColor="@color/gris"
                            android:textColorLink="@android:color/holo_green_dark"
                            android:textSize="@dimen/sustituir_elem_title_textsize"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/textAddMatricula"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center"
                        android:text="@string/introduzca_matricula"
                        android:textAlignment="center"
                        android:textSize="20sp" />
                    <!--
                                        <TextView
                                            android:id="@+id/textScannedMatriculaRetirado"
                                            android:layout_width="match_parent"
                                            android:layout_height="40dp"
                                            android:layout_marginLeft="50dp"
                                            android:layout_marginRight="50dp"
                                            android:text="-"
                                            android:textStyle="bold"
                                            android:textAlignment="center"
                                            android:gravity="center"
                                            android:background="@drawable/rectangle"
                                            android:textSize="20sp" />
                    -->

                    <EditText
                        android:id="@+id/textScannedMatriculaRetirado"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/spacing_2"
                        android:layout_marginEnd="@dimen/spacing_2"
                        android:ems="10"
                        android:gravity="center"
                        android:inputType="textCapCharacters"
                        android:textAlignment="center"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textAddTag"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center"
                        android:text="@string/tag"
                        android:textAlignment="center"
                        android:textSize="20sp" />

                    <TextView
                        android:id="@+id/textScannedTagRetirado"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginStart="@dimen/spacing_2"
                        android:layout_marginEnd="@dimen/spacing_2"
                        android:background="@drawable/rectangle"
                        android:ems="10"
                        android:gravity="center"
                        android:text="-"
                        android:textAlignment="center"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="@dimen/sustituir_elem_img_size"
                            android:layout_height="@dimen/sustituir_elem_img_size"
                            android:layout_gravity="center_vertical"
                            android:src="@drawable/ic_depositar_black_60dp" />

                        <TextView
                            android:id="@+id/textDepositado"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:ellipsize="end"
                            android:lines="1"
                            android:paddingLeft="@dimen/spacing_1"
                            android:text="@string/depositado"
                            android:textAlignment="center"
                            android:textAppearance="?android:attr/textAppearanceMedium"
                            android:textColor="@color/gris"
                            android:textColorLink="@android:color/holo_green_dark"
                            android:textSize="@dimen/sustituir_elem_title_textsize"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/textAddMatricula"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center"
                        android:text="@string/introduzca_matricula"
                        android:textAlignment="center"
                        android:textSize="20sp" />
                    <!--
                                        <TextView
                                            android:id="@+id/textScannedMatriculaDepositado"
                                            android:layout_width="match_parent"
                                            android:layout_height="40dp"
                                            android:layout_marginLeft="50dp"
                                            android:layout_marginRight="50dp"
                                            android:text="-"
                                            android:textStyle="bold"
                                            android:textAlignment="center"
                                            android:gravity="center"
                                            android:background="@drawable/rectangle"
                                            android:textSize="20sp" />
                    -->

                    <EditText
                        android:id="@+id/textScannedMatriculaDepositado"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/spacing_2"
                        android:layout_marginEnd="@dimen/spacing_2"
                        android:ems="10"
                        android:gravity="center"
                        android:inputType="textCapCharacters"
                        android:textAlignment="center"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textAddTag"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:gravity="center"
                        android:text="@string/tag"
                        android:textAlignment="center"
                        android:textSize="20sp" />

                    <TextView
                        android:id="@+id/textScannedTagDepositado"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginStart="@dimen/spacing_2"
                        android:layout_marginEnd="@dimen/spacing_2"
                        android:background="@drawable/rectangle"
                        android:ems="10"
                        android:gravity="center"
                        android:text="-"
                        android:textAlignment="center"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</RelativeLayout>

