<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/LinearLayoutLecturaTag"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".TagNotFoundListActivity" >

    <RelativeLayout
        android:id="@+id/rlBotonera"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_principal" >

        <Button
            android:id="@+id/btSalir"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:background="@drawable/bg_key"
            android:drawableRight="@drawable/ic_action_back"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:minWidth="150dp"
            android:text="@string/volver"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />
    </RelativeLayout>

    <ListView
        android:id="@+id/lvTAG"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/rlBotonera"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/titulo"
        android:footerDividersEnabled="true" >
    </ListView>

    <TextView
        android:id="@+id/titulo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:background="@color/default_color"
        android:padding="@dimen/activity_vertical_margin"
        android:text="@string/lecturaTag"
        android:textAlignment="center"
        android:textAppearance="?android:attr/textAppearanceSmall"
        android:textColor="@color/pressed_color"
        android:textColorLink="@android:color/holo_green_dark"
        android:textStyle="normal" />

    <View
        android:id="@+id/viewSeparatorUp"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_alignBottom="@+id/titulo"
        android:layout_alignParentLeft="true"
        android:background="@color/theme_principal" />

</RelativeLayout>