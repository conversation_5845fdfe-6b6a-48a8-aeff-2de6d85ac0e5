<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/list_item"
    android:layout_width="match_parent"
    android:layout_height="55dip"
    android:orientation="vertical"
    android:weightSum="1">

    <LinearLayout
        android:id="@+id/contenedor_Controls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:descendantFocusability="blocksDescendants">

        <CheckBox
            android:id="@+id/chkSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:enabled="false" />

        <ImageView
            android:id="@+id/ico_item"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center"
            android:src="@drawable/bubble_blue" />

        <TextView
            android:id="@+id/lblListItem"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingBottom="15dp"
            android:paddingLeft="4dp"
            android:paddingTop="19dp"
            android:textColor="@color/blanco"
            android:textSize="16dip" />
    </LinearLayout>

</LinearLayout>