<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/RelativeLayout1"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="5dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/textItem"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="5dp"
        android:paddingVertical="8sp"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:textColor="@color/black_color"
        android:textSize="16sp" />

<!--    <TextView-->
<!--        android:id="@+id/textRight"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_toRightOf="@id/textItem"-->
<!--        android:layout_alignBaseline="@id/textItem"-->
<!--        android:layout_marginRight="5dp"-->
<!--        android:layout_marginLeft="16dp"-->
<!--        android:gravity="center_vertical"-->
<!--        android:textColor="@color/black_color"-->
<!--        android:textSize="16sp"-->
<!--        android:visibility="gone" />-->

    <LinearLayout
        android:id="@+id/containerRight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_toRightOf="@id/textItem"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="8dp"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:visibility="gone"
        />

<!--    <TextView-->
<!--        android:id="@+id/textBottom"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_below="@id/textItem"-->
<!--        android:layout_marginTop="0dp"-->
<!--        android:layout_marginBottom="8dp"-->
<!--        android:layout_marginHorizontal="8dp"-->
<!--        android:gravity="center_vertical"-->
<!--        android:textColor="@color/black_color"-->
<!--        android:textSize="14sp"-->
<!--        android:visibility="gone" />-->

    <LinearLayout
        android:id="@+id/containerBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_below="@id/textItem"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="8dp"
        android:layout_marginHorizontal="8dp"
        android:textSize="14sp"
        android:visibility="gone"
        />

</RelativeLayout>
