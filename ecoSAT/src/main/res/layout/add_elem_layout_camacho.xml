<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:scrollbars="vertical">-->

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/layoutAceptarCancelar"
        android:layout_alignParentStart="false"
        android:layout_alignParentTop="true"
        android:scrollbars="vertical">

        <RelativeLayout
            android:id="@+id/svPanel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <LinearLayout
                android:id="@+id/llModelo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="5dp"
                android:background="#D4EED7">

                <TextView
                    android:id="@+id/textAddModelo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="@string/selecciona_modelo"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <Spinner
                    android:id="@+id/cbModelos"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="false"
                    android:layout_alignParentTop="false"
                    android:padding="2sp" />
            </LinearLayout>


            <RelativeLayout
                android:id="@+id/llNombre"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llModelo"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A">

                <TextView
                    android:id="@+id/lblNombre"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="false"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:text="@string/Nombre"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:visibility="visible" />

                <Spinner
                    android:id="@+id/eProvincias"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/lblNombre"
                    android:layout_marginLeft="6sp"
                    android:layout_marginBottom="8sp"
                    android:imeOptions="flagNoExtractUi"
                    android:scrollbarSize="2dp" />

                <Spinner
                    android:id="@+id/eMunicipio"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/eProvincias"
                    android:layout_marginLeft="6sp"
                    android:imeOptions="flagNoExtractUi"
                    android:scrollbarSize="2dp" />

                <!--android:inputType="textPersonName|number"-->
                <EditText
                    android:id="@+id/ePrefijoNombre"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/eMunicipio"
                    android:layout_marginLeft="6sp"
                    android:hint="Municipio"
                    android:textSize="13sp" />

                <EditText
                    android:id="@+id/ebNombre"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/eMunicipio"
                    android:layout_marginLeft="4sp"
                    android:layout_toEndOf="@+id/ePrefijoNombre"
                    android:digits="0123456789"
                    android:hint="Introduce el nº de elemento"
                    android:inputType="number"
                    android:textSize="13sp" />

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/llMatricula"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llNombre"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textAddMatricula"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="@string/introduzca_matricula"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/ebMatricula"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textAddMatricula"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentRight="true"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1"
                    android:ems="10"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="textCapCharacters"
                    android:textColorLink="@android:color/holo_green_dark" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llDescripcion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llMatricula"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textAddDescripcion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="@string/introduzca_descripcion"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/ebDescripcion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/textAddDescripcion"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentRight="true"
                    android:layout_weight="1"
                    android:ems="10"
                    android:imeOptions="flagNoExtractUi"
                    android:inputType="text"
                    android:textColorLink="@android:color/holo_green_dark">

                </EditText>

            </LinearLayout>


            <LinearLayout
                android:id="@+id/llCritico"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llDescripcion"
                android:layout_marginBottom="5dp"
                android:background="#D4EED7"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textCritico"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="Crítico"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <Spinner
                    android:id="@+id/cbCritico"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:dropDownWidth="match_parent" />

            </LinearLayout>


            <RelativeLayout
                android:id="@+id/llSimilar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llCritico"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A">

                <TextView
                    android:id="@+id/textElementoSimilar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="false"
                    android:layout_alignParentBottom="false"
                    android:layout_marginBottom="5dp"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="Elemento Similar"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <Spinner
                    android:id="@+id/eProvinciasSimilar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/textElementoSimilar"
                    android:layout_marginLeft="6sp"
                    android:layout_marginBottom="8sp"
                    android:imeOptions="flagNoExtractUi"
                    android:scrollbarSize="2dp" />

                <Spinner
                    android:id="@+id/eMunicipioSimilar"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@+id/eProvinciasSimilar"
                    android:layout_marginLeft="6sp"
                    android:imeOptions="flagNoExtractUi"
                    android:scrollbarSize="2dp" />

                <EditText
                    android:id="@+id/ePrefijoNombreSimilar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/eMunicipioSimilar"
                    android:layout_marginLeft="6sp"
                    android:hint="Municipio"
                    android:textSize="13sp" />

                <EditText
                    android:id="@+id/textEditElementoSimilar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/eMunicipioSimilar"
                    android:layout_marginLeft="4sp"
                    android:layout_marginBottom="6sp"
                    android:layout_toEndOf="@id/ePrefijoNombreSimilar"
                    android:digits="0123456789"
                    android:hint="Introduce el nº de elemento similar"
                    android:inputType="text"
                    android:textSize="13sp" />

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/llZonas"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llSimilar"
                android:layout_marginBottom="5dp"
                android:background="#D4EED7"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/textZonaElemento"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="false"
                    android:layout_gravity="center_vertical"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:paddingRight="2dp"
                    android:text="Zona"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <Spinner
                    android:id="@+id/cbTipoZona"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:dropDownWidth="match_parent" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/llChecks"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llZonas"
                android:layout_marginBottom="5dp"
                android:background="#128D8A8A"
                android:orientation="vertical"
                android:paddingBottom="5dp">

                <CheckBox
                    android:id="@+id/cbRotativo"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="4sp"
                    android:text="Rotativo"
                    android:textSize="18sp" />

                <CheckBox
                    android:id="@+id/cbBajoDemanda"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="4sp"
                    android:text="Bajo demanda"
                    android:textSize="18sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llDiasBloqueTitle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/llChecks"
                android:orientation="vertical"
                android:paddingLeft="@dimen/activity_horizontal_margin">

                <TextView
                    android:id="@+id/textDiasBloqueo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="5dp"
                    android:text="Días de bloqueo"
                    android:textColorLink="@android:color/holo_green_dark"
                    android:textSize="18sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <HorizontalScrollView
                android:id="@+id/llScrollDias"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/llDiasBloqueTitle"

                android:scrollbars="horizontal">

                <LinearLayout
                    android:id="@+id/llDias"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="false"
                    android:layout_marginBottom="5dp"
                    android:paddingLeft="@dimen/activity_horizontal_margin"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:scrollbars="horizontal">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/ctvLunes"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_margin="2dp"
                            android:background="@drawable/button_style_corner"
                            android:text="L"
                            android:textColor="#545353"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvLunesManiana"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="1dp"
                            android:adjustViewBounds="true"
                            android:background="@drawable/button_style_corner"
                            android:text="MAÑANA"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvLunesTarde"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:adjustViewBounds="true"
                            android:background="@drawable/button_style_corner"
                            android:text="TARDE"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/ctvMartes"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_margin="2dp"
                            android:layout_toRightOf="@+id/ctvLunes"
                            android:layout_weight="1"
                            android:background="@drawable/button_style_corner"
                            android:text="M"
                            android:textColor="#545353"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvMartesManiana"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="1dp"
                            android:background="@drawable/button_style_corner"
                            android:text="MAÑANA"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvMartesTarde"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@drawable/button_style_corner"
                            android:text="TARDE"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/ctvMiercoles"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_margin="2dp"
                            android:layout_toRightOf="@+id/ctvMartes"
                            android:layout_weight="1"
                            android:background="@drawable/button_style_corner"
                            android:text="X"
                            android:textColor="#545353"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvMiercolesManiana"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="1dp"
                            android:background="@drawable/button_style_corner"
                            android:text="MAÑANA"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvMiercolesTarde"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@drawable/button_style_corner"
                            android:text="TARDE"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/ctvJueves"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_margin="2dp"
                            android:layout_toRightOf="@+id/ctvMiercoles"
                            android:layout_weight="1"
                            android:background="@drawable/button_style_corner"
                            android:text="J"
                            android:textColor="#545353"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvJuevesManiana"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="1dp"
                            android:background="@drawable/button_style_corner"
                            android:text="MAÑANA"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvJuevesTarde"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@drawable/button_style_corner"
                            android:text="TARDE"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/ctvViernes"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="2dp"
                            android:layout_toRightOf="@+id/ctvJueves"
                            android:layout_weight="1"
                            android:background="@drawable/button_style_corner"
                            android:text="V"
                            android:textColor="#545353"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvViernesManiana"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="1dp"
                            android:background="@drawable/button_style_corner"
                            android:text="MAÑANA"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvViernesTarde"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@drawable/button_style_corner"
                            android:text="TARDE"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/ctvSabado"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="2dp"
                            android:layout_toRightOf="@+id/ctvViernes"
                            android:layout_weight="1"
                            android:background="@drawable/button_style_corner"
                            android:text="S"
                            android:textColor="#545353"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvSabadoManiana"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="1dp"
                            android:background="@drawable/button_style_corner"
                            android:text="MAÑANA"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvSabadoTarde"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@drawable/button_style_corner"
                            android:text="TARDE"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <Button
                            android:id="@+id/ctvDomingo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_margin="2dp"
                            android:layout_toRightOf="@+id/ctvSabado"
                            android:layout_weight="1"
                            android:background="@drawable/button_style_corner"
                            android:text="D"
                            android:textColor="#545353"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvDomingoManiana"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginBottom="1dp"
                            android:background="@drawable/button_style_corner"
                            android:text="MAÑANA"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                        <Button
                            android:id="@+id/ctvDomingoTarde"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:background="@drawable/button_style_corner"
                            android:text="TARDE"
                            android:textColor="#545353"
                            android:textSize="10sp"
                            android:textStyle="bold" />

                    </LinearLayout>


                </LinearLayout>
            </HorizontalScrollView>

            <!-- Historial Imagenes (servidor) -->
            <RelativeLayout
                android:id="@+id/relativelayout_hist_elem"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llScrollDias"
                android:layout_marginTop="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentTop="true"
                    android:background="@color/blanco"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/lbl_historial_foto_elem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_alignParentTop="true"
                            android:layout_marginLeft="10sp"
                            android:layout_marginTop="5dp"
                            android:text="@string/imagenes_historial"
                            android:textSize="22sp" />

                        <TextView
                            android:id="@+id/txt_hist_img_numero_elem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignBaseline="@+id/lbl_historial_foto_elem"
                            android:layout_alignBottom="@+id/lbl_historial_foto_elem"
                            android:layout_marginLeft="8dp"
                            android:layout_toRightOf="@+id/lbl_historial_foto_elem"
                            android:text="0"
                            android:textAppearance="?android:attr/textAppearanceSmall"
                            android:textSize="18sp" />

                    </RelativeLayout>

                    <View
                        android:layout_width="fill_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="5dp" />

                    <HorizontalScrollView
                        android:id="@+id/horizontalScrollViewHistElem"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="2dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginRight="2dp"
                        android:layout_marginBottom="5dp"
                        android:scrollbars="none">

                        <LinearLayout
                            android:id="@+id/layout_scroll_hist_elem"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"></LinearLayout>
                    </HorizontalScrollView>

                </LinearLayout>
            </RelativeLayout>

            <LinearLayout
                android:id="@+id/llImagenes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/relativelayout_hist_elem"
                android:layout_alignParentLeft="false"
                android:layout_alignParentTop="false"
                android:background="#128D8A8A"
                android:orientation="vertical">

                <RelativeLayout
                    android:id="@+id/rlImagenElemento"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5sp"
                    android:layout_marginTop="5sp"
                    android:layout_marginEnd="5sp"
                    android:layout_marginBottom="5sp">

                    <TextView
                        android:id="@+id/textoImagenes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_alignParentTop="true"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="5dp"
                        android:text="Imagen Elemento"
                        android:textSize="18sp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentTop="true"
                        android:layout_alignParentRight="true"
                        android:layout_marginTop="5dp">

                        <ImageView
                            android:id="@+id/btExisteImagen"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="true"
                            android:scaleType="centerInside"
                            android:src="@drawable/ic_menu_report_image" />

                        <ImageView
                            android:id="@+id/btImagenElem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@android:drawable/ic_menu_camera" />

                        <ImageView
                            android:id="@+id/btPapeleraElem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@android:drawable/ic_menu_delete" />

                        <ImageView
                            android:id="@+id/btGalleryElem"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="5dp"
                            android:adjustViewBounds="false"
                            android:src="@drawable/gallery" />

                    </LinearLayout>
                </RelativeLayout>

                <View
                    android:layout_width="fill_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="5dp" />

                <HorizontalScrollView
                    android:id="@+id/scrollImagenes"
                    android:layout_width="fill_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="2dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="2dp"
                    android:layout_marginBottom="5dp"
                    android:scrollbars="none">

                    <LinearLayout
                        android:id="@+id/layout_scroll_imagenes"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"></LinearLayout>
                </HorizontalScrollView>

            </LinearLayout>

        </RelativeLayout>

    </ScrollView>

    <LinearLayout
        android:id="@+id/layoutAceptarCancelar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="bottom"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="false"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnAceptarAdd"
            android:layout_width="0dip"
            android:layout_height="match_parent"
            android:layout_alignParentBottom="false"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco" />

    </LinearLayout>

</RelativeLayout>
