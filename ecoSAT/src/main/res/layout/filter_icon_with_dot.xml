<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <ImageView
        android:id="@+id/ivFilterIcon"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:src="@android:drawable/ic_menu_sort_by_size"
        android:contentDescription="Filter"
        android:background="?android:attr/selectableItemBackgroundBorderless" />

    <!-- Simple red dot -->
    <View
        android:id="@+id/vFilterDot"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_gravity="top|start"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="2dp"
        android:background="@drawable/filter_badge"
        android:visibility="gone" />

</FrameLayout>