<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <!-- Date Filter Section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/filtro_por_fecha"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/pressed_color" />

        <RadioGroup
            android:id="@+id/rgDateFilter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <RadioButton
                android:id="@+id/rbDateAll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/todas_las_fechas"
                android:checked="true" />

            <RadioButton
                android:id="@+id/rbDateToday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/hoy" />

            <RadioButton
                android:id="@+id/rbDateYesterday"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ayer" />

            <RadioButton
                android:id="@+id/rbDateLast7Days"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/ultimos_7_dias" />

            <RadioButton
                android:id="@+id/rbDateCustom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/rango_personalizado" />

        </RadioGroup>

        <!-- Custom Date Range Section -->
        <LinearLayout
            android:id="@+id/llCustomDateRange"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/desde"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnFromDate"
                    style="?android:attr/buttonStyleSmall"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/seleccionar_fecha"
                    android:textAllCaps="false" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/hasta"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btnToDate"
                    style="?android:attr/buttonStyleSmall"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/seleccionar_fecha"
                    android:textAllCaps="false" />

            </LinearLayout>

        </LinearLayout>

        <!-- Sort Section -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/theme_principal"
            android:layout_marginVertical="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/ordenar_por_fecha"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/pressed_color" />

        <RadioGroup
            android:id="@+id/rgSortOrder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <RadioButton
                android:id="@+id/rbSortNewestFirst"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/mas_recientes_primero"
                android:checked="true" />

            <RadioButton
                android:id="@+id/rbSortOldestFirst"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/mas_antiguas_primero" />

        </RadioGroup>

        <!-- Status Filter Section -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/theme_principal"
            android:layout_marginVertical="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/filtro_por_estado"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/pressed_color" />

        <Spinner
            android:id="@+id/spinnerStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- Incident Type Filter Section -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/theme_principal"
            android:layout_marginVertical="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/filtro_por_tipo_incidencia"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/pressed_color" />

        <Spinner
            android:id="@+id/spinnerIncidentType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp" />

        <!-- Owner Filter Section -->
        <LinearLayout
            android:id="@+id/llOwnerFilterSection"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/theme_principal"
                android:layout_marginVertical="8dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/filtro_por_propietario"
                android:textStyle="bold"
                android:textSize="16sp"
                android:layout_marginBottom="8dp"
                android:textColor="@color/pressed_color" />

            <Spinner
                android:id="@+id/spinnerOwner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp" />

        </LinearLayout>

        <!-- Element Filter Section -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/theme_principal"
            android:layout_marginVertical="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/filtro_por_elemento"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginBottom="8dp"
            android:textColor="@color/pressed_color" />

        <CheckBox
            android:id="@+id/cbWithElementOnly"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/solo_incidencias_con_elemento"
            android:layout_marginBottom="16dp" />

        </LinearLayout>

    </ScrollView>

    <!-- Action Buttons - Fixed at bottom -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/theme_principal" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:background="@color/default_color">

        <Button
            android:id="@+id/btnClearFilters"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/limpiar"
            android:textAllCaps="false"
            android:background="@color/gris"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btnCancelFilter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cancelar"
            android:textAllCaps="false"
            android:background="@color/gris"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btnApplyFilter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/aplicar"
            android:textAllCaps="false"
            android:background="@color/theme_principal"
            android:textColor="@android:color/white" />

    </LinearLayout>

</LinearLayout>