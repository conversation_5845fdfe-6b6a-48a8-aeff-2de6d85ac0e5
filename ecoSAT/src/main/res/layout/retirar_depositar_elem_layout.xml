<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <LinearLayout
        android:id="@+id/layoutVolverAceptar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:orientation="horizontal"
        android:weightSum="1.0">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnAceptar"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/crear"
            android:textColor="@color/blanco" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:layout_above="@id/layoutVolverAceptar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layoutEstado"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_margin="20dp"
                android:padding="10dp"
                android:background="@color/gris">

                <ImageView
                    android:id="@+id/imgEstado"
                    android:layout_width="@dimen/sustituir_elem_status_img_size"
                    android:layout_height="@dimen/sustituir_elem_status_img_size"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_depositar_black_60dp"
                    android:tint="@color/white_color"/>

                <TextView
                    android:id="@+id/textEstado"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/sustituir_elem_status_img_size"
                    android:ems="40"
                    android:layout_marginStart="@dimen/sustituir_elem_status_margin"
                    android:text="@string/escanear_elem_depositado_o_matricula"
                    android:textColor="@color/white_color"
                    android:textAlignment="center"
                    android:gravity="center"
                    android:autoSizeTextType="uniform"/>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:orientation="vertical"
                android:layout_marginBottom="30dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">

                    <TextView
                        android:id="@+id/textAddMatricula"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/introduzca_matricula"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textSize="20sp" />

                    <EditText
                        android:id="@+id/editMatricula"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/rectangle"
                        android:ems="12"
                        android:gravity="center"
                        android:hint="Introduzca la matrícula"
                        android:inputType="textCapCharacters"
                        android:text=""
                        android:textAlignment="center"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:autofillHints="">

                        <requestFocus />
                    </EditText>

                    <Button
                        android:id="@+id/leerTag"
                        android:layout_width="122dp"
                        android:layout_height="wrap_content"
                        android:layout_below="@+id/ebMatricula"
                        android:layout_weight="1"
                        android:ems="10"
                        android:text="Leer tag"
                        android:textColorLink="@android:color/holo_green_dark" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutTag"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_horizontal"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/textAddTag"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@string/tag"
                        android:textAlignment="center"
                        android:gravity="center"
                        android:textSize="20sp" />

                    <TextView
                        android:id="@+id/textTag"
                        android:layout_width="wrap_content"
                        android:layout_height="40dp"
                        android:background="@drawable/rectangle"
                        android:ems="12"
                        android:gravity="center"
                        android:text="-"
                        android:textAlignment="center"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layoutBotonComprobarMatriculaCrearElemento"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:visibility="visible">

                    <Button
                        android:id="@+id/btnComprobarMatriculaCrearElemento"
                        android:layout_width="wrap_content"
                        android:layout_height="50dp"
                        android:layout_marginTop="10dp"
                        android:paddingLeft="40dp"
                        android:paddingRight="40dp"
                        android:background="@drawable/bg_key"
                        android:singleLine="false"
                        android:text="@string/comprobar_matricula"
                        android:textColor="@color/blanco" />

                </LinearLayout>

                <CheckBox
                    android:id="@+id/baja"
                    android:layout_marginTop="15px"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:onClick="bajaSeleccionado"
                    android:layout_gravity="center"
                    android:text="@string/baja"
                    android:textSize="18sp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</RelativeLayout>

