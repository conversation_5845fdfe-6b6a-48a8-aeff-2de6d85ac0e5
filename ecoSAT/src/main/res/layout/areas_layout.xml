<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/LinearLayoutInfo"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <RelativeLayout
        android:id="@+id/rlBotonera"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_principal" >

        <Button
            android:id="@+id/btNinguno"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:background="@drawable/bg_key"
            android:drawableRight="@drawable/ic_action_check_nothing"
            android:minWidth="150dp"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:text="@string/bt_ninguno"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

        <Button
            android:id="@+id/btTodos"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentLeft="true"
            android:background="@drawable/bg_key"
            android:drawableLeft="@drawable/ic_action_check_all"
            android:minWidth="150dp"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:text="@string/bt_todos"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />
    </RelativeLayout>

    <EditText
        android:id="@+id/BuscadorAreas"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/viewSeparatorUp"
        android:ems="10" >

        <requestFocus />
    </EditText>

    <ListView
        android:id="@+id/lvAreas"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/rlBotonera"
        android:layout_below="@+id/BuscadorAreas"
        android:paddingLeft="@dimen/activity_horizontal_margin" >

    </ListView>

    <View
        android:id="@+id/viewSeparatorUp"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_alignParentLeft="true"
        android:background="@color/theme_principal" />

</RelativeLayout>