<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent" >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentTop="true"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin"
        android:id="@+id/linearLayout">
        <TextView
            android:id="@+id/TextView02"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:editable="false"
            android:text="@string/pesaje_label" />

        <EditText
            android:id="@+id/editPesaje"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ems="10"
            android:selectAllOnFocus="true"
            android:inputType="number" >
        </EditText>
        <TextView
            android:id="@+id/textView1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/Desde" />

        <EditText
            android:id="@+id/editDesde"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:editable="false"
            android:onClick="showTimePickerDialog" >
        </EditText>

        <TextView
            android:id="@+id/TextView01"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/Hasta" />

        <EditText
            android:id="@+id/editHasta"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:editable="false"
            android:onClick="showTimePickerDialog" >
        </EditText>

        <TextView
            android:id="@+id/textPlanchada"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/planchada" />

        <Spinner
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/spinnerPlanchadas" />


    </LinearLayout>

    <Button
        android:id="@+id/btnInsertPesaje"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_key"
        android:layout_marginRight="@dimen/activity_horizontal_margin"
        android:minWidth="150dip"
        android:text="@string/pesaje_button"
        android:textColor="@color/default_color"
        android:layout_below="@+id/linearLayout"
        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true" />

</RelativeLayout>