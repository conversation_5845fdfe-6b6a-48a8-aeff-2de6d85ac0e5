<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/LinearLayout1"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:padding="5dp"
    android:orientation="vertical"
    android:weightSum="1">

    <TextView
        android:id="@+id/textItem"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="5dp"
        android:paddingVertical="8sp"
        android:drawableLeft="@drawable/ic_launcher"
        android:drawablePadding="8dp"
        android:textColor="@color/black_color"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/textDistance"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="5dp"
        android:height="20dp"
        android:gravity="center_vertical"
        android:textColor="@color/black_color"
        android:textSize="14sp"
        android:layout_weight="0.01" />

</LinearLayout>