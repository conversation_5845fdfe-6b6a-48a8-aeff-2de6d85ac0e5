<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/layoutAceptarCancelar"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal"
        android:weightSum="1.0">

        <Button
            android:id="@+id/btnVolver"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:text="@string/volver"
            android:textColor="@color/blanco" />

        <Button
            android:id="@+id/btnProcesa"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:layout_weight=".5"
            android:background="@drawable/bg_key"
            android:singleLine="false"
            android:text="@string/bt_aceptar"
            android:textColor="@color/blanco" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/layoutAceptarCancelar">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/lblTituloSwitch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/layoutTitulo"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:padding="@dimen/activity_vertical_margin"
                android:text="Activar Municipios"
                android:visibility="invisible" />

            <Switch
                android:id="@+id/btnSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/lblTituloSwitch"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:textOff="@string/off"
                android:textOn="@string/on"
                android:visibility="invisible" />

            <LinearLayout
                android:id="@+id/layoutTitulo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ProgressBar
                    android:id="@+id/progressCargarMunicipios"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:paddingTop="5dp"
                    android:paddingBottom="5dp"
                    android:background="@color/theme_principal" />

                <TextView
                    android:id="@+id/lblTitulo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/theme_principal"
                    android:padding="@dimen/activity_vertical_margin"
                    android:text="@string/cargando_municipios"
                    android:textColor="@color/blanco"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentStart="true" />

            </LinearLayout>


            <Spinner
                android:id="@+id/eProvincias"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/btnSwitch"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:ems="10"
                android:imeOptions="flagNoExtractUi"
                android:visibility="invisible" />

            <Spinner
                android:id="@+id/eMunicipios"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/eProvincias"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:ems="10"
                android:imeOptions="flagNoExtractUi"
                android:visibility="invisible" />

            <LinearLayout
                android:id="@+id/llLine"
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_below="@+id/eMunicipios"
                android:orientation="vertical"></LinearLayout>

            <CheckBox
                android:id="@+id/checkProcesado"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/llLine"
                android:layout_marginLeft="10dp"
                android:checked="true"
                android:text="Procesado"
                android:visibility="invisible" />

            <CheckBox
                android:id="@+id/checkVaciadoParcial"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/checkProcesado"
                android:layout_marginLeft="10dp"
                android:text="Vaciado Parcial"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/tvElemento"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/checkVaciadoParcial"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:padding="@dimen/cinco_pixeles"
                android:text="@string/NumeroElemento"
                android:textSize="16sp"
                android:textStyle="bold" />


            <EditText
                android:id="@+id/numeroElemento"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tvElemento"
                android:layout_alignParentStart="true"
                android:layout_alignParentLeft="true"
                android:ems="10"
                android:hint="Introduzca el nº de elemento"
                android:imeOptions="flagNoExtractUi"
                android:inputType="number"
                android:maxWidth="20dip"
                android:selectAllOnFocus="true"
                android:textSize="16sp">

                <requestFocus />
            </EditText>

        </RelativeLayout>

    </ScrollView>

</RelativeLayout>