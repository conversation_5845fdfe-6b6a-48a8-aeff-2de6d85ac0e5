<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/LinearLayoutInfo"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/titulo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:background="@color/default_color"
        android:padding="@dimen/activity_vertical_margin"
        android:text="@string/txt_modelos"
        android:textAlignment="center"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="@color/pressed_color"
        android:textColorLink="@android:color/holo_green_dark"
        android:textStyle="normal" />

    <View
        android:id="@+id/viewSeparatorUp"
        android:layout_width="fill_parent"
        android:layout_height="1dp"
        android:layout_alignBottom="@+id/titulo"
        android:layout_alignParentLeft="true"
        android:background="@color/theme_principal" />

    <ListView
        android:id="@+id/lvModelos"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/rlBotonera"
        android:layout_below="@+id/titulo"
        android:background="@drawable/background_button"
        android:paddingHorizontal="@dimen/activity_horizontal_margin" />

    <RelativeLayout
        android:id="@+id/rlBotonera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/theme_principal">

        <Button
            android:id="@+id/btTodos"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/btConfirmarFiltro"
            android:background="@drawable/bg_key"
            android:drawableLeft="@drawable/ic_action_check_nothing"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:text="@string/bt_todos"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

        <Button
            android:id="@+id/btConfirmarFiltro"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:background="@drawable/bg_key"
            android:paddingHorizontal="@dimen/activity_horizontal_margin"
            android:text="Confirmar"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

        <Button
            android:id="@+id/btNinguno"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/btConfirmarFiltro"
            android:background="@drawable/bg_key"
            android:drawableRight="@drawable/ic_action_check_nothing"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:text="@string/bt_ninguno"
            android:textColor="@color/default_color"
            android:textColorLink="@android:color/holo_green_dark" />

    </RelativeLayout>

</RelativeLayout>