<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:layout_width="wrap_content">

    <item android:id="@+id/filter"
        android:title="Filter"
        app:showAsAction="ifRoom"
        app:actionLayout="@layout/filter_icon_with_dot"
        android:orderInCategory="1" />

    <item android:id="@+id/search"
        android:title="Search"
        android:icon="@drawable/action_search"
        app:showAsAction="collapseActionView|ifRoom"
        app:actionViewClass="androidx.appcompat.widget.SearchView"
        android:orderInCategory="2" />
</menu>