<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- A 'parent' preference, which enables/disables child preferences (below)
         when checked/unchecked. -->
    <SwitchPreference
        android:key="notifications_new_message"
        android:title="@string/pref_title_new_message_notifications"
        android:defaultValue="true" />

    <!-- Allows the user to choose a ringtone in the 'notification' category. -->
    <!-- NOTE: This preference will be enabled only when the checkbox above is checked. -->
    <!-- NOTE: RingtonePreference's summary should be set to its value by the activity code. -->
    <RingtonePreference
        android:dependency="notifications_new_message"
        android:key="notifications_new_message_ringtone"
        android:title="@string/pref_title_ringtone"
        android:ringtoneType="notification"
        android:defaultValue="content://settings/system/notification_sound" />

    <!-- NOTE: This preference will be enabled only when the checkbox above is checked. -->
    <SwitchPreference
        android:dependency="notifications_new_message"
        android:key="notifications_new_message_vibrate"
        android:title="@string/pref_title_vibrate"
        android:defaultValue="true" />

</PreferenceScreen>
