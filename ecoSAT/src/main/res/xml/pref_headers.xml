<preference-headers xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!-- These settings headers are only used on tablets. -->

    <header app:fragment="com.movisat.ecosat.SettingsActivity$GeneralPreferenceFragment"
        app:title="@string/pref_header_general"
        app:icon="@drawable/ic_info_black_24dp" />

    <!--<header android:fragment="com.movisat.ecosat.SettingsActivity$NotificationPreferenceFragment"-->
        <!--android:title="@string/pref_header_notifications"-->
        <!--android:icon="@drawable/ic_notifications_black_24dp" />-->

    <header app:fragment="com.movisat.ecosat.SettingsActivity$DataSyncPreferenceFragment"
        app:title="@string/pref_header_data_sync"
        app:icon="@drawable/ic_sync_black_24dp" />

</preference-headers>
