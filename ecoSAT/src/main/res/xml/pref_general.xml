<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <!--<SwitchPreference-->
        <!--android:key="example_switch"-->
        <!--android:title="@string/pref_title_social_recommendations"-->
        <!--android:summary="@string/pref_description_social_recommendations"-->
        <!--android:defaultValue="true" />-->

    <!--&lt;!&ndash; NOTE: EditTextPreference accepts EditText attributes. &ndash;&gt;-->
    <!--&lt;!&ndash; NOTE: EditTextPreference's summary should be set to its value by the activity code. &ndash;&gt;-->
    <EditTextPreference
        android:key="time_operation_done_level"
        android:title="@string/pref_title_operation_done_level"
        android:defaultValue="18"
        android:selectAllOnFocus="true"
        android:inputType="number"
        android:capitalize="none"
        android:singleLine="true"
        android:maxLines="1"
        app:iconSpaceReserved="false"/>

    <EditTextPreference
        android:key="time_operation_done_clean"
        android:title="@string/pref_title_operation_done_clean"
        android:defaultValue="18"
        android:selectAllOnFocus="true"
        android:inputType="number"
        android:capitalize="none"
        android:singleLine="true"
        android:maxLines="1"
        app:iconSpaceReserved="false"/>

    <!-- NOTE: Hide buttons to simplify the UI. Users can touch outside the dialog to
         dismiss it. -->
    <!-- NOTE: ListPreference's summary should be set to its value by the activity code. -->


    <ListPreference
        android:key="zoom_min_cluster"
        android:title="@string/pref_title_factor_escala_min_cluster"
        android:defaultValue="11"
        android:entries="@array/pref_factor_escala_min_cluster_list_titles"
        android:entryValues="@array/pref_factor_escala_min_cluster_list_values"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        app:iconSpaceReserved="false"/>

    <ListPreference
        android:key="zoom_cluster"
        android:title="@string/pref_title_factor_escala_cluster"
        android:defaultValue="17"
        android:entries="@array/pref_factor_escala_cluster_list_titles"
        android:entryValues="@array/pref_factor_escala_cluster_list_values"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        app:iconSpaceReserved="false"/>

    <ListPreference
        android:key="zoom_zonas"
        android:title="@string/pref_title_factor_escala_zonas"
        android:defaultValue="17"
        android:entries="@array/pref_factor_escala_zonas_list_titles"
        android:entryValues="@array/pref_factor_escala_zonas_list_values"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        app:iconSpaceReserved="false"/>

    <ListPreference
        android:key="keep_alive"
        android:title="@string/pref_title_keep_alive"
        android:defaultValue="0"
        android:entries="@array/pref_keep_alive_titles"
        android:entryValues="@array/pref_keep_alive_values"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        app:iconSpaceReserved="false"/>

    <ListPreference
        android:key="show_levels"
        android:title="@string/pref_title_show_level"
        android:defaultValue="0"
        android:entries="@array/pref_show_level_titles"
        android:entryValues="@array/pref_show_level_values"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:id="@+id/show_collection_freq"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:defaultValue="0"
        android:entries="@array/pref_show_collection_freq_titles"
        android:entryValues="@array/pref_show_collection_freq_values"
        android:key="show_collection_freq"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        android:title="@string/pref_title_show_collection_freq"
        app:isPreferenceVisible="false"
        app:iconSpaceReserved="false" />

</PreferenceScreen>
