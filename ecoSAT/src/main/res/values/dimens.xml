<resources>

    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16sp</dimen>
    <dimen name="activity_vertical_margin">16sp</dimen>
    <dimen name="cinco_pixeles">5dp</dimen>
    <dimen name="round_button_diameter">20dp</dimen>
    <dimen name="add_button_margin">16dp</dimen>

    <dimen name="sustituir_elem_status_img_size">60dp</dimen>
    <dimen name="sustituir_elem_img_size">50dp</dimen>
    <dimen name="sustituir_elem_title_textsize">30sp</dimen>
    <dimen name="sustituir_elem_status_textsize">30sp</dimen>
    <dimen name="sustituir_elem_padding_side">50dp</dimen>
    <dimen name="sustituir_elem_status_margin">20dp</dimen>

    <dimen name="spacing_0">2dp</dimen>
    <dimen name="spacing_1">5dp</dimen>
    <dimen name="spacing_2">10dp</dimen>
    <dimen name="spacing_3">15dp</dimen>
    <dimen name="spacing_4">20dp</dimen>

    <!-- toast_uhf_reading.xml (Custom Toast para lectura UHF) -->
    <dimen name="toast_uhf_reading_width">300dp</dimen>
    <dimen name="toast_uhf_reading_height">150dp</dimen>
    <dimen name="toast_uhf_reading_padding">25dp</dimen>
    <dimen name="toast_uhf_reading_image_size">60dp</dimen>
    <dimen name="toast_uhf_reading_image_text_spacing">10dp</dimen>
    <dimen name="toast_uhf_reading_textsize">24sp</dimen>

    <!-- Sync ProgressBar -->
    <dimen name="sync_progressbar_height">3dp</dimen>
    <dimen name="sync_progressbar_stroke_width">5dp</dimen>
    <dimen name="sync_progressbar_stroke_separator_length">50dp</dimen>

</resources>
