<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <!--
        Base application theme, dependent on API level. This theme is replaced
        by AppBaseTheme from res/values-vXX/styles.xml on newer devices.


    -->
    <style name="AppBaseTheme" parent="@style/CustomActionBarTheme">
        <!--
            Theme customizations available in newer API levels can go in
            res/values-vXX/styles.xml, while customizations related to
            backward-compatibility can go here.


        -->
    </style>

    <!-- Application theme. -->
    <style name="AppTheme" parent="AppBaseTheme">
        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
    </style>

    <style name="InfoDialogStyle" parent="@android:style/Theme.Holo.Dialog">
        <item name="android:textColor">#ffffff</item>
        <item name="android:typeface">normal</item>
        <item name="android:childDivider">@color/theme_principal</item>
        <item name="android:windowTitleStyle">@style/dialog_title_style</item>
    </style>

    <style name="dialog_title_style" parent="android:Widget.TextView">
        <item name="android:textAppearance">@android:style/TextAppearance.DialogWindowTitle</item>
    </style>

    <style name="LoginFormContainer">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
    </style>

    <!-- Estilo para Activity invisible -->
    <style name="Theme.Transparent" parent="android:Theme">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

</resources>