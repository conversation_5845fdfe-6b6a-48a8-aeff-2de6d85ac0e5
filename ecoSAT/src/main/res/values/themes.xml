<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- the theme applied to the application or activity -->
    <style name="CustomActionBarTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!--<item name="android:actionBarStyle">@style/MyActionBar</item>-->
        <item name="colorPrimary">@color/theme_principal</item>

        <!--<item name="toolbarNavigationButtonStyle">@style/cusToolbarNavigationButtonStyle</item>-->

        <item name="android:preferenceStyle">@style/PreferenceCompatTheme</item>
        <!-- Opt‑out edge‑to‑edge en Android 15, esto bloquea la obligación de adaptar la app a
        edge-to-edge, si se sube a sdk 36 ya no se puede bloquear y tendremos que adaptar la app -->
        <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
    </style>

    <style name="MainTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/theme_principal</item>
        <item name="android:preferenceStyle">@style/PreferenceCompatTheme</item>
        <item name="android:windowBackground">@color/theme_principal</item>
        <!-- Opt‑out edge‑to‑edge en Android 15 -->
        <item name="android:windowOptOutEdgeToEdgeEnforcement">true</item>
    </style>
    
    <!-- ActionBar styles -->
    <style name="MyActionBar" parent="Widget.AppCompat.Light.ActionBar.Solid.Inverse">
        <item name="background">@color/theme_principal</item>
    </style>

    <!--<style name="cusToolbarNavigationButtonStyle" parent="@style/Widget.AppCompat.Toolbar.Button.Navigation">-->
        <!--&lt;!&ndash;default is 56dp&ndash;&gt;-->
        <!--<item name="android:minWidth">0dp</item>-->
        <!--<item name="android:paddingLeft">0dp</item>-->
        <!--<item name="android:paddingRight">0dp</item>-->
        <!--<item name="android:scaleType">centerCrop</item>-->
    <!--</style>-->

    <style name="MyToolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:navigationIcon">?attr/homeAsUpIndicator</item>
        <item name="navigationIcon">?attr/homeAsUpIndicator</item>
    </style>

</resources>