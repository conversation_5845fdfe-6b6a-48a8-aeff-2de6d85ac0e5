#15060101;

CREATE TABLE IF NOT EXISTS `sensores_nivel_llenado` (
	`id`	INTEGER PRIMARY KEY AUTOINCREMENT,
	`codigo_elemento`	INTEGER NOT NULL,
	`codigo_movil`	INTEGER,
	`empresa`	INTEGER NOT NULL,
	`fecha_registro`	NUMERIC NOT NULL,
	`numero_fraccion`	INTEGER NOT NULL,
	`procesado`	INTEGER NOT NULL DEFAULT 0
);

CREATE INDEX IF NOT EXISTS [sensores_nivel_llenado_idx1] ON [sensores_nivel_llenado] ([codigo_elemento]);
CREATE INDEX IF NOT EXISTS [sensores_nivel_llenado_idx2] ON [sensores_nivel_llenado] ([procesado]);


CREATE TABLE IF NOT EXISTS `sensores_lavado` (
	`id`	INTEGER PRIMARY KEY AUTOINCREMENT,
	`codigo_elemento`	INTEGER NOT NULL,
	`codigo_movil`	INTEGER,
	`empresa`	INTEGER NOT NULL,
	`fecha_registro`	NUMERIC NOT NULL
);

CREATE INDEX IF NOT EXISTS [sensores_lavado_idx1] ON [sensores_lavado] ([codigo_elemento]);

ALTER TABLE elementos ADD COLUMN descripcion TEXT;

CREATE TABLE IF NOT EXISTS `municipios` (
	`id`	INTEGER PRIMARY KEY AUTOINCREMENT,
	`municipio`	TEXT
);

ALTER TABLE municipios ADD COLUMN provincia INTEGER;

CREATE TABLE IF NOT EXISTS `provincias` (
	`id`	INTEGER PRIMARY KEY,
	`provincia`	TEXT
);

CREATE TABLE IF NOT EXISTS `flota` (
	`codigo`	INTEGER,
	`nombre`	TEXT,
	`empresa`	INTEGER,
	PRIMARY KEY(codigo)
);
CREATE INDEX IF NOT EXISTS [flota_idx1] ON [flota] ([empresa]);

#15062401;
 
CREATE TABLE IF NOT EXISTS  [areas] (
  [codigo] INTEGER NOT NULL, 
  [empresa] INTEGER NOT NULL, 
  [abreviatura] VARCHAR2(50), 
  [nombre] VARCHAR2(255), 
  [superficie] INTEGER, 
  [radio_lado] INTEGER, 
  [centro_lat] DOUBLE, 
  [centro_lon] DOUBLE, 
  [forma] INTEGER, 
  CONSTRAINT [] PRIMARY KEY ([codigo], [empresa])
 );
 
 
CREATE TABLE IF NOT EXISTS  [areas_puntos] (
  [area] INTEGER NOT NULL, 
  [empresa] INTEGER NOT NULL, 
  [latitud] DOUBLE, 
  [longitud] DOUBLE, 
  [orden] INTEGER,  
  CONSTRAINT [] PRIMARY KEY ([area], [empresa], [latitud], [longitud], [orden])
  );
  
  
#15062501;

CREATE TABLE IF NOT EXISTS  [areas_puntos_control] (
  [area] INTEGER NOT NULL, 
  [empresa] INTEGER NOT NULL, 
  [latitud] DOUBLE, 
  [longitud] DOUBLE, 
  CONSTRAINT [] PRIMARY KEY ([area], [empresa], [latitud], [longitud])
  );

#15081101;
ALTER TABLE Areas ADD COLUMN nombre_grupo VARCHAR2(255);
CREATE INDEX IF NOT EXISTS [areas_nombre_grupo_idx1] ON [areas] ([nombre_grupo]);


#15081701;
ALTER TABLE Areas ADD COLUMN grupo INTEGER;
CREATE INDEX IF NOT EXISTS [areas_grupo_idx1] ON [areas] ([grupo]);

CREATE TABLE IF NOT EXISTS  [areas_grupos] (
  [id] INTEGER NOT NULL,
  [empresa] INTEGER NOT NULL,
  [abreviatura] VARCHAR2(50),
  [nombre] VARCHAR2(255),
  CONSTRAINT [] PRIMARY KEY ([id], [empresa])
);

#15102301;
ALTER TABLE areas_grupos ADD COLUMN clicks INTEGER DEFAULT 0;
CREATE INDEX IF NOT EXISTS [areas_grupo_idx2] ON [areas_grupos] ([nombre]);

#16030401;
CREATE TABLE IF NOT EXISTS [planchadas] (
    [id] INTEGER NOT NULL,
    [empresa] INTEGER NOT NULL,
    [codigo_planchada] VARCHAR2(50) NOT NULL,
    [descripcion] VARCHAR2(50),
    [lote_id] INTEGER NOT NULL,
    CONSTRAINT [] PRIMARY KEY ([id], [empresa])
);

CREATE INDEX IF NOT EXISTS [planchadas_idx1] ON [planchadas] ([empresa]);

#16030901;
DELETE FROM conf WHERE clave='ultSincroElementos' AND usuario<>
(SELECT usuario FROM conf WHERE clave='ultSincroElementos' LIMIT 1);

DELETE FROM conf WHERE clave='ultSincroElementosModelos' AND usuario<>
(SELECT usuario FROM conf WHERE clave='ultSincroElementosModelos' LIMIT 1);

DELETE FROM conf WHERE clave='ultSincroElementosTipo' AND usuario<>
(SELECT usuario FROM conf WHERE clave='ultSincroElementosTipo' LIMIT 1);

DELETE FROM conf WHERE clave='ultSincroIncidenciasTipo' AND usuario<>
(SELECT usuario FROM conf WHERE clave='ultSincroIncidenciasTipo' LIMIT 1);

DELETE FROM conf WHERE clave='ultSincroIncidenciasMotivo' AND usuario<>
(SELECT usuario FROM conf WHERE clave='ultSincroIncidenciasMotivo' LIMIT 1);

UPDATE conf SET usuario = NULL WHERE clave='ultSincroElementos';

UPDATE conf SET usuario = NULL WHERE clave='ultSincroElementosModelos';

UPDATE conf SET usuario = NULL WHERE clave='ultSincroElementosTipo';

UPDATE conf SET usuario = NULL WHERE clave='ultSincroIncidenciasTipo';

UPDATE conf SET usuario = NULL WHERE clave='ultSincroIncidenciasMotivo';

#17062801;
CREATE TABLE IF NOT EXISTS [operations_done] (
    [id] INTEGER NOT NULL,
    [code_mobile] INTEGER NOT NULL,
    [code_operation] INTEGER NOT NULL,
    [date] DATETIME NOT NULL,
    [description] VARCHAR2(255),
    [value] VARCHAR2(255),
    [lat] DOUBLE,
    [lng] DOUBLE,
    CONSTRAINT [] PRIMARY KEY ([id])
);

CREATE INDEX IF NOT EXISTS [operations_done_idx1] ON [operations_done] ([code_mobile]);

#17091801;
ALTER TABLE usuarios ADD COLUMN admin INTEGER;

#18021301;
ALTER TABLE tags ADD COLUMN idInternoElemento INTEGER;

#18022001;
ALTER TABLE operations_done ADD COLUMN idInternoElemento INTEGER;

#18052501;
CREATE TABLE IF NOT EXISTS [vehiculos] (
    [codigo] INTEGER NOT NULL,
    [nombre] TEXT,
    [empresa] INTEGER NOT NULL,
    [tipo] INTEGER NOT NULL,
    [imei] TEXT,
    CONSTRAINT [] PRIMARY KEY ([codigo], [empresa], [tipo]
);

#18110601;
ALTER TABLE elementos ADD COLUMN nivel_critico NVARCHAR(255);
ALTER TABLE elementos ADD COLUMN elemento_similar INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN tipo_zona INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN diabloq_lunes INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN diabloq_martes INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN diabloq_miercoles INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN diabloq_jueves INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN diabloq_viernes INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN diabloq_sabado INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN diabloq_domingo INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN rotativo INTEGER NOT NULL DEFAULT 0;
ALTER TABLE elementos ADD COLUMN vacia_bajo_demanda INTEGER NOT NULL DEFAULT 0;

CREATE TABLE IF NOT EXISTS [tipo_zonas] (
    [id] INTEGER NOT NULL,
    [nombre] VARCHAR2(255) NOT NULL,
    [abreviatura] VARCHAR2(255) NOT NULL,
    [fecha_baja] DATETIME DEFAULT NULL,
    CONSTRAINT [] PRIMARY KEY ([id])
);


#19012301;
ALTER TABLE incidencias ADD COLUMN tipoPropietario INTEGER NOT NULL DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN propietario INTEGER NOT NULL DEFAULT 0;

#19051301;
ALTER TABLE elementos ADD COLUMN tieneImagen INTEGER NOT NULL DEFAULT 0;

#19061901;
CREATE TABLE IF NOT EXISTS [flota_posiciones_historico] (
  	[idRuta] INTEGER,
  	[empresa] INTEGER NOT NULL,
  	[movil] INTEGER NOT NULL,
  	[fecha] DATETIME NOT NULL,
  	[altura] INTEGER,
  	[rumbo] INTEGER,
  	[lat] DOUBLE,
  	[lon] DOUBLE,
  	[velocidad] DOUBLE,
  	[tiempoParada] DOUBLE,
  	CONSTRAINT [] PRIMARY KEY ([movil], [empresa], [fecha])
);

CREATE INDEX IF NOT EXISTS [flota_posiciones_historico_idx1] ON [flota_posiciones_historico] ([lat], [lon]);


#19092701;
ALTER TABLE incidencias_fotos ADD COLUMN foto_url VARCHAR2(255);

#21061101;
CREATE TABLE IF NOT EXISTS [motivos_baja] (
    [id] INTEGER NOT NULL,
    [abreviatura] VARCHAR2(255) NOT NULL,
    [nombre] VARCHAR2(255) NOT NULL,
    [descripcion] VARCHAR2(255) NOT NULL,
    CONSTRAINT [] PRIMARY KEY ([id])
);

#21091601;
ALTER TABLE usuarios ADD COLUMN idIndra NVARCHAR(255);

#22020201;
ALTER TABLE elementos ADD COLUMN codFisico NVARCHAR(255);

#22081101;
ALTER TABLE elementos ADD COLUMN fechaUltRecogida NVARCHAR(255);
ALTER TABLE elementos ADD COLUMN fechaUltLavado NVARCHAR(255);

#23021701;
ALTER TABLE incidencias_estados ADD COLUMN esAvisoFalso INTEGER;
ALTER TABLE elementos ADD COLUMN frecuenciaProcesado INTEGER;

#23022001;
ALTER TABLE incidencias_estados ADD COLUMN observacion NVARCHAR(255);

#23030901;
ALTER TABLE elementos ADD COLUMN volum_imei TEXT;

#23061401;
CREATE UNIQUE INDEX IF NOT EXISTS [idx_unique_code_mobile_date] ON [operations_done] ([code_mobile], [date]);
DELETE FROM [operations_done] WHERE [id] NOT IN (SELECT MIN([id]) FROM [operations_done] GROUP BY [code_mobile], [date]);

#23063001;
ALTER TABLE incidencias ADD COLUMN fechaModificacion INTEGER;
ALTER TABLE incidencias ADD COLUMN ultimoEstado INTEGER;

#23091301;
DELETE FROM incidencias
WHERE ROWID NOT IN (
    SELECT ROWID
    FROM (
        SELECT ROWID, idExterno, MAX(fechaModificacion) as max_fecha
        FROM incidencias
        GROUP BY idExterno
    )
);

#23111301;
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS [nueva_elementos] (
    [id] INTEGER PRIMARY KEY AUTOINCREMENT,
    [idExterno] INTEGER NOT NULL UNIQUE,
    [empresa] INTEGER NOT NULL,
    [nombre] NVARCHAR(255) NOT NULL,
    [matricula] NVARCHAR(255) NOT NULL,
    [modelo] INTEGER NOT NULL,
    [estado] INTEGER,
    [lat] DOUBLE,
    [lon] DOUBLE,
    [descripcion] TEXT,
    [nivel_critico] NVARCHAR(255),
    [elemento_similar] INTEGER NOT NULL DEFAULT 0,
    [tipo_zona] INTEGER NOT NULL DEFAULT 0,
    [diabloq_lunes] INTEGER NOT NULL DEFAULT 0,
    [diabloq_martes] INTEGER NOT NULL DEFAULT 0,
    [diabloq_miercoles] INTEGER NOT NULL DEFAULT 0,
    [diabloq_jueves] INTEGER NOT NULL DEFAULT 0,
    [diabloq_viernes] INTEGER NOT NULL DEFAULT 0,
    [diabloq_sabado] INTEGER NOT NULL DEFAULT 0,
    [diabloq_domingo] INTEGER NOT NULL DEFAULT 0,
    [rotativo] INTEGER NOT NULL DEFAULT 0,
    [vacia_bajo_demanda] INTEGER NOT NULL DEFAULT 0,
    [tieneImagen] INTEGER NOT NULL DEFAULT 0,
    [codFisico] NVARCHAR(255),
    [fechaUltRecogida] NVARCHAR(255),
    [fechaUltLavado] NVARCHAR(255),
    [frecuenciaProcesado] INTEGER,
    [volum_imei] TEXT
);
INSERT INTO [nueva_elementos] SELECT * FROM [elementos] GROUP BY [idExterno] HAVING COUNT(*) = 1;
DROP TABLE [elementos];
ALTER TABLE [nueva_elementos] RENAME TO [elementos];
UPDATE conf SET valor = '2023-09-01T00:00:00' WHERE clave = 'ultSincroElementos';

COMMIT;

#24010301;
CREATE TABLE IF NOT EXISTS [lecturas] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT,
  	[fecha] NVARCHAR(255),
  	[tag] NVARCHAR(255),
  	[raw] NVARCHAR(255),
  	[tipo] NVARCHAR(255)
);

#24011601;
CREATE INDEX IF NOT EXISTS [elem_idx1] ON [elementos] ([empresa], [lat], [lon]);
CREATE INDEX IF NOT EXISTS [elem_idx2] ON [elementos] ([idExterno], [empresa]);

#24051001;
CREATE INDEX IF NOT EXISTS [elem_idx3] ON [elementos] ([empresa], [modelo]);

#24061101;
CREATE TABLE IF NOT EXISTS identificaciones (
    codigoMovil INTEGER,
    nombreMovil TEXT,
    nombreCategoria TEXT,
    nombreEmpleado TEXT,
    fechaInicio TEXT
);

#25060402;
ALTER TABLE incidencias ADD COLUMN totalRegistros INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN movil INTEGER;
ALTER TABLE incidencias ADD COLUMN idSincro TEXT;
ALTER TABLE incidencias ADD COLUMN ruta INTEGER;
ALTER TABLE incidencias ADD COLUMN rutaH INTEGER;
ALTER TABLE incidencias ADD COLUMN fechaBaja TEXT;
ALTER TABLE incidencias ADD COLUMN latitudInt INTEGER;
ALTER TABLE incidencias ADD COLUMN longitudInt INTEGER;
ALTER TABLE incidencias ADD COLUMN motivoDesc TEXT;
ALTER TABLE incidencias ADD COLUMN infoGeo TEXT;
ALTER TABLE incidencias ADD COLUMN fecha TEXT;
ALTER TABLE incidencias ADD COLUMN tipoElem INTEGER;
ALTER TABLE incidencias ADD COLUMN matricula TEXT;
ALTER TABLE incidencias ADD COLUMN imagen TEXT;
ALTER TABLE incidencias ADD COLUMN idEquipo INTEGER;
ALTER TABLE incidencias ADD COLUMN emailCiudadano TEXT;
ALTER TABLE incidencias ADD COLUMN validada INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN informado INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN acera INTEGER;
ALTER TABLE incidencias ADD COLUMN textoCambioEstado TEXT;
ALTER TABLE incidencias ADD COLUMN borrado INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN ordenTrabajo INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN x INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN y INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN xMercator INTEGER DEFAULT 0;
ALTER TABLE incidencias ADD COLUMN yMercator INTEGER DEFAULT 0;
UPDATE conf SET valor = '0000-00-00 00:00:00' WHERE clave = 'ultSincroIncidenciasHistorico';

#25062501;
ALTER TABLE incidencias ADD COLUMN municipio TEXT;