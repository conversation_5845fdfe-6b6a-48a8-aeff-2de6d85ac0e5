CREATE TABLE IF NOT EXISTS [conf] (
	[clave]		NVARCHAR(255) NOT NULL,
	[valor]		NVARCHAR(1024) NOT NULL,
	[empresa]	INTEGER DEFAULT NULL,
	[usuario]	INTEGER DEFAULT NULL
);
CREATE INDEX IF NOT EXISTS [idx1] ON [conf] ([clave]);
CREATE INDEX IF NOT EXISTS [idx2] ON [conf] ([clave], [empresa]);
CREATE INDEX IF NOT EXISTS [idx3] ON [conf] ([clave], [empresa], [usuario]);

CREATE TABLE IF NOT EXISTS [empresas] (
	[id]		INTEGER PRIMARY KEY,
	[nombre]	NVARCHAR(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS [usuarios] (
	[id]		INTEGER PRIMARY KEY AUTOINCREMENT,
	[idExterno]	INTEGER	NOT NULL,
	[empresa]	INTEGER	NOT NULL,
	[nombre]	NVARCHAR(255) NOT NULL,
	[login]		NVARCHAR(20) NOT NULL,
	[passw]		NVARCHAR(20),
	[admin]     INTEGER,
	[idIndra]	NVARCHAR(255)
);
CREATE INDEX IF NOT EXISTS [usu_idx1] ON [usuarios] ([empresa], [login]);
CREATE INDEX IF NOT EXISTS [usu_idx2] ON [usuarios] ([idExterno], [empresa]);

CREATE TABLE IF NOT EXISTS [trabajadores_tipo] (
	[id]		INTEGER PRIMARY KEY AUTOINCREMENT,
	[idExterno]	INTEGER	NOT NULL,
	[empresa]	INTEGER	NOT NULL,
	[nombre]	NVARCHAR(255) NOT NULL
);
CREATE INDEX IF NOT EXISTS [trabajadores_tipo_idx1] ON [trabajadores_tipo] ([idExterno], [empresa]);

CREATE TABLE IF NOT EXISTS [trabajadores] (
  	[id] 		INTEGER PRIMARY KEY AUTOINCREMENT, 
  	[idExterno] INTEGER NOT NULL,
  	[empresa] 	INTEGER NOT NULL, 
  	[nombre] 	NVARCHAR(255) NOT NULL, 
  	[tipo] 		INTEGER NOT NULL, 
  	[identif] 	NVARCHAR(50) 
);
CREATE INDEX IF NOT EXISTS [trabajadores_idx1] ON [trabajadores] ([idExterno], [empresa]);

CREATE TABLE IF NOT EXISTS [elementos_tipo] (
	[id] 		INTEGER PRIMARY KEY AUTOINCREMENT,
	[idExterno] INTEGER NOT NULL,
  	[empresa] 	INTEGER NOT NULL,
	[nombre]	NVARCHAR(255) NOT NULL
);
CREATE INDEX IF NOT EXISTS [elementos_tipo_idx1] ON [elementos_tipo] ([idExterno], [empresa]);

CREATE TABLE IF NOT EXISTS [elementos_modelo] (
  	[id] 		INTEGER PRIMARY KEY AUTOINCREMENT,
  	[idExterno]	INTEGER NOT NULL,  
  	[empresa] 	INTEGER NOT NULL, 
  	[nombre] 	NVARCHAR(255) NOT NULL, 
  	[tipo] 		INTEGER NOT NULL, 
  	[residuo] 	INTEGER NOT NULL, 
  	[recogida] 	INTEGER NOT NULL, 
  	[capacidad] INTEGER, 
  	[icono] 	BLOB
);
CREATE INDEX IF NOT EXISTS [elementos_modelo_idx1] ON [elementos_modelo] ([idExterno], [empresa]);
  
CREATE TABLE IF NOT EXISTS [elementos] (
		[id] INTEGER PRIMARY KEY AUTOINCREMENT,
		[idExterno] INTEGER NOT NULL UNIQUE,
		[empresa] INTEGER NOT NULL,
		[nombre] NVARCHAR(255) NOT NULL,
		[matricula] NVARCHAR(255) NOT NULL,
		[modelo] INTEGER NOT NULL,
		[estado] INTEGER,
		[lat] DOUBLE,
		[lon] DOUBLE,
		[descripcion] TEXT,
		[nivel_critico] NVARCHAR(255),
		[elemento_similar] INTEGER NOT NULL DEFAULT 0,
		[tipo_zona] INTEGER NOT NULL DEFAULT 0,
		[diabloq_lunes] INTEGER NOT NULL DEFAULT 0,
		[diabloq_martes] INTEGER NOT NULL DEFAULT 0,
		[diabloq_miercoles] INTEGER NOT NULL DEFAULT 0,
		[diabloq_jueves] INTEGER NOT NULL DEFAULT 0,
		[diabloq_viernes] INTEGER NOT NULL DEFAULT 0,
		[diabloq_sabado] INTEGER NOT NULL DEFAULT 0,
		[diabloq_domingo] INTEGER NOT NULL DEFAULT 0,
		[rotativo] INTEGER NOT NULL DEFAULT 0,
		[vacia_bajo_demanda] INTEGER NOT NULL DEFAULT 0,
		[tieneImagen] INTEGER NOT NULL DEFAULT 0,
		[codFisico] NVARCHAR(255),
		[fechaUltRecogida] NVARCHAR(255),
		[fechaUltLavado] NVARCHAR(255),
		[frecuenciaProcesado] INTEGER,
		[volum_imei] TEXT
);

CREATE INDEX IF NOT EXISTS [elem_idx1] ON [elementos] ([empresa], [lat], [lon]);
CREATE INDEX IF NOT EXISTS [elem_idx2] ON [elementos] ([idExterno], [empresa]);
CREATE INDEX IF NOT EXISTS [elem_idx3] ON [elementos] ([empresa], [modelo]);

CREATE TABLE IF NOT EXISTS [bandeja_salida] (
  	[id] 		INTEGER PRIMARY KEY AUTOINCREMENT, 
  	[empresa] 	INTEGER NOT NULL, 
  	[usuario] 	INTEGER NOT NULL, 
  	[prioridad] INTEGER DEFAULT 0, 
  	[tipo] 		INTEGER, 
  	[dtime]		TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  	[datos] 	BLOB
);
CREATE INDEX IF NOT EXISTS [bandeja_salida_idx1] ON [bandeja_salida] ([prioridad], [id]);

CREATE TABLE IF NOT EXISTS [incidencias_tipo] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT, 
  	[idExterno] INT NOT NULL, 
  	[empresa] INT NOT NULL, 
  	[nombre] NVARCHAR(255) NOT NULL 
);
CREATE INDEX IF NOT EXISTS [incidencias_tipo_idx1] ON [incidencias_tipo] ([idExterno], [empresa]);

CREATE TABLE IF NOT EXISTS [incidencias_modelo] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT, 
  	[idExterno] INT NOT NULL,
  	[empresa] INT NOT NULL, 
  	[tipo] INT NOT NULL, 
  	[nombre] NVARCHAR(255) NOT NULL
);
CREATE INDEX IF NOT EXISTS [incidencias_modelo_idx1] ON [incidencias_modelo] ([idExterno], [empresa]);

CREATE TABLE IF NOT EXISTS [incidencias_motivo] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT,
  	[idExterno] INT NOT NULL,  
  	[empresa] INT NOT NULL, 
  	[modelo] INT NOT NULL, 
  	[nombre] NVARCHAR(255) NOT NULL 
);
CREATE INDEX IF NOT EXISTS [incidencias_motivo_idx1] ON [incidencias_motivo] ([idExterno], [empresa]);


CREATE TABLE IF NOT EXISTS [incidencias] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT, 
  	[idExterno] INTEGER NOT NULL, 
  	[empresa] INTEGER NOT NULL,
  	[usuario] INTEGER NOT NULL, 
  	[tipo] INTEGER NOT NULL, 
  	[modelo] INTEGER NOT NULL, 
  	[motivo] INTEGER NOT NULL, 
  	[lat] DOUBLE NOT NULL, 
  	[lon] DOUBLE NOT NULL, 
  	[estado] INTEGER NOT NULL DEFAULT 0, 
  	[elemento] INTEGER,
  	[tipoPropietario] INTEGER NOT NULL DEFAULT 0,
    [propietario] INTEGER NOT NULL DEFAULT 0,
  	[observ] NVARCHAR(255),
	[fechaModificacion] INTEGER,
	[ultimoEstado] INTEGER,
	[totalRegistros] INTEGER DEFAULT 0,
	[movil] INTEGER,
	[idSincro] TEXT,
	[ruta] INTEGER,
	[rutaH] INTEGER,
	[fechaBaja] TEXT,
	[latitudInt] INTEGER,
	[longitudInt] INTEGER,
	[motivoDesc] TEXT,
	[infoGeo] TEXT,
	[fecha] TEXT,
	[tipoElem] INTEGER,
	[matricula] TEXT,
	[imagen] TEXT,
	[idEquipo] INTEGER,
	[emailCiudadano] TEXT,
	[validada] INTEGER DEFAULT 0,
	[informado] INTEGER DEFAULT 0,
	[acera] INTEGER,
	[textoCambioEstado] TEXT,
	[borrado] INTEGER DEFAULT 0,
	[ordenTrabajo] INTEGER DEFAULT 0,
	[x] INTEGER DEFAULT 0,
	[y] INTEGER DEFAULT 0,
	[xMercator] INTEGER DEFAULT 0,
	[yMercator] INTEGER DEFAULT 0,
	[municipio] TEXT
);
CREATE INDEX IF NOT EXISTS  [inci_idx1] ON [incidencias] ([idExterno], [empresa]);


CREATE TABLE IF NOT EXISTS [incidencias_fotos] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT,
  	[idExterno] INTEGER NOT NULL, 
  	[empresa] INTEGER NOT NULL,
  	[incidencia] INTEGER NOT NULL,
  	[incidenciaExterno] INTEGER NOT NULL,
  	[usuario] INTEGER NOT NULL, 
  	[foto] BLOB,
  	[foto_url] NVARCHAR(255)

);
CREATE INDEX IF NOT EXISTS  [inci_idx1] ON [incidencias_fotos] ([idExterno], [empresa]);


#-- Volcando estructura para tabla ecosat.ecoincidenciasestados;
CREATE TABLE IF NOT EXISTS [incidencias_estados] (
  [id] INTEGER PRIMARY KEY AUTOINCREMENT,
  [idExterno] INT NOT NULL,
  [empresa] INT NOT NULL,
  [incidencia] INT NOT NULL,
  [incidenciaExterno] INT NOT NULL,
  [estado] INT NOT NULL,
  [fecha] DATETIME NOT NULL,
  [esAvisoFalso] INT,
  [observacion] NVARCHAR(255)
);
CREATE INDEX IF NOT EXISTS [incidencias_estados_idx1] ON [incidencias_estados] ([estado], [empresa]);
CREATE INDEX IF NOT EXISTS [incidencias_estados_idx2] ON [incidencias_estados] ([incidencia], [empresa]);
CREATE INDEX IF NOT EXISTS [incidencias_estados_idx4] ON [incidencias_estados] ([incidenciaExterno], [empresa]);
CREATE INDEX IF NOT EXISTS [incidencias_estados_idx3] ON [incidencias_estados] ([idExterno], [empresa]);


#-- Volcando estructura para tabla ecosat.ecoestados;
CREATE TABLE IF NOT EXISTS [estados] (
  [id] INTEGER PRIMARY KEY AUTOINCREMENT,
  [idExterno] INT NOT NULL,
  [empresa] INTEGER NOT NULL DEFAULT 0,
  [estado] NVARCHAR(45) NOT NULL
);
CREATE INDEX IF NOT EXISTS [estados_idx1] ON [estados] ([idExterno], [empresa]);


CREATE TABLE IF NOT EXISTS [flota_posiciones] (
  	[id] INTEGER PRIMARY KEY  AUTOINCREMENT, 
  	[empresa] INTEGER NOT NULL,
  	[descripcion] NVARCHAR(255) NOT NULL, 
  	[movil] INTEGER NOT NULL,
  	[fecha] DATETIME NOT NULL,  
  	[estado] INTEGER NOT NULL, 
  	[lat] DOUBLE, 
  	[lon] DOUBLE
);
CREATE INDEX IF NOT EXISTS [flota_posiciones_idx1] ON [flota_posiciones] ([id], [empresa]);
CREATE INDEX IF NOT EXISTS [flota_posiciones_idx2] ON [flota_posiciones] ([lat], [lon]);


CREATE TABLE IF NOT EXISTS [flota_posiciones_historico] (
  	[idRuta] INTEGER,
  	[empresa] INTEGER NOT NULL,
  	[movil] INTEGER NOT NULL,
  	[fecha] DATETIME NOT NULL,
  	[altura] INTEGER,
  	[rumbo] INTEGER,
  	[lat] DOUBLE,
  	[lon] DOUBLE,
  	[velocidad] DOUBLE,
  	[tiempoParada] DOUBLE,
  	CONSTRAINT [] PRIMARY KEY ([movil], [empresa], [fecha])
);

CREATE INDEX IF NOT EXISTS [flota_posiciones_historico_idx1] ON [flota_posiciones_historico] ([lat], [lon]);


CREATE TABLE IF NOT EXISTS [tags] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT,
  	[idExterno] INTEGER NOT NULL,
  	[empresa] INTEGER NOT NULL,
  	[matricula] NVARCHAR(50) NOT NULL,
  	[tag] NVARCHAR(11) NOT NULL,
  	[idExternoElemento] INTEGER NULL,
  	[idInternoElemento] INTEGER NULL
);
CREATE INDEX IF NOT EXISTS  [tags_idx1] ON [tags] ([idExterno], [empresa]);

CREATE TABLE IF NOT EXISTS [usu_moviles] (
  	[id] INTEGER PRIMARY KEY  AUTOINCREMENT, 
  	[empresa] INTEGER NOT NULL,
  	[usuario] INTEGER NOT NULL, 
  	[movil] INTEGER NOT NULL
);
CREATE INDEX IF NOT EXISTS [usu_moviles_idx1] ON [usu_moviles] ([empresa], [usuario], [movil]);

CREATE TABLE IF NOT EXISTS `elementos_modelos_nllenado` (
	`id`	INTEGER NOT NULL,
	`empresa`	INTEGER NOT NULL,
	`codigo_modelo`	INTEGER NOT NULL,
	`fracciones`	INTEGER NOT NULL,
	`peso_lleno`	INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS [elementos_modelos_nllenado_idx1] ON [elementos_modelos_nllenado] ([empresa], [codigo_modelo]);

CREATE TABLE IF NOT EXISTS `errores` (
	`id`	INTEGER PRIMARY KEY AUTOINCREMENT,
	`message`	TEXT,
	`fecha`	DATETIME NOT NULL,
	`stacktrace`	TEXT
);


CREATE TABLE IF NOT EXISTS `provincias` (
	`id`	INTEGER PRIMARY KEY,
	`provincia`	TEXT
);

CREATE TABLE IF NOT EXISTS `sensores_nivel_llenado` (
	`id`	INTEGER PRIMARY KEY AUTOINCREMENT,
	`codigo_elemento`	INTEGER NOT NULL,
	`codigo_movil`	INTEGER,
	`empresa`	INTEGER NOT NULL,
	`fecha_registro`	NUMERIC NOT NULL,
	`numero_fraccion`	INTEGER NOT NULL,
	`procesado`	INTEGER NOT NULL DEFAULT 0
);

CREATE INDEX IF NOT EXISTS [sensores_nivel_llenado_idx1] ON [sensores_nivel_llenado] ([codigo_elemento]);
CREATE INDEX IF NOT EXISTS [sensores_nivel_llenado_idx2] ON [sensores_nivel_llenado] ([procesado]);

CREATE TABLE IF NOT EXISTS `sensores_lavado` (
	`id`	INTEGER PRIMARY KEY AUTOINCREMENT,
	`codigo_elemento`	INTEGER NOT NULL,
	`codigo_movil`	INTEGER,
	`empresa`	INTEGER NOT NULL,
	`fecha_registro`	NUMERIC NOT NULL
);
CREATE INDEX IF NOT EXISTS [sensores_lavado_idx1] ON [sensores_lavado] ([codigo_elemento]);


CREATE TABLE IF NOT EXISTS `municipios` (
	`id`	INTEGER PRIMARY KEY AUTOINCREMENT,
	`municipio`	TEXT,
	`provincia` INTEGER
);


CREATE TABLE IF NOT EXISTS `flota` (
	`codigo`	INTEGER,
	`nombre`	TEXT,
	`empresa`	INTEGER,
	PRIMARY KEY(codigo)
);
CREATE INDEX IF NOT EXISTS [flota_idx1] ON [flota] ([empresa]);

 CREATE TABLE IF NOT EXISTS  [areas] (
  [codigo] INTEGER NOT NULL,
  [empresa] INTEGER NOT NULL,
  [abreviatura] VARCHAR2(50),
  [nombre] VARCHAR2(255),
  [superficie] INTEGER,
  [radio_lado] INTEGER,
  [centro_lat] DOUBLE,
  [centro_lon] DOUBLE,
  [forma] INTEGER,
  [grupo] INTEGER,
  [nombre_grupo] VARCHAR2(255),
  CONSTRAINT [] PRIMARY KEY ([codigo], [empresa])
 );
 CREATE INDEX IF NOT EXISTS [areas_nombre_grupo_idx1] ON [areas] ([nombre_grupo]);
 CREATE INDEX IF NOT EXISTS [areas_grupo_idx1] ON [areas] ([grupo]);

CREATE TABLE IF NOT EXISTS  [areas_puntos] (
  [area] INTEGER NOT NULL,
  [empresa] INTEGER NOT NULL,
  [latitud] DOUBLE,
  [longitud] DOUBLE,
  [orden] INTEGER,
  CONSTRAINT [] PRIMARY KEY ([area], [empresa], [latitud], [longitud], [orden])
  );

CREATE TABLE IF NOT EXISTS  [areas_puntos_control] (
  [area] INTEGER NOT NULL,
  [empresa] INTEGER NOT NULL,
  [latitud] DOUBLE,
  [longitud] DOUBLE,
  CONSTRAINT [] PRIMARY KEY ([area], [empresa], [latitud], [longitud])
  );

CREATE TABLE IF NOT EXISTS  [areas_grupos] (
  [id] INTEGER NOT NULL,
  [empresa] INTEGER NOT NULL,
  [abreviatura] VARCHAR2(50),
  [nombre] VARCHAR2(255),
  [clicks] INTEGER DEFAULT 0,
  CONSTRAINT [] PRIMARY KEY ([id], [empresa])
);

CREATE INDEX IF NOT EXISTS [areas_grupo_idx2] ON [areas_grupos] ([nombre]);

CREATE TABLE IF NOT EXISTS [planchadas] (
    [id] INTEGER NOT NULL,
    [empresa] INTEGER NOT NULL,
    [codigo_planchada] VARCHAR2(50) NOT NULL,
    [descripcion] VARCHAR2(50),
    [lote_id] INTEGER NOT NULL,
    CONSTRAINT [] PRIMARY KEY ([id], [empresa])
);

CREATE INDEX IF NOT EXISTS [planchadas_idx1] ON [planchadas] ([empresa]);

CREATE TABLE IF NOT EXISTS [operations_done] (
    [id] INTEGER NOT NULL,
    [code_mobile] INTEGER NOT NULL,
    [code_operation] INTEGER NOT NULL,
    [date] DATETIME NOT NULL,
    [description] VARCHAR2(255),
    [value] VARCHAR2(255),
    [lat] DOUBLE,
    [lng] DOUBLE,
    [idInternoElemento] INTEGER,
    CONSTRAINT [] PRIMARY KEY ([id])
);

CREATE INDEX IF NOT EXISTS [operations_done_idx1] ON [operations_done] ([code_mobile]);
CREATE UNIQUE INDEX IF NOT EXISTS [idx_unique_code_mobile_date] ON [operations_done] ([code_mobile], [date]);

CREATE TABLE IF NOT EXISTS [vehiculos] (
    [codigo] INTEGER NOT NULL,
    [nombre] TEXT,
    [empresa] INTEGER NOT NULL,
    [tipo] INTEGER NOT NULL,
    [imei] TEXT,
    CONSTRAINT [] PRIMARY KEY ([codigo], [empresa], [tipo])
);

CREATE TABLE IF NOT EXISTS [tipo_zonas] (
    [id] INTEGER NOT NULL,
    [nombre] VARCHAR2(255) NOT NULL,
    [abreviatura] VARCHAR2(255) NOT NULL,
    [fecha_baja] DATETIME DEFAULT NULL,
    CONSTRAINT [] PRIMARY KEY ([id])
);

#21061101;
CREATE TABLE IF NOT EXISTS [motivos_baja] (
    [id] INTEGER NOT NULL,
    [abreviatura] VARCHAR2(255) NOT NULL,
    [nombre] VARCHAR2(255) NOT NULL,
    [descripcion] VARCHAR2(255) NOT NULL,
    CONSTRAINT [] PRIMARY KEY ([id])
);

#Valores pre-configurados;
DELETE FROM conf WHERE clave='versionScript';
INSERT INTO conf (clave, valor) VALUES ('versionScript', '23030901');

#DELETE FROM conf WHERE clave='tipoSoft';
#INSERT INTO conf (clave, valor) VALUES ('tipoSoft', '101');
#DELETE FROM conf WHERE clave='menu';
#DELETE FROM conf WHERE clave='save_ruta';
#INSERT INTO conf (clave, valor) VALUES ('save_ruta', '0');

#INSERT INTO conf (clave, valor) VALUES ('webSvc', 'http://cieza.movisat.com:8083');
#INSERT INTO conf (clave, valor) VALUES ('webSvc', 'http://********:8084');
#INSERT INTO conf (clave, valor) VALUES ('webSvc', 'http://*********:8083');

CREATE TABLE IF NOT EXISTS [lecturas] (
  	[id] INTEGER PRIMARY KEY AUTOINCREMENT,
  	[fecha] NVARCHAR(255),
  	[tag] NVARCHAR(255),
  	[raw] NVARCHAR(255),
  	[tipo] NVARCHAR(255)
);

CREATE TABLE IF NOT EXISTS identificaciones (
    codigoMovil INTEGER,
    nombreMovil TEXT,
    nombreCategoria TEXT,
    nombreEmpleado TEXT,
    fechaInicio TEXT
);
