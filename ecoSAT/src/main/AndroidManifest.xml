<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE"/>
        </intent>
    </queries>

    <permission
        android:name="com.google.maps.android.utils.permission.MAPS_RECEIVE"
        android:protectionLevel="signature" />

    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission android:name="com.android.vending.CHECK_LICENSE" />
    <uses-permission android:name="com.google.maps.android.utils.permission.MAPS_RECEIVE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!--uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<!--    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />-->
    <!--<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />-->
    <uses-permission android:name="android.permission.SET_ORIENTATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- Permite realizar la primera sincro en segundo plano-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <!-- Para las notificaciones -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- // APPCIRCLE_UPDATER -->
<!--    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/>-->



    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.nfc"
        android:required="false" />

    <application
        android:name="com.movisat.application.EcoSATApplication"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:hardwareAccelerated="false"
        android:largeHeap="true"
        android:theme="@style/CustomActionBarTheme"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        android:allowBackup="false"
        android:enableOnBackInvokedCallback="true"
        tools:replace="android:allowBackup"
        >

        <receiver
            android:name=".RestartReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="**********">
                <category android:name="android.intent.category.DEFAULT"/>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <provider
             android:name="androidx.core.content.FileProvider"
             android:authorities="com.movisat.ecosat"
             android:exported="false"
             android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.maps.v2.API_KEY"
            android:value="AIzaSyAgWVhIYu_UkdO1szK4I-VV4wRKA70FK3w" />

        <activity
            android:name=".NfcActivity"
            android:exported="true">

            <intent-filter>
                <action android:name="android.nfc.action.TECH_DISCOVERED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <meta-data
                android:name="android.nfc.action.TECH_DISCOVERED"
                android:resource="@xml/nfc_tech_list" />
        </activity>

        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:theme="@style/MainTheme"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MyLoadingActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/inicio_sesion" />
        <activity
            android:name=".LoginActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/inicio_sesion" />
        <activity
            android:name=".OperationsActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_operations_dones" />
        <activity
            android:name=".AddElemActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/add_elem_activity"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".AddElemActivityCamacho"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/add_elem_activity"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ProcesarElementoActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize"
            android:label="@string/procesar_elementos"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".NivelLlenadoActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/nivel_llenado_title" />
        <activity
            android:name=".FilterElemActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/filter_elem_activity" />
        <activity
            android:name=".FlotaActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:label="@string/tituloFlota">
            <meta-data
                android:name="android.app.searchable"
                android:resource="@xml/searchable" />
            <intent-filter>
                <action android:name="android.intent.action.SEARCH" />
            </intent-filter>
        </activity>

        <activity
            android:name=".FlotaHistoricoActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/hitoricoRutas" />

        <activity
            android:name=".AreasActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/tituloAreasActivity" />
        <activity
            android:name=".IncidenciasActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/listado_incidencias" />
        <activity
            android:name=".AddInciActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/add_inci_activity" />
        <activity
            android:name=".AsignarIncidenciasActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/asignar_incidencias_activity" />
        <activity
            android:name=".SeleccionUsuarioIncidenciaActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/seleccion_usuario_incidencia_activity" />
        <activity
            android:name=".FilterInciActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/filter_inci_activity" />
        <activity
            android:name=".FilterInciTiposActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/filter_inci_tipos_activity" />
        <activity
            android:name=".UpdateEstadoIncidencia"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_update_estado_incidencia" />
        <activity
            android:name=".Activacion"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_activacion" />
        <activity
            android:name=".InfoActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_info" />
        <activity
            android:name=".AddPesajeActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize"
            android:label="@string/title_vertedero"
            android:windowSoftInputMode="stateVisible" />
        <activity
            android:name=".TagNotFoundListActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_lectura_tag" />
        <activity
            android:name=".SustituirElemActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/sustituir_elemento" />
        <activity
            android:name=".DepositarElemActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/depositar_elemento" />
        <activity
            android:name=".RetirarElemActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/retirar_elemento" />
        <activity
            android:name=".AddElemActivity2"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/CreacionElemento" />
        <activity
            android:name=".SeleccionarVehiculoActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/seleccion_vehiculo" />

        <service
            android:name="com.movisat.services.MyLocationService"
            android:foregroundServiceType="location" />

        <receiver
            android:name="com.movisat.fragment.GestionElementos.receiverGestionElementos"
            android:exported="true"
            android:enabled="true" />
        <receiver
            android:name=".UpdateEstadoIncidencia.receiverUpdateEstado"
            android:exported="true"
            android:enabled="true" />
        <receiver
           
            android:name=".ShutdownReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
                <action android:name="android.intent.action.QUICKBOOT_POWEROFF" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".GruposActivity"
            android:exported="true"
            android:label="@string/title_activity_grupos" />
        <activity
            android:name=".SettingsActivity"
            android:exported="true"
            android:label="@string/title_activity_settings" />
        <activity
            android:name=".CallApiActivity"
            android:exported="true"
            android:label="@string/title_callAPI"
            android:launchMode="singleTop" />

        <activity
            android:name=".SustituirElementoActivity"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_sustitucion" />

        <receiver
            android:name=".NotificacionesActivity"
            android:label="NotificacionesActivity"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND">
            <intent-filter>
                <action android:name="com.movisat.INCIDENCIAS" />
            </intent-filter>
        </receiver>


        <provider
            android:name="com.movisat.utilities.MovisatShared"
            android:authorities="com.movisat.ecosat.shareDB"
            android:exported="true" />

        <service
            android:name="com.movisat.services.SyncService"
            android:foregroundServiceType="location"
            android:enabled="true"
            android:exported="false" />

    </application>

</manifest>