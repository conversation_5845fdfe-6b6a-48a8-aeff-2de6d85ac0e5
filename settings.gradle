include ':ecoSAT', ':materialDesign', ':googlemapcluster_lib', ':api', ':utils', ':rfid_uhf_u9000', ':utils_android',
        ':log', ":log_provider_console_android"

project(':api').projectDir = new File('./android-core/api')
project(':log').projectDir = new File('./android-core/log')
project(':log_provider_console_android').projectDir = new File('./android-core/log_provider_console_android')
project(':utils').projectDir = new File('./android-core/utils')
project(':utils_android').projectDir = new File('./android-core/utils_android')
project(':rfid_uhf_u9000').projectDir = new File('./android-core/rfid_uhf_u9000')

// APPCIRCLE_UPDATER
//include "app_update_appcircle"
//project (':app_update_appcircle').projectDir = new File('./android-core/app_update_appcircle')
