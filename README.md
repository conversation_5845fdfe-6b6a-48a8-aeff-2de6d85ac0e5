
# Cómo añadir módulos a un proyecto Android
No parecen funcionar las dependencias transitivas, se deben añadir también todos los módulos de android-core que importe el módulo.

1- En el settings.gradle añadir el nombre del módulo/librería/paquete y la ruta:

    include ':mi_modulo'
    project (':mi_modulo').projectDir = new File('./android-core/mi_modulo')

2- En el archivo build.gradle dentro de la carpeta "app" añadir la dependencias en "dependencies":

    dependencies {
        //...
        implementation project(':mi_modulo')
    }