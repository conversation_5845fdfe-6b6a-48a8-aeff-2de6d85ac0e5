# FUNCIONA BASTANTE BIEN
# adb exec-out run-as com.movisat.ecosat cat databases/ecosat.sqlite > ecosat.sqlite

# ESTA FUNCIONA PEOR
# adb shell "run-as com.movisat.ecosat cat '/data/data/com.movisat.ecosat/databases/ecosat.sqlite' > 'mnt/sdcard/ecosat.sqlite'";
# adb pull "mnt/sdcard/ecosat.sqlite" './ecosat.sqlite';
# adb shell "rm 'mnt/sdcard/ecosat.sqlite'";

#!/bin/bash

# Nombre del paquete de la aplicación
app_package="com.movisat.ecosat"

# Ejecutar el comando "adb devices -l" y obtener la lista de dispositivos
devices=$(adb devices -l | grep -v "List of devices")

# Verificar si la lista de dispositivos está vacía
if [[ -z $devices ]]; then
  echo "No se detectaron dispositivos conectados."
  exit 1
fi

# Contar el número de dispositivos conectados
devices_count=$(echo "$devices" | wc -l)

# Si solo hay un dispositivo, se selecciona automáticamente
if [[ $devices_count -eq 1 ]]; then
  name=$(echo "$devices" | awk '{print $1}')
  echo "Se detectó un dispositivo: $name. Procediendo con la descarga..."
else
  # Si hay más de un dispositivo, se muestra la lista y se pide seleccionar uno
  echo "Dispositivos conectados:"
  echo "$devices"
  read -p "Introduce el transport_id: " transport_id
fi

# Verificar si la aplicación está instalada
app_installed=$(adb -t "$transport_id" shell pm list packages | grep -w "package:$app_package")

if [[ -z $app_installed ]]; then
  echo "El dispositivo seleccionado no tiene la aplicación $app_package instalada."
  exit 1
fi

# Obtener la fecha y hora actual
current_datetime=$(date +"%y-%m-%d_%H-%M-%S")

# Ejecutar el comando adb con el transport_id seleccionado y guardar el resultado en un archivo
output_file="ecosat_${current_datetime}.sqlite"
adb -t "$transport_id" exec-out run-as com.movisat.ecosat cat databases/ecosat.sqlite > "$output_file"
status=$?

if [ $status -eq 0 ]; then
  echo "¡Archivo guardado exitosamente en $output_file!"
  
  # Intentar abrir el archivo con el comando apropiado según el sistema operativo
  if command -v xdg-open &> /dev/null; then
    xdg-open "$output_file"
  elif command -v open &> /dev/null; then
    open "$output_file"
  else
    start "$output_file"
  fi
else
  echo "Hubo un error al guardar el archivo. Verifica que el transport_id sea correcto y que el dispositivo esté autorizado."
fi
