package com.movisat.ftp_service;

import java.io.File;

public class FtpService {

    private final String HOST = "sempre01.movisat.com";
    private final String USER = "android";
    private final String PASS = "movisatAndroid2015";

    public void upload(File file, String remoteName) {
        Ftp.sendFile(
                HOST, USER, PASS,
                file.getAbsolutePath(),
                remoteName
        );
    }

    public void upload(String host, String user, String pass, File file, String remoteName) {
        Ftp.sendFile(
                host, user, pass,
                file.getAbsolutePath(),
                remoteName
        );
    }


    private static FtpService _instance = null;

    private FtpService() {

    }

    public static FtpService get() {
        if (_instance == null)
            _instance = new FtpService();
        return _instance;
    }

}



